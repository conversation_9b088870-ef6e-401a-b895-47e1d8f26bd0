#!/usr/bin/env python3
"""
战斗系统测试UI

提供完整的图形界面来测试战斗系统，显示所有内部和外部信息
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class BattleTestUI:
    def __init__(self, root):
        self.root = root
        self.root.title("奥奇传说AI战斗系统测试界面")
        self.root.geometry("1400x900")
        
        # 战斗引擎和相关对象
        self.engine = None
        self.formation1 = None
        self.formation2 = None
        self.spirit_service = None
        self.available_spirits = []
        
        # 创建界面
        self.create_widgets()
        
        # 初始化系统
        self.initialize_system()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧面板 - 信息显示区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_control_panel(left_frame)
        self.create_info_panel(right_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 系统状态
        status_frame = ttk.LabelFrame(parent, text="系统状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="系统未初始化", foreground="red")
        self.status_label.pack()
        
        # 战斗配置
        config_frame = ttk.LabelFrame(parent, text="战斗配置", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 回合限制
        ttk.Label(config_frame, text="回合限制:").pack(anchor=tk.W)
        self.round_limit_var = tk.StringVar(value="10")
        ttk.Entry(config_frame, textvariable=self.round_limit_var, width=10).pack(anchor=tk.W, pady=(0, 5))
        
        # 顺位加气数量
        ttk.Label(config_frame, text="顺位加气数量:").pack(anchor=tk.W)
        self.bonus_energy_var = tk.StringVar(value="50")
        ttk.Entry(config_frame, textvariable=self.bonus_energy_var, width=10).pack(anchor=tk.W, pady=(0, 5))
        
        # 精灵选择
        spirits_frame = ttk.LabelFrame(parent, text="精灵选择", padding=10)
        spirits_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 队伍1精灵
        ttk.Label(spirits_frame, text="队伍1精灵:").pack(anchor=tk.W)
        self.spirit1_var = tk.StringVar()
        self.spirit1_combo = ttk.Combobox(spirits_frame, textvariable=self.spirit1_var, width=25)
        self.spirit1_combo.pack(anchor=tk.W, pady=(0, 5))
        
        # 队伍2精灵
        ttk.Label(spirits_frame, text="队伍2精灵:").pack(anchor=tk.W)
        self.spirit2_var = tk.StringVar()
        self.spirit2_combo = ttk.Combobox(spirits_frame, textvariable=self.spirit2_var, width=25)
        self.spirit2_combo.pack(anchor=tk.W, pady=(0, 5))
        
        # 控制按钮
        button_frame = ttk.LabelFrame(parent, text="控制操作", padding=10)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="初始化系统", command=self.initialize_system).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="创建战斗", command=self.create_battle).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="执行一回合", command=self.execute_round).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="自动战斗", command=self.auto_battle).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="重置战斗", command=self.reset_battle).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="清空日志", command=self.clear_logs).pack(fill=tk.X, pady=(0, 5))
    
    def create_info_panel(self, parent):
        """创建信息显示面板"""
        # 创建笔记本控件
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 战斗状态标签页
        self.create_battle_status_tab(notebook)
        
        # 精灵信息标签页
        self.create_spirits_info_tab(notebook)
        
        # 引擎信息标签页
        self.create_engine_info_tab(notebook)
        
        # 回合详情标签页
        self.create_round_details_tab(notebook)
        
        # 系统日志标签页
        self.create_logs_tab(notebook)
        
        # 调试信息标签页
        self.create_debug_tab(notebook)
    
    def create_battle_status_tab(self, notebook):
        """创建战斗状态标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="战斗状态")
        
        # 战斗概览
        overview_frame = ttk.LabelFrame(frame, text="战斗概览", padding=10)
        overview_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.battle_overview_text = scrolledtext.ScrolledText(overview_frame, height=8, width=80)
        self.battle_overview_text.pack(fill=tk.BOTH, expand=True)
        
        # 阵型显示
        formation_frame = ttk.LabelFrame(frame, text="阵型状态", padding=10)
        formation_frame.pack(fill=tk.BOTH, expand=True)
        
        self.formation_text = scrolledtext.ScrolledText(formation_frame, height=15, width=80)
        self.formation_text.pack(fill=tk.BOTH, expand=True)
    
    def create_spirits_info_tab(self, notebook):
        """创建精灵信息标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="精灵信息")
        
        # 精灵详细信息
        self.spirits_info_text = scrolledtext.ScrolledText(frame, height=30, width=80)
        self.spirits_info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_engine_info_tab(self, notebook):
        """创建引擎信息标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="引擎信息")
        
        # 引擎配置和状态
        self.engine_info_text = scrolledtext.ScrolledText(frame, height=30, width=80)
        self.engine_info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_round_details_tab(self, notebook):
        """创建回合详情标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="回合详情")
        
        # 回合执行详情
        self.round_details_text = scrolledtext.ScrolledText(frame, height=30, width=80)
        self.round_details_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_logs_tab(self, notebook):
        """创建系统日志标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="系统日志")
        
        # 系统日志
        self.logs_text = scrolledtext.ScrolledText(frame, height=30, width=80)
        self.logs_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_debug_tab(self, notebook):
        """创建调试信息标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="调试信息")
        
        # 调试信息
        self.debug_text = scrolledtext.ScrolledText(frame, height=30, width=80)
        self.debug_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.logs_text.insert(tk.END, log_entry)
        self.logs_text.see(tk.END)
        
        # 同时输出到控制台
        print(f"{level}: {message}")
    
    def update_status(self, status, color="black"):
        """更新状态显示"""
        self.status_label.config(text=status, foreground=color)
        self.log_message(f"状态更新: {status}")
    
    def initialize_system(self):
        """初始化系统"""
        try:
            self.log_message("开始初始化核心系统...")
            
            # 初始化核心系统
            from core.system_manager import initialize_core_systems
            initialize_core_systems()
            
            # 获取精灵服务
            from core.spirit.spirit_service import get_spirit_service
            self.spirit_service = get_spirit_service()
            self.available_spirits = self.spirit_service.list_available_spirits()
            
            # 更新精灵选择列表
            spirit_names = [spirit for spirit in self.available_spirits]
            self.spirit1_combo['values'] = spirit_names
            self.spirit2_combo['values'] = spirit_names
            
            # 设置默认选择
            if len(spirit_names) >= 2:
                self.spirit1_var.set(spirit_names[0])
                self.spirit2_var.set(spirit_names[1])
            
            self.update_status("系统初始化完成", "green")
            self.log_message(f"发现 {len(self.available_spirits)} 个可用精灵")
            
            # 更新系统信息
            self.update_system_info()
            
        except Exception as e:
            self.update_status(f"系统初始化失败: {e}", "red")
            self.log_message(f"系统初始化失败: {e}", "ERROR")
            messagebox.showerror("错误", f"系统初始化失败: {e}")
    
    def update_system_info(self):
        """更新系统信息显示"""
        try:
            info = []
            info.append("=== 系统信息 ===")
            info.append(f"可用精灵数量: {len(self.available_spirits)}")
            info.append(f"精灵列表: {', '.join(self.available_spirits)}")
            info.append("")
            
            # 显示系统配置
            info.append("=== 系统配置 ===")
            try:
                from core.config import get_config
                config = get_config()
                info.append(f"调试模式: {config.debug}")
                info.append(f"日志级别: {config.log_level}")
            except:
                info.append("配置信息获取失败")
            
            info.append("")
            
            self.debug_text.delete(1.0, tk.END)
            self.debug_text.insert(tk.END, "\n".join(info))
            
        except Exception as e:
            self.log_message(f"更新系统信息失败: {e}", "ERROR")
    
    def create_battle(self):
        """创建战斗"""
        try:
            if not self.spirit_service:
                messagebox.showerror("错误", "请先初始化系统")
                return
            
            self.log_message("开始创建战斗...")
            
            # 获取选择的精灵
            spirit1_name = self.spirit1_var.get()
            spirit2_name = self.spirit2_var.get()
            
            if not spirit1_name or not spirit2_name:
                messagebox.showerror("错误", "请选择两个精灵")
                return
            
            # 创建阵型
            from core.formation import Formation
            
            self.formation1 = Formation()
            self.formation2 = Formation()
            
            # 创建精灵
            spirit1 = self.spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
            spirit2 = self.spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
            
            if not spirit1 or not spirit2:
                messagebox.showerror("错误", "精灵创建失败")
                return
            
            # 添加到阵型
            self.formation1.add_spirit(spirit1, 1, 1)
            self.formation2.add_spirit(spirit2, 3, 1)
            
            # 创建战斗引擎
            from core.battle.engines.factory import create_battle_engine
            
            round_limit = int(self.round_limit_var.get())
            bonus_energy = int(self.bonus_energy_var.get())
            
            self.engine = create_battle_engine(
                self.formation1,
                self.formation2,
                round_limit=round_limit,
                turn_order_bonus_energy=bonus_energy
            )
            
            self.update_status("战斗创建完成", "green")
            self.log_message("战斗引擎创建成功")
            
            # 更新所有信息显示
            self.update_all_info()
            
        except Exception as e:
            self.update_status(f"战斗创建失败: {e}", "red")
            self.log_message(f"战斗创建失败: {e}", "ERROR")
            messagebox.showerror("错误", f"战斗创建失败: {e}")
    
    def update_all_info(self):
        """更新所有信息显示"""
        self.update_battle_overview()
        self.update_formation_display()
        self.update_spirits_info()
        self.update_engine_info()
    
    def update_battle_overview(self):
        """更新战斗概览"""
        if not self.engine:
            return
        
        try:
            info = []
            info.append("=== 战斗概览 ===")
            info.append(f"当前回合: {self.engine.battle_state.round_num}")
            info.append(f"回合限制: {self.engine.round_limit}")
            info.append(f"战斗状态: {'进行中' if not self.engine.battle_state.winner else '已结束'}")
            
            if self.engine.battle_state.winner is not None:
                info.append(f"获胜方: 队伍{self.engine.battle_state.winner}")
            
            info.append("")
            info.append("=== 队伍状态 ===")
            
            # 队伍1状态
            team1_spirits = self.engine.battle_state.get_living_spirits(0)
            info.append(f"队伍1: {len(team1_spirits)} 个存活精灵")
            
            # 队伍2状态
            team2_spirits = self.engine.battle_state.get_living_spirits(1)
            info.append(f"队伍2: {len(team2_spirits)} 个存活精灵")
            
            self.battle_overview_text.delete(1.0, tk.END)
            self.battle_overview_text.insert(tk.END, "\n".join(info))
            
        except Exception as e:
            self.log_message(f"更新战斗概览失败: {e}", "ERROR")
    
    def update_formation_display(self):
        """更新阵型显示"""
        if not self.formation1 or not self.formation2:
            return
        
        try:
            info = []
            info.append("=== 阵型状态 ===")
            info.append("")
            info.append("队伍1阵型:")
            info.append(str(self.formation1))
            info.append("")
            info.append("队伍2阵型:")
            info.append(str(self.formation2))
            
            self.formation_text.delete(1.0, tk.END)
            self.formation_text.insert(tk.END, "\n".join(info))
            
        except Exception as e:
            self.log_message(f"更新阵型显示失败: {e}", "ERROR")

    def update_spirits_info(self):
        """更新精灵信息"""
        if not self.engine:
            return

        try:
            info = []
            info.append("=== 精灵详细信息 ===")
            info.append("")

            all_spirits = self.engine.battle_state.get_all_spirits()

            for i, spirit in enumerate(all_spirits, 1):
                info.append(f"精灵 {i}: {spirit.name}")
                info.append(f"  队伍: {spirit.team}")
                info.append(f"  位置: {getattr(spirit, 'position', 'Unknown')}")
                info.append(f"  生命值: {spirit.current_hp}/{spirit.max_hp}")
                info.append(f"  气势: {getattr(spirit, 'energy', 0)}/{getattr(spirit, 'max_energy', 100)}")
                info.append(f"  存活状态: {'存活' if spirit.is_alive else '死亡'}")

                # 属性信息
                if hasattr(spirit, 'attributes'):
                    attrs = spirit.attributes
                    info.append(f"  实攻: {getattr(attrs, 'actual_attack', 'N/A')}")
                    info.append(f"  实防: {getattr(attrs, 'actual_defense', 'N/A')}")
                    info.append(f"  速度: {getattr(attrs, 'speed', 'N/A')}")

                # 效果信息
                if hasattr(spirit, 'effects') and spirit.effects:
                    info.append(f"  当前效果: {len(spirit.effects)} 个")
                    for effect in spirit.effects[:3]:  # 只显示前3个效果
                        info.append(f"    - {getattr(effect, 'name', 'Unknown Effect')}")
                else:
                    info.append(f"  当前效果: 无")

                info.append("")

            self.spirits_info_text.delete(1.0, tk.END)
            self.spirits_info_text.insert(tk.END, "\n".join(info))

        except Exception as e:
            self.log_message(f"更新精灵信息失败: {e}", "ERROR")

    def update_engine_info(self):
        """更新引擎信息"""
        if not self.engine:
            return

        try:
            info = []
            info.append("=== 战斗引擎信息 ===")
            info.append("")

            # 基本配置
            info.append("基本配置:")
            info.append(f"  引擎类型: {self.engine.__class__.__name__}")
            info.append(f"  回合限制: {self.engine.round_limit}")
            info.append(f"  顺位加气数量: {self.engine.turn_order_bonus_energy}")
            info.append("")

            # 回合顺序策略
            info.append("回合顺序策略:")
            strategy = self.engine.turn_order_strategy
            info.append(f"  策略类型: {strategy.__class__.__name__}")

            if hasattr(strategy, 'base_strategy'):
                info.append(f"  基础策略: {strategy.base_strategy.__class__.__name__}")

            if hasattr(strategy, 'bonus_manager'):
                bonus_mgr = strategy.bonus_manager
                info.append(f"  奖励管理器: {bonus_mgr.__class__.__name__}")
                info.append(f"  奖励数量: {bonus_mgr.energy_bonus}")
                info.append(f"  当前顺位索引: {bonus_mgr.current_turn_index}")
                info.append(f"  已获得奖励精灵: {len(bonus_mgr.spirits_received_bonus)}")

            info.append("")

            # 胜负判定
            info.append("胜负判定:")
            condition = self.engine.condition_checker
            info.append(f"  判定类型: {condition.__class__.__name__}")
            info.append("")

            # 动作执行器
            info.append("动作执行器:")
            executor = self.engine.action_executor
            info.append(f"  执行器类型: {executor.__class__.__name__}")
            info.append("")

            # 战斗状态
            info.append("战斗状态:")
            battle_state = self.engine.battle_state
            info.append(f"  当前回合: {battle_state.round_num}")
            info.append(f"  获胜方: {battle_state.winner if battle_state.winner is not None else '未决定'}")
            info.append(f"  总精灵数: {len(battle_state.get_all_spirits())}")
            info.append(f"  队伍1存活: {len(battle_state.get_living_spirits(0))}")
            info.append(f"  队伍2存活: {len(battle_state.get_living_spirits(1))}")

            self.engine_info_text.delete(1.0, tk.END)
            self.engine_info_text.insert(tk.END, "\n".join(info))

        except Exception as e:
            self.log_message(f"更新引擎信息失败: {e}", "ERROR")

    def execute_round(self):
        """执行一回合"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return

        try:
            self.log_message(f"开始执行第 {self.engine.battle_state.round_num + 1} 回合...")

            # 执行回合
            result = self.engine.execute_round()

            # 记录回合详情
            self.log_round_details(result)

            # 更新所有信息
            self.update_all_info()

            # 检查战斗是否结束
            if result.get("type") == "battle_end":
                winner = result.get("winner")
                self.update_status(f"战斗结束！获胜方: 队伍{winner}", "blue")
                self.log_message(f"战斗结束！获胜方: 队伍{winner}")
                messagebox.showinfo("战斗结束", f"战斗结束！\n获胜方: 队伍{winner}")
            else:
                self.update_status(f"第 {self.engine.battle_state.round_num} 回合执行完成", "green")
                self.log_message(f"第 {self.engine.battle_state.round_num} 回合执行完成")

        except Exception as e:
            self.update_status(f"回合执行失败: {e}", "red")
            self.log_message(f"回合执行失败: {e}", "ERROR")
            messagebox.showerror("错误", f"回合执行失败: {e}")

    def log_round_details(self, result):
        """记录回合详情"""
        try:
            details = []
            details.append(f"=== 第 {self.engine.battle_state.round_num} 回合详情 ===")
            details.append(f"时间: {datetime.now().strftime('%H:%M:%S')}")
            details.append(f"结果类型: {result.get('type', 'unknown')}")
            details.append("")

            # 行动队列信息
            if hasattr(self.engine, 'turn_order_strategy'):
                try:
                    action_queue = self.engine.turn_order_strategy.create_action_queue(self.engine.battle_state)
                    details.append("行动队列:")
                    for i, spirit in enumerate(action_queue, 1):
                        details.append(f"  {i}. {spirit.name} (队伍{spirit.team})")
                    details.append("")
                except:
                    details.append("行动队列信息获取失败")
                    details.append("")

            # 精灵状态变化
            details.append("精灵状态:")
            all_spirits = self.engine.battle_state.get_all_spirits()
            for spirit in all_spirits:
                status = "存活" if spirit.is_alive else "死亡"
                energy = getattr(spirit, 'energy', 0)
                details.append(f"  {spirit.name}: HP={spirit.current_hp}/{spirit.max_hp}, 气势={energy}, {status}")

            details.append("")
            details.append("-" * 50)
            details.append("")

            # 添加到回合详情显示
            self.round_details_text.insert(tk.END, "\n".join(details))
            self.round_details_text.see(tk.END)

        except Exception as e:
            self.log_message(f"记录回合详情失败: {e}", "ERROR")

    def auto_battle(self):
        """自动战斗"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return

        def run_auto_battle():
            try:
                self.log_message("开始自动战斗...")
                round_count = 0
                max_rounds = int(self.round_limit_var.get())

                while (self.engine.battle_state.winner is None and
                       self.engine.battle_state.round_num < max_rounds):

                    # 在主线程中执行回合
                    self.root.after(0, self.execute_round)

                    round_count += 1

                    # 短暂延迟以便观察
                    import time
                    time.sleep(1)

                    # 检查是否需要停止
                    if round_count >= max_rounds:
                        break

                # 在主线程中更新最终状态
                self.root.after(0, lambda: self.log_message("自动战斗完成"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"自动战斗失败: {e}", "ERROR"))

        # 在新线程中运行自动战斗
        threading.Thread(target=run_auto_battle, daemon=True).start()

    def reset_battle(self):
        """重置战斗"""
        try:
            self.engine = None
            self.formation1 = None
            self.formation2 = None

            # 清空所有显示
            self.battle_overview_text.delete(1.0, tk.END)
            self.formation_text.delete(1.0, tk.END)
            self.spirits_info_text.delete(1.0, tk.END)
            self.engine_info_text.delete(1.0, tk.END)
            self.round_details_text.delete(1.0, tk.END)

            self.update_status("战斗已重置", "orange")
            self.log_message("战斗已重置")

        except Exception as e:
            self.log_message(f"重置战斗失败: {e}", "ERROR")

    def clear_logs(self):
        """清空日志"""
        self.logs_text.delete(1.0, tk.END)
        self.debug_text.delete(1.0, tk.END)
        self.round_details_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

def main():
    """主函数"""
    root = tk.Tk()
    app = BattleTestUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
