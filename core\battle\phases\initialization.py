"""BattleInitializationPhase – handles battle start logic."""
from __future__ import annotations

from typing import List, Any

from .base import IBattlePhase
from ..models import BattleState  # type: ignore
from ...performance import monitor_battle_performance


class BattleInitializationPhase(IBattlePhase):
    """战斗初始化阶段 – 负责分发 BattleStartEvent。"""

    def __init__(self, turn_order_strategy):
        self.turn_order_strategy = turn_order_strategy

    @monitor_battle_performance
    def execute(self, battle_state: BattleState) -> List[Any]:
        """初始化战斗，分发战斗开始事件。"""
        from ...action import DispatchEventAction
        from ...event.events import BattleStartEvent

        actions: List[Any] = []

        # 分发战斗开始事件
        battle_start_event = BattleStartEvent(battle_state=battle_state)
        actions.append(
            DispatchEventAction(caster=None, event=battle_start_event)
        )

        return actions