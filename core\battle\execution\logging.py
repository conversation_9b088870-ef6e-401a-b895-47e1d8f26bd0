"""Logging-related action handlers."""
from __future__ import annotations

from typing import Optional, List

from core.battle.executor.executor import handler, UnifiedActionExecutor
from ...action import LogAction, BattleAction


@handler(LogAction)
def _handle_log(self: UnifiedActionExecutor, action: LogAction) -> Optional[List[BattleAction]]:  # noqa: D401
    """Record log entry into battle_log and battle_state."""
    if hasattr(self.battle_state, "add_log"):
        self.battle_state.add_log(action.message, action.level)

    log_entry = {
        "round": self.battle_state.round_num,
        "caster": action.caster.name if action.caster else "System",
        "message": action.message,
        "level": action.level,
    }
    self.battle_log.append(log_entry)
    return None 