"""
Targeting系统集成

将AI行动生成系统与现有的targeting系统和components系统集成，
提供更智能和准确的目标选择功能。
"""

from __future__ import annotations
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from dataclasses import dataclass

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill

from core.logging import get_logger

logger = get_logger("ai.targeting")

@dataclass
class EnhancedTargetSelectionResult:
    """增强的目标选择结果"""
    targets: List['IBattleEntity']
    reason: str
    selector_type: str = "unknown"
    priority_scores: Optional[Dict[str, float]] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.priority_scores is None:
            self.priority_scores = {}
        if self.metadata is None:
            self.metadata = {}

class EnhancedTargetSelector:
    """增强目标选择器 - 与targeting系统深度集成"""
    
    def __init__(self):
        self._selector_cache = {}  # 缓存目标选择器实例
    
    def select_targets_with_ai(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> EnhancedTargetSelectionResult:
        """使用AI增强的目标选择"""
        
        try:
            # 1. 首先尝试使用技能自带的目标选择器
            if hasattr(skill, 'target_selector') and skill.target_selector:
                return self._use_skill_target_selector(spirit, skill, battle_state)
            
            # 2. 根据技能类型推断目标选择器
            inferred_result = self._infer_target_selector(spirit, skill, battle_state)
            if inferred_result.targets:
                return inferred_result
            
            # 3. 使用智能目标选择
            return self._intelligent_target_selection(spirit, skill, battle_state)
            
        except Exception as e:
            logger.error(f"增强目标选择失败: {e}")
            return self._fallback_selection(spirit, battle_state)
    
    def _use_skill_target_selector(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> EnhancedTargetSelectionResult:
        """使用技能自带的目标选择器"""
        
        try:
            target_selector = skill.target_selector
            selector_name = type(target_selector).__name__
            
            # 创建技能上下文
            context = self._create_skill_context(skill)
            
            # 使用目标选择器获取目标
            targets = []
            try:
                # 确保battle_state有必要的方法
                if hasattr(battle_state, 'get_living_spirits') and not hasattr(battle_state, 'get_targetable_living_spirits'):
                    battle_state.get_targetable_living_spirits = battle_state.get_living_spirits

                # 如果context为None，创建一个默认的
                if context is None:
                    try:
                        from core.skill.skills import SkillContext
                        context = SkillContext(skill_level=1, additional_data={'ai_selection': True})
                    except:
                        # 如果还是失败，跳过使用技能目标选择器
                        raise Exception("无法创建SkillContext")

                targets = target_selector.select_targets(spirit, battle_state, context)

            except Exception as e:
                logger.warning(f"使用技能目标选择器失败: {e}")
                targets = []
            
            if targets:
                # 对目标进行AI优先级排序
                prioritized_targets = self._prioritize_targets(spirit, targets, skill, battle_state)
                
                target_names = [getattr(t, 'name', 'Unknown') for t in prioritized_targets]
                logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 使用 {selector_name} 选择目标: {', '.join(target_names)}")
                
                return EnhancedTargetSelectionResult(
                    targets=prioritized_targets,
                    reason=f"使用技能的 {selector_name}，AI优化排序",
                    selector_type=selector_name,
                    metadata={
                        'original_targets': len(targets),
                        'prioritized_targets': len(prioritized_targets),
                        'context': context.__dict__ if hasattr(context, '__dict__') else {}
                    }
                )
            else:
                return EnhancedTargetSelectionResult(
                    targets=[],
                    reason=f"技能的 {selector_name} 没有找到有效目标",
                    selector_type=selector_name
                )
                
        except Exception as e:
            logger.error(f"使用技能目标选择器失败: {e}")
            return EnhancedTargetSelectionResult(
                targets=[],
                reason=f"技能目标选择器执行失败: {e}",
                selector_type="error"
            )
    
    def _infer_target_selector(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> EnhancedTargetSelectionResult:
        """根据技能类型推断目标选择器"""
        
        try:
            # 分析技能元数据
            skill_metadata = getattr(skill, 'metadata', None)
            if not skill_metadata:
                return EnhancedTargetSelectionResult(targets=[], reason="技能缺少元数据")
            
            skill_name = getattr(skill_metadata, 'name', '')
            skill_type = getattr(skill_metadata, 'cast_type', 'ACTIVE')
            skill_tags = getattr(skill_metadata, 'tags', [])
            
            # 根据技能特征推断目标类型
            if skill_type == 'PASSIVE':
                return EnhancedTargetSelectionResult(
                    targets=[],
                    reason="被动技能不需要目标",
                    selector_type="passive"
                )
            
            # 根据技能名称和标签推断
            if any(keyword in skill_name.lower() for keyword in ['治疗', '恢复', '加血', 'heal']):
                # 治疗技能：选择血量最低的队友
                return self._select_healing_targets(spirit, battle_state)
            
            elif any(keyword in skill_name.lower() for keyword in ['群攻', '范围', 'aoe', '全体']):
                # 群攻技能：选择所有敌人
                return self._select_all_enemies(spirit, battle_state)
            
            elif any(tag in skill_tags for tag in ['BUFF', 'SUPPORT']):
                # 增益技能：选择队友
                return self._select_ally_targets(spirit, battle_state)
            
            else:
                # 默认：单体攻击敌人
                return self._select_single_enemy(spirit, battle_state)
                
        except Exception as e:
            logger.error(f"推断目标选择器失败: {e}")
            return EnhancedTargetSelectionResult(targets=[], reason=f"推断失败: {e}")
    
    def _select_healing_targets(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> EnhancedTargetSelectionResult:
        """选择治疗目标"""
        try:
            ally_team = getattr(spirit, 'team', 0)
            allies = battle_state.get_living_spirits(ally_team)
            
            if not allies:
                return EnhancedTargetSelectionResult(targets=[], reason="没有存活的队友")
            
            # 选择血量百分比最低的队友
            target = min(allies, key=lambda a: getattr(a, 'current_hp', 0) / max(getattr(a, 'max_hp', 1), 1))
            
            return EnhancedTargetSelectionResult(
                targets=[target],
                reason=f"选择血量最低的队友: {getattr(target, 'name', 'Unknown')}",
                selector_type="healing"
            )
        except Exception as e:
            return EnhancedTargetSelectionResult(targets=[], reason=f"选择治疗目标失败: {e}")
    
    def _select_all_enemies(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> EnhancedTargetSelectionResult:
        """选择所有敌人"""
        try:
            enemy_team = 1 - getattr(spirit, 'team', 0)
            enemies = battle_state.get_living_spirits(enemy_team)
            
            return EnhancedTargetSelectionResult(
                targets=enemies,
                reason=f"选择所有敌人 ({len(enemies)} 个)",
                selector_type="all_enemies"
            )
        except Exception as e:
            return EnhancedTargetSelectionResult(targets=[], reason=f"选择所有敌人失败: {e}")
    
    def _select_ally_targets(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> EnhancedTargetSelectionResult:
        """选择队友目标"""
        try:
            ally_team = getattr(spirit, 'team', 0)
            allies = battle_state.get_living_spirits(ally_team)
            
            # 排除自己
            allies = [ally for ally in allies if ally != spirit]
            
            if not allies:
                # 如果没有其他队友，选择自己
                return EnhancedTargetSelectionResult(
                    targets=[spirit],
                    reason="没有其他队友，选择自己",
                    selector_type="self"
                )
            
            # 选择第一个队友（可以进一步优化）
            return EnhancedTargetSelectionResult(
                targets=[allies[0]],
                reason=f"选择队友: {getattr(allies[0], 'name', 'Unknown')}",
                selector_type="ally"
            )
        except Exception as e:
            return EnhancedTargetSelectionResult(targets=[], reason=f"选择队友目标失败: {e}")
    
    def _select_single_enemy(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> EnhancedTargetSelectionResult:
        """选择单个敌人"""
        try:
            enemy_team = 1 - getattr(spirit, 'team', 0)
            enemies = battle_state.get_living_spirits(enemy_team)
            
            if not enemies:
                return EnhancedTargetSelectionResult(targets=[], reason="没有存活的敌人")
            
            # 使用AI优先级选择最佳目标
            target = self._select_best_enemy_target(spirit, enemies, battle_state)
            
            return EnhancedTargetSelectionResult(
                targets=[target],
                reason=f"AI选择最佳敌人: {getattr(target, 'name', 'Unknown')}",
                selector_type="single_enemy_ai"
            )
        except Exception as e:
            return EnhancedTargetSelectionResult(targets=[], reason=f"选择单个敌人失败: {e}")
    
    def _intelligent_target_selection(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> EnhancedTargetSelectionResult:
        """智能目标选择 - 综合考虑多种因素"""
        
        # 默认选择单个敌人
        return self._select_single_enemy(spirit, battle_state)
    
    def _fallback_selection(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> EnhancedTargetSelectionResult:
        """回退选择"""
        try:
            enemy_team = 1 - getattr(spirit, 'team', 0)
            enemies = battle_state.get_living_spirits(enemy_team)
            
            if enemies:
                return EnhancedTargetSelectionResult(
                    targets=[enemies[0]],
                    reason="回退选择第一个敌人",
                    selector_type="fallback"
                )
        except:
            pass
        
        return EnhancedTargetSelectionResult(targets=[], reason="回退选择失败")
    
    def _create_skill_context(self, skill: 'Skill'):
        """创建技能上下文"""
        try:
            from core.skill.skills import SkillContext
            return SkillContext(
                skill_level=getattr(skill, 'current_level', 1),
                additional_data={'ai_selection': True}
            )
        except:
            # 如果无法创建SkillContext，返回None，让目标选择器使用默认值
            return None
    
    def _prioritize_targets(
        self,
        spirit: 'IBattleEntity',
        targets: List['IBattleEntity'],
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> List['IBattleEntity']:
        """对目标进行AI优先级排序"""
        
        if len(targets) <= 1:
            return targets
        
        try:
            # 计算每个目标的优先级分数
            target_scores = {}
            for target in targets:
                score = self._calculate_target_priority(spirit, target, skill, battle_state)
                target_scores[target] = score
            
            # 按分数排序
            return sorted(targets, key=lambda t: target_scores.get(t, 0), reverse=True)
            
        except Exception as e:
            logger.warning(f"目标优先级排序失败: {e}")
            return targets
    
    def _calculate_target_priority(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: Optional['Skill'],
        battle_state: 'IBattleState'
    ) -> float:
        """计算目标优先级分数"""

        score = 0.0

        try:
            # 基础分数
            score += 10.0

            # 生命值因素
            if hasattr(target, 'current_hp') and hasattr(target, 'max_hp'):
                hp_percentage = target.current_hp / target.max_hp
                score += (1.0 - hp_percentage) * 20.0  # 血量越低分数越高

                # 斩杀线加成
                if hp_percentage <= 0.3:
                    score += 15.0
                if hp_percentage <= 0.1:
                    score += 25.0

            # 威胁度评估
            target_attack = getattr(target, 'attack', 0)
            if target_attack > 0:
                score += target_attack * 0.01

            # 无法行动状态加成
            try:
                from core.status import battle_status_checker
                if battle_status_checker.status_checker.is_unable_to_act(target):
                    score += 30.0  # 无法行动的目标优先级很高
            except:
                pass

            # 技能特定加成（如果提供了技能信息）
            if skill and hasattr(skill, 'metadata'):
                # 根据技能类型调整优先级
                cast_type = getattr(skill.metadata, 'cast_type', 'ACTIVE')
                if cast_type == 'ULTIMATE':
                    score += 5.0  # 大招优先攻击重要目标

        except Exception as e:
            logger.warning(f"计算目标优先级时出错: {e}")

        return score
    
    def _select_best_enemy_target(
        self,
        spirit: 'IBattleEntity',
        enemies: List['IBattleEntity'],
        battle_state: 'IBattleState'
    ) -> 'IBattleEntity':
        """选择最佳敌人目标"""
        
        if len(enemies) == 1:
            return enemies[0]
        
        # 计算每个敌人的优先级
        best_target = enemies[0]
        best_score = 0.0
        
        for enemy in enemies:
            score = self._calculate_target_priority(spirit, enemy, None, battle_state)
            if score > best_score:
                best_score = score
                best_target = enemy
        
        return best_target
