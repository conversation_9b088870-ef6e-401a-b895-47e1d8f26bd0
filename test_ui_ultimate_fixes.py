#!/usr/bin/env python3
"""
测试UI超杀修复效果

验证超杀阈值显示和效果数量是否正确
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ultimate_threshold_display():
    """测试超杀阈值显示"""
    print("🔧 测试超杀阈值显示...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"可用精灵: {available_spirits}")
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        # 测试每个精灵的超杀阈值获取
        for spirit_name in available_spirits:
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"\n📊 精灵: {spirit.name}")
            print(f"  当前气势: {spirit.energy}")
            print(f"  气势上限: {spirit.max_energy}")
            
            # 使用修复后的逻辑获取超杀阈值
            ultimate_threshold = 300  # 默认值
            if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                # 从超杀技能配置中获取最低阈值
                ultimate_skills = getattr(spirit.ultimate_manager, 'ultimate_skills', {})
                if ultimate_skills:
                    thresholds = []
                    for skill_config in ultimate_skills.values():
                        threshold = getattr(skill_config, 'energy_threshold', 300)
                        thresholds.append(threshold)
                        print(f"    超杀技能: {skill_config.name}, 阈值: {threshold}")
                    if thresholds:
                        ultimate_threshold = min(thresholds)  # 使用最低阈值
            
            print(f"  超杀阈值: {ultimate_threshold}")
            print(f"  显示格式: {spirit.energy}/{ultimate_threshold}")
            
            # 测试效果数量计算
            effects_count = 0
            
            # 检查 effect_manager.effects
            if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
                effects_count += len(spirit.effect_manager.effects)
            
            # 检查 spirit.effects 列表
            if hasattr(spirit, 'effects') and spirit.effects:
                effects_count += len(spirit.effects)
            
            # 检查被动技能作为效果
            passive_count = 0
            if hasattr(spirit, 'skills'):
                for skill in spirit.skills:
                    if hasattr(skill, 'metadata'):
                        cast_type = getattr(skill.metadata, 'cast_type', None)
                        if cast_type == 'PASSIVE':
                            effects_count += 1
                            passive_count += 1
                            print(f"    被动技能: {skill.metadata.name}")
            
            print(f"  总效果数量: {effects_count} (包括 {passive_count} 个被动技能)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components_with_fixes():
    """测试修复后的UI组件"""
    print("\n🔧 测试修复后的UI组件...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗创建成功")
        
        # 测试精灵列表显示逻辑
        print(f"\n📊 测试精灵列表显示:")
        all_spirits = engine.battle_state.get_all_spirits()
        
        for spirit in all_spirits:
            # 获取效果数量 - 使用与UI一致的逻辑
            effects_count = 0
            
            # 检查 effect_manager.effects
            if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
                effects_count += len(spirit.effect_manager.effects)
            
            # 检查 spirit.effects 列表
            if hasattr(spirit, 'effects') and spirit.effects:
                effects_count += len(spirit.effects)
            
            # 检查被动技能作为效果
            if hasattr(spirit, 'skills'):
                for skill in spirit.skills:
                    if hasattr(skill, 'metadata'):
                        cast_type = getattr(skill.metadata, 'cast_type', None)
                        if cast_type == 'PASSIVE':
                            effects_count += 1

            # 获取超杀阈值 - 使用与UI一致的逻辑
            ultimate_threshold = 300  # 默认值
            if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                # 从超杀技能配置中获取最低阈值
                ultimate_skills = getattr(spirit.ultimate_manager, 'ultimate_skills', {})
                if ultimate_skills:
                    thresholds = []
                    for skill_config in ultimate_skills.values():
                        threshold = getattr(skill_config, 'energy_threshold', 300)
                        thresholds.append(threshold)
                    if thresholds:
                        ultimate_threshold = min(thresholds)  # 使用最低阈值

            # 格式化显示信息
            hp_text = f"{spirit.current_hp:.0f}/{spirit.max_hp:.0f}"
            current_energy = getattr(spirit, 'energy', 0)
            energy_text = f"{current_energy}/{ultimate_threshold}"  # 显示超杀阈值而不是气势上限
            status_text = "存活" if spirit.is_alive else "死亡"
            
            print(f"  {spirit.name}:")
            print(f"    队伍: {spirit.team}")
            print(f"    生命值: {hp_text}")
            print(f"    超杀气势: {energy_text}")
            print(f"    效果数量: {effects_count}")
            print(f"    状态: {status_text}")
        
        # 创建战斗记录器并测试
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        
        # 创建初始快照
        initial_snapshot = recorder.create_snapshot(engine.battle_state, 0)
        
        print(f"\n📝 战斗记录器测试:")
        print(f"  精灵数量: {len(initial_snapshot.spirits)}")
        for name, spirit_snapshot in initial_snapshot.spirits.items():
            print(f"    {name}: {len(spirit_snapshot.effects)} 个效果")
            for effect in spirit_snapshot.effects:
                print(f"      - {effect['name']} ({effect['type']})")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 UI超杀修复效果测试")
    print("="*60)
    
    tests = [
        ("超杀阈值显示", test_ultimate_threshold_display),
        ("修复后UI组件", test_ui_components_with_fixes),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！UI修复成功")
        print("\n📋 修复内容:")
        print("  ✅ 超杀阈值正确显示（从技能配置获取）")
        print("  ✅ 效果数量正确计算（包括被动技能）")
        print("  ✅ 精灵列表显示格式正确")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
