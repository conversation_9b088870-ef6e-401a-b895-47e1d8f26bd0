<template>
  <div class="spirit-analytics-detail">
    <!-- 精灵基本信息 -->
    <div class="spirit-header bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white rounded-lg mb-6">
      <div class="flex items-center">
        <div class="spirit-avatar w-16 h-16 rounded-full bg-white/20 flex items-center justify-center text-xl font-bold mr-4">
          {{ spiritStats.name.charAt(0) }}
        </div>
        <div>
          <h2 class="text-xl font-bold">{{ spiritStats.name }}</h2>
          <p class="text-white/80">详细战斗统计</p>
        </div>
      </div>
    </div>

    <!-- 核心统计 -->
    <div class="core-stats grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-green-400">{{ spiritStats.winRate }}%</div>
        <div class="text-slate-400 text-xs">胜率</div>
        <div class="text-xs text-slate-500">{{ spiritStats.wins }}/{{ spiritStats.battles }}</div>
      </div>
      
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-blue-400">{{ spiritStats.battles }}</div>
        <div class="text-slate-400 text-xs">战斗次数</div>
        <div class="text-xs text-slate-500">总参与</div>
      </div>
      
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-red-400">{{ spiritStats.avgDamage }}</div>
        <div class="text-slate-400 text-xs">平均伤害</div>
        <div class="text-xs text-slate-500">每场战斗</div>
      </div>
      
      <div class="stat-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
        <div class="text-2xl font-bold text-purple-400">{{ spiritStats.deathRate }}%</div>
        <div class="text-slate-400 text-sm">死亡率</div>
        <div class="text-xs text-slate-500 mt-1">战斗中阵亡</div>
      </div>
    </div>

    <!-- 详细数据选项卡 -->
    <el-tabs v-model="activeTab" class="analytics-tabs">
      <!-- 战斗表现 -->
      <el-tab-pane label="战斗表现" name="performance">
        <div class="performance-charts grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 伤害趋势 -->
          <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <h3 class="text-white font-medium mb-4">伤害趋势</h3>
            <div ref="damageChart" class="chart-container h-48"></div>
          </div>
          
          <!-- 生存时间 -->
          <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <h3 class="text-white font-medium mb-4">生存时间分布</h3>
            <div ref="survivalChart" class="chart-container h-48"></div>
          </div>
        </div>
        
        <!-- 详细统计表格 -->
        <div class="performance-table mt-6 bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
          <h3 class="text-white font-medium mb-4">详细数据</h3>
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="stat-item">
              <div class="text-slate-400 text-sm">最高伤害</div>
              <div class="text-white font-bold">{{ performanceData.maxDamage }}</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">最低伤害</div>
              <div class="text-white font-bold">{{ performanceData.minDamage }}</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">暴击次数</div>
              <div class="text-white font-bold">{{ performanceData.criticalHits }}</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">暴击率</div>
              <div class="text-white font-bold">{{ performanceData.criticalRate }}%</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">总治疗量</div>
              <div class="text-white font-bold">{{ performanceData.totalHealing }}</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">平均生存回合</div>
              <div class="text-white font-bold">{{ performanceData.avgSurvivalRounds }}</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">击杀数</div>
              <div class="text-white font-bold">{{ performanceData.kills }}</div>
            </div>
            <div class="stat-item">
              <div class="text-slate-400 text-sm">助攻数</div>
              <div class="text-white font-bold">{{ performanceData.assists }}</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 技能分析 -->
      <el-tab-pane label="技能分析" name="skills">
        <div class="skills-analysis">
          <div
            v-for="skill in skillsData"
            :key="skill.name"
            class="skill-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30 mb-4"
          >
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-white font-medium">{{ skill.name }}</h4>
              <el-tag :type="getSkillTypeColor(skill.type)" size="small">
                {{ skill.type }}
              </el-tag>
            </div>
            
            <div class="skill-stats grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="stat-item">
                <div class="text-slate-400 text-sm">使用次数</div>
                <div class="text-white font-bold">{{ skill.usageCount }}</div>
              </div>
              <div class="stat-item">
                <div class="text-slate-400 text-sm">成功率</div>
                <div class="text-green-400 font-bold">{{ skill.successRate }}%</div>
              </div>
              <div class="stat-item">
                <div class="text-slate-400 text-sm">平均伤害</div>
                <div class="text-red-400 font-bold">{{ skill.avgDamage }}</div>
              </div>
              <div class="stat-item">
                <div class="text-slate-400 text-sm">暴击率</div>
                <div class="text-yellow-400 font-bold">{{ skill.critRate }}%</div>
              </div>
            </div>
            
            <!-- 技能使用趋势 -->
            <div class="skill-trend mt-4">
              <div class="text-slate-400 text-sm mb-2">使用频率趋势</div>
              <el-progress 
                :percentage="skill.usageFrequency" 
                :color="getSkillProgressColor(skill.usageFrequency)"
                :show-text="false"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 对战记录 -->
      <el-tab-pane label="对战记录" name="battles">
        <div class="battle-records">
          <div class="records-filter mb-4 flex items-center space-x-4">
            <el-select v-model="battleFilter" placeholder="筛选结果" style="width: 120px">
              <el-option label="全部" value="all" />
              <el-option label="胜利" value="win" />
              <el-option label="失败" value="lose" />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
            />
          </div>
          
          <div class="records-list space-y-3">
            <div
              v-for="record in filteredBattleRecords"
              :key="record.id"
              class="record-item bg-slate-800/50 rounded-lg p-4 border border-slate-600/30"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-3">
                  <el-tag :type="record.result === 'win' ? 'success' : 'danger'" size="small">
                    {{ record.result === 'win' ? '胜利' : '失败' }}
                  </el-tag>
                  <span class="text-slate-400 text-sm">{{ formatDate(record.date) }}</span>
                </div>
                <div class="text-slate-400 text-sm">
                  {{ record.duration }}s | {{ record.rounds }}回合
                </div>
              </div>
              
              <div class="record-stats grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="text-slate-400">造成伤害:</span>
                  <span class="text-red-400 ml-2">{{ record.damageDealt }}</span>
                </div>
                <div>
                  <span class="text-slate-400">承受伤害:</span>
                  <span class="text-orange-400 ml-2">{{ record.damageTaken }}</span>
                </div>
                <div>
                  <span class="text-slate-400">治疗量:</span>
                  <span class="text-green-400 ml-2">{{ record.healingDone }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 配对分析 -->
      <el-tab-pane label="配对分析" name="synergy">
        <div class="synergy-analysis">
          <h3 class="text-white font-medium mb-4">最佳队友</h3>
          <div class="teammates-list space-y-3 mb-6">
            <div
              v-for="teammate in bestTeammates"
              :key="teammate.name"
              class="teammate-item bg-slate-800/50 rounded-lg p-4 border border-slate-600/30"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="teammate-avatar w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                    {{ teammate.name.charAt(0) }}
                  </div>
                  <div>
                    <div class="text-white font-medium">{{ teammate.name }}</div>
                    <div class="text-slate-400 text-sm">配合 {{ teammate.battles }} 场</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-green-400 font-bold">{{ teammate.winRate }}%</div>
                  <div class="text-slate-400 text-sm">胜率</div>
                </div>
              </div>
            </div>
          </div>
          
          <h3 class="text-white font-medium mb-4">克制关系</h3>
          <div class="counters-grid grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="advantages bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h4 class="text-green-400 font-medium mb-3">优势对手</h4>
              <div class="space-y-2">
                <div
                  v-for="enemy in advantageousEnemies"
                  :key="enemy.name"
                  class="flex items-center justify-between"
                >
                  <span class="text-white">{{ enemy.name }}</span>
                  <span class="text-green-400">{{ enemy.winRate }}%</span>
                </div>
              </div>
            </div>
            
            <div class="disadvantages bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h4 class="text-red-400 font-medium mb-3">劣势对手</h4>
              <div class="space-y-2">
                <div
                  v-for="enemy in disadvantageousEnemies"
                  :key="enemy.name"
                  class="flex items-center justify-between"
                >
                  <span class="text-white">{{ enemy.name }}</span>
                  <span class="text-red-400">{{ enemy.winRate }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  spiritStats: {
    name: string
    battles: number
    wins: number
    winRate: number
    avgDamage: number
    avgHealing: number
    deathRate: number
  }
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('performance')
const battleFilter = ref('all')
const dateRange = ref<[Date, Date] | null>(null)

// 图表引用
const damageChart = ref()
const survivalChart = ref()

// 模拟数据
const performanceData = ref({
  maxDamage: 3450,
  minDamage: 890,
  criticalHits: 234,
  criticalRate: 23.5,
  totalHealing: 12450,
  avgSurvivalRounds: 8.3,
  kills: 89,
  assists: 156
})

const skillsData = ref([
  {
    name: '万象归元',
    type: '支援',
    usageCount: 156,
    successRate: 95.7,
    avgDamage: 0,
    critRate: 0,
    usageFrequency: 85
  },
  {
    name: '天剑凌云',
    type: '攻击',
    usageCount: 234,
    successRate: 87.3,
    avgDamage: 1890,
    critRate: 23.5,
    usageFrequency: 92
  }
])

const battleRecords = ref([
  {
    id: 'B001',
    date: new Date(),
    result: 'win',
    duration: 45.2,
    rounds: 8,
    damageDealt: 2340,
    damageTaken: 1560,
    healingDone: 450
  },
  {
    id: 'B002',
    date: new Date(Date.now() - 86400000),
    result: 'lose',
    duration: 38.7,
    rounds: 6,
    damageDealt: 1890,
    damageTaken: 2100,
    healingDone: 320
  }
])

const bestTeammates = ref([
  { name: '神曜圣谕·女帝', battles: 45, winRate: 78.5 },
  { name: '希望之神·夏因', battles: 38, winRate: 72.1 },
  { name: '月律之神·银织', battles: 32, winRate: 69.8 }
])

const advantageousEnemies = ref([
  { name: '速攻型精灵', winRate: 85.2 },
  { name: '脆皮法师', winRate: 79.3 },
  { name: '单体输出', winRate: 73.6 }
])

const disadvantageousEnemies = ref([
  { name: '控制型精灵', winRate: 34.7 },
  { name: '群体治疗', winRate: 41.2 },
  { name: '免疫精灵', winRate: 45.8 }
])

// 计算属性
const filteredBattleRecords = computed(() => {
  let result = battleRecords.value
  
  if (battleFilter.value !== 'all') {
    result = result.filter(record => record.result === battleFilter.value)
  }
  
  if (dateRange.value) {
    const [start, end] = dateRange.value
    result = result.filter(record => 
      record.date >= start && record.date <= end
    )
  }
  
  return result
})

// 方法
const getSkillTypeColor = (type: string) => {
  const colorMap = {
    '攻击': 'danger',
    '防御': 'primary',
    '支援': 'success',
    '特殊': 'warning'
  }
  return colorMap[type] || ''
}

const getSkillProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#10b981'
  if (percentage >= 60) return '#f59e0b'
  return '#ef4444'
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const initCharts = () => {
  nextTick(() => {
    // 伤害趋势图
    if (damageChart.value) {
      const chart1 = echarts.init(damageChart.value)
      chart1.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { 
          type: 'category', 
          data: ['第1场', '第2场', '第3场', '第4场', '第5场', '第6场', '第7场']
        },
        yAxis: { type: 'value' },
        series: [{
          data: [1890, 2340, 1560, 2100, 1780, 2450, 2200],
          type: 'line',
          smooth: true,
          itemStyle: { color: '#ef4444' },
          areaStyle: { color: 'rgba(239, 68, 68, 0.1)' }
        }]
      })
    }

    // 生存时间分布图
    if (survivalChart.value) {
      const chart2 = echarts.init(survivalChart.value)
      chart2.setOption({
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 35, name: '1-3回合', itemStyle: { color: '#ef4444' } },
            { value: 25, name: '4-6回合', itemStyle: { color: '#f59e0b' } },
            { value: 40, name: '7+回合', itemStyle: { color: '#10b981' } }
          ]
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped lang="scss">
.spirit-analytics-detail {
  .stat-card {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      border-color: rgba(139, 92, 246, 0.3);
    }
  }
  
  .chart-container {
    width: 100%;
    height: 100%;
  }
  
  .skill-card,
  .record-item,
  .teammate-item {
    transition: all 0.3s ease;
    
    &:hover {
      border-color: rgba(139, 92, 246, 0.3);
    }
  }
  
  .stat-item {
    text-align: center;
    padding: 8px;
  }
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(148, 163, 184, 0.2);
}

:deep(.el-tabs__active-bar) {
  background-color: #8b5cf6;
}

:deep(.el-tabs__item.is-active) {
  color: #8b5cf6;
}
</style>