"""
赤妖王·御神 - 技能组件模块

包含御神技能使用的特殊组件：
- AzureFlameDebuffComponent: 青焰燎尾的减益组件
- ThousandWebSwitchComponent: 千机罗网的目标切换组件
- SpiritWisdomTeamBuffComponent: 灵目慧心的团队增益组件
- SpiritWisdomBattleBoostComponent: 灵目慧心的战斗加成组件
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING, Dict, Any

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import SkillContext

from .effects import create_spirit_wisdom_battle_boost_effect


class AzureFlameDebuffComponent:
    """青焰燎尾的减益组件 - 降低目标攻击力和防御力"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from core.action import LogAction, ApplyEffectAction
        from core.effect.effects import AttributeModifierEffect
        
        actions = []
        
        for target in targets:
            if target.is_alive:
                # 创建攻击力减益效果
                attack_debuff = AttributeModifierEffect(
                    attribute_name="attack",
                    modifier=-0.2,  # 降低20%攻击力
                    duration=2  # 持续2回合
                )

                # 创建防御力减益效果
                defense_debuff = AttributeModifierEffect(
                    attribute_name="pdef",
                    modifier=-0.15,  # 降低15%物理防御
                    duration=2  # 持续2回合
                )
                
                actions.extend([
                    LogAction(
                        caster=caster,
                        target=target,
                        message=f"🔥 [青焰燎尾] {target.name} 被青焰灼烧，攻击力和防御力下降！"
                    ),
                    ApplyEffectAction(
                        caster=caster,
                        target=target,
                        effect=attack_debuff
                    ),
                    ApplyEffectAction(
                        caster=caster,
                        target=target,
                        effect=defense_debuff
                    )
                ])
        
        return actions


class ThousandWebSwitchComponent:
    """千机罗网的目标切换组件 - 智能选择最优目标"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from core.action import LogAction, DamageAction, DamageType
        
        actions = []
        
        # 智能目标选择逻辑
        optimal_target = self._select_optimal_target(targets, battle_state)
        
        if optimal_target:
            # 对最优目标造成额外伤害
            base_damage = getattr(caster, 'attack', 0) * 0.5  # 额外50%攻击力伤害
            
            actions.extend([
                LogAction(
                    caster=caster,
                    target=optimal_target,
                    message=f"🕸️ [千机罗网] {caster.name} 的罗网锁定了最优目标 {optimal_target.name}！"
                ),
                DamageAction(
                    caster=caster,
                    target=optimal_target,
                    damage_value=int(base_damage) if base_damage else None,
                    damage_type=DamageType.PHYSICAL,
                    label="千机罗网额外伤害"
                )
            ])
        
        return actions
    
    def _select_optimal_target(self, targets: List[IBattleEntity], battle_state: IBattleState) -> IBattleEntity:
        """选择最优目标"""
        if not targets:
            return None
        
        # 优先选择生命值最低的目标
        optimal_target = min(targets, key=lambda t: getattr(t, 'current_hp', 0) if t.is_alive else float('inf'))
        
        return optimal_target if optimal_target.is_alive else None


class SpiritWisdomTeamBuffComponent:
    """灵目慧心的团队增益组件 - 为全体队友提供增益"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from core.action import LogAction, ApplyEffectAction
        from core.effect.effects import AttributeModifierEffect
        
        actions = []
        
        # 获取所有队友（包括自己）
        team_spirits = self._get_team_spirits(caster, battle_state)
        
        for ally in team_spirits:
            if ally.is_alive:
                # 创建攻击力增益效果
                attack_buff = AttributeModifierEffect(
                    attribute_name="attack",
                    modifier=0.25,  # 增加25%攻击力
                    duration=3  # 持续3回合
                )

                # 创建暴击率增益效果
                crit_buff = AttributeModifierEffect(
                    attribute_name="crit_rate",
                    modifier=0.15,  # 增加15%暴击率
                    duration=3  # 持续3回合
                )
                
                actions.extend([
                    ApplyEffectAction(
                        caster=caster,
                        target=ally,
                        effect=attack_buff
                    ),
                    ApplyEffectAction(
                        caster=caster,
                        target=ally,
                        effect=crit_buff
                    )
                ])
        
        # 添加团队增益日志
        actions.append(
            LogAction(
                caster=caster,
                message=f"👁️ [灵目慧心] {caster.name} 为全体队友提供攻击力和暴击率增益！"
            )
        )
        
        return actions
    
    def _get_team_spirits(self, caster: IBattleEntity, battle_state: IBattleState) -> List[IBattleEntity]:
        """获取队友列表"""
        team_spirits = []
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                team_spirits = battle_state.get_team_spirits(caster.team)
        except:
            # 如果获取失败，至少返回施法者自己
            team_spirits = [caster]
        
        return team_spirits


class SpiritWisdomBattleBoostComponent:
    """灵目慧心的战斗加成组件 - 为全体队友添加战斗加成效果"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from core.action import LogAction, ApplyEffectAction
        
        actions = []
        
        # 获取所有队友（包括自己）
        team_spirits = self._get_team_spirits(caster, battle_state)
        
        for ally in team_spirits:
            if ally.is_alive:
                # 创建灵目慧心战斗加成效果
                battle_boost_effect = create_spirit_wisdom_battle_boost_effect(
                    duration=3,  # 持续3回合
                    caster=caster
                )
                
                actions.append(
                    ApplyEffectAction(
                        caster=caster,
                        target=ally,
                        effect=battle_boost_effect
                    )
                )
        
        # 添加战斗加成日志
        actions.append(
            LogAction(
                caster=caster,
                message=f"👁️ [灵目慧心] {caster.name} 为全体队友添加战斗加成！攻击无法行动精灵时获得额外加成！"
            )
        )
        
        return actions
    
    def _get_team_spirits(self, caster: IBattleEntity, battle_state: IBattleState) -> List[IBattleEntity]:
        """获取队友列表"""
        team_spirits = []
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                team_spirits = battle_state.get_team_spirits(caster.team)
        except:
            # 如果获取失败，至少返回施法者自己
            team_spirits = [caster]
        
        return team_spirits


class EnergyManipulationComponent:
    """气势操作组件 - 为队友增加气势"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from core.action import LogAction
        
        actions = []
        
        # 获取所有队友（包括自己）
        team_spirits = self._get_team_spirits(caster, battle_state)
        
        energy_gain = context.additional_data.get("energy_gain", 50)  # 默认增加50点气势
        
        for ally in team_spirits:
            if ally.is_alive and hasattr(ally, 'gain_energy'):
                ally.gain_energy(energy_gain)
        
        # 添加气势增加日志
        actions.append(
            LogAction(
                caster=caster,
                message=f"⚡ [灵目慧心] 全体队友获得{energy_gain}点气势！"
            )
        )
        
        return actions
    
    def _get_team_spirits(self, caster: IBattleEntity, battle_state: IBattleState) -> List[IBattleEntity]:
        """获取队友列表"""
        team_spirits = []
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                team_spirits = battle_state.get_team_spirits(caster.team)
        except:
            # 如果获取失败，至少返回施法者自己
            team_spirits = [caster]
        
        return team_spirits


# 导出所有组件类
__all__ = [
    'AzureFlameDebuffComponent',
    'ThousandWebSwitchComponent',
    'SpiritWisdomTeamBuffComponent',
    'SpiritWisdomBattleBoostComponent',
    'EnergyManipulationComponent'
]
