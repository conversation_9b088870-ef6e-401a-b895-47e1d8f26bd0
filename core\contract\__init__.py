from __future__ import annotations
from typing import Dict, List, Set, Optional, Tuple, TYPE_CHECKING

# 第三方库导入
try:
    from sqlalchemy.orm import Session
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    Session = None
    SQLALCHEMY_AVAILABLE = False

from collections import defaultdict
import json
import os

# 本地导入
# from src.database import get_db  # 暂时注释掉，避免导入错误
# from src.database import Base, Contract as DBContract, get_db  # 暂时注释掉，避免导入错误
from ..interfaces import IBattleEntity
from ..spirit.spirit import Spirit

"""契约系统。

本模块定义并管理游戏中的契约。契约是基于特定精灵组合的特殊加成。

主要组件:
- `ContractDefinition`: 描述单个契约激活条件的数据类。
- `ContractManager`: 一个单例管理器，负责从数据库加载契约定义，
  并在战斗开始时检测哪些契约被激活。

该系统通过在战斗开始时调用 `detect_and_activate` 来工作，它会检查
当前阵容是否满足任何已加载契约的条件，并触发相应的事件。
"""


try:
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    Session = None
    Base = object
    DBContract = None
    get_db = None
    SQLALCHEMY_AVAILABLE = False



def _load_contracts_from_db(db) -> Dict[str, ContractDefinition]:
    """
    从数据库会话中加载所有契约定义。

    Args:
        db: SQLAlchemy 数据库会话。

    Returns:
        一个字典，将契约ID映射到其 `ContractDefinition` 实例。
    """
    if not SQLALCHEMY_AVAILABLE or db is None:
        return {}
    contracts_from_db = db.query(DBContract).filter(DBContract.is_active == True).all()
    contracts: Dict[str, ContractDefinition] = {}
    for item in contracts_from_db:
        contracts[str(item.id)] = ContractDefinition(
            contract_id=str(item.id),
            hero_tag=str(item.hero_tag),
            required_spirit_tags=list(item.spirits) if isinstance(item.spirits, (list, tuple)) else [str(s) for s in item.spirits],
        )
    return contracts


class ContractDefinition:
    """
    定义一个契约的激活条件。

    Attributes:
        contract_id (str): 契约的唯一标识。
        hero_tag (str): 激活此契约的主君必须拥有的标签。
        required_spirit_tags (Set[str]): 激活此契约的阵容中必须存在的精灵标签集合。
    """

    def __init__(
        self, contract_id: str, hero_tag: str, required_spirit_tags: List[str]
    ):
        """
        初始化一个契约定义。

        Args:
            contract_id: 契约的唯一标识。
            hero_tag: 激活此契约的主君必须拥有的标签。
            required_spirit_tags: 激活此契约的阵容中必须存在的精灵标签列表。
        """
        self.contract_id = contract_id
        self.hero_tag = hero_tag
        self.required_spirit_tags = set(required_spirit_tags)

    def __repr__(self) -> str:
        return (
            f"ContractDefinition(contract_id={self.contract_id!r}, "
            f"hero_tag={self.hero_tag!r}, "
            f"required_spirit_tags={self.required_spirit_tags!r})"
        )


class ContractManager:
    """
    契约管理器 - 单例。

    负责加载、管理和检测契约激活。在战斗开始时使用。
    """
    _instance = None  # Singleton instance
    _initialized = False  # Prevent re-initialization

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # Ensure __init__ only runs once
        if not self._initialized:
            self.contracts: Dict[str, ContractDefinition] = {}
            self._initialized = True

    def load_contracts(self, db_session: Optional[Session] = None) -> None:
        """
        从数据库加载契约定义。

        Args:
            db_session: 可选的数据库会话。如果未提供，则尝试创建一个新会话。
        """
        if db_session is not None:
            self.contracts = _load_contracts_from_db(db_session)
        elif SQLALCHEMY_AVAILABLE:
            try:
                # 尝试使用依赖注入方式获取数据库会话
                db_gen = get_db()
                db = next(db_gen)
                try:
                    self.contracts = _load_contracts_from_db(db)
                finally:
                    # 确保正确关闭数据库会话
                    try:
                        db_gen.send(None)  # 触发生成器的清理部分
                    except StopIteration:
                        pass
            except Exception:
                # 如果无法获取数据库会话，使用空的契约集合
                self.contracts = {}
        else:
            # SQLAlchemy不可用
            self.contracts = {}

    def detect_and_activate(
        self, team_spirits: Dict[int, List[Spirit]]
    ) -> List[Tuple[ContractDefinition, Spirit]]:
        """
        检测哪些契约在给定的阵容中被激活。

        Args:
            team_spirits: 按队伍分组的精灵列表 {team_id: [spirits]}。

        Returns:
            被激活的契约列表，每个元素是一个元组 (契约定义, 主君精灵)。
        """
        activated_contracts = []

        # 收集所有精灵的标签
        spirit_tags_by_team: Dict[int, Dict[str, Spirit]] = defaultdict(dict)
        hero_spirits_by_team: Dict[int, List[Spirit]] = defaultdict(list)

        for team_id, spirits in team_spirits.items():
            for spirit in spirits:
                # 收集精灵的所有标签
                if hasattr(spirit, 'metadata') and hasattr(spirit.metadata, 'tags'):
                    for tag in spirit.metadata.tags:
                        spirit_tags_by_team[team_id][tag] = spirit
                
                # 收集可能作为主君的精灵
                if (
                    hasattr(spirit, 'metadata') 
                    and hasattr(spirit.metadata, 'professions')
                    and 'HERO' in spirit.metadata.professions
                ):
                    hero_spirits_by_team[team_id].append(spirit)

        # 检查每个队伍的契约激活情况
        for team_id in team_spirits:
            team_tags = set(spirit_tags_by_team[team_id].keys())
            heroes = hero_spirits_by_team[team_id]

            # 检查每个英雄是否激活了任何契约
            for hero in heroes:
                # 获取主君的标签
                hero_tags = set()
                if hasattr(hero, 'metadata') and hasattr(hero.metadata, 'tags'):
                    hero_tags = set(hero.metadata.tags)

                # 检查每个契约
                for contract in self.contracts.values():
                    # 检查主君是否符合条件
                    if contract.hero_tag in hero_tags:
                        # 检查是否满足所有必需的精灵标签
                        if contract.required_spirit_tags.issubset(team_tags):
                            activated_contracts.append((contract, hero))

        return activated_contracts

    def get_contract(self, contract_id: str) -> Optional[ContractDefinition]:
        """
        获取指定ID的契约定义。

        Args:
            contract_id: 契约ID。

        Returns:
            契约定义，如果未找到则返回None。
        """
        return self.contracts.get(contract_id)

    def list_contracts(self) -> List[ContractDefinition]:
        """
        列出所有已加载的契约定义。

        Returns:
            契约定义列表。
        """
        return list(self.contracts.values())


# 全局契约管理器实例
contract_manager = ContractManager()