// 全局样式文件
@import './variables.scss';
@import './components.scss';
@import './animations.scss';
@import './analytics.scss';

// 基础样式重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
  
  &:hover {
    background: rgba(139, 92, 246, 0.7);
  }
}

// 暗色主题支持
.dark {
  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 精灵悬停提示样式
.spirit-tooltip-popper, .enhanced-spirit-tooltip {
  background: rgba(15, 23, 42, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  padding: 0 !important;
  max-width: 320px;
  z-index: 9999 !important;

  .el-tooltip__arrow {
    &::before {
      background: rgba(15, 23, 42, 0.95) !important;
      border: 1px solid rgba(139, 92, 246, 0.3) !important;
    }
  }
}

// 工具类
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}