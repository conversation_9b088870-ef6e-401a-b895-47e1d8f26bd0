#!/usr/bin/env python3
"""
测试战斗系统（包含系统初始化）

验证修复后的战斗系统是否能正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_battle_system():
    """测试完整的战斗系统"""
    print("🚀 测试完整战斗系统...")
    
    try:
        # 1. 初始化核心系统
        print("1. 初始化核心系统...")
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        print("✅ 核心系统初始化成功")
        
        # 2. 获取精灵服务
        print("2. 获取精灵服务...")
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        print(f"✅ 发现 {len(available_spirits)} 个可用精灵")
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足2个")
            return False
        
        # 3. 创建测试阵型
        print("3. 创建测试阵型...")
        from core.formation import Formation
        
        formation1 = Formation()
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        if spirit1:
            formation1.add_spirit(spirit1, 1, 1)
            print(f"✅ 队伍1: {spirit1.name}")
        else:
            print("❌ 创建队伍1精灵失败")
            return False
        
        formation2 = Formation()
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        if spirit2:
            formation2.add_spirit(spirit2, 3, 1)
            print(f"✅ 队伍2: {spirit2.name}")
        else:
            print("❌ 创建队伍2精灵失败")
            return False
        
        # 4. 创建战斗引擎（测试回合限制）
        print("4. 创建战斗引擎...")
        from core.battle.engines import create_battle_engine
        
        battle_engine = create_battle_engine(
            formation1=formation1,
            formation2=formation2,
            victory="ko",
            round_limit=5,  # 设置为5回合进行测试
            executor_type="phased"
        )
        print("✅ 战斗引擎创建成功")
        print(f"  - 回合限制: {getattr(battle_engine, 'round_limit', '未知')}")
        
        # 5. 运行战斗测试
        print("5. 运行战斗测试...")
        round_count = 0
        max_test_rounds = 3  # 限制测试回合数
        
        for round_result in battle_engine.run_battle():
            round_count += 1
            print(f"📊 第{round_count}回合完成")
            
            if round_result.get("type") == "battle_end":
                winner = round_result.get("winner")
                if winner is not None:
                    team_name = "队伍1" if winner == 0 else "队伍2"
                    print(f"🏆 战斗结束！获胜方: {team_name}")
                else:
                    print("🤝 战斗平局")
                break
            
            # 限制测试回合数
            if round_count >= max_test_rounds:
                print(f"⏰ 测试回合数达到限制({max_test_rounds})，停止战斗")
                break
        
        print("✅ 战斗系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 战斗系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_round_limit_enforcement():
    """测试回合限制是否正确执行"""
    print("\n🔧 测试回合限制执行...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 精灵不足")
            return False
        
        # 创建阵型
        from core.formation import Formation
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        if not spirit1 or not spirit2:
            print("❌ 精灵创建失败")
            return False
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎，设置很小的回合限制
        from core.battle.engines import create_battle_engine
        battle_engine = create_battle_engine(
            formation1=formation1,
            formation2=formation2,
            victory="ko",
            round_limit=2,  # 只允许2回合
            executor_type="phased"
        )
        
        print(f"✅ 战斗引擎创建成功，回合限制: {battle_engine.round_limit}")
        
        # 运行战斗并计数回合
        round_count = 0
        for round_result in battle_engine.run_battle():
            round_count += 1
            print(f"📊 执行第{round_count}回合")
            
            if round_result.get("type") == "battle_end":
                print(f"🏁 战斗结束于第{round_count}回合")
                break
        
        # 验证回合限制
        if round_count <= battle_engine.round_limit:
            print(f"✅ 回合限制正确执行：{round_count}/{battle_engine.round_limit}")
            return True
        else:
            print(f"❌ 回合限制未正确执行：{round_count}/{battle_engine.round_limit}")
            return False
        
    except Exception as e:
        print(f"❌ 回合限制测试失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 战斗系统完整测试（包含系统初始化）")
    print("="*60)
    
    tests = [
        ("完整战斗系统", test_complete_battle_system),
        ("回合限制执行", test_round_limit_enforcement),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！战斗系统修复成功！")
        print("\n📋 修复验证:")
        print("  ✅ ActionStartEvent参数修复")
        print("  ✅ LogAction导入修复")
        print("  ✅ 战斗引擎回合限制修复")
        print("  ✅ 系统初始化正常")
        print("  ✅ 精灵动作生成正常")
        print("\n🚀 现在可以运行 python battle_program.py 进行完整战斗！")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
