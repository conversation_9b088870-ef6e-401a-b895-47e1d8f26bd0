#!/usr/bin/env python3
"""
简单的被动技能测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_passive():
    """简单的被动技能测试"""
    print("🔧 简单的被动技能测试...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        fuyao_spirit = None
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            print("❌ 未找到伏妖精灵")
            return False
        
        # 直接创建并添加被动效果
        print(f"\n📋 直接创建并添加被动效果:")
        
        try:
            from spirits_data.神曜虚无·伏妖.passive_effects import create_bian_shusha_passive_effect
            
            # 创建被动效果
            passive_effect = create_bian_shusha_passive_effect(fuyao_spirit)
            print(f"  被动效果创建成功: {passive_effect.name}")
            
            # 检查效果管理器
            if hasattr(fuyao_spirit, 'effect_manager') and fuyao_spirit.effect_manager:
                print(f"  效果管理器存在: ✅")
                
                effects_before = len(fuyao_spirit.effect_manager.effects)
                print(f"  添加前效果数量: {effects_before}")
                
                # 创建简单的战斗状态
                from core.formation import Formation
                from core.battle.engines.factory import create_battle_engine
                
                other_spirit = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
                formation1 = Formation()
                formation2 = Formation()
                formation1.add_spirit(fuyao_spirit, 1, 1)
                formation2.add_spirit(other_spirit, 3, 1)
                
                engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
                battle_state = engine.battle_state
                
                # 添加被动效果
                result = fuyao_spirit.effect_manager.add_effect(passive_effect, battle_state)
                print(f"  添加结果: {result.success if result else False}")
                
                effects_after = len(fuyao_spirit.effect_manager.effects)
                print(f"  添加后效果数量: {effects_after}")
                
                if effects_after > effects_before:
                    print(f"  ✅ 被动效果添加成功！")
                    
                    # 显示效果
                    for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                        effect_name = getattr(effect, 'name', 'Unknown')
                        print(f"    - {effect_name}")
                    
                    return True
                else:
                    print(f"  ❌ 被动效果添加失败")
                    return False
            else:
                print(f"  ❌ 效果管理器不存在")
                return False
                
        except Exception as e:
            print(f"  ❌ 被动效果处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*40)
    print("🔧 简单被动技能测试")
    print("="*40)
    
    result = test_simple_passive()
    
    print("\n" + "="*40)
    if result:
        print("✅ 简单被动技能测试成功")
        print("被动效果可以正确添加")
    else:
        print("❌ 简单被动技能测试失败")

if __name__ == "__main__":
    main()
