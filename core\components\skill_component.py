"""
技能组件

管理精灵的技能系统，包括技能列表、技能获取、技能使用条件检查等。
"""
from __future__ import annotations
from typing import List, Optional, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from ..spirit.spirit import Spirit
    from ..interfaces import IBattleState

from ..logging import spirit_logger


class SkillComponent:
    """技能组件 - 管理精灵的技能系统"""
    
    def __init__(self, owner: 'Spirit', skills: Optional[List[Any]] = None):
        """
        初始化技能组件
        
        Args:
            owner: 拥有此组件的精灵
            skills: 初始技能列表
        """
        self.owner = owner
        self._skills: List[Any] = skills or []
        self._skill_cooldowns: Dict[str, int] = {}  # 技能冷却时间
        self._skill_usage_count: Dict[str, int] = {}  # 技能使用次数统计
    
    @property
    def skills(self) -> List[Any]:
        """获取技能列表"""
        return self._skills.copy()

    def get_skills_by_type(self, skill_type: str) -> List[Any]:
        """
        按类型获取技能

        Args:
            skill_type: 技能类型 ('active', 'passive', 'shenyao', 'tongling' 等)

        Returns:
            指定类型的技能列表
        """
        matching_skills = []
        for skill in self._skills:
            skill_matched = False

            # 首先检查tags匹配
            if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'tags'):
                if skill_type in skill.metadata.tags:
                    matching_skills.append(skill)
                    skill_matched = True

            # 如果tags没有匹配，再检查cast_type匹配
            if not skill_matched and hasattr(skill, 'metadata') and hasattr(skill.metadata, 'cast_type'):
                cast_type = skill.metadata.cast_type

                # 直接匹配
                if skill_type == cast_type:
                    matching_skills.append(skill)
                # 大小写不敏感匹配
                elif skill_type.upper() == cast_type.upper():
                    matching_skills.append(skill)
                # 特殊兼容性匹配
                elif (skill_type.lower() == 'passive' and cast_type == 'PASSIVE') or \
                     (skill_type.lower() == 'active' and cast_type == 'ACTIVE') or \
                     (skill_type.lower() == 'ultimate' and cast_type == 'ULTIMATE'):
                    matching_skills.append(skill)
        return matching_skills

    def get_active_skills(self) -> List[Any]:
        """获取主动技能"""
        return self.get_skills_by_type('active')

    def get_passive_skills(self) -> List[Any]:
        """获取被动技能"""
        return self.get_skills_by_type('passive')

    def get_shenyao_skills(self) -> List[Any]:
        """获取神曜技能"""
        return self.get_skills_by_type('shenyao')

    def get_tongling_skills(self) -> List[Any]:
        """获取通灵技能"""
        return self.get_skills_by_type('tongling')

    def can_use_skill(self, skill_name: str) -> bool:
        """
        检查是否可以使用指定技能

        Args:
            skill_name: 技能名称

        Returns:
            是否可以使用该技能
        """
        # 查找技能
        skill = self.get_skill(skill_name)
        if not skill:
            return False

        # 检查冷却时间
        if self.is_skill_on_cooldown(skill_name):
            return False

        # 检查能量消耗
        if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'energy_cost'):
            energy_cost = skill.metadata.energy_cost

            # 🔧 超杀技能适配：超杀技能不检查energy_cost，而是检查energy_threshold
            cast_type = getattr(skill.metadata, 'cast_type', '')
            if cast_type in ['ULTIMATE', 'TONGLING_ULTIMATE']:
                # 超杀技能使用阈值检查
                return self._check_ultimate_energy_threshold(skill)

            # 普通技能检查energy_cost
            if hasattr(self.owner, 'energy') and self.owner.energy < energy_cost:
                return False

        # 检查技能特定条件
        if hasattr(skill, 'can_cast'):
            try:
                # 这里需要 battle_state，暂时返回 True
                # 在实际战斗中会传入正确的 battle_state
                return True
            except:
                return False

        return True

    def _check_ultimate_energy_threshold(self, skill) -> bool:
        """检查超杀技能的气势阈值"""
        try:
            # 方法1: 从超杀管理器获取阈值
            if hasattr(self.owner, 'ultimate_manager') and self.owner.ultimate_manager:
                skill_name = getattr(skill.metadata, 'name', '') if hasattr(skill, 'metadata') else ''
                if hasattr(self.owner.ultimate_manager, 'get_ultimate_skill'):
                    skill_config = self.owner.ultimate_manager.get_ultimate_skill(skill_name)
                    if skill_config:
                        current_energy = getattr(self.owner, 'energy', 0)
                        return current_energy >= skill_config.energy_threshold

                # 获取最低阈值
                if hasattr(self.owner.ultimate_manager, 'get_lowest_threshold'):
                    threshold = self.owner.ultimate_manager.get_lowest_threshold()
                    current_energy = getattr(self.owner, 'energy', 0)
                    return current_energy >= threshold

            # 方法2: 使用默认阈值
            current_energy = getattr(self.owner, 'energy', 0)
            return current_energy >= 300  # 默认超杀阈值
        except:
            return False
    
    def add_skill(self, skill: Any) -> None:
        """
        添加技能
        
        Args:
            skill: 要添加的技能
        """
        if skill not in self._skills:
            self._skills.append(skill)
            skill_name = getattr(skill, 'name', str(skill))
            spirit_logger.debug(f"{self.owner.name} 学会了技能: {skill_name}")
    
    def remove_skill(self, skill_name: str) -> bool:
        """
        移除技能
        
        Args:
            skill_name: 要移除的技能名称
            
        Returns:
            是否成功移除
        """
        for i, skill in enumerate(self._skills):
            if getattr(skill, 'name', '') == skill_name:
                removed_skill = self._skills.pop(i)
                spirit_logger.debug(f"{self.owner.name} 遗忘了技能: {skill_name}")
                
                # 清理相关数据
                if skill_name in self._skill_cooldowns:
                    del self._skill_cooldowns[skill_name]
                if skill_name in self._skill_usage_count:
                    del self._skill_usage_count[skill_name]
                
                return True
        return False
    
    def get_skill(self, skill_name: str) -> Optional[Any]:
        """
        按名称获取技能
        
        Args:
            skill_name: 技能名称
            
        Returns:
            技能对象，如果不存在则返回None
        """
        for skill in self._skills:
            if getattr(skill, 'name', '') == skill_name:
                return skill
        return None
    
    def has_skill(self, skill_name: str) -> bool:
        """
        检查是否拥有指定技能
        
        Args:
            skill_name: 技能名称
            
        Returns:
            是否拥有该技能
        """
        return self.get_skill(skill_name) is not None
    
    def can_cast_skill(self, skill_name: str, battle_state: 'IBattleState') -> bool:
        """
        检查是否可以释放技能
        
        Args:
            skill_name: 技能名称
            battle_state: 战斗状态
            
        Returns:
            是否可以释放
        """
        skill = self.get_skill(skill_name)
        if not skill:
            return False
        
        # 检查冷却时间
        if self.is_skill_on_cooldown(skill_name):
            return False
        
        # 检查技能自身的使用条件
        if hasattr(skill, 'can_use'):
            try:
                return skill.can_use(self.owner, battle_state)
            except Exception as e:
                spirit_logger.warning(f"检查技能 {skill_name} 使用条件时出错: {e}")
                return False
        
        return True
    
    def get_available_skills(self, battle_state: 'IBattleState') -> List[Any]:
        """
        获取当前可用的技能列表
        
        Args:
            battle_state: 战斗状态
            
        Returns:
            可用技能列表
        """
        available_skills = []
        for skill in self._skills:
            skill_name = getattr(skill, 'name', '')
            if skill_name and self.can_cast_skill(skill_name, battle_state):
                available_skills.append(skill)
        return available_skills
    
    def set_skill_cooldown(self, skill_name: str, cooldown: int) -> None:
        """
        设置技能冷却时间
        
        Args:
            skill_name: 技能名称
            cooldown: 冷却回合数
        """
        if cooldown > 0:
            self._skill_cooldowns[skill_name] = cooldown
            spirit_logger.debug(f"{self.owner.name} 的技能 {skill_name} 进入冷却 ({cooldown} 回合)")
        elif skill_name in self._skill_cooldowns:
            del self._skill_cooldowns[skill_name]
    
    def is_skill_on_cooldown(self, skill_name: str) -> bool:
        """
        检查技能是否在冷却中
        
        Args:
            skill_name: 技能名称
            
        Returns:
            是否在冷却中
        """
        return self._skill_cooldowns.get(skill_name, 0) > 0
    
    def get_skill_cooldown(self, skill_name: str) -> int:
        """
        获取技能剩余冷却时间
        
        Args:
            skill_name: 技能名称
            
        Returns:
            剩余冷却回合数
        """
        return self._skill_cooldowns.get(skill_name, 0)
    
    def reduce_all_cooldowns(self, amount: int = 1) -> None:
        """
        减少所有技能的冷却时间
        
        Args:
            amount: 减少的回合数
        """
        skills_to_remove = []
        for skill_name, cooldown in self._skill_cooldowns.items():
            new_cooldown = cooldown - amount
            if new_cooldown <= 0:
                skills_to_remove.append(skill_name)
                spirit_logger.debug(f"{self.owner.name} 的技能 {skill_name} 冷却完成")
            else:
                self._skill_cooldowns[skill_name] = new_cooldown
        
        # 移除冷却完成的技能
        for skill_name in skills_to_remove:
            del self._skill_cooldowns[skill_name]
    
    def record_skill_usage(self, skill_name: str) -> None:
        """
        记录技能使用
        
        Args:
            skill_name: 技能名称
        """
        self._skill_usage_count[skill_name] = self._skill_usage_count.get(skill_name, 0) + 1
        spirit_logger.debug(f"{self.owner.name} 使用了技能: {skill_name}")
    
    def get_skill_usage_count(self, skill_name: str) -> int:
        """
        获取技能使用次数
        
        Args:
            skill_name: 技能名称
            
        Returns:
            使用次数
        """
        return self._skill_usage_count.get(skill_name, 0)
    
    def clear_all_cooldowns(self) -> None:
        """清除所有技能冷却"""
        cleared_skills = list(self._skill_cooldowns.keys())
        self._skill_cooldowns.clear()
        
        if cleared_skills:
            spirit_logger.debug(f"{self.owner.name} 的所有技能冷却被清除: {', '.join(cleared_skills)}")
    
    def get_skill_count(self) -> int:
        """获取技能数量"""
        return len(self._skills)
    
    def __len__(self) -> int:
        """返回技能数量"""
        return len(self._skills)
    
    def __repr__(self) -> str:
        """字符串表示"""
        skill_names = [getattr(skill, 'name', 'Unknown') for skill in self._skills]
        return f"SkillComponent({len(self._skills)} skills: {', '.join(skill_names)})"
