import { ref, computed } from 'vue'
import type { Spirit, ElementType, ProfessionType } from '../types/battle'

/**
 * 精灵筛选和排序功能
 */
export function useSpiritFilters() {
  // 筛选条件
  const searchQuery = ref('')
  const elementFilter = ref<ElementType | ''>('')
  const professionFilter = ref<ProfessionType | ''>('')
  const levelRange = ref<[number, number]>([1, 100])
  const sortBy = ref('fitness')
  const sortOrder = ref<'asc' | 'desc'>('desc')

  // 高级筛选
  const showAdvancedFilters = ref(false)
  const minAttack = ref(0)
  const minDefense = ref(0)
  const minHp = ref(0)
  const minSpeed = ref(0)
  const tagsFilter = ref<string[]>([])

  /**
   * 应用所有筛选条件
   */
  const applyFilters = (spirits: Spirit[], recommendations: any[] = []) => {
    let result = [...spirits]

    // 基础搜索
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(spirit => 
        spirit.name.toLowerCase().includes(query) ||
        spirit.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // 元素筛选
    if (elementFilter.value) {
      result = result.filter(spirit => spirit.element === elementFilter.value)
    }

    // 职业筛选
    if (professionFilter.value) {
      result = result.filter(spirit => 
        spirit.professions.includes(professionFilter.value as ProfessionType)
      )
    }

    // 等级范围筛选
    result = result.filter(spirit => 
      spirit.level >= levelRange.value[0] && spirit.level <= levelRange.value[1]
    )

    // 高级属性筛选
    if (showAdvancedFilters.value) {
      result = result.filter(spirit => 
        spirit.attributes.attack >= minAttack.value &&
        spirit.attributes.defense >= minDefense.value &&
        spirit.attributes.maxHp >= minHp.value &&
        spirit.attributes.speed >= minSpeed.value
      )

      // 标签筛选
      if (tagsFilter.value.length > 0) {
        result = result.filter(spirit =>
          tagsFilter.value.some(tag => spirit.tags.includes(tag))
        )
      }
    }

    // 排序
    result.sort((a, b) => {
      let comparison = 0

      switch (sortBy.value) {
        case 'fitness':
          const aFitness = recommendations.find(r => r.spirit.id === a.id)?.fitness || 0
          const bFitness = recommendations.find(r => r.spirit.id === b.id)?.fitness || 0
          comparison = bFitness - aFitness
          break
        case 'level':
          comparison = b.level - a.level
          break
        case 'attack':
          comparison = b.attributes.attack - a.attributes.attack
          break
        case 'defense':
          comparison = b.attributes.defense - a.attributes.defense
          break
        case 'hp':
          comparison = b.attributes.maxHp - a.attributes.maxHp
          break
        case 'speed':
          comparison = b.attributes.speed - a.attributes.speed
          break
        case 'name':
        default:
          comparison = a.name.localeCompare(b.name)
          break
      }

      return sortOrder.value === 'desc' ? comparison : -comparison
    })

    return result
  }

  /**
   * 重置所有筛选条件
   */
  const resetFilters = () => {
    searchQuery.value = ''
    elementFilter.value = ''
    professionFilter.value = ''
    levelRange.value = [1, 100]
    sortBy.value = 'fitness'
    sortOrder.value = 'desc'
    showAdvancedFilters.value = false
    minAttack.value = 0
    minDefense.value = 0
    minHp.value = 0
    minSpeed.value = 0
    tagsFilter.value = []
  }

  /**
   * 获取可用的标签列表
   */
  const getAvailableTags = (spirits: Spirit[]) => {
    const allTags = spirits.flatMap(spirit => spirit.tags)
    return [...new Set(allTags)].sort()
  }

  /**
   * 获取属性范围
   */
  const getAttributeRanges = (spirits: Spirit[]) => {
    if (spirits.length === 0) {
      return {
        attack: [0, 0],
        defense: [0, 0],
        hp: [0, 0],
        speed: [0, 0],
        level: [1, 1]
      }
    }

    return {
      attack: [
        Math.min(...spirits.map(s => s.attributes.attack)),
        Math.max(...spirits.map(s => s.attributes.attack))
      ],
      defense: [
        Math.min(...spirits.map(s => s.attributes.defense)),
        Math.max(...spirits.map(s => s.attributes.defense))
      ],
      hp: [
        Math.min(...spirits.map(s => s.attributes.maxHp)),
        Math.max(...spirits.map(s => s.attributes.maxHp))
      ],
      speed: [
        Math.min(...spirits.map(s => s.attributes.speed)),
        Math.max(...spirits.map(s => s.attributes.speed))
      ],
      level: [
        Math.min(...spirits.map(s => s.level)),
        Math.max(...spirits.map(s => s.level))
      ]
    }
  }

  return {
    // 筛选条件
    searchQuery,
    elementFilter,
    professionFilter,
    levelRange,
    sortBy,
    sortOrder,
    showAdvancedFilters,
    minAttack,
    minDefense,
    minHp,
    minSpeed,
    tagsFilter,

    // 方法
    applyFilters,
    resetFilters,
    getAvailableTags,
    getAttributeRanges
  }
}