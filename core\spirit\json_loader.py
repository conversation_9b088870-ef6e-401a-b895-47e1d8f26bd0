"""
精灵属性 JSON 加载器

从 JSON 文件中加载精灵的初始属性配置
"""
from __future__ import annotations
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from ..attribute import Attributes
from ..element import ElementType
from ..profession import ProfessionType
from ..logging import get_logger

logger = get_logger("core.spirit.json_loader")


@dataclass
class SpiritJsonConfig:
    """精灵 JSON 配置数据类"""
    id: str
    name: str
    element: str
    professions: List[str]
    tags: List[str]
    shenge_level: int
    attributes: Dict[str, float]
    position: List[int]
    skills: List[Dict[str, Any]]  # 统一的技能格式
    passive_effects: List[str]
    shenyao_config: Optional[Dict[str, Any]] = None  # 神曜配置
    description: str = ""
    rarity: str = "N"
    version: str = "1.0.0"


class SpiritJsonLoader:
    """精灵属性 JSON 加载器"""
    
    def __init__(self, json_dir: str = "spirits_json"):
        self.json_dir = Path(json_dir)
        self._cache: Dict[str, SpiritJsonConfig] = {}
        self._ensure_json_dir()
    
    def _ensure_json_dir(self):
        """确保 JSON 目录存在"""
        if not self.json_dir.exists():
            self.json_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建精灵 JSON 目录: {self.json_dir}")

    def _get_chinese_filename(self, spirit_id: str) -> str:
        """根据精灵ID获取中文文件名"""
        chinese_name_map = {
            "chiyaowang_yushen": "赤妖王·御神.json",
            "shenyao_xuwu_fuyao": "神曜虚无·伏妖.json",
            "tianen_shengji_kongling_shenglong": "天恩圣祭·空灵圣龙.json",
            "shenyao_shengyu_nudi": "神曜圣谕·女帝.json"
        }
        return chinese_name_map.get(spirit_id, f"{spirit_id}.json")
    
    def load_spirit_config(self, spirit_id: str) -> Optional[SpiritJsonConfig]:
        """
        加载精灵配置

        Args:
            spirit_id: 精灵ID

        Returns:
            精灵配置对象，如果不存在则返回 None
        """
        # 检查缓存
        if spirit_id in self._cache:
            return self._cache[spirit_id]

        # 尝试多种文件路径（支持中文文件名）
        possible_files = [
            self.json_dir / f"{spirit_id}.json",
            # 根据 spirit_id 映射到中文文件名
            self.json_dir / self._get_chinese_filename(spirit_id)
        ]

        json_file = None
        for file_path in possible_files:
            if file_path.exists():
                json_file = file_path
                break

        if json_file is None:
            logger.warning(f"精灵配置文件不存在: {spirit_id}")
            return None
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证必需字段
            required_fields = ['id', 'name', 'element', 'attributes']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 创建配置对象
            config = SpiritJsonConfig(
                id=data['id'],
                name=data['name'],
                element=data['element'],
                professions=data.get('professions', []),
                tags=data.get('tags', []),
                shenge_level=data.get('shenge_level', 1),
                attributes=data['attributes'],
                position=data.get('position', [1, 1]),
                skills=data.get('skills', []),
                passive_effects=data.get('passive_effects', []),
                shenyao_config=data.get('shenyao_config'),
                description=data.get('description', ''),
                rarity=data.get('rarity', 'N'),
                version=data.get('version', '1.0.0')
            )
            
            # 缓存配置
            self._cache[spirit_id] = config
            
            logger.debug(f"成功加载精灵配置: {spirit_id}")
            return config
            
        except Exception as e:
            logger.error(f"加载精灵配置失败 {spirit_id}: {e}")
            return None
    
    def create_attributes_from_config(self, config: SpiritJsonConfig) -> Attributes:
        """
        从配置创建 Attributes 对象
        
        Args:
            config: 精灵配置
            
        Returns:
            Attributes 对象
        """
        attrs = config.attributes
        
        # 创建 Attributes 对象，使用配置中的值或默认值
        attributes = Attributes(
            base_hp=attrs.get('base_hp', 1000),
            hp_p=attrs.get('hp_p', 0.0),
            hp_flat=attrs.get('hp_flat', 0.0),
            base_attack=attrs.get('base_attack', 100),
            attack_p=attrs.get('attack_p', 0.0),
            attack_flat=attrs.get('attack_flat', 0.0),
            base_pdef=attrs.get('base_pdef', 50),
            pdef_p=attrs.get('pdef_p', 0.0),
            pdef_flat=attrs.get('pdef_flat', 0.0),
            base_mdef=attrs.get('base_mdef', 50),
            mdef_p=attrs.get('mdef_p', 0.0),
            mdef_flat=attrs.get('mdef_flat', 0.0),
            base_speed=attrs.get('base_speed', 100),
            base_hit_rate=attrs.get('base_hit_rate', 0.0),
            base_dodge_rate=attrs.get('base_dodge_rate', 0.0),
            base_break_rate=attrs.get('base_break_rate', 0.0),
            base_block_rate=attrs.get('base_block_rate', 0.0),
            base_crit_rate=attrs.get('base_crit_rate', 0.0),
            base_crit_res_rate=attrs.get('base_crit_res_rate', 0.0),
            base_crit_damage=attrs.get('base_crit_damage', 1.5),
            base_damage_reduction=attrs.get('base_damage_reduction', 0.0),
            base_penetration=attrs.get('base_penetration', 0.0)
        )



        return attributes
    
    def get_element_type(self, config: SpiritJsonConfig) -> ElementType:
        """
        获取元素类型
        
        Args:
            config: 精灵配置
            
        Returns:
            ElementType 枚举值
        """
        element_map = {
            'FIRE': ElementType.FIRE,
            'WATER': ElementType.WATER,
            'GRASS': ElementType.GRASS,
            'AIR': ElementType.AIR,
            'CREATION': ElementType.CREATION,
            'LIGHT': ElementType.LIGHT,
            'DARK': ElementType.DARK
        }

        return element_map.get(config.element.upper(), ElementType.FIRE)
    
    def get_profession_types(self, config: SpiritJsonConfig) -> set:
        """
        获取职业类型集合
        
        Args:
            config: 精灵配置
            
        Returns:
            职业类型集合
        """
        profession_map = {
            'HERO': ProfessionType.HERO,
            'BALANCE': ProfessionType.BALANCE,
            'MAGIC': ProfessionType.MAGIC,
            'MAGE': ProfessionType.MAGIC,  # 别名
            'TANK': ProfessionType.TANK,
            'SPEED': ProfessionType.SPEED,
            'CONTROL': ProfessionType.CONTROL,
            'HEALER': ProfessionType.HEALER,
            'CLAW': ProfessionType.CLAW,
            'SUMMONER': ProfessionType.SUMMONER,
            'DIVINE_REVELATION': ProfessionType.DIVINE_REVELATION,
            'NECROMANCER': ProfessionType.NECROMANCER,
            'ELEMENTALIST': ProfessionType.ELEMENTALIST,
            'GOD_EYE': ProfessionType.GOD_EYE,
            'AWAKENER': ProfessionType.AWAKENER,
            'SHENYAO': ProfessionType.SHENYAO
        }
        
        professions = set()
        for prof_str in config.professions:
            prof_type = profession_map.get(prof_str.upper())
            if prof_type:
                professions.add(prof_type)
        
        return professions
    
    def save_spirit_config(self, config: SpiritJsonConfig) -> bool:
        """
        保存精灵配置到 JSON 文件
        
        Args:
            config: 精灵配置
            
        Returns:
            是否保存成功
        """
        json_file = self.json_dir / f"{config.id}.json"
        
        try:
            # 构建数据字典
            data = {
                'id': config.id,
                'name': config.name,
                'element': config.element,
                'professions': config.professions,
                'tags': config.tags,
                'shenge_level': config.shenge_level,
                'attributes': config.attributes,
                'position': config.position,
                'skills': config.skills,
                'passive_effects': config.passive_effects,
                'description': config.description,
                'rarity': config.rarity,
                'version': config.version
            }
            
            # 写入文件
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 更新缓存
            self._cache[config.id] = config
            
            logger.info(f"成功保存精灵配置: {config.id}")
            return True
            
        except Exception as e:
            logger.error(f"保存精灵配置失败 {config.id}: {e}")
            return False
    
    def list_available_spirits(self) -> List[str]:
        """
        列出所有可用的精灵ID
        
        Returns:
            精灵ID列表
        """
        spirit_ids = []
        
        if not self.json_dir.exists():
            return spirit_ids
        
        for json_file in self.json_dir.glob("*.json"):
            spirit_id = json_file.stem
            spirit_ids.append(spirit_id)
        
        return sorted(spirit_ids)
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.debug("精灵配置缓存已清空")


# 全局加载器实例
_global_loader: Optional[SpiritJsonLoader] = None

def get_spirit_json_loader() -> SpiritJsonLoader:
    """获取全局精灵 JSON 加载器实例"""
    global _global_loader
    if _global_loader is None:
        _global_loader = SpiritJsonLoader()
    return _global_loader


def load_spirit_attributes_from_json(spirit_id: str) -> Optional[Attributes]:
    """
    从 JSON 文件加载精灵属性的便捷函数
    
    Args:
        spirit_id: 精灵ID
        
    Returns:
        Attributes 对象，如果加载失败则返回 None
    """
    loader = get_spirit_json_loader()
    config = loader.load_spirit_config(spirit_id)
    
    if config is None:
        return None
    
    return loader.create_attributes_from_config(config)


def load_spirit_metadata_from_json(spirit_id: str) -> Optional[Dict[str, Any]]:
    """
    从 JSON 文件加载精灵元数据的便捷函数
    
    Args:
        spirit_id: 精灵ID
        
    Returns:
        元数据字典，如果加载失败则返回 None
    """
    loader = get_spirit_json_loader()
    config = loader.load_spirit_config(spirit_id)
    
    if config is None:
        return None
    
    return {
        'element': loader.get_element_type(config),
        'professions': loader.get_profession_types(config),
        'tags': set(config.tags),
        'shenge_level': config.shenge_level,
        'position': tuple(config.position),
        'skills': config.skills,
        'passive_effects': config.passive_effects,
        'description': config.description,
        'rarity': config.rarity,
        'version': config.version
    }
