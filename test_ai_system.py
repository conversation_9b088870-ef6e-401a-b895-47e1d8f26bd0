#!/usr/bin/env python3
"""
AI行动生成系统测试

验证AI行动生成系统的各个组件是否正常工作。
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_system():
    """测试AI行动生成系统"""
    
    print("=== AI行动生成系统测试 ===")
    
    try:
        # 1. 测试模块导入
        print("\n1. 测试模块导入...")
        
        from core.ai import (
            IntelligentActionGenerator,
            ActionCapabilityChecker,
            DynamicConditionEvaluator,
            ConditionalEffectCalculator,
            get_action_generator
        )
        print("✅ 核心模块导入成功")
        
        from core.ai.extensions import (
            ExtensionManager,
            get_condition_registry,
            get_effect_registry,
            get_strategy_registry
        )
        print("✅ 扩展模块导入成功")
        
        # 2. 测试全局实例
        print("\n2. 测试全局实例...")
        
        generator = get_action_generator()
        print(f"✅ 全局行动生成器: {type(generator).__name__}")
        
        # 3. 测试组件初始化
        print("\n3. 测试组件初始化...")
        
        capability_checker = ActionCapabilityChecker()
        print(f"✅ 行动能力检查器: {len(capability_checker.checkers)} 个检查器")
        
        condition_evaluator = DynamicConditionEvaluator()
        print(f"✅ 动态条件评估器: {len(condition_evaluator.evaluators)} 个评估器")
        
        effect_calculator = ConditionalEffectCalculator()
        print(f"✅ 条件性效果计算器: {len(effect_calculator.calculators)} 个计算器")
        
        # 4. 测试扩展系统
        print("\n4. 测试扩展系统...")
        
        condition_registry = get_condition_registry()
        effect_registry = get_effect_registry()
        strategy_registry = get_strategy_registry()
        
        print(f"✅ 条件检查器注册表: {len(condition_registry._extensions)} 个扩展")
        print(f"✅ 效果计算器注册表: {len(effect_registry._extensions)} 个扩展")
        print(f"✅ 行动策略注册表: {len(strategy_registry._extensions)} 个扩展")
        
        # 5. 测试扩展统计
        print("\n5. 测试扩展统计...")
        
        stats = ExtensionManager.get_extension_stats()
        print(f"✅ 扩展统计: {stats}")
        
        all_extensions = ExtensionManager.list_all_extensions()
        for category, extensions in all_extensions.items():
            print(f"  {category}: {len(extensions)} 个")
            for name, info in extensions.items():
                print(f"    - {name} v{info.version}")
        
        # 6. 测试动作类型
        print("\n6. 测试动作类型...")
        
        from core.action import (
            EnhancedAttackAction,
            UnableToActEvent,
            ActionDecisionAction,
            ConditionalEffectTriggerAction
        )
        print("✅ 增强动作类型导入成功")
        
        # 7. 测试执行器
        print("\n7. 测试执行器...")
        
        try:
            from core.battle.execution.enhanced_actions import (
                _handle_enhanced_attack,
                _handle_unable_to_act,
                _handle_action_decision,
                _handle_conditional_effect_trigger
            )
            print("✅ 增强动作执行器导入成功")
        except ImportError as e:
            print(f"⚠️ 执行器导入警告: {e}")
        
        # 8. 测试示例扩展
        print("\n8. 测试示例扩展...")
        
        try:
            from core.ai.examples.sample_extensions import demonstrate_extensions
            demonstrate_extensions()
            print("✅ 示例扩展运行成功")
        except Exception as e:
            print(f"⚠️ 示例扩展警告: {e}")
        
        print("\n🎉 AI行动生成系统测试完成！")
        print("\n📋 系统组件总结:")
        print("  ✅ 行动能力检查器 - 处理控制效果和资源检查")
        print("  ✅ 动态条件评估器 - 评估攻击时的各种条件")
        print("  ✅ 条件性效果计算器 - 计算复杂的条件性效果")
        print("  ✅ 智能行动生成器 - 整合所有逻辑生成行动")
        print("  ✅ 扩展接口系统 - 支持插件化扩展")
        print("  ✅ 增强动作类型 - 支持复杂的战斗逻辑")
        print("  ✅ 专用执行器 - 处理增强动作的执行")
        
        print("\n🚀 系统已准备就绪，可以处理复杂的战斗逻辑！")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_battle():
    """测试模拟战斗场景"""
    
    print("\n=== 模拟战斗场景测试 ===")
    
    try:
        from core.ai import get_action_generator
        from core.ai.capability_checker import ActionCapabilityResult
        from core.ai.condition_evaluator import AttackConditionResult
        from core.ai.effect_calculator import ConditionalEffectResult
        
        # 创建模拟对象
        class MockSpirit:
            def __init__(self, name, hp=1000, max_hp=1000, energy=100, team=0):
                self.name = name
                self.id = name.lower()
                self.current_hp = hp
                self.max_hp = max_hp
                self.current_energy = energy
                self.max_energy = 300
                self.team = team
                self.is_alive = hp > 0
                self.attack = 200
                self.defense = 100
                self.crit_rate = 0.1
        
        class MockBattleState:
            def __init__(self):
                self.round_num = 5
                self.team0_spirits = [MockSpirit("精灵A", team=0)]
                self.team1_spirits = [MockSpirit("精灵B", team=1)]
            
            def get_living_spirits(self, team):
                if team == 0:
                    return [s for s in self.team0_spirits if s.is_alive]
                else:
                    return [s for s in self.team1_spirits if s.is_alive]
        
        class MockSkill:
            def __init__(self):
                self.metadata = type('Metadata', (), {
                    'name': '测试技能',
                    'cast_type': 'ACTIVE',
                    'energy_cost': 50
                })()
        
        # 创建测试对象
        attacker = MockSpirit("攻击者")
        target = MockSpirit("目标", hp=300)  # 低血量目标
        skill = MockSkill()
        battle_state = MockBattleState()
        
        generator = get_action_generator()
        
        print("\n1. 测试行动能力检查...")
        capability = generator.capability_checker.can_act(attacker, battle_state)
        print(f"  能否行动: {capability.can_act}")
        print(f"  原因: {capability.reason}")
        
        print("\n2. 测试动态条件评估...")
        conditions = generator.condition_evaluator.evaluate_attack_conditions(
            attacker, target, skill, battle_state
        )
        print(f"  评估条件数量: {len(conditions.conditions)}")
        print(f"  目标低血量: {conditions.get_condition('target_low_hp', False)}")
        print(f"  目标血量百分比: {conditions.get_condition('target_hp_percentage', 1.0):.2f}")
        
        print("\n3. 测试条件性效果计算...")
        effects = generator.effect_calculator.calculate_conditional_effects(
            attacker, target, skill, conditions, battle_state
        )
        print(f"  计算效果数量: {len(effects.effects)}")
        print(f"  触发事件数量: {len(effects.trigger_events)}")
        
        if effects.effects:
            print("  效果详情:")
            for effect_name, value in effects.effects.items():
                print(f"    - {effect_name}: {value}")
        
        print("\n✅ 模拟战斗场景测试完成！")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 模拟战斗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始AI行动生成系统测试...\n")
    
    # 运行基础系统测试
    basic_test_passed = test_ai_system()
    
    # 运行模拟战斗测试
    if basic_test_passed:
        mock_test_passed = test_mock_battle()
    else:
        mock_test_passed = False
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"  基础系统测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"  模拟战斗测试: {'✅ 通过' if mock_test_passed else '❌ 失败'}")
    
    if basic_test_passed and mock_test_passed:
        print("\n🎉 所有测试通过！AI行动生成系统已准备就绪！")
        print("\n📖 使用说明:")
        print("  1. 精灵会自动调用 generate_actions() 方法生成行动")
        print("  2. 系统会自动处理控制效果、动态条件和条件性效果")
        print("  3. 可以通过扩展接口添加自定义的条件和效果")
        print("  4. 查看 core/ai/README.md 获取详细文档")
    else:
        print("\n❌ 部分测试失败，请检查系统配置")
    
    print("="*50)
