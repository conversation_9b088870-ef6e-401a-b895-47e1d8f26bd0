/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BattleAnalyticsDetail: typeof import('./src/components/analytics/BattleAnalyticsDetail.vue')['default']
    BattleEffects: typeof import('./src/components/battle/BattleEffects.vue')['default']
    BattleField2D: typeof import('./src/components/battle/BattleField2D.vue')['default']
    BattleGrid: typeof import('./src/components/battle/BattleGrid.vue')['default']
    BattleLog: typeof import('./src/components/battle/BattleLog.vue')['default']
    BattleScene3D: typeof import('./src/components/battle/BattleScene3D.vue')['default']
    BattleSettings: typeof import('./src/components/battle/BattleSettings.vue')['default']
    BattleStats: typeof import('./src/components/battle/BattleStats.vue')['default']
    ChartCard: typeof import('./src/components/analytics/ChartCard.vue')['default']
    EffectIndicator: typeof import('./src/components/spirit/EffectIndicator.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    FormationEditor: typeof import('./src/components/tactics/FormationEditor.vue')['default']
    NotificationCenter: typeof import('./src/components/common/NotificationCenter.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SimpleSpiritTooltip: typeof import('./src/components/spirit/SimpleSpiritTooltip.vue')['default']
    SpiritAnalyticsDetail: typeof import('./src/components/analytics/SpiritAnalyticsDetail.vue')['default']
    SpiritCard: typeof import('./src/components/spirit/SpiritCard.vue')['default']
    SpiritDetails: typeof import('./src/components/spirit/SpiritDetails.vue')['default']
    SpiritForm: typeof import('./src/components/spirit/SpiritForm.vue')['default']
    SpiritQuickActions: typeof import('./src/components/spirit/SpiritQuickActions.vue')['default']
    SpiritSelectionList: typeof import('./src/components/spirit/SpiritSelectionList.vue')['default']
    SpiritSelector: typeof import('./src/components/spirit/SpiritSelector.vue')['default']
    SpiritTooltip: typeof import('./src/components/spirit/SpiritTooltip.vue')['default']
    StatCard: typeof import('./src/components/analytics/StatCard.vue')['default']
    TacticForm: typeof import('./src/components/tactics/TacticForm.vue')['default']
    TacticPrioritiesEditor: typeof import('./src/components/tactics/TacticPrioritiesEditor.vue')['default']
    TacticRulesEditor: typeof import('./src/components/tactics/TacticRulesEditor.vue')['default']
    TacticTestDialog: typeof import('./src/components/tactics/TacticTestDialog.vue')['default']
    TacticTestResults: typeof import('./src/components/tactics/TacticTestResults.vue')['default']
    TacticTriggersEditor: typeof import('./src/components/tactics/TacticTriggersEditor.vue')['default']
    TeamAnalysisChart: typeof import('./src/components/spirit/TeamAnalysisChart.vue')['default']
  }
}
