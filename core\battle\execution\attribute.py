"""Attribute manipulation handlers."""
from __future__ import annotations

from typing import Optional, List, cast

from core.battle.executor.executor import handler, UnifiedActionExecutor
from .decorators import validate_target, safe_execute
from ...action import (
    ModifyAttributeAction,
    AddAttributeAction,
    SetSpiritPropertyAction,
    UpdateSpiritAttributeAction,
    BattleAction,
    LogAction,
)


@handler(ModifyAttributeAction)
@validate_target(alive_required=True)
@safe_execute()
def _handle_modify_attribute(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    """处理属性修改（向精灵属性添加/移除修改器）。

    此处理器管理可以通过来源 ID 跟踪和移除的临时属性修改。
    用于增益、减益和装备效果。

    参数：
        self: UnifiedActionExecutor 实例
        action: 包含属性详情的 ModifyAttributeAction

    返回：
        None（属性修改不会生成新动作）
    """
    modify_action = cast(ModifyAttributeAction, action)
    target = modify_action.target

    if not hasattr(target, "attributes"):
        return None

    if modify_action.is_remove:
        if hasattr(target.attributes, "remove_modifier"):
            target.attributes.remove_modifier(modify_action.attribute_name, modify_action.source_id)  # type: ignore[attr-defined]
    else:
        if hasattr(target.attributes, "add_modifier"):
            target.attributes.add_modifier(
                modify_action.attribute_name, modify_action.value, modify_action.source_id  # type: ignore[attr-defined]
            )
        else:
            # fallback: direct attr mutation
            if hasattr(target.attributes, modify_action.attribute_name):
                current_value = getattr(target.attributes, modify_action.attribute_name)
                setattr(target.attributes, modify_action.attribute_name, current_value + modify_action.value)
    return None


@handler(AddAttributeAction)
def _handle_add_attribute(
    self: UnifiedActionExecutor, action: AddAttributeAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    """处理直接属性值增减。
    
    参数：
        self: UnifiedActionExecutor 实例
        action: 包含属性变化的 AddAttributeAction
        
    返回：
        None（属性修改不会生成新动作）
    """
    target = action.target
    if hasattr(target, action.attribute_name):
        current = getattr(target, action.attribute_name)
        setattr(target, action.attribute_name, current + action.value)
    else:
        setattr(target, action.attribute_name, action.value)
    return None


@handler(SetSpiritPropertyAction)
def _handle_set_property(
    self: UnifiedActionExecutor, action: SetSpiritPropertyAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    """处理精灵属性直接设置。
    
    参数：
        self: UnifiedActionExecutor 实例
        action: 包含属性设置的 SetSpiritPropertyAction
        
    返回：
        None（属性设置不会生成新动作）
    """
    try:
        setattr(action.target, action.property_name, action.value)
    except Exception:
        pass
    return None


@handler(UpdateSpiritAttributeAction)
def _handle_update_spirit_attribute(
    self: UnifiedActionExecutor, action: UpdateSpiritAttributeAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    """将精灵的属性更新为特定值。
    
    此处理器直接设置精灵的属性（如 HP、能量等）为新值。
    与 ModifyAttributeAction 不同，这是直接赋值而不是添加/移除修改器。
    
    参数：
        self: UnifiedActionExecutor 实例
        action: 包含新属性值的 UpdateSpiritAttributeAction
        
    返回：
        可选的 LogAction 来记录属性变化
    """
    if action.caster and hasattr(action.caster, action.attribute_name):
        setattr(action.caster, action.attribute_name, action.value)
        
        # Optional: log the change
        if action.reason:
            log_msg = f"{action.caster.name} {action.attribute_name} updated to {action.value} (reason: {action.reason})"
        else:
            log_msg = f"{action.caster.name} {action.attribute_name} updated to {action.value}"
            
        return [LogAction(caster=action.caster, message=log_msg, level="DEBUG")]
    return None