"""Revive and Die action handlers."""
from __future__ import annotations

from typing import Optional, List

from core.battle.executor.executor import handler, UnifiedActionExecutor
from ...action import ReviveAction, DieAction, DispatchEventAction, ApplyEffectAction, BattleAction


@handler(ReviveAction)
def _handle_revive(
    self: UnifiedActionExecutor, action: ReviveAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    if target.is_alive:  # type: ignore[attr-defined]
        return None

    # Revive
    target.revive(action.health_percent)  # type: ignore[attr-defined]

    from ...event.events import SpiritReviveEvent  # local import
    revive_event = SpiritReviveEvent(spirit=target, health_percent=action.health_percent)  # type: ignore[arg-type]
    actions: List[BattleAction] = [
        DispatchEventAction(caster=action.caster, event=revive_event)  # type: ignore[arg-type]
    ]

    for eff in action.effects_to_apply:
        actions.append(ApplyEffectAction(caster=action.caster, target=target, effect=eff))  # type: ignore[arg-type]
    return actions or None


@handler(DieAction)
def _handle_die(
    self: UnifiedActionExecutor, action: DieAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    if not target.is_alive:  # ensure only once
        return None

    # call faint event
    from ...event.events import SpiritFaintEvent

    faint_event = SpiritFaintEvent(spirit=target)  # type: ignore[arg-type]
    return [DispatchEventAction(caster=action.caster, event=faint_event)] 