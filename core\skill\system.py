#!/usr/bin/env python3
"""
技能注册系统

提供统一的技能注册和管理机制。
"""

from typing import Dict, Callable, Any, List, Optional, Type
from pathlib import Path
import inspect
import importlib.util

from ..logging import get_logger
# from .skills import Skill  # 暂时注释，因为文件被重置

logger = get_logger("skill.system")


class SkillRegistry:
    """技能注册表 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.skills: Dict[str, Callable] = {}
            self.metadata: Dict[str, Dict[str, Any]] = {}
            self._initialized = True
            logger.info("技能注册表初始化完成")
    
    def register_skill(self,
                       skill_id: str,
                       creator_func: Callable,
                       name: Optional[str] = None,
                       category: str = "NORMAL",
                       **metadata):
        """
        注册技能
        
        Args:
            skill_id: 技能唯一ID
            creator_func: 技能创建函数
            name: 技能显示名称
            category: 技能分类
            **metadata: 其他元数据
        """
        if skill_id in self.skills:
            logger.warning(f"技能 {skill_id} 已存在，将被覆盖")
        
        self.skills[skill_id] = creator_func
        
        # 如果没有提供名称，尝试从创建函数获取
        if name is None:
            try:
                # 安全地创建临时技能实例获取名称
                temp_skill = creator_func()
                if hasattr(temp_skill, 'metadata') and temp_skill.metadata:
                    name = getattr(temp_skill.metadata, 'name', skill_id)
                else:
                    name = getattr(temp_skill, 'name', skill_id)

                # 清理临时实例（如果有清理方法）
                if hasattr(temp_skill, 'cleanup'):
                    try:
                        temp_skill.cleanup()
                    except Exception:
                        pass  # 忽略清理错误

            except Exception as e:
                logger.warning(f"无法获取技能 {skill_id} 的名称: {e}")
                name = skill_id
        
        # 存储元数据
        self.metadata[skill_id] = {
            "id": skill_id,
            "name": name,
            "creator": creator_func,
            "category": category,
            **metadata
        }
        
        logger.info(f"技能注册成功: {skill_id} -> {name}")
    
    def get_creator(self, skill_id: str) -> Optional[Callable]:
        """获取技能创建函数"""
        return self.skills.get(skill_id)

    def get_metadata(self, skill_id: str) -> Optional[Dict[str, Any]]:
        """获取技能元数据"""
        return self.metadata.get(skill_id)

    def list_skills(self) -> List[str]:
        """列出所有已注册的技能ID"""
        return list(self.skills.keys())

    def clear(self):
        """清空注册表"""
        self.skills.clear()
        self.metadata.clear()
        logger.info("技能注册表已清空")


# 全局注册表实例
skill_registry = SkillRegistry()


def register_skill(skill_id: str,
                   creator_func: Optional[Callable] = None,
                   name: Optional[str] = None,
                   category: str = "NORMAL",
                   **metadata):
    """
    技能注册装饰器
    
    用法:
    @register_skill("my_skill_id", name="我的技能")
    def create_my_skill():
        return MySkill(...)
    
    或者直接调用:
    register_skill("my_skill_id", create_my_skill, name="我的技能")
    """
    def decorator(func):
        skill_registry.register_skill(
            skill_id=skill_id,
            creator_func=func,
            name=name,
            category=category,
            **metadata
        )
        return func
    
    if creator_func is not None:
        # 直接调用模式
        return decorator(creator_func)
    else:
        # 装饰器模式
        return decorator


def auto_discover_skills(skills_dir: str = "skills_data") -> int:
    """
    自动发现并注册技能
    
    Args:
        skills_dir: 技能数据目录
        
    Returns:
        注册的技能数量
    """
    logger.info(f"开始自动发现技能: {skills_dir}")
    
    registered_count = 0
    skills_path = Path(skills_dir)
    
    if not skills_path.exists():
        logger.warning(f"技能目录不存在: {skills_dir}")
        return 0
    
    # 遍历技能文件
    for py_file in skills_path.glob("*.py"):
        if py_file.name.startswith("_"):
            continue  # 跳过私有文件
        
        module_name = py_file.stem
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                f"skills_data.{module_name}",
                py_file
            )
            if spec is None or spec.loader is None:
                logger.warning(f"无法创建模块规范: {module_name}")
                continue

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找创建函数
            creator_functions = []
            for name, obj in inspect.getmembers(module):
                if (inspect.isfunction(obj) and 
                    name.startswith("create_") and 
                    not name.startswith("create_test")):  # 排除测试函数
                    creator_functions.append((name, obj))
            
            # 注册找到的技能
            for func_name, func in creator_functions:
                try:
                    # 安全地创建技能实例获取信息
                    temp_skill = None
                    try:
                        temp_skill = func()
                        skill_id = getattr(temp_skill, 'id', func_name.replace('create_', ''))

                        # 优先从metadata获取名称
                        if hasattr(temp_skill, 'metadata') and temp_skill.metadata:
                            skill_name = getattr(temp_skill.metadata, 'name', skill_id)
                            # 从metadata获取分类
                            category = getattr(temp_skill.metadata, 'cast_type', "NORMAL")
                        else:
                            skill_name = getattr(temp_skill, 'name', skill_id)
                            category = "NORMAL"

                        # 基于名称推断分类（如果metadata中没有）
                        if category == "NORMAL":
                            if "ultimate" in skill_name.lower():
                                category = "ULTIMATE"
                            elif "passive" in skill_name.lower():
                                category = "PASSIVE"

                    finally:
                        # 清理临时技能实例
                        if temp_skill and hasattr(temp_skill, 'cleanup'):
                            try:
                                temp_skill.cleanup()
                            except Exception:
                                pass  # 忽略清理错误

                    # 注册技能
                    skill_registry.register_skill(
                        skill_id=skill_id,
                        creator_func=func,
                        name=skill_name,
                        category=category,
                        module=module_name,
                        function=func_name
                    )

                    registered_count += 1
                    logger.info(f"自动注册技能: {skill_id} ({skill_name})")

                except Exception as e:
                    logger.error(f"注册技能失败 {func_name}: {e}")
                    
        except Exception as e:
            logger.error(f"导入模块失败 {module_name}: {e}")
    
    logger.info(f"自动发现完成，注册了 {registered_count} 个技能")
    return registered_count