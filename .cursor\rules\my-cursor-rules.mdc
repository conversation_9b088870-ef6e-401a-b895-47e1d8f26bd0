---
alwaysApply: true
---

When creating classes, methods, objects, etc., it is necessary to annotate the purpose, parameters, and structure.


Modifications to classes, methods, objects, and so on require cross-referencing to reduce errors.

Before carrying out the action, it is necessary to fully understand the purpose of the task, think about the method, and set the to-do to complete according to the plan.