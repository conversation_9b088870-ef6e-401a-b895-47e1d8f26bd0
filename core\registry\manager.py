#!/usr/bin/env python3
"""
统一注册管理器

集中管理系统中所有类型的注册，包括精灵、技能、效果等。
"""

from typing import Dict, Any, List
import logging

from ..spirit.registry import spirit_registry
from ..skill.system import skill_registry
from ..effect.factory import effect_factory

logger = logging.getLogger(__name__)


class RegistryManager:
    """统一注册管理器"""
    
    def __init__(self):
        self.registries = {
            'spirit': spirit_registry,
            'skill': skill_registry,
            'effect': effect_factory
        }
        logger.info("注册管理器初始化完成")
    
    def get_registry(self, registry_type: str):
        """获取指定类型的注册表"""
        return self.registries.get(registry_type)
    
    def list_registry_types(self) -> List[str]:
        """列出所有注册表类型"""
        return list(self.registries.keys())
    
    def get_all_registered_items(self) -> Dict[str, Any]:
        """获取所有已注册的项目"""
        result = {}
        
        # 精灵注册信息
        result['spirits'] = {
            'count': len(spirit_registry.list_spirits()),
            'items': spirit_registry.list_spirits()
        }
        
        # 技能注册信息
        result['skills'] = {
            'count': len(skill_registry.list_skills()),
            'items': skill_registry.list_skills()
        }
        
        # 效果注册信息
        result['effect_types'] = {
            'count': len(effect_factory.get_effect_types()),
            'items': list(effect_factory.get_effect_types().keys())
        }
        
        result['effect_templates'] = {
            'count': len(effect_factory.get_templates()),
            'items': list(effect_factory.get_templates().keys())
        }
        
        return result
    
    def auto_discover_all(self, spirits_dir: str = "spirits_data", 
                         skills_dir: str = "skills_data") -> Dict[str, int]:
        """自动发现并注册所有类型的项目"""
        from ..spirit.registry import auto_discover_spirits
        from ..skill.system import auto_discover_skills
        
        result = {}
        
        # 自动发现精灵
        try:
            result['spirits'] = auto_discover_spirits(spirits_dir)
        except Exception as e:
            logger.error(f"自动发现精灵失败: {e}")
            result['spirits'] = 0
        
        # 自动发现技能
        try:
            result['skills'] = auto_discover_skills(skills_dir)
        except Exception as e:
            logger.error(f"自动发现技能失败: {e}")
            result['skills'] = 0
        
        logger.info(f"自动发现完成: {result}")
        return result
    
    def clear_all(self):
        """清空所有注册表"""
        spirit_registry.clear()
        skill_registry.clear()
        logger.info("所有注册表已清空")


# 全局注册管理器实例
registry_manager = RegistryManager()