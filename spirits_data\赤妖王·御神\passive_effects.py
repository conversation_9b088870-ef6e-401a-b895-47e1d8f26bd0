"""
赤妖王·御神 - 被动技能效果模块

包含御神的被动技能效果：
- FoxSpiritPowerEffect: 狐念之力被动效果
"""
from __future__ import annotations
from typing import List, Optional, TYPE_CHECKING, Dict, Any
import uuid

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.spirit.refactored_spirit import RefactoredSpirit
from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition, BeforeDamageAppliedCondition, RoundEndCondition
from core.status import battle_status_checker


class FoxSpiritPowerEffect(IEffect):
    """
    狐念之力被动效果

    完整技能描述：
    1. 受到致命伤害时，若敌方有X位精灵处于无法行动状态，则令自身重生并恢复50%最大生命值（X初始为1，每次触发+1）
    2. 已阵亡精灵攻击无法行动的精灵时，若自身存活，则随机烧伤敌阵一位精灵并令自身恢复30气势
    3. 受击时，敌阵每有1位处于无法行动状态的存活精灵，则自身获得25%减伤（最多75%减伤）
    烧伤：对目标造成御神攻击*80%的伤害
    """

    def __init__(self, owner_spirit: RefactoredSpirit):
        super().__init__(
            effect_id=f"fox_spirit_power_{owner_spirit.id}",
            name="狐念之力",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGHEST,  # 最高优先级
            duration=-1  # 永久效果
        )

        self.owner_spirit = owner_spirit
        self.revive_threshold = 1  # X初始为1，每次触发+1
        self.revive_count = 0  # 重生触发次数
        
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True
    
    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        from core.effect.triggers import BeforeAttackCondition, BeforeDamageAppliedCondition
        return [
            BeforeDamageAppliedCondition(target="self"),  # 自身受到伤害前触发（减伤计算）
            BeforeAttackCondition(),  # 攻击前触发（检测已阵亡精灵攻击）
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理事件触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "BEFORE_DAMAGE_APPLIED":
                return self._handle_before_damage(event_data, battle_state)
            elif event_type == "BEFORE_ATTACK":
                return self._handle_before_attack(event_data, battle_state)

            return EffectResult.success()

        except Exception as e:
            from core.logging import spirit_logger
            spirit_logger.error(f"狐念之力被动效果触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)

    def _handle_before_damage(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理受到伤害前的逻辑 - 实现减伤和重生机制"""
        target = event_data.get("target")
        if target != self.owner_spirit:
            return EffectResult.success("不是效果拥有者受伤")

        damage = event_data.get("damage", 0)
        current_hp = getattr(target, 'current_hp', 0)

        # 计算减伤
        damage_reduction = self._calculate_damage_reduction(battle_state)
        if damage_reduction > 0:
            # 应用减伤
            reduced_damage = damage * (1 - damage_reduction)
            event_data["damage"] = reduced_damage

        # 检查是否为致命伤害（应用减伤后）
        final_damage = event_data.get("damage", 0)
        if final_damage < current_hp:
            # 不是致命伤害，只应用减伤
            if damage_reduction > 0:
                from core.action import LogAction
                actions = [LogAction(
                    caster=self.owner_spirit,
                    message=f"🦊 {getattr(target, 'name', '精灵')} 狐念之力减伤：{damage_reduction*100:.0f}%"
                )]
                return EffectResult.success_with_actions(actions, "狐念之力减伤触发")
            return EffectResult.success("不是致命伤害")

        # 是致命伤害，检查重生条件
        enemy_team = 1 if getattr(target, 'team', 0) == 0 else 0
        unable_to_act_count = self._count_unable_to_act_enemies(battle_state, enemy_team)

        if unable_to_act_count < self.revive_threshold:
            return EffectResult.success(f"敌方无法行动精灵数量不足（需要{self.revive_threshold}个，实际{unable_to_act_count}个）")

        # 触发重生
        return self._trigger_revive(event_data, battle_state)

    def _handle_before_attack(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理攻击前事件 - 检测已阵亡精灵攻击无法行动精灵"""
        # 检查攻击者是否为已阵亡精灵
        attacker = event_data.get("attacker") or event_data.get("source")
        if not attacker or getattr(attacker, 'is_alive', True):
            return EffectResult.success("攻击者不是已阵亡精灵")

        # 检查目标是否无法行动
        target = event_data.get("target")
        if not target or not battle_status_checker.status_checker.is_unable_to_act(target):
            return EffectResult.success("目标可以行动")

        # 检查御神是否存活
        if not getattr(self.owner_spirit, 'is_alive', False):
            return EffectResult.success("御神已阵亡")

        # 触发烧伤和气势恢复
        return self._trigger_burn_and_energy(battle_state)

    def _trigger_revive(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """触发重生机制"""
        from core.action import LogAction, SetHPAction

        actions = []

        # 阻止死亡，恢复50%最大生命值
        max_hp = getattr(self.owner_spirit, 'max_hp', 0)
        revive_hp = max_hp * 0.5

        # 设置生命值
        revive_action = SetHPAction(
            caster=self.owner_spirit,
            target=self.owner_spirit,
            hp=int(revive_hp)
        )
        actions.append(revive_action)

        # 增加重生阈值
        self.revive_count += 1
        self.revive_threshold += 1

        # 阻止原始伤害
        event_data["damage"] = 0

        log_action = LogAction(
            caster=self.owner_spirit,
            message=f"🦊 {getattr(self.owner_spirit, 'name', '御神')} 狐念之力重生！恢复50%最大生命值，下次重生需要{self.revive_threshold}个无法行动的敌人！"
        )
        actions.append(log_action)

        return EffectResult.success_with_actions(actions, "狐念之力重生触发")

    def _trigger_burn_and_energy(self, battle_state) -> EffectResult:
        """触发烧伤和气势恢复"""
        from src.core.action import LogAction, create_burn_damage_action
        import random

        actions = []

        # 获取敌方存活精灵
        enemy_team = 1 if getattr(self.owner_spirit, 'team', 0) == 0 else 0
        enemy_spirits = self._get_alive_enemies(battle_state, enemy_team)

        if enemy_spirits:
            # 随机选择一个敌方精灵进行烧伤
            target_enemy = random.choice(enemy_spirits)

            # 创建烧伤伤害动作：御神攻击*80%
            burn_action = create_burn_damage_action(
                caster=self.owner_spirit,
                target=target_enemy,
                power_multiplier=0.8,
                source="狐念之力烧伤"
            )
            actions.append(burn_action)

            actions.append(LogAction(
                caster=self.owner_spirit,
                message=f"🔥 {getattr(target_enemy, 'name', '敌人')} 被狐念之力烧伤！"
            ))

        # 御神恢复30气势
        current_energy = getattr(self.owner_spirit, 'energy', 0)
        max_energy = getattr(self.owner_spirit, 'max_energy', 300)
        new_energy = min(current_energy + 30, max_energy)
        setattr(self.owner_spirit, 'energy', new_energy)

        actions.append(LogAction(
            caster=self.owner_spirit,
            message=f"⚡ {getattr(self.owner_spirit, 'name', '御神')} 恢复30点气势！（当前气势：{new_energy}）"
        ))

        return EffectResult.success_with_actions(actions, "狐念之力烧伤和气势恢复触发")

    def _get_alive_enemies(self, battle_state, enemy_team: int) -> List:
        """获取敌方存活精灵列表"""
        alive_enemies = []
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                for spirit in enemy_spirits:
                    if getattr(spirit, 'is_alive', False):
                        alive_enemies.append(spirit)
        except:
            # 如果获取失败，返回空列表
            pass

        return alive_enemies

    def _trigger_revive_ally(self, battle_state) -> EffectResult:
        """触发复活队友逻辑"""
        from src.core.action import LogAction, SetHPAction

        actions = []

        # 检查是否有已死亡的队友
        dead_allies = self._get_dead_allies(battle_state)
        if not dead_allies:
            return EffectResult.success("没有已死亡的队友")

        # 随机选择一个死亡的队友
        import random
        target_ally = random.choice(dead_allies)

        # 复活队友30%生命值
        max_hp = getattr(target_ally, 'max_hp', 0)
        revive_hp = max_hp * 0.3

        revive_action = SetHPAction(
            caster=self.owner_spirit,
            target=target_ally,
            hp=int(revive_hp)
        )
        actions.append(revive_action)

        # 自身损失30%生命值
        self_max_hp = getattr(self.owner_spirit, 'max_hp', 0)
        self_damage = self_max_hp * 0.3
        current_hp = getattr(self.owner_spirit, 'current_hp', 0)
        new_hp = max(1, current_hp - self_damage)  # 确保不会死亡

        self_damage_action = SetHPAction(
            caster=self.owner_spirit,
            target=self.owner_spirit,
            hp=int(new_hp)
        )
        actions.append(self_damage_action)

        log_action = LogAction(
            caster=self.owner_spirit,
            message=f"🦊 {self.owner_spirit.name} 狐念之力触发！复活 {target_ally.name} 30%生命值，自身损失30%生命值！"
        )
        actions.append(log_action)

        return EffectResult.success_with_actions(actions, "狐念之力复活队友触发")

    def _count_unable_to_act_enemies(self, battle_state, enemy_team: int) -> int:
        """计算敌方无法行动精灵数量"""
        count = 0
        # 这里需要根据实际的battle_state结构来实现
        # 暂时返回模拟值
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                for spirit in enemy_spirits:
                    if spirit.is_alive and battle_status_checker.status_checker.is_unable_to_act(spirit):
                        count += 1
        except:
            # 如果获取失败，返回默认值
            count = 0
        
        return count

    def _get_dead_allies(self, battle_state) -> List:
        """获取已死亡的队友列表"""
        dead_allies = []
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                ally_spirits = battle_state.get_team_spirits(self.owner_spirit.team)
                for spirit in ally_spirits:
                    if not spirit.is_alive and spirit != self.owner_spirit:
                        dead_allies.append(spirit)
        except:
            # 如果获取失败，返回空列表
            pass
        
        return dead_allies

    def _calculate_damage_reduction(self, battle_state) -> float:
        """计算基于敌方无法行动精灵数量的减伤"""
        enemy_team = 1 if self.owner_spirit.team == 0 else 0
        unable_to_act_count = self._count_unable_to_act_enemies(battle_state, enemy_team)
        
        # 每个无法行动的敌方精灵提供25%减伤，最多75%
        reduction = min(unable_to_act_count * 0.25, 0.75)
        return reduction

    def on_apply(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果应用时触发"""
        # 计算初始减伤
        damage_reduction = self._calculate_damage_reduction(battle_state)
        self.set_data("damage_reduction", damage_reduction)
        
        return EffectResult.success_with_data(
            {"applied": True, "damage_reduction": damage_reduction}, 
            f"{target.name} 获得狐念之力被动效果"
        )

    def on_remove(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果移除时触发"""
        target_name = getattr(target, 'name', '精灵')
        return EffectResult.success_with_data(
            {"removed": True},
            f"{target_name} 失去狐念之力效果"
        )

    def on_update(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果更新时触发（每回合）"""
        # 每回合更新减伤值
        damage_reduction = self._calculate_damage_reduction(battle_state)
        self.set_data("damage_reduction", damage_reduction)
        
        target_name = getattr(target, 'name', '精灵')
        return EffectResult.success_with_data(
            {"damage_reduction": damage_reduction},
            f"{target_name}狐念之力减伤更新：{damage_reduction*100:.0f}%"
        )

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        damage_reduction = self.get_data("damage_reduction", 0)
        return {
            "name": self.name,
            "description": f"狐念之力被动效果（减伤{damage_reduction*100:.0f}%）",
            "damage_reduction": f"{damage_reduction*100:.0f}%",
            "duration": -1  # 永久效果
        }


# 导出函数
def create_fox_spirit_power_effect(owner_spirit: RefactoredSpirit) -> FoxSpiritPowerEffect:
    """创建狐念之力被动效果"""
    return FoxSpiritPowerEffect(owner_spirit)


# 导出所有效果类
__all__ = [
    'FoxSpiritPowerEffect',
    'create_fox_spirit_power_effect'
]
