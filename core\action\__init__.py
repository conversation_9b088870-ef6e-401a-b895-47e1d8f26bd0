from __future__ import annotations
from dataclasses import dataclass, field
from enum import Enum
from typing import TYPE_CHECKING, Optional, Dict, Any, List

# 本地导入
from ..event.events import GameEvent
from ..effect import EffectCategory

if TYPE_CHECKING:
    from ..spirit.spirit import Spirit
from ..effect import IEffect as ITypedEffect
from ..interfaces import IBattleEntity, IAction, IBattleState

"""战斗动作模块。

定义了在战斗中可以发生的所有可能动作。这些动作以数据类（dataclass）的形式
表示，用于在战斗系统各部分之间传递信息和意图。

所有动作都继承自基类 `BattleAction`。
"""


if TYPE_CHECKING:
    pass


class DamageType(Enum):
    """伤害类型枚举。

    Attributes:
        PHYSICAL: 物理伤害。
        DESTRUCTION: 毁灭伤害，通常无视防御或有特殊效果。
        MAGIC: 魔法伤害。
        FIRE: 火焰伤害，非直接伤害类型。
        ICE: 冰霜伤害，非直接伤害类型。
        LIGHTNING: 雷电伤害，非直接伤害类型。
        POISON: 毒素伤害，非直接伤害类型。
        BURN: 灼烧伤害，非直接伤害类型。
        BLEED: 流血伤害，非直接伤害类型。
        CURSE: 诅咒伤害，非直接伤害类型。
        HOLY: 神圣伤害，非直接伤害类型。
        DARK: 暗影伤害，非直接伤害类型。
        NATURE: 自然伤害，非直接伤害类型。
        PSYCHIC: 精神伤害，非直接伤害类型。
        SONIC: 音波伤害，非直接伤害类型。
        INDIRECT: 间接伤害，通用非直接伤害类型。
    """
    PHYSICAL = "物理"
    DESTRUCTION = "毁灭"
    MAGIC = "魔法"
    # 非直接伤害类型
    FIRE = "火焰"
    ICE = "冰霜"
    LIGHTNING = "雷电"
    POISON = "毒素"
    BURN = "灼烧"
    BLEED = "流血"
    CURSE = "诅咒"
    HOLY = "神圣"
    DARK = "暗影"
    NATURE = "自然"
    PSYCHIC = "精神"
    SONIC = "音波"
    INDIRECT = "间接"

    @classmethod
    def is_indirect_damage_type(cls, damage_type: 'DamageType') -> bool:
        """判断伤害类型是否为非直接伤害类型"""
        indirect_types = {
            cls.FIRE, cls.ICE, cls.LIGHTNING, cls.POISON,
            cls.BURN, cls.BLEED, cls.CURSE, cls.HOLY,
            cls.DARK, cls.NATURE, cls.PSYCHIC, cls.SONIC, cls.INDIRECT
        }
        return damage_type in indirect_types

    @classmethod
    def get_damage_type_category(cls, damage_type: 'DamageType') -> str:
        """获取伤害类型的分类"""
        if damage_type in {cls.PHYSICAL, cls.MAGIC, cls.DESTRUCTION}:
            return "direct"
        elif cls.is_indirect_damage_type(damage_type):
            return "indirect"
        else:
            return "unknown"

    @classmethod
    def get_damage_type_description(cls, damage_type: 'DamageType') -> str:
        """获取伤害类型的详细描述"""
        descriptions = {
            cls.PHYSICAL: "物理伤害，受物理防御影响",
            cls.MAGIC: "魔法伤害，受魔法防御影响",
            cls.DESTRUCTION: "毁灭伤害，通常无视防御",
            cls.FIRE: "火焰伤害，可能造成灼烧效果",
            cls.ICE: "冰霜伤害，可能造成冰冻效果",
            cls.LIGHTNING: "雷电伤害，可能造成麻痹效果",
            cls.POISON: "毒素伤害，持续性伤害",
            cls.BURN: "灼烧伤害，持续性火焰伤害",
            cls.BLEED: "流血伤害，持续性物理伤害",
            cls.CURSE: "诅咒伤害，魔法性质的持续伤害",
            cls.HOLY: "神圣伤害，对邪恶单位额外有效",
            cls.DARK: "暗影伤害，对光明单位额外有效",
            cls.NATURE: "自然伤害，环境性质的伤害",
            cls.PSYCHIC: "精神伤害，直接作用于意识",
            cls.SONIC: "音波伤害，震动性质的伤害",
            cls.INDIRECT: "间接伤害，通用非直接伤害类型"
        }
        return descriptions.get(damage_type, "未知伤害类型")


def create_indirect_damage_action(
    caster: IBattleEntity,
    target: IBattleEntity,
    damage_type: DamageType,
    power_multiplier: float = 1.0,
    indirect_source: Optional[str] = None,
    bypass_immunity: bool = False,
    bypass_shields: bool = False,
    label: str = ""
) -> 'DamageAction':
    """
    创建非直接伤害动作的辅助函数

    Args:
        caster: 伤害来源
        target: 伤害目标
        damage_type: 伤害类型（必须是非直接伤害类型）
        power_multiplier: 威力倍数
        indirect_source: 非直接伤害的来源描述
        bypass_immunity: 是否绕过免疫效果
        bypass_shields: 是否绕过护盾
        label: 伤害标签

    Returns:
        配置好的DamageAction实例
    """
    if not DamageType.is_indirect_damage_type(damage_type):
        raise ValueError(f"伤害类型 {damage_type.value} 不是非直接伤害类型")

    return DamageAction(
        caster=caster,
        target=target,
        damage_type=damage_type,
        power_multiplier=power_multiplier,
        is_indirect=True,
        indirect_source=indirect_source or f"{damage_type.value}伤害",
        bypass_immunity=bypass_immunity,
        bypass_shields=bypass_shields,
        label=label or f"{damage_type.value}伤害"
    )


def create_burn_damage_action(
    caster: IBattleEntity,
    target: IBattleEntity,
    power_multiplier: float = 0.8,
    source: str = "灼烧"
) -> 'DamageAction':
    """创建灼烧伤害动作"""
    return create_indirect_damage_action(
        caster=caster,
        target=target,
        damage_type=DamageType.BURN,
        power_multiplier=power_multiplier,
        indirect_source=source,
        label=f"{source}伤害"
    )


def create_poison_damage_action(
    caster: IBattleEntity,
    target: IBattleEntity,
    power_multiplier: float = 0.5,
    source: str = "中毒"
) -> 'DamageAction':
    """创建毒素伤害动作"""
    return create_indirect_damage_action(
        caster=caster,
        target=target,
        damage_type=DamageType.POISON,
        power_multiplier=power_multiplier,
        indirect_source=source,
        bypass_shields=True,  # 毒素通常绕过护盾
        label=f"{source}伤害"
    )


def create_bleed_damage_action(
    caster: IBattleEntity,
    target: IBattleEntity,
    power_multiplier: float = 0.6,
    source: str = "流血"
) -> 'DamageAction':
    """创建流血伤害动作"""
    return create_indirect_damage_action(
        caster=caster,
        target=target,
        damage_type=DamageType.BLEED,
        power_multiplier=power_multiplier,
        indirect_source=source,
        bypass_shields=True,  # 流血通常绕过护盾
        label=f"{source}伤害"
    )


def create_destruction_damage_action(
    caster: IBattleEntity,
    target: IBattleEntity,
    damage_value: Optional[int] = None,
    power_multiplier: float = 1.0,
    skill_name: str = "毁灭攻击",
    is_critical: bool = False,
    is_ultimate: bool = False
) -> 'DamageAction':
    """创建毁灭伤害动作

    毁灭伤害的特点：
    - 无视防御
    - 无视格挡
    - 无视减伤
    - 可以对免疫状态的目标造成伤害

    Args:
        caster: 施法者
        target: 目标
        damage_value: 固定伤害值（如果为None则由计算器计算）
        power_multiplier: 技能倍率
        skill_name: 技能名称
        is_critical: 是否暴击
        is_ultimate: 是否为超杀技能
    """
    return DamageAction(
        caster=caster,
        target=target,
        damage_value=damage_value,
        damage_type=DamageType.DESTRUCTION,
        power_multiplier=power_multiplier,
        skill_name=skill_name,
        is_critical=is_critical,
        is_ultimate=is_ultimate,
        label=f"{skill_name}(毁灭)"
    )

@dataclass(slots=True)
class BattleAction(IAction):
    """所有战斗动作的基类。

    Attributes:
        caster: 产生此动作的精灵。
    """
    caster: Optional[IBattleEntity]
    # source: Optional[Any] = None # 动作的来源，可以是技能、效果等
    
    def execute(self, battle_state: IBattleState) -> None:
        """执行动作的默认实现。子类应该重写此方法。"""
        pass

    def get_targets(self) -> List[IBattleEntity]:
        """获取动作的目标列表。"""
        return []

    def to_dict(self) -> Dict[str, Any]:
        """将动作转换为可序列化的字典。"""
        return {
            "caster_id": self.caster.id if self.caster else None,
        }


class TriggerSkillType(Enum):
    """要触发的技能类型。"""
    BASIC_ATTACK = "basic_attack"
    ULTIMATE_SKILL = "ultimate_skill"

@dataclass(slots=True)
class DamageAction(BattleAction):
    """对目标造成伤害的动作。

    Attributes:
        target: 承受伤害的精灵。
        damage_value: 基础伤害数值。
        damage_type: 伤害类型。
        is_critical: 是否为暴击。
        power_multiplier: 技能的威力乘数。
        is_ultimate: 是否为超杀攻击。
        is_splash: 是否为溅射伤害。
        overflow_energy: 超杀攻击溢出的气势。
        label: 用于日志记录的伤害标签。
        skill_name: 产生此伤害的技能名称。
        is_indirect: 是否为非直接伤害（如持续伤害、反射伤害等）。
        indirect_source: 非直接伤害的来源描述。
        bypass_immunity: 是否绕过免疫效果。
        bypass_shields: 是否绕过护盾。
    """
    target: IBattleEntity
    damage_value: Optional[int] = None
    damage_type: DamageType = DamageType.PHYSICAL
    is_critical: bool = False
    power_multiplier: float = 1.0
    is_ultimate: bool = False
    is_splash: bool = False
    overflow_energy: int = 0
    ultimate_threshold: Optional[int] = None
    label: str = ""
    skill_name: Optional[str] = None
    damage_breakdown: Optional[Dict[str, Any]] = None
    is_indirect: bool = False
    indirect_source: Optional[str] = None
    bypass_immunity: bool = False
    bypass_shields: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "damage_value": self.damage_value,
            "damage_type": self.damage_type.value,
            "is_critical": self.is_critical,
            "skill_name": self.skill_name,
            "damage_breakdown": self.damage_breakdown,
            "is_indirect": self.is_indirect,
            "indirect_source": self.indirect_source,
            "bypass_immunity": self.bypass_immunity,
            "bypass_shields": self.bypass_shields,
            "is_ultimate": self.is_ultimate,
            "is_splash": self.is_splash,
            "overflow_energy": self.overflow_energy,
            "ultimate_threshold": self.ultimate_threshold,
            "power_multiplier": self.power_multiplier,
            "label": self.label,
        })
        return data


@dataclass(slots=True)
class UpdateDamageAction(BattleAction):
    """更新一个已在队列中的 `DamageAction` 的伤害值。

    Attributes:
        original_action: 需要被更新的原始伤害动作。
        new_damage: 新的伤害值。
    """
    original_action: DamageAction
    new_damage: int

    def get_targets(self) -> List[IBattleEntity]:
        return [self.original_action.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "original_action_target_id": self.original_action.target.id,
            "new_damage": self.new_damage
        })
        return data


@dataclass(slots=True)
class SetHPAction(BattleAction):
    """直接设置目标的生命值。

    Attributes:
        target: 目标精灵。
        hp: 要设置的生命值。
    """
    target: IBattleEntity
    hp: int

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "hp": self.hp
        })
        return data


@dataclass(slots=True)
class UpdateEffectAction(BattleAction):
    """更新目标身上一个效果的状态。

    Attributes:
        target: 目标精灵。
        effect_id: 要更新的效果的唯一ID。
        updates: 一个包含要更新的属性和新值的字典。
    """
    target: IBattleEntity
    effect_id: str
    updates: Dict[str, Any]

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "effect_id": self.effect_id,
            "updates": self.updates
        })
        return data


@dataclass(slots=True)
class RemoveEffectAction(BattleAction):
    """从目标身上移除一个效果。

    Attributes:
        target: 目标精灵。
        effect_id: 要移除的效果的唯一ID。
    """
    target: IBattleEntity
    effect_id: str

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "effect_id": self.effect_id
        })
        return data


@dataclass(slots=True)
class ApplyEffectAction(BattleAction):
    """向目标施加一个效果。

    Attributes:
        target: 目标精灵。
        effect: 要施加的效果实例。
        from_attack: 标记此效果是否来自攻击技能（用于免疫检查）。
    """
    target: IBattleEntity
    effect: ITypedEffect
    from_attack: bool = False  # 新增：标记是否来自攻击

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "effect_name": self.effect.name,
            "from_attack": self.from_attack,
        })
        return data


@dataclass(slots=True)
class ConsumeEnergyAction(BattleAction):
    """消耗目标的能量（气势）。

    Attributes:
        target: 目标精灵。
        amount: 要消耗的能量值。
    """
    target: IBattleEntity
    amount: int

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "amount": self.amount
        })
        return data


@dataclass(slots=True)
class ExtraTurnAction(BattleAction):
    """一个给予目标额外行动机会的动作。"""
    pass


@dataclass(slots=True)
class UpdateSkillAction(BattleAction):
    """一个通用的更新技能属性的动作。"""
    target: IBattleEntity
    skill_id: str
    updates: Dict[str, Any]

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]
    
    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "skill_id": self.skill_id,
            "updates": self.updates
        })
        return data


@dataclass(slots=True)
class UpdateSpiritAttributeAction(BattleAction):
    """一个通用的更新精灵属性的动作。"""
    attribute_name: str
    value: Any
    reason: Optional[str] = None

    def get_targets(self) -> List[IBattleEntity]:
        # This action targets the caster itself
        return [self.caster] if self.caster else []

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "attribute_name": self.attribute_name,
            "value": self.value,
            "reason": self.reason
        })
        return data


@dataclass(slots=True)
class ReviveAction(BattleAction):
    """复活一个单位。

    Attributes:
        target: 要被复活的目标精灵。
        health_percent: 复活后恢复的生命值百分比 (0.0 to 1.0)。
        effects_to_apply: 复活后要施加到目标身上的效果列表。
    """
    target: IBattleEntity
    health_percent: float = 1.0
    effects_to_apply: List[ITypedEffect] = field(default_factory=list)

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "health_percent": self.health_percent,
        })
        return data


@dataclass(slots=True)
class LogAction(BattleAction):
    """记录一条战斗日志。

    Attributes:
        message: 要记录的日志信息。
        caster: 记录日志的来源，继承自基类。
    """
    message: str
    target: Optional[IBattleEntity] = None
    level: str = "INFO" # e.g., INFO, DEBUG, WARNING, ERROR

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target] if self.target else []

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "message": self.message,
            "level": self.level
        })
        return data


@dataclass(slots=True)
class ModifyAttributeAction(BattleAction):
    """修改目标的基础属性。

    此动作用于临时或永久地改变精灵的属性，如攻击力、防御力等。

    Attributes:
        target: 目标精灵。
        attribute_name: 要修改的属性名称。
        value: 属性的修改值。
        source_id: 修改来源的唯一标识符（如技能ID或效果ID），用于追踪和移除。
        is_remove: 如果为 True，则表示移除此来源的修改，而不是添加。
    """
    target: IBattleEntity
    attribute_name: str
    value: float
    source_id: Optional[str] = None
    is_remove: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "attribute_name": self.attribute_name,
            "value": self.value,
            "is_remove": self.is_remove
        })
        return data


@dataclass(slots=True)
class AddAttributeAction(BattleAction):
    """
    直接增减一个属性值（例如气势、护盾值）。
    这与 ModifyAttributeAction 不同，它不是修改基础属性的修正值，
    而是直接操作运行时属性。
    """
    target: IBattleEntity
    attribute_name: str
    value: float
    reason: Optional[str] = None

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "attribute_name": self.attribute_name,
            "value": self.value,
            "reason": self.reason
        })
        return data


@dataclass(slots=True)
class DieAction(BattleAction):
    """
    宣告一个单位死亡的动作。
    """
    target: IBattleEntity
    reason: Optional[str] = None
    # 新增：追踪造成这次死亡的伤害动作
    damage_action: Optional[DamageAction] = None

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "reason": self.reason
        })
        return data


@dataclass(slots=True)
class SetSpiritPropertyAction(BattleAction):
    """直接设置目标精灵的某个属性值。

    Attributes:
        target: 目标精灵。
        property_name: 要设置的属性的名称。
        value: 新的属性值。
    """
    target: IBattleEntity
    property_name: str
    value: Any

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "property_name": self.property_name,
            "value": self.value
        })
        return data


@dataclass(slots=True)
class TriggerSkillAction(BattleAction):
    """
    立即触发另一个单位使用特定类型的技能。
    """
    target: IBattleEntity
    skill_type: TriggerSkillType
    original_targets: List[IBattleEntity] = field(default_factory=list)

    def get_targets(self) -> List[IBattleEntity]:
        return self.original_targets or [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "skill_type": self.skill_type.value,
            "original_target_ids": [t.id for t in self.original_targets]
        })
        return data


@dataclass(slots=True)
class SwapSkillsAction(BattleAction):
    """
    替换目标精灵的技能。
    
    Attributes:
        target: 目标精灵。
        skill_map: 一个字典，key为要被替换掉的旧技能的cast_type(例如"ACTIVE", "ULTIMATE"), 
                   value为新技能的注册名。
    """
    target: IBattleEntity
    skill_map: Dict[str, str]

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "skill_map": self.skill_map
        })
        return data


@dataclass(slots=True)
class RemoveEffectsByCategoryAction(BattleAction):
    """
    移除目标身上指定类别的所有效果。
    
    Attributes:
        target: 目标精灵。
        category: 要移除的效果类别。
    """
    target: IBattleEntity
    category: EffectCategory

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "category": self.category.name
        })
        return data


@dataclass(slots=True)
class DispatchEventAction(BattleAction):
    """
    一个用于请求战斗系统分发一个新事件的动作。
    
    Attributes:
        event (GameEvent): 要被分发的事件实例。
    """
    event: "GameEvent"

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "event_name": getattr(self.event, 'name', getattr(self.event, 'event_type', type(self.event).__name__)),
            "event_data": getattr(self.event, 'data', str(self.event)), # Note: data should be serializable
        })
        return data


@dataclass(slots=True)
class CloneSpiritAction(BattleAction):
    """
    克隆一个精灵的动作。

    Attributes:
        spirit_to_clone: 被克隆的精灵。
        team: 克隆体所属的队伍ID。
        position: 克隆体要出现的位置。
        attribute_ratio: 克隆体继承原精灵的属性百分比。
        initial_effects: 克隆体初始拥有的效果列表。
    """
    spirit_to_clone: IBattleEntity
    team: int
    position: tuple[int, int]
    attribute_ratio: float = 0.6
    initial_effects: List[ITypedEffect] = field(default_factory=list)

    def get_targets(self) -> List[IBattleEntity]:
        # This action doesn't have a direct target in the same way,
        # but one could argue the clone itself is the target.
        # For now, returning empty as it's a creation action.
        return []

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "spirit_to_clone_id": self.spirit_to_clone.id,
            "team": self.team,
            "position": self.position,
            "attribute_ratio": self.attribute_ratio,
        })
        return data


# ============================================================================
# 护盾系统Action - 奥奇传说中的护盾机制
# ============================================================================

@dataclass(slots=True)
class CreateShieldAction(BattleAction):
    """
    创建护盾的动作。

    护盾可以吸收伤害，有不同的类型和特性。

    Attributes:
        target: 获得护盾的目标精灵。
        shield_value: 护盾的吸收值。
        shield_type: 护盾类型（物理、魔法、全能等）。
        duration: 护盾持续回合数，-1表示永久。
        shield_id: 护盾的唯一标识符。
        can_stack: 是否可以与同类型护盾叠加。
        reflect_damage: 是否反射被吸收的伤害。
        reflect_ratio: 反射伤害的比例。
    """
    target: IBattleEntity
    shield_value: int
    shield_type: str = "physical"  # physical, magic, omnipotent
    duration: int = 3
    shield_id: Optional[str] = None
    can_stack: bool = False
    reflect_damage: bool = False
    reflect_ratio: float = 0.0

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "shield_value": self.shield_value,
            "shield_type": self.shield_type,
            "duration": self.duration,
            "shield_id": self.shield_id,
            "can_stack": self.can_stack,
            "reflect_damage": self.reflect_damage,
            "reflect_ratio": self.reflect_ratio
        })
        return data


@dataclass(slots=True)
class UpdateShieldAction(BattleAction):
    """
    更新护盾状态的动作。

    Attributes:
        target: 拥有护盾的目标精灵。
        shield_id: 要更新的护盾ID。
        value_change: 护盾值的变化（正数增加，负数减少）。
        duration_change: 持续时间的变化。
        new_properties: 要更新的护盾属性。
    """
    target: IBattleEntity
    shield_id: str
    value_change: Optional[int] = None
    duration_change: Optional[int] = None
    new_properties: Optional[Dict[str, Any]] = None

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "shield_id": self.shield_id,
            "value_change": self.value_change,
            "duration_change": self.duration_change,
            "new_properties": self.new_properties
        })
        return data


@dataclass(slots=True)
class RemoveShieldAction(BattleAction):
    """
    移除护盾的动作。

    Attributes:
        target: 拥有护盾的目标精灵。
        shield_id: 要移除的护盾ID，None表示移除所有护盾。
        shield_type: 要移除的护盾类型，None表示移除所有类型。
        reason: 移除原因（过期、破坏、驱散等）。
    """
    target: IBattleEntity
    shield_id: Optional[str] = None
    shield_type: Optional[str] = None
    reason: str = "removed"

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "shield_id": self.shield_id,
            "shield_type": self.shield_type,
            "reason": self.reason
        })
        return data


# ============================================================================
# 反射系统Action - 奥奇传说中的反射机制
# ============================================================================

@dataclass(slots=True)
class ReflectDamageAction(BattleAction):
    """
    反射伤害的动作。

    当精灵受到攻击时，可以反射部分或全部伤害给攻击者。

    Attributes:
        original_target: 原始受到攻击的目标。
        reflect_target: 反射伤害的目标（通常是攻击者）。
        original_damage: 原始伤害值。
        reflect_damage: 反射的伤害值。
        reflect_type: 反射类型（伤害反射、效果反射等）。
        reflect_ratio: 反射比例。
        ignore_defense: 反射伤害是否无视防御。
    """
    original_target: IBattleEntity
    reflect_target: IBattleEntity
    original_damage: int
    reflect_damage: int
    reflect_type: str = "damage"  # damage, effect, both
    reflect_ratio: float = 1.0
    ignore_defense: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.reflect_target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "original_target_id": self.original_target.id,
            "reflect_target_id": self.reflect_target.id,
            "original_damage": self.original_damage,
            "reflect_damage": self.reflect_damage,
            "reflect_type": self.reflect_type,
            "reflect_ratio": self.reflect_ratio,
            "ignore_defense": self.ignore_defense
        })
        return data


@dataclass(slots=True)
class ReflectEffectAction(BattleAction):
    """
    反射效果的动作。

    将施加给自己的负面效果反射给施法者。

    Attributes:
        original_target: 原始效果目标。
        reflect_target: 反射效果的目标。
        effect: 要反射的效果。
        reflect_success: 反射是否成功。
        original_effect_removed: 是否移除原始效果。
    """
    original_target: IBattleEntity
    reflect_target: IBattleEntity
    effect: ITypedEffect
    reflect_success: bool = True
    original_effect_removed: bool = True

    def get_targets(self) -> List[IBattleEntity]:
        return [self.reflect_target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "original_target_id": self.original_target.id,
            "reflect_target_id": self.reflect_target.id,
            "effect_name": self.effect.name,
            "reflect_success": self.reflect_success,
            "original_effect_removed": self.original_effect_removed
        })
        return data


# ============================================================================
# 传送和位置系统Action - 奥奇传说中的位置变换机制
# ============================================================================

@dataclass(slots=True)
class TeleportAction(BattleAction):
    """
    传送精灵的动作。

    将精灵传送到指定位置，可能触发特殊效果。

    Attributes:
        target: 要传送的目标精灵。
        new_position: 新的位置坐标。
        teleport_type: 传送类型（瞬移、交换等）。
        trigger_effects: 传送时是否触发位置相关效果。
        can_be_blocked: 传送是否可以被阻挡。
    """
    target: IBattleEntity
    new_position: tuple[int, int]
    teleport_type: str = "instant"  # instant, swap, push
    trigger_effects: bool = True
    can_be_blocked: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "new_position": self.new_position,
            "teleport_type": self.teleport_type,
            "trigger_effects": self.trigger_effects,
            "can_be_blocked": self.can_be_blocked
        })
        return data


@dataclass(slots=True)
class SwapPositionAction(BattleAction):
    """
    交换两个精灵位置的动作。

    Attributes:
        target1: 第一个精灵。
        target2: 第二个精灵。
        force_swap: 是否强制交换（无视阻挡效果）。
        trigger_effects: 交换时是否触发位置相关效果。
    """
    target1: IBattleEntity
    target2: IBattleEntity
    force_swap: bool = False
    trigger_effects: bool = True

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target1, self.target2]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target1_id": self.target1.id,
            "target2_id": self.target2.id,
            "force_swap": self.force_swap,
            "trigger_effects": self.trigger_effects
        })
        return data


@dataclass(slots=True)
class PushAction(BattleAction):
    """
    推动精灵的动作。

    将目标精灵推向指定方向，可能造成位移伤害。

    Attributes:
        target: 要推动的目标精灵。
        direction: 推动方向（上下左右）。
        distance: 推动距离。
        push_damage: 推动造成的伤害。
        can_push_off_field: 是否可以推出战场。
    """
    target: IBattleEntity
    direction: str  # up, down, left, right
    distance: int = 1
    push_damage: int = 0
    can_push_off_field: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "direction": self.direction,
            "distance": self.distance,
            "push_damage": self.push_damage,
            "can_push_off_field": self.can_push_off_field
        })
        return data


# ============================================================================
# 能量系统Action - 奥奇传说中的能量管理机制
# ============================================================================

@dataclass(slots=True)
class TransferEnergyAction(BattleAction):
    """
    转移能量的动作。

    将能量从一个精灵转移到另一个精灵。

    Attributes:
        source: 能量来源精灵。
        target: 能量接收精灵。
        amount: 转移的能量数量。
        transfer_type: 转移类型（给予、偷取、共享）。
        efficiency: 转移效率（0.0-1.0）。
        can_exceed_max: 是否可以超过最大能量值。
    """
    source: IBattleEntity
    target: IBattleEntity
    amount: int
    transfer_type: str = "give"  # give, steal, share
    efficiency: float = 1.0
    can_exceed_max: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.source, self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "source_id": self.source.id,
            "target_id": self.target.id,
            "amount": self.amount,
            "transfer_type": self.transfer_type,
            "efficiency": self.efficiency,
            "can_exceed_max": self.can_exceed_max
        })
        return data


@dataclass(slots=True)
class DrainEnergyAction(BattleAction):
    """
    吸取能量的动作。

    从目标精灵吸取能量，可能转化为其他资源。

    Attributes:
        target: 被吸取能量的目标。
        drain_amount: 吸取的能量数量。
        convert_to: 转化目标（hp, shield, buff等）。
        conversion_ratio: 转化比例。
        drain_type: 吸取类型（直接、百分比）。
    """
    target: IBattleEntity
    drain_amount: int
    convert_to: str = "energy"  # energy, hp, shield, buff
    conversion_ratio: float = 1.0
    drain_type: str = "direct"  # direct, percentage

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "drain_amount": self.drain_amount,
            "convert_to": self.convert_to,
            "conversion_ratio": self.conversion_ratio,
            "drain_type": self.drain_type
        })
        return data


@dataclass(slots=True)
class BurnEnergyAction(BattleAction):
    """
    燃烧能量的动作。

    消耗目标的能量来造成伤害或产生特殊效果。

    Attributes:
        target: 目标精灵。
        energy_cost: 消耗的能量数量。
        damage_per_energy: 每点能量造成的伤害。
        burn_type: 燃烧类型（伤害、治疗、增益）。
        max_energy_burn: 最大燃烧能量数量。
    """
    target: IBattleEntity
    energy_cost: int
    damage_per_energy: float = 1.0
    burn_type: str = "damage"  # damage, heal, buff
    max_energy_burn: Optional[int] = None

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "energy_cost": self.energy_cost,
            "damage_per_energy": self.damage_per_energy,
            "burn_type": self.burn_type,
            "max_energy_burn": self.max_energy_burn
        })
        return data


# ============================================================================
# 特殊状态Action - 奥奇传说中的特殊游戏状态
# ============================================================================

@dataclass(slots=True)
class InvincibilityAction(BattleAction):
    """
    无敌状态的动作。

    使目标在指定时间内免疫所有伤害。

    Attributes:
        target: 目标精灵。
        duration: 无敌持续回合数。
        invincibility_type: 无敌类型（完全、物理、魔法）。
        can_be_dispelled: 是否可以被驱散。
        immune_to_effects: 是否同时免疫负面效果。
    """
    target: IBattleEntity
    duration: int = 1
    invincibility_type: str = "complete"  # complete, physical, magic
    can_be_dispelled: bool = True
    immune_to_effects: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "duration": self.duration,
            "invincibility_type": self.invincibility_type,
            "can_be_dispelled": self.can_be_dispelled,
            "immune_to_effects": self.immune_to_effects
        })
        return data


@dataclass(slots=True)
class StealthAction(BattleAction):
    """
    隐身状态的动作。

    使目标进入隐身状态，无法被选择为攻击目标。

    Attributes:
        target: 目标精灵。
        duration: 隐身持续回合数。
        stealth_type: 隐身类型（完全、部分）。
        break_on_attack: 攻击时是否破除隐身。
        detection_immunity: 是否免疫侦测效果。
    """
    target: IBattleEntity
    duration: int = 2
    stealth_type: str = "complete"  # complete, partial
    break_on_attack: bool = True
    detection_immunity: bool = False

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "duration": self.duration,
            "stealth_type": self.stealth_type,
            "break_on_attack": self.break_on_attack,
            "detection_immunity": self.detection_immunity
        })
        return data


@dataclass(slots=True)
class ForbidAction(BattleAction):
    """
    禁止状态的动作。

    禁止目标使用特定类型的技能或行动。

    Attributes:
        target: 目标精灵。
        forbid_type: 禁止类型（技能、移动、道具等）。
        duration: 禁止持续回合数。
        specific_skills: 特定被禁止的技能列表。
        can_be_cleansed: 是否可以被净化。
    """
    target: IBattleEntity
    forbid_type: str = "skill"  # skill, move, item, all
    duration: int = 3
    specific_skills: List[str] = field(default_factory=list)
    can_be_cleansed: bool = True

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "forbid_type": self.forbid_type,
            "duration": self.duration,
            "specific_skills": self.specific_skills,
            "can_be_cleansed": self.can_be_cleansed
        })
        return data


@dataclass(slots=True)
class TransformAction(BattleAction):
    """
    变身状态的动作。

    将目标变身为其他形态，改变属性和技能。

    Attributes:
        target: 目标精灵。
        transform_type: 变身类型（临时、永久）。
        new_form_id: 新形态的ID。
        duration: 变身持续时间。
        attribute_changes: 属性变化。
        skill_changes: 技能变化。
        can_be_dispelled: 是否可以被驱散。
    """
    target: IBattleEntity
    transform_type: str = "temporary"  # temporary, permanent
    new_form_id: str = ""
    duration: int = 5
    attribute_changes: Dict[str, float] = field(default_factory=dict)
    skill_changes: Dict[str, str] = field(default_factory=dict)
    can_be_dispelled: bool = True

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target]

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id,
            "transform_type": self.transform_type,
            "new_form_id": self.new_form_id,
            "duration": self.duration,
            "attribute_changes": self.attribute_changes,
            "skill_changes": self.skill_changes,
            "can_be_dispelled": self.can_be_dispelled
        })
        return data


# === AI行动生成系统的增强动作类型 ===

@dataclass(slots=True)
class EnhancedAttackAction(BattleAction):
    """增强攻击动作 - 包含条件性效果"""
    target: IBattleEntity
    skill: Any  # Skill类型，避免循环导入
    conditional_effects: Dict[str, Any] = field(default_factory=dict)

    def get_enhanced_damage(self) -> float:
        """获取增强后的伤害"""
        try:
            base_damage = getattr(self.skill, 'base_damage', 100)
            if hasattr(self.skill, 'get_base_damage'):
                base_damage = self.skill.get_base_damage(self.caster)

            multiplier = self.conditional_effects.get('damage_multiplier', 1.0)
            return base_damage * multiplier
        except:
            return 100.0

    def get_enhanced_crit_rate(self) -> float:
        """获取增强后的暴击率"""
        try:
            base_crit = getattr(self.caster, 'crit_rate', 0.05)
            bonus = self.conditional_effects.get('crit_rate_bonus', 0.0)
            return min(base_crit + bonus, 1.0)  # 最大100%
        except:
            return 0.05

    def get_enhanced_crit_damage(self) -> float:
        """获取增强后的暴击伤害"""
        try:
            base_crit_damage = 1.5  # 基础150%暴击伤害
            bonus = self.conditional_effects.get('crit_damage_bonus', 0.0)
            return base_crit_damage + bonus
        except:
            return 1.5

    def get_enhanced_pierce_rate(self) -> float:
        """获取增强后的破击率"""
        try:
            base_pierce = getattr(self.caster, 'pierce_rate', 0.0)
            bonus = self.conditional_effects.get('pierce_rate_bonus', 0.0)
            return min(base_pierce + bonus, 1.0)  # 最大100%
        except:
            return 0.0

    def get_energy_gain(self) -> int:
        """获取气势获得量"""
        return self.conditional_effects.get('energy_gain', 0)

    def has_combo_effect(self) -> bool:
        """检查是否有连击效果"""
        return self.conditional_effects.get('combo_attack', False)

    def get_targets(self) -> List[IBattleEntity]:
        return [self.target] if self.target else []

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_id": self.target.id if self.target else None,
            "skill_name": getattr(self.skill.metadata, 'name', 'Unknown') if hasattr(self.skill, 'metadata') else 'Unknown',
            "conditional_effects": self.conditional_effects,
            "enhanced_damage": self.get_enhanced_damage(),
            "enhanced_crit_rate": self.get_enhanced_crit_rate(),
            "enhanced_crit_damage": self.get_enhanced_crit_damage(),
            "enhanced_pierce_rate": self.get_enhanced_pierce_rate(),
            "energy_gain": self.get_energy_gain(),
            "has_combo": self.has_combo_effect()
        })
        return data


@dataclass(slots=True)
class UnableToActEvent(BattleAction):
    """无法行动事件动作"""
    reason: str
    blocked_by: List[str] = field(default_factory=list)

    def get_targets(self) -> List[IBattleEntity]:
        return [self.caster] if self.caster else []

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "reason": self.reason,
            "blocked_by": self.blocked_by
        })
        return data


@dataclass(slots=True)
class ActionDecisionAction(BattleAction):
    """行动决策动作 - 用于AI决策过程"""
    available_skills: List[Any] = field(default_factory=list)  # 可用技能列表
    decision_factors: Dict[str, Any] = field(default_factory=dict)  # 决策因素
    selected_skill: Optional[Any] = None  # 选择的技能
    selected_targets: List[IBattleEntity] = field(default_factory=list)  # 选择的目标

    def get_targets(self) -> List[IBattleEntity]:
        return self.selected_targets

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "available_skills_count": len(self.available_skills),
            "decision_factors": self.decision_factors,
            "selected_skill_name": getattr(self.selected_skill.metadata, 'name', 'None') if self.selected_skill and hasattr(self.selected_skill, 'metadata') else 'None',
            "selected_targets_count": len(self.selected_targets)
        })
        return data


@dataclass(slots=True)
class ConditionalEffectTriggerAction(BattleAction):
    """条件性效果触发动作"""
    effect_name: str
    trigger_conditions: Dict[str, Any] = field(default_factory=dict)
    effect_data: Dict[str, Any] = field(default_factory=dict)

    def get_targets(self) -> List[IBattleEntity]:
        return [self.caster] if self.caster else []

    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "effect_name": self.effect_name,
            "trigger_conditions": self.trigger_conditions,
            "effect_data": self.effect_data
        })
        return data
