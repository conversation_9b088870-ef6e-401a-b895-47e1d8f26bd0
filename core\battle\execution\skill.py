"""Skill-related action handlers: trigger, swap, update skill."""
from __future__ import annotations

from typing import Optional, List

from core.battle.executor.executor import handler, UnifiedActionExecutor
from ...action import (
    TriggerSkillAction,
    SwapSkillsAction,
    UpdateSkillAction,
    BattleAction,
)


@handler(TriggerSkillAction)
def _handle_trigger_skill(
    self: UnifiedActionExecutor, action: TriggerSkillAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    skill_type = action.skill_type
    original_targets = action.original_targets

    skill_to_trigger = None
    for skill in target.skills:
        if hasattr(skill, "metadata"):
            if skill_type.value == "basic_attack" and skill.metadata.cast_type == "ACTIVE":
                skill_to_trigger = skill
                break
            if skill_type.value == "ultimate_skill" and skill.metadata.cast_type == "ULTIMATE":
                skill_to_trigger = skill
                break

    if not skill_to_trigger:
        return None

    skill_to_trigger.owner = target
    targets = original_targets or []
    if not targets and hasattr(skill_to_trigger, "target_selector"):
        targets = skill_to_trigger.target_selector.select_targets(target, self.battle_state, context={})
    if not targets:
        return None

    return skill_to_trigger.cast(self.battle_state, forced_targets=targets)  # type: ignore[arg-type]


@handler(SwapSkillsAction)
def _handle_swap_skills(
    self: UnifiedActionExecutor, action: SwapSkillsAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    swapped: List[str] = []
    for old_cast_type, new_skill_name in action.skill_map.items():
        for idx, skill in enumerate(target.skills):
            if getattr(skill, "metadata", None) and skill.metadata.cast_type == old_cast_type:
                # Log swap; actual registry retrieval omitted
                swapped.append(f"{skill.metadata.name}->{new_skill_name}")
                break
    if swapped:
        self.battle_log.append({"level": "INFO", "message": f"SwapSkills: {swapped}"})
    return None


@handler(UpdateSkillAction)
def _handle_update_skill(
    self: UnifiedActionExecutor, action: UpdateSkillAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    for skill in target.skills:
        if getattr(skill, "id", None) == action.skill_id or getattr(skill, "name", None) == action.skill_id:
            for attr_name, new_value in action.updates.items():
                if hasattr(skill, attr_name):
                    setattr(skill, attr_name, new_value)
                elif hasattr(skill, "metadata") and hasattr(skill.metadata, attr_name):
                    setattr(skill.metadata, attr_name, new_value)
            break
    return None 