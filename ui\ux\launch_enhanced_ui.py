#!/usr/bin/env python3
"""
启动增强版战斗UI

包含依赖检查和错误处理的启动脚本
"""

import sys
import os

def check_dependencies():
    """检查依赖"""
    try:
        import tkinter
        print("✅ tkinter 可用")
        return True
    except ImportError:
        print("❌ tkinter 不可用，请安装 tkinter")
        print("在 Ubuntu/Debian 上: sudo apt-get install python3-tk")
        print("在 CentOS/RHEL 上: sudo yum install tkinter")
        print("在 Windows 上: tkinter 通常随 Python 一起安装")
        return False

def main():
    """主函数"""
    print("="*60)
    print("🚀 奥奇传说AI战斗系统 - 增强版UI")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 添加项目根目录到路径
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)
    
    try:
        # 导入并运行增强UI
        from ui.ux.enhanced_battle_ui import main as run_enhanced_ui
        print("✅ 启动增强版UI...")
        run_enhanced_ui()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有必要的模块都可用")
        
        # 尝试启动简化版UI作为备选
        try:
            print("🔄 尝试启动简化版UI...")
            sys.path.insert(0, project_root)
            from simple_battle_ui import main as run_simple_ui
            run_simple_ui()
        except:
            print("❌ 简化版UI也无法启动")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
