"""
统一阶段化执行器


"""
from __future__ import annotations

import time
import traceback
from collections import deque, defaultdict
from typing import Dict, List, Optional, Any, TYPE_CHECKING
from enum import Enum, auto

if TYPE_CHECKING:
    from core.action import BattleAction
    from core.battle.models import BattleState
    from core.battle.conditions import Battle<PERSON>ond<PERSON><PERSON>hecker

from core.logging import get_logger

logger = get_logger("battle.execution.unified")

# 全局处理器注册表
_handler_registry: Dict[type, callable] = {}


def handler(action_type: type):
    """装饰器，用于将动作类型的处理器注册到全局注册表"""
    def decorator(func):
        if action_type in _handler_registry:
            raise RuntimeError(f"Handler for {action_type.__name__} already registered")
        _handler_registry[action_type] = func
        return func
    return decorator


class ExecutionPhase(Enum):
    """执行阶段枚举"""
    PRE_ACTION = auto()          # 前置处理阶段
    MAIN_ACTION = auto()         # 主要动作阶段
    IMMEDIATE_RESPONSE = auto()  # 立即响应阶段
    POST_ACTION = auto()         # 后处理阶段
    CLEANUP = auto()             # 清理阶段

    @property
    def priority(self) -> int:
        """获取阶段优先级（数值越小优先级越高）"""
        phase_priorities = {
            ExecutionPhase.PRE_ACTION: 1,
            ExecutionPhase.MAIN_ACTION: 2,
            ExecutionPhase.IMMEDIATE_RESPONSE: 3,
            ExecutionPhase.POST_ACTION: 4,
            ExecutionPhase.CLEANUP: 5
        }
        return phase_priorities[self]


class ActionClassifier:
    """动作分类器 - 将动作分类到合适的执行阶段"""
    
    def __init__(self):
        # 预定义的动作类型到阶段的映射
        self._action_phase_map = {
            # PRE_ACTION (前置处理阶段)
            'InitializeStateAction': ExecutionPhase.PRE_ACTION,
            'UpdateTurnOrderAction': ExecutionPhase.PRE_ACTION,
            'RegenerateResourceAction': ExecutionPhase.PRE_ACTION,
            'CheckSkillConditionAction': ExecutionPhase.PRE_ACTION,
            'CheckEnergyAction': ExecutionPhase.PRE_ACTION,
            'PositionAdjustAction': ExecutionPhase.PRE_ACTION,
            'SetBattlePropertyAction': ExecutionPhase.PRE_ACTION,
            'UpdateBattlefieldAction': ExecutionPhase.PRE_ACTION,
            'CheckEffectTriggerAction': ExecutionPhase.PRE_ACTION,
            'PrepareEffectAction': ExecutionPhase.PRE_ACTION,
            
            # MAIN_ACTION (主要动作阶段)
            'DamageAction': ExecutionPhase.MAIN_ACTION,
            'HealAction': ExecutionPhase.MAIN_ACTION,
            'CastSkillAction': ExecutionPhase.MAIN_ACTION,
            'TriggerSkillAction': ExecutionPhase.MAIN_ACTION,
            'ConsumeEnergyAction': ExecutionPhase.MAIN_ACTION,
            'ModifyAttributeAction': ExecutionPhase.MAIN_ACTION,
            'AddAttributeAction': ExecutionPhase.MAIN_ACTION,
            'SetHPAction': ExecutionPhase.MAIN_ACTION,
            'SetSpiritPropertyAction': ExecutionPhase.MAIN_ACTION,
            'UpdateSpiritAttributeAction': ExecutionPhase.MAIN_ACTION,
            'SwapSkillsAction': ExecutionPhase.MAIN_ACTION,
            'CloneSpiritAction': ExecutionPhase.MAIN_ACTION,
            
            # IMMEDIATE_RESPONSE (立即响应阶段)
            'CounterAttackAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            'BlockAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            'DodgeAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            'ImmunityAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            'ReflectDamageAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            'ReflectEffectAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            'InterruptAction': ExecutionPhase.IMMEDIATE_RESPONSE,
            
            # POST_ACTION (后处理阶段)
            'ApplyEffectAction': ExecutionPhase.POST_ACTION,
            'UpdateEffectAction': ExecutionPhase.POST_ACTION,
            'DispatchEventAction': ExecutionPhase.POST_ACTION,
            'LogAction': ExecutionPhase.POST_ACTION,
            'UpdateDamageAction': ExecutionPhase.POST_ACTION,
            'CreateShieldAction': ExecutionPhase.POST_ACTION,
            'UpdateShieldAction': ExecutionPhase.POST_ACTION,
            'TeleportAction': ExecutionPhase.POST_ACTION,
            'SwapPositionAction': ExecutionPhase.POST_ACTION,
            'PushAction': ExecutionPhase.POST_ACTION,
            'TransferEnergyAction': ExecutionPhase.POST_ACTION,
            'DrainEnergyAction': ExecutionPhase.POST_ACTION,
            'BurnEnergyAction': ExecutionPhase.POST_ACTION,
            'InvincibilityAction': ExecutionPhase.POST_ACTION,
            'StealthAction': ExecutionPhase.POST_ACTION,
            'ForbidAction': ExecutionPhase.POST_ACTION,
            'TransformAction': ExecutionPhase.POST_ACTION,
            
            # CLEANUP (清理阶段)
            'RemoveEffectAction': ExecutionPhase.CLEANUP,
            'RemoveEffectsByCategoryAction': ExecutionPhase.CLEANUP,
            'RemoveShieldAction': ExecutionPhase.CLEANUP,
            'DieAction': ExecutionPhase.CLEANUP,
            'ReviveAction': ExecutionPhase.CLEANUP,
            'CleanupAction': ExecutionPhase.CLEANUP,
            'EndTurnAction': ExecutionPhase.CLEANUP,
        }
        
        self._dynamic_rules = []
    
    def classify_action(self, action: 'BattleAction', current_phase: ExecutionPhase, 
                       parent_action: Optional['BattleAction'] = None) -> ExecutionPhase:
        """将动作分类到合适的执行阶段"""
        action_type = type(action).__name__
        
        # 1. 检查预定义映射
        if action_type in self._action_phase_map:
            target_phase = self._action_phase_map[action_type]
            
            # 确保不会向前调度（只能向后或同级）
            if target_phase.priority >= current_phase.priority:
                return target_phase
            else:
                # 如果目标阶段已经过去，调度到下一个合适的阶段
                return self._find_next_suitable_phase(target_phase, current_phase)
        
        # 2. 应用动态规则
        for rule in self._dynamic_rules:
            result = rule(action, current_phase, parent_action)
            if result:
                return result
        
        # 3. 基于动作名称的启发式分类
        return self._heuristic_classification(action, current_phase, parent_action)
    
    def _find_next_suitable_phase(self, target_phase: ExecutionPhase, 
                                 current_phase: ExecutionPhase) -> ExecutionPhase:
        """找到下一个合适的阶段"""
        phases = list(ExecutionPhase)
        current_index = phases.index(current_phase)
        
        # 从当前阶段开始，找到第一个优先级不低于目标阶段的阶段
        for phase in phases[current_index:]:
            if phase.priority >= target_phase.priority:
                return phase
        
        # 如果没找到，返回最后一个阶段
        return ExecutionPhase.CLEANUP
    
    def _heuristic_classification(self, action: 'BattleAction', current_phase: ExecutionPhase,
                                 parent_action: Optional['BattleAction']) -> ExecutionPhase:
        """基于启发式规则的动作分类"""
        action_name = type(action).__name__
        
        if 'Check' in action_name or 'Prepare' in action_name or 'Initialize' in action_name:
            return ExecutionPhase.PRE_ACTION
            
        if 'Damage' in action_name or 'Heal' in action_name or 'Cast' in action_name or 'Trigger' in action_name:
            return ExecutionPhase.MAIN_ACTION
            
        if 'Counter' in action_name or 'Block' in action_name or 'Dodge' in action_name or 'Immune' in action_name:
            return ExecutionPhase.IMMEDIATE_RESPONSE
            
        if 'Apply' in action_name or 'Update' in action_name or 'Dispatch' in action_name:
            return ExecutionPhase.POST_ACTION
            
        if 'Remove' in action_name or 'Die' in action_name or 'Cleanup' in action_name or 'Revive' in action_name:
            return ExecutionPhase.CLEANUP
        
        # 基于父动作类型推断
        if parent_action:
            parent_type = type(parent_action).__name__
            if parent_type == 'DamageAction':
                # 伤害动作产生的子动作通常是立即响应或后处理
                if hasattr(action, 'caster') and action.caster != parent_action.caster:
                    return ExecutionPhase.IMMEDIATE_RESPONSE  # 可能是反击
                else:
                    return ExecutionPhase.POST_ACTION
        
        # 默认分类
        if current_phase == ExecutionPhase.PRE_ACTION:
            return ExecutionPhase.MAIN_ACTION
        elif current_phase == ExecutionPhase.MAIN_ACTION:
            return ExecutionPhase.POST_ACTION
        else:
            return ExecutionPhase.CLEANUP
    
    def add_dynamic_rule(self, rule_func):
        """添加动态分类规则"""
        self._dynamic_rules.append(rule_func)
    
    def register_action_phase(self, action_type: str, phase: ExecutionPhase):
        """注册动作类型到阶段的映射"""
        self._action_phase_map[action_type] = phase


class UnifiedActionExecutor:
    """统一阶段化动作执行器 - 只使用阶段化队列模式"""
    
    def __init__(self, battle_state: 'BattleState', condition_checker: 'BattleConditionChecker',
                 battle_log: Optional[List[dict]] = None):
        self.battle_state = battle_state
        self.condition_checker = condition_checker
        self.battle_log = battle_log or []
        
        # 阶段化执行相关
        self.phase_queues: Dict[ExecutionPhase, deque] = {
            phase: deque() for phase in ExecutionPhase
        }
        self.classifier = ActionClassifier()
        self.current_phase = ExecutionPhase.PRE_ACTION
        
        # 执行控制
        self.max_iterations_per_phase = 1000
        self.max_total_iterations = 5000
        self.enable_phase_logging = True
        self.enable_performance_monitoring = True
        
        # 历史记录
        self.action_history: List['BattleAction'] = []
        self.error_counts: Dict[str, int] = defaultdict(int)
        
        # 统计信息
        self.execution_stats = {
            'total_actions': 0,
            'phase_stats': defaultdict(lambda: {'actions': 0, 'time': 0.0}),
            'errors': [],
            'warnings': [],
            'action_types': defaultdict(int)
        }
        
        # 记录器（可选）
        self.recorder = None
    
    def execute_actions(self, actions: List['BattleAction']) -> None:
        """执行动作列表 - 使用阶段化处理"""
        if not actions:
            return
        
        # 将初始动作分类到各个阶段队列
        for action in actions:
            phase = self.classifier.classify_action(action, ExecutionPhase.PRE_ACTION)
            self.phase_queues[phase].append(action)
        
        # 按阶段顺序执行
        total_iterations = 0
        
        for phase in ExecutionPhase:
            if total_iterations >= self.max_total_iterations:
                logger.warning(f"达到最大总迭代次数限制 ({self.max_total_iterations})")
                break
            
            self.current_phase = phase
            phase_start_time = time.perf_counter()
            phase_iterations = 0
            
            if self.enable_phase_logging:
                logger.debug(f"开始执行阶段: {phase.name}")
            
            # 执行当前阶段的所有动作
            while self.phase_queues[phase] and phase_iterations < self.max_iterations_per_phase:
                action = self.phase_queues[phase].popleft()
                phase_iterations += 1
                total_iterations += 1
                
                try:
                    new_actions = self._execute_single_action(action)
                    if new_actions:
                        # 将新生成的动作分类到合适的阶段
                        for new_action in new_actions:
                            target_phase = self.classifier.classify_action(
                                new_action, self.current_phase, action
                            )
                            self.phase_queues[target_phase].append(new_action)
                
                except Exception as e:
                    self._handle_execution_error(action, e)
            
            # 记录阶段统计
            phase_time = time.perf_counter() - phase_start_time
            self.execution_stats['phase_stats'][phase.name]['actions'] += phase_iterations
            self.execution_stats['phase_stats'][phase.name]['time'] += phase_time
            
            if self.enable_phase_logging:
                logger.debug(f"阶段 {phase.name} 完成: {phase_iterations} 个动作, 耗时 {phase_time:.4f}s")
            
            # 检查阶段迭代限制
            if phase_iterations >= self.max_iterations_per_phase:
                logger.warning(f"阶段 {phase.name} 达到最大迭代次数限制 ({self.max_iterations_per_phase})")
        
        self.execution_stats['total_actions'] += total_iterations
        
        if self.enable_performance_monitoring:
            logger.debug(f"动作执行完成: 总计 {total_iterations} 个动作")
    
    def _execute_single_action(self, action: 'BattleAction') -> Optional[List['BattleAction']]:
        """执行单个动作"""
        # 记录动作历史
        self.action_history.append(action)
        
        # 统计动作类型
        action_type = type(action).__name__
        self.execution_stats['action_types'][action_type] += 1
        
        # 记录动作用于回放
        if self.recorder:
            self.recorder.record_action(action)
        
        # 使用注册的处理器执行动作
        handler_fn = _handler_registry.get(type(action))
        if handler_fn is None:
            logger.error(f"没有为 {action_type} 注册处理器")
            return None

        try:
            result = handler_fn(self, action)

            # 调用统计跟踪器记录动作结果
            if hasattr(self, 'stats_tracker') and self.stats_tracker:
                # 为统计跟踪器创建动作结果
                action_result = self._create_action_result_for_stats(action)
                self.stats_tracker.on_action(action, action_result)

            return result
        except Exception as e:
            # 增加错误计数
            self.error_counts[action_type] += 1
            # 重新抛出异常以便上层捕获
            raise e

    def _create_action_result_for_stats(self, action: 'BattleAction') -> Dict[str, Any]:
        """为统计跟踪器创建动作结果"""
        from core.action import DamageAction, ConsumeEnergyAction, LogAction

        result = {}

        if isinstance(action, DamageAction):
            # 伤害动作的统计信息
            damage_value = getattr(action, 'damage_value', 0)
            target = getattr(action, 'target', None)

            if target:
                hp_change = -damage_value if damage_value > 0 else 0
                result = {
                    'hp_change': hp_change,
                    'hp_after': getattr(target, 'current_hp', 0),
                    'energy_change': 0,
                    'energy_after': getattr(target, 'energy', 0),
                    'is_critical': getattr(action, 'is_critical', False),
                    'is_dodged': False,
                    'is_blocked': False,
                    'is_damage_immune': False,
                    'is_killed': not getattr(target, 'is_alive', True),
                    'applied_effects': [],
                    'removed_effects': []
                }

        elif isinstance(action, ConsumeEnergyAction):
            # 气势消耗动作的统计信息
            target = getattr(action, 'target', None)
            amount = getattr(action, 'amount', 0)

            if target:
                result = {
                    'hp_change': 0,
                    'hp_after': getattr(target, 'current_hp', 0),
                    'energy_change': -amount,
                    'energy_after': getattr(target, 'energy', 0),
                    'is_critical': False,
                    'is_dodged': False,
                    'is_blocked': False,
                    'is_damage_immune': False,
                    'is_killed': False,
                    'applied_effects': [],
                    'removed_effects': []
                }

        else:
            # 其他动作类型的默认统计信息
            result = {
                'hp_change': 0,
                'hp_after': 0,
                'energy_change': 0,
                'energy_after': 0,
                'is_critical': False,
                'is_dodged': False,
                'is_blocked': False,
                'is_damage_immune': False,
                'is_killed': False,
                'applied_effects': [],
                'removed_effects': []
            }

        return result
    
    def _handle_execution_error(self, action: 'BattleAction', error: Exception):
        """处理执行错误"""
        action_type = type(action).__name__
        error_msg = f"执行 {action_type} 时发生错误: {error}"
        
        logger.error(error_msg)
        logger.debug(f"错误堆栈: {traceback.format_exc()}")
        
        self.execution_stats['errors'].append({
            'action_type': action_type,
            'error': str(error),
            'timestamp': time.time()
        })
        
        # 记录到战斗日志
        self.battle_log.append({
            "level": "ERROR",
            "message": error_msg,
            "timestamp": time.time()
        })
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return {
            'total_actions': self.execution_stats['total_actions'],
            'phase_stats': dict(self.execution_stats['phase_stats']),
            'action_types': dict(self.execution_stats['action_types']),
            'error_counts': dict(self.error_counts),
            'total_errors': len(self.execution_stats['errors']),
            'total_warnings': len(self.execution_stats['warnings'])
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.execution_stats = {
            'total_actions': 0,
            'phase_stats': defaultdict(lambda: {'actions': 0, 'time': 0.0}),
            'errors': [],
            'warnings': [],
            'action_types': defaultdict(int)
        }
        self.error_counts.clear()
        self.action_history.clear()


# 导出全局注册表和装饰器
__all__ = ['UnifiedActionExecutor', 'handler', '_handler_registry', 'ExecutionPhase', 'ActionClassifier']
