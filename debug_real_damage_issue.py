#!/usr/bin/env python3
"""
深入调试真实的伤害问题

检查为什么两个精灵都只造成很少伤害
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_actual_battle_damage():
    """调试实际战斗中的伤害"""
    print("🔧 调试实际战斗中的伤害...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"📊 战斗前状态:")
        print(f"  {spirit1.name}: {spirit1.current_hp}/{spirit1.max_hp} HP")
        print(f"  {spirit2.name}: {spirit2.current_hp}/{spirit2.max_hp} HP")
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        # 执行一回合并详细追踪
        print(f"\n🎯 执行第一回合...")
        
        # 记录战斗前的HP
        before_hp = {}
        all_spirits = engine.battle_state.get_all_spirits()
        for spirit in all_spirits:
            before_hp[spirit.name] = spirit.current_hp
        
        # 执行回合
        result = engine.execute_round()
        
        # 记录战斗后的HP
        after_hp = {}
        for spirit in all_spirits:
            after_hp[spirit.name] = spirit.current_hp
        
        print(f"\n📈 战斗后状态:")
        for spirit in all_spirits:
            before = before_hp[spirit.name]
            after = after_hp[spirit.name]
            damage = before - after
            print(f"  {spirit.name}: {before} -> {after} (伤害: {damage})")
        
        # 检查是否有伤害记录
        print(f"\n🔍 检查战斗记录...")
        if hasattr(engine, 'battle_state') and hasattr(engine.battle_state, 'action_history'):
            history = engine.battle_state.action_history
            print(f"  行动历史记录数: {len(history)}")
            for i, action in enumerate(history):
                print(f"    行动 {i+1}: {action}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_damage_action_execution():
    """调试伤害动作的执行过程"""
    print("\n🔧 调试伤害动作执行过程...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        print(f"📊 精灵属性对比:")
        print(f"  攻击方 {spirit1.name}:")
        print(f"    实际攻击力: {spirit1.attributes.get_actual_attack(spirit1)}")
        print(f"    当前HP: {spirit1.current_hp}")
        
        print(f"  防御方 {spirit2.name}:")
        print(f"    物理防御: {spirit2.attributes.pdef}")
        print(f"    当前HP: {spirit2.current_hp}")
        
        # 创建模拟的伤害动作
        from core.action import DamageAction, DamageType
        
        damage_action = DamageAction(
            caster=spirit1,
            target=spirit2,
            damage_value=None,  # 让系统计算
            power_multiplier=1.0,
            damage_type=DamageType.PHYSICAL,
            skill_name="测试攻击"
        )
        
        print(f"\n🎯 创建伤害动作:")
        print(f"  施放者: {damage_action.caster.name}")
        print(f"  目标: {damage_action.target.name}")
        print(f"  倍率: {damage_action.power_multiplier}")
        print(f"  类型: {damage_action.damage_type}")
        
        # 手动计算预期伤害
        from core.battle.utilities.formula_damage_calculator import calculate_formula_damage
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        battle_state = BattleState(formation1, formation2)
        
        expected_damage, breakdown = calculate_formula_damage(
            caster=spirit1,
            target=spirit2,
            action=damage_action,
            battle_state=battle_state
        )
        
        print(f"\n📊 预期伤害计算:")
        print(f"  计算结果: {expected_damage}")
        print(f"  计算详情字段数: {len(breakdown)}")
        
        # 记录目标的初始HP
        initial_hp = spirit2.current_hp
        print(f"\n🎯 执行伤害动作:")
        print(f"  目标初始HP: {initial_hp}")
        
        # 直接调用take_damage方法
        actual_damage = spirit2.take_damage(expected_damage)
        final_hp = spirit2.current_hp
        
        print(f"  预期伤害: {expected_damage}")
        print(f"  实际伤害: {actual_damage}")
        print(f"  目标最终HP: {final_hp}")
        print(f"  HP变化: {initial_hp} -> {final_hp} (差值: {initial_hp - final_hp})")
        
        # 检查是否有差异
        if abs(actual_damage - expected_damage) > 1:
            print(f"  ⚠️ 伤害不匹配！预期{expected_damage}，实际{actual_damage}")
        else:
            print(f"  ✅ 伤害匹配")
        
        return True
        
    except Exception as e:
        print(f"❌ 伤害动作调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_skill_damage_calculation():
    """调试技能伤害计算"""
    print("\n🔧 调试技能伤害计算...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        print(f"📊 技能伤害计算测试:")
        print(f"  攻击方: {spirit1.name}")
        print(f"  防御方: {spirit2.name}")
        
        # 获取攻击方的技能
        if hasattr(spirit1, 'skills') and spirit1.skills:
            for i, skill in enumerate(spirit1.skills):
                if hasattr(skill, 'metadata'):
                    cast_type = getattr(skill.metadata, 'cast_type', None)
                    if cast_type == 'ACTIVE':
                        print(f"\n  技能 {i}: {skill.metadata.name}")
                        print(f"    描述: {getattr(skill.metadata, 'description', '无')}")
                        print(f"    气势消耗: {getattr(skill.metadata, 'energy_cost', 0)}")
                        
                        # 检查技能组件
                        if hasattr(skill, 'components'):
                            print(f"    组件数量: {len(skill.components)}")
                            for j, component in enumerate(skill.components):
                                print(f"      组件 {j}: {type(component).__name__}")
                                if hasattr(component, 'power_multiplier'):
                                    print(f"        伤害倍率: {component.power_multiplier}")
                                if hasattr(component, 'damage_type'):
                                    print(f"        伤害类型: {component.damage_type}")
        
        # 测试不同的伤害倍率
        test_multipliers = [0.1, 0.5, 1.0, 1.2, 2.0, 3.0]
        
        print(f"\n📈 不同倍率的伤害测试:")
        for multiplier in test_multipliers:
            from core.action import DamageAction, DamageType
            
            damage_action = DamageAction(
                caster=spirit1,
                target=spirit2,
                damage_value=None,
                power_multiplier=multiplier,
                damage_type=DamageType.PHYSICAL,
                skill_name=f"测试攻击x{multiplier}"
            )
            
            from core.battle.utilities.formula_damage_calculator import calculate_formula_damage
            from core.formation import Formation
            from core.battle.models import BattleState
            
            formation1 = Formation()
            formation2 = Formation()
            formation1.add_spirit(spirit1, 1, 1)
            formation2.add_spirit(spirit2, 3, 1)
            battle_state = BattleState(formation1, formation2)
            
            damage, breakdown = calculate_formula_damage(
                caster=spirit1,
                target=spirit2,
                action=damage_action,
                battle_state=battle_state
            )
            
            print(f"    倍率 {multiplier}: 伤害 {damage}")
        
        return True
        
    except Exception as e:
        print(f"❌ 技能伤害调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_hp_values():
    """调试HP数值"""
    print("\n🔧 调试HP数值...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        for spirit_name in available_spirits:
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"\n📊 {spirit.name} HP分析:")
            print(f"  当前HP: {spirit.current_hp}")
            print(f"  最大HP: {spirit.max_hp}")
            print(f"  HP比例: {spirit.current_hp / spirit.max_hp * 100:.1f}%")
            
            # 检查HP组件
            if hasattr(spirit, 'components'):
                health_comp = spirit.components.get_component('HealthComponent')
                if health_comp:
                    print(f"  健康组件存在: {health_comp}")
                    print(f"    组件当前HP: {getattr(health_comp, '_current_hp', '未知')}")
                    print(f"    组件最大HP: {getattr(health_comp, '_max_hp', '未知')}")
            
            # 检查属性中的HP
            if hasattr(spirit, 'attributes'):
                attrs = spirit.attributes
                base_hp = getattr(attrs, 'base_hp', 0)
                panel_hp = getattr(attrs, 'hp', 0)
                print(f"  属性基础HP: {base_hp}")
                print(f"  属性面板HP: {panel_hp}")
            
            # 测试伤害
            print(f"  测试伤害:")
            test_damages = [100, 1000, 10000, 100000]
            for test_damage in test_damages:
                # 创建副本进行测试
                test_spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
                initial_hp = test_spirit.current_hp
                actual_damage = test_spirit.take_damage(test_damage)
                final_hp = test_spirit.current_hp
                
                print(f"    测试伤害{test_damage}: 实际伤害{actual_damage}, HP {initial_hp}->{final_hp}")
        
        return True
        
    except Exception as e:
        print(f"❌ HP调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 深入调试真实伤害问题")
    print("="*60)
    
    tests = [
        ("HP数值调试", debug_hp_values),
        ("技能伤害计算", debug_skill_damage_calculation),
        ("伤害动作执行", debug_damage_action_execution),
        ("实际战斗伤害", debug_actual_battle_damage),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
