#!/usr/bin/env python3
"""
测试超杀系统修复效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ultimate_skills_recognition():
    """测试超杀技能识别"""
    print("🔧 测试超杀技能识别...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        print(f"📊 测试精灵: {spirit.name}")
        
        # 检查技能
        ultimate_skills = []
        if hasattr(spirit, 'skills'):
            for skill in spirit.skills:
                if hasattr(skill, 'metadata') and skill.metadata.cast_type == 'ULTIMATE':
                    ultimate_skills.append(skill)
                    print(f"  ✅ 找到超杀技能: {skill.metadata.name}")
        
        print(f"  超杀技能总数: {len(ultimate_skills)}")
        
        # 测试get_skills_by_type方法
        if hasattr(spirit, 'components'):
            from core.components import SkillComponent
            skill_component = spirit.components.get_component(SkillComponent)
            
            if skill_component:
                # 测试不同的参数
                test_params = ['ULTIMATE', 'ultimate', 'Ultimate']
                
                for param in test_params:
                    result = skill_component.get_skills_by_type(param)
                    print(f"  get_skills_by_type('{param}'): {len(result)} 个技能")
                    
                    if len(result) > 0:
                        print(f"    ✅ 修复成功！")
                        return True
        
        if len(ultimate_skills) > 0:
            print(f"  ✅ 超杀技能识别成功")
            return True
        else:
            print(f"  ❌ 没有找到超杀技能")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_ultimate_selection():
    """测试AI超杀技能选择"""
    print("\n🔧 测试AI超杀技能选择...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 设置高气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置 {spirit.name} 气势为: {energy_component._current_energy}")
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 使用AI生成器
        from core.ai import get_action_generator
        ai_generator = get_action_generator()
        
        print(f"  AI生成器类型: {type(ai_generator).__name__}")
        print(f"  当前气势: {spirit.energy}")
        
        # 生成动作
        actions = ai_generator.generate_actions(spirit, battle_state)
        
        print(f"  生成的动作数量: {len(actions)}")
        
        # 检查是否有超杀动作
        ultimate_found = False
        for i, action in enumerate(actions):
            print(f"    动作 {i}: {type(action).__name__}")
            
            if hasattr(action, 'skill'):
                skill = action.skill
                if hasattr(skill, 'metadata'):
                    skill_name = getattr(skill.metadata, 'name', 'Unknown')
                    cast_type = getattr(skill.metadata, 'cast_type', 'Unknown')
                    
                    print(f"      技能: {skill_name} ({cast_type})")
                    
                    if cast_type == 'ULTIMATE':
                        ultimate_found = True
                        print(f"      ✅ 找到超杀技能！")
        
        if ultimate_found:
            print(f"  ✅ AI成功选择超杀技能")
            return True
        else:
            print(f"  ❌ AI没有选择超杀技能")
            return False
        
    except Exception as e:
        print(f"❌ AI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_battle_ultimate_usage():
    """测试战斗中的超杀使用"""
    print("\n🔧 测试战斗中的超杀使用...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 设置高气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置 {spirit1.name} 气势为: {energy_component._current_energy}")
        
        print(f"📊 战斗前状态:")
        print(f"  {spirit1.name}: 气势 {spirit1.energy}")
        print(f"  {spirit2.name}: 气势 {spirit2.energy}")
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=3,
            turn_order_bonus_energy=50
        )
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        # 检查战斗日志中是否有超杀技能
        ultimate_used = False
        
        # 检查精灵的技能使用情况
        if hasattr(engine, 'battle_state'):
            all_spirits = engine.battle_state.get_all_spirits()
            for spirit in all_spirits:
                if hasattr(spirit, 'skills'):
                    for skill in spirit.skills:
                        if hasattr(skill, 'metadata') and skill.metadata.cast_type == 'ULTIMATE':
                            # 检查技能是否被使用（这里简化检查）
                            if spirit.energy < 300:  # 如果气势减少了，可能使用了超杀
                                ultimate_used = True
                                print(f"  ✅ {spirit.name} 可能使用了超杀技能 {skill.metadata.name}")
        
        # 检查HP变化
        print(f"\n📊 战斗后状态:")
        all_spirits = engine.battle_state.get_all_spirits()
        for spirit in all_spirits:
            print(f"  {spirit.name}: HP {spirit.current_hp}/{spirit.max_hp}, 气势 {spirit.energy}")
        
        if ultimate_used:
            print(f"  ✅ 检测到超杀技能使用")
            return True
        else:
            print(f"  ❌ 没有检测到超杀技能使用")
            return False
        
    except Exception as e:
        print(f"❌ 战斗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 超杀系统修复效果测试")
    print("="*60)
    
    tests = [
        ("超杀技能识别", test_ultimate_skills_recognition),
        ("AI超杀技能选择", test_ai_ultimate_selection),
        ("战斗中超杀使用", test_battle_ultimate_usage),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！超杀系统修复成功")
        print("\n📋 修复内容:")
        print("  ✅ 超杀技能正确识别为ULTIMATE类型")
        print("  ✅ get_skills_by_type方法支持ULTIMATE查询")
        print("  ✅ AI生成器能够选择超杀技能")
        print("  ✅ 战斗中能够正确使用超杀技能")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
