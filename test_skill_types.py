#!/usr/bin/env python3
"""
测试技能类型
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_skill_types():
    """测试技能类型"""
    print("🔧 测试技能类型...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        fuyao_spirit = None
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            print("❌ 未找到伏妖精灵")
            return False
        
        # 检查所有技能的类型
        print(f"\n📋 检查所有技能类型:")
        
        for i, skill in enumerate(fuyao_spirit.skills):
            skill_name = getattr(skill.metadata, 'name', f'技能{i+1}')
            cast_type = getattr(skill.metadata, 'cast_type', 'UNKNOWN')
            components_count = len(skill.components)
            
            print(f"  技能{i+1}: {skill_name}")
            print(f"    cast_type: {cast_type}")
            print(f"    组件数量: {components_count}")
            
            # 检查是否被识别为被动技能
            is_passive = cast_type == 'PASSIVE'
            print(f"    是否被动技能: {is_passive}")
            
            if is_passive and components_count > 0:
                print(f"    ⚠️ 被动技能不应该有组件！")
            elif not is_passive and components_count > 0:
                print(f"    ✅ 非被动技能有组件是正常的")
            elif is_passive and components_count == 0:
                print(f"    ✅ 被动技能没有组件是正确的")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 技能类型测试")
    print("="*50)
    
    result = test_skill_types()
    
    print("\n" + "="*50)
    if result:
        print("✅ 技能类型测试成功")
    else:
        print("❌ 技能类型测试失败")

if __name__ == "__main__":
    main()
