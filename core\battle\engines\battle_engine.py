"""
重构后的战斗引擎

将 Battle9v9 拆分为多个职责明确的类，提高可维护性。
"""
from __future__ import annotations
from typing import Dict, Any, Optional, cast

from ..models import BattleState
from ...interfaces import IBattleEntity
from ...turn_order import FixedGridTurnOrderStrategy
# New modular executor and phases
# 注册式执行器已移除，只保留阶段化执行器

from ..execution import UnifiedActionExecutor
from ..phases import (
    IBattlePhase,
    BattleInitializationPhase as PhaseInit,
    RoundStartPhase as PhaseRoundStart,
    RoundEndPhase as PhaseRoundEnd,
    BattleEndPhase as PhaseBattleEnd,
)

from ..monitoring import BattleRecorder, BattleStatsTracker
# 新增：引入胜负判定策略接口与默认实现
from ..conditions import IBattleEndCondition, KnockoutCondition
# 向后兼容：旧代码可能直接引用 BattleConditionChecker
BattleConditionChecker = KnockoutCondition  # type: ignore[assignment]

# Handler模块会通过executor包的__init__.py自动导入




class BattleConditionChecker:
    """战斗条件检查器 - 确定战斗何时结束以及谁获胜。
    
    此类封装了检查战斗结束条件的逻辑，例如当一个队伍的所有精灵都被击败时。
    它提供了与主战斗引擎的清晰职责分离。
    """
    
    def check_battle_end(self, battle_state: BattleState) -> Optional[int]:
        """检查战斗是否已结束并确定获胜者。
        
        检查两个队伍是否有任何一方没有存活的精灵。
        当一个或两个队伍完全被击败时战斗结束。
        
        参数：
            battle_state: 包含所有精灵的当前战斗状态
            
        返回：
            如果战斗结束则返回获胜队伍 ID（0 或 1），如果战斗继续则返回 None
            如果平局（两队同时被击败）则返回 None
        """
        # 修复：使用正确的方法名
        team0_alive = len(battle_state.get_living_spirits(0)) > 0
        team1_alive = len(battle_state.get_living_spirits(1)) > 0
        
        if not team0_alive and not team1_alive:
            return None  # 平局
        elif not team0_alive:
            return 1  # 队伍1获胜
        elif not team1_alive:
            return 0  # 队伍0获胜
        else:
            return None  # 战斗继续


class RefactoredBattleEngine:
    """使用模块化阶段和执行器架构的重构战斗引擎。
    
    此引擎通过不同的阶段（初始化、回合开始、动作生成、回合结束、战斗结束）
    处理战斗，并使用基于注册的动作执行器来处理不同类型的战斗动作。
    
    主要特性：
    - 模块化设计：每个阶段处理特定的战斗逻辑
    - 基于注册的动作：动作由注册的处理器处理
    - 战斗记录：记录所有动作和状态变化
    - 灵活的回合顺序：支持不同的回合顺序策略
    - 错误恢复：处理器中的异常不会导致战斗崩溃
    """
    
    def __init__(self, formation1, formation2, turn_order_strategy=None, *,
                 condition_strategy: Optional[IBattleEndCondition] = None,
                 executor_type: str = "phased",
                 unified_event_manager=None,
                 use_spirit_turns: bool = True,  # 🎯 小回合系统开关
                 spirit_turn_config: Optional[Any] = None,
                 use_refactored_coordination: bool = True,  # 🆕 强制使用重构协调器
                 battle_config: Optional[Any] = None,  # 🆕 战斗配置对象
                 round_limit: int = 10,  # 🆕 回合限制参数
                 turn_order_bonus_energy: int = 50):  # 🆕 顺位加气数量（默认逻辑）
        """使用两个队伍和配置初始化战斗引擎。

        设置战斗状态、阶段、动作执行器和记录系统。
        初始化后引擎就可以运行战斗了。

        参数：
            formation1: 队伍1的阵型（精灵和位置）
            formation2: 队伍2的阵型（精灵和位置）
            turn_order_strategy: 确定动作顺序的策略（默认：FixedGridTurnOrderStrategy）
            condition_strategy: 胜负判定策略（默认：KnockoutCondition）
            executor_type: 执行器类型（只支持 "phased"）
            unified_event_manager: 统一事件管理器实例（可选）
            use_spirit_turns: 是否使用小回合系统（默认：True）
            spirit_turn_config: 小回合系统配置（可选）
            use_refactored_coordination: 是否使用重构后的协调器（默认：True）
            battle_config: 战斗配置对象（可选）
            round_limit: 最大回合数限制（默认：10）
        """

        # 注意：配置系统已简化，直接使用默认配置
        # 强制使用重构协调器
        self.use_refactored_coordination = True
        # 保存回合限制
        self.round_limit = round_limit
        # 🔧 使用统一管理器：优先从系统管理器获取事件管理器
        self.event_manager = self._get_or_create_event_manager()

        # 创建战斗状态（传入事件管理器）
        self.battle_state = self._create_battle_state(formation1, formation2, unified_event_manager)
        
        # 创建/注入胜负判定策略
        # 若未提供，自带经典 KO 制逻辑
        self.condition_checker = cast(BattleConditionChecker, (
            condition_strategy or KnockoutCondition()
        ))
        
        # 创建战斗记录器
        self.recorder = BattleRecorder()

        # 🔧 使用统一管理器：从系统管理器获取或创建统计跟踪器
        self.stats_tracker = self._get_or_create_stats_tracker()
        
        # 为StreamingEngine兼容性添加battle_log
        self.battle_log = []

        # 🆕 设置回合顺序策略，默认包含顺位加气功能
        self.turn_order_bonus_energy = turn_order_bonus_energy
        self.turn_order_strategy = self._setup_turn_order_strategy(turn_order_strategy)

        # 🎯 不再需要ActionGenerationPhase，完全使用重构协调器
        from ..logging import battle_logger
        battle_logger.info("🚀 使用纯重构协调器系统")

        # 创建所有阶段（不包含action_generation）
        self.phases = self._create_battle_phases()
        
        # 创建动作执行器
        from ..logging import battle_logger
        
        # 只使用阶段化动作执行器
        if executor_type == "phased":
            battle_logger.info("使用阶段化动作执行器")
        else:
            battle_logger.info(f"未知的执行器类型 '{executor_type}'，使用默认的阶段化动作执行器")

        self.action_executor = UnifiedActionExecutor(
            self.battle_state,
            self.condition_checker,
            self.battle_log
        )
        
        # 设置统计跟踪器
        self.action_executor.stats_tracker = self.stats_tracker  # type: ignore[attr-defined]
        
        # 注入记录器
        self.action_executor.recorder = self.recorder  # type: ignore[assignment]

        # 注意：重构协调器已被移除，使用直接的动作执行逻辑
        self.refactored_coordinator = None

        from ..logging import battle_logger
        battle_logger.info("🚀 使用直接动作执行系统（无协调器）")

        # 初始化战斗
        self._initialize_battle()
    
    def _initialize_battle(self) -> None:
        """初始化战斗。"""
        # 记录初始状态
        self.recorder.capture_spirit_states(self.battle_state, "start")
        
        # 执行初始化阶段
        init_actions = self.phases["init"].execute(self.battle_state)
        self.action_executor.execute_actions(init_actions)
    
    def execute_round(self) -> Dict[str, Any]:
        """
        执行一个完整的战斗回合。

        Returns:
            包含回合执行结果的字典。
        """
        # 🆕 性能监控开始
        import time
        round_start_time = time.time()

        # 记录回合开始
        from ..logging import battle_logger
        system_type = "refactored" if self.use_refactored_coordination else "legacy"
        battle_logger.info(f"==== 开始执行第 {self.battle_state.round_num + 1} 回合 ({system_type}) ====")

        try:
            # 1. 回合开始阶段
            # ---- 新增：创建回合记录 ----
            self.recorder.start_round(self.battle_state.round_num + 1)
            round_start_actions = self.phases["round_start"].execute(self.battle_state)
            self.action_executor.execute_actions(round_start_actions)

            # 记录回合开始状态
            self.recorder.capture_spirit_states(self.battle_state, "start")

            # 2. 动作生成和执行阶段 - 纯重构模式
            battle_logger.info("使用重构协调器生成和执行动作...")
            action_queue = self.turn_order_strategy.create_action_queue(self.battle_state)

            total_actions = 0
            for spirit in action_queue:
                if spirit.is_alive:
                    # 直接使用精灵的动作生成逻辑，不使用协调器
                    try:
                        if hasattr(spirit, 'generate_actions'):
                            actions = spirit.generate_actions(self.battle_state)
                            if actions:
                                self.action_executor.execute_actions(actions)
                                total_actions += len(actions)
                                battle_logger.debug(f"精灵 {spirit.name} 生成了 {len(actions)} 个动作")
                        else:
                            battle_logger.warning(f"精灵 {spirit.name} 没有 generate_actions 方法")
                    except Exception as e:
                        battle_logger.error(f"精灵 {spirit.name} 动作生成失败: {e}")

            battle_logger.info(f"直接动作执行完成，生成了 {total_actions} 个动作")

            # 处理额外回合，并在每个额外回合后检查战斗结束
            for spirit in self.battle_state.get_all_spirits():
                if spirit.is_alive:
                    self._handle_extra_turns(spirit)
                    # 在额外回合后检查战斗是否结束
                    winner = self.condition_checker.check_battle_end(self.battle_state)
                    if winner is not None:
                        self.battle_state.winner = winner
                        battle_logger.info(f"战斗结束，获胜方: {winner}")
                        end_actions = self.phases["end"].execute(self.battle_state)
                        self.action_executor.execute_actions(end_actions)

                        # 记录回合执行时间
                        round_end_time = time.time()
                        execution_time = round_end_time - round_start_time
                        battle_logger.debug(f"回合执行耗时: {execution_time:.3f}s")

                        return {
                            "type": "battle_end",
                            "winner": winner,
                            "round_num": self.battle_state.round_num,
                            "state": self.battle_state.to_dict()
                        }
        
            # 最终检查战斗是否结束
            winner = self.condition_checker.check_battle_end(self.battle_state)
            if winner is not None:
                self.battle_state.winner = winner
                battle_logger.info(f"战斗结束，获胜方: {winner}")
                # 执行战斗结束阶段
                end_actions = self.phases["end"].execute(self.battle_state)
                self.action_executor.execute_actions(end_actions)

                # 记录回合执行时间
                round_end_time = time.time()
                execution_time = round_end_time - round_start_time
                battle_logger.debug(f"回合执行耗时: {execution_time:.3f}s")

                return {
                    "type": "battle_end",
                    "winner": winner,
                    "round_num": self.battle_state.round_num,
                    "state": self.battle_state.to_dict()
                }

            # 3. 回合结束阶段
            battle_logger.info("执行回合结束阶段...")
            round_end_actions = self.phases["round_end"].execute(self.battle_state)
            self.action_executor.execute_actions(round_end_actions)

            # 记录回合结束状态
            self.recorder.capture_spirit_states(self.battle_state, "end")
            battle_logger.info(f"==== 第 {self.battle_state.round_num} 回合执行完成 ====")

            # 4. 返回回合数据
            result = {
                "type": "round_end",
                "round_num": self.battle_state.round_num,
                "state": self.battle_state.to_dict()
            }

            # 🆕 记录成功的性能数据
            round_end_time = time.time()
            execution_time = round_end_time - round_start_time

            battle_logger.debug(f"回合执行耗时: {execution_time:.3f}s")

            if execution_time > 0.1:  # 如果执行时间超过100ms，记录警告
                battle_logger.warning(f"回合执行时间较长: {execution_time:.3f}s ({system_type})")

            return result

        except Exception as e:
            # 🆕 记录失败的性能数据
            round_end_time = time.time()
            execution_time = round_end_time - round_start_time

            battle_logger.debug(f"回合执行耗时: {execution_time:.3f}s")
            battle_logger.error(f"回合执行失败: {e}")
            raise

    def get_battle_result(self) -> Dict[str, Any]:
        """获取最终战斗结果。"""
        result = self.recorder.get_record().to_dict()
        result["statistics"] = self.stats_tracker.data
        return result

    def _setup_turn_order_strategy(self, turn_order_strategy=None):
        """设置回合顺序策略，默认包含顺位加气功能"""
        from ...turn_order import FixedGridTurnOrderStrategy, EnhancedTurnOrderStrategy, TurnOrderBonus

        # 如果没有提供策略，使用默认策略
        if turn_order_strategy is None:
            base_strategy = FixedGridTurnOrderStrategy()
        else:
            base_strategy = turn_order_strategy

        # 顺位加气是默认逻辑，总是包装为增强策略
        # 检查是否已经是增强策略
        if isinstance(base_strategy, EnhancedTurnOrderStrategy):
            # 如果已经是增强策略，更新奖励数量
            base_strategy.bonus_manager.energy_bonus = self.turn_order_bonus_energy
            return base_strategy
        else:
            # 创建奖励管理器并包装策略
            bonus_manager = TurnOrderBonus(energy_bonus=self.turn_order_bonus_energy)
            return EnhancedTurnOrderStrategy(base_strategy, bonus_manager)

    def run_battle(self):
        """运行完整战斗流程 - 修复缺失的方法"""
        from ...logging import get_logger
        battle_logger = get_logger("battle.engine")
        
        battle_logger.info("开始战斗流程")
        
        while self.battle_state.winner is None and self.battle_state.round_num < self.round_limit:
            try:
                # 执行回合（回合数在RoundStartPhase中递增）
                round_result = self.execute_round()
                yield round_result
                
                # 检查是否战斗结束
                if round_result.get("type") == "battle_end":
                    battle_logger.info(f"战斗结束，获胜方: {round_result.get('winner')}")
                    break
                    
            except Exception as e:
                battle_logger.error(f"战斗回合执行失败: {e}")
                break
        
        battle_logger.info(f"战斗完成，总回合数: {self.battle_state.round_num}")
        return self.get_battle_result()
    
    def is_battle_over(self) -> bool:
        """检查战斗是否结束"""
        return self.battle_state.winner is not None

    def get_current_turn_queue(self) -> List[IBattleEntity]:
        """获取当前回合的精灵行动队列"""
        return self.turn_order_strategy.create_action_queue(self.battle_state)

    def execute_next_spirit_turn(self) -> Dict[str, Any]:
        """执行下一只精灵的回合

        Returns:
            Dict包含执行结果信息
        """
        from ..logging import battle_logger

        try:
            # 获取当前回合队列
            action_queue = self.get_current_turn_queue()

            if not action_queue:
                return {
                    "type": "no_spirits",
                    "message": "没有存活的精灵可以行动"
                }

            # 获取或初始化当前精灵索引
            if not hasattr(self, '_current_spirit_index'):
                self._current_spirit_index = 0
                # 如果是新回合，执行回合开始阶段
                if not hasattr(self, '_round_started'):
                    self.recorder.start_round(self.battle_state.round_num + 1)
                    round_start_actions = self.phases["round_start"].execute(self.battle_state)
                    self.action_executor.execute_actions(round_start_actions)
                    self.recorder.capture_spirit_states(self.battle_state, "start")
                    self._round_started = True

            # 检查是否需要开始新回合
            if self._current_spirit_index >= len(action_queue):
                # 当前回合结束，执行回合结束阶段
                battle_logger.info("当前回合所有精灵已行动完毕，执行回合结束阶段")

                # 处理额外回合
                for spirit in self.battle_state.get_all_spirits():
                    if spirit.is_alive:
                        self._handle_extra_turns(spirit)
                        # 检查战斗是否结束
                        winner = self.condition_checker.check_battle_end(self.battle_state)
                        if winner is not None:
                            self.battle_state.winner = winner
                            battle_logger.info(f"战斗结束，获胜方: {winner}")
                            end_actions = self.phases["end"].execute(self.battle_state)
                            self.action_executor.execute_actions(end_actions)
                            return {
                                "type": "battle_end",
                                "winner": winner,
                                "round_num": self.battle_state.round_num,
                                "message": f"战斗结束，获胜方: 队伍{winner}"
                            }

                # 执行回合结束阶段
                round_end_actions = self.phases["round_end"].execute(self.battle_state)
                self.action_executor.execute_actions(round_end_actions)

                # 检查战斗结束条件
                winner = self.condition_checker.check_battle_end(self.battle_state)
                if winner is not None:
                    self.battle_state.winner = winner
                    battle_logger.info(f"战斗结束，获胜方: {winner}")
                    end_actions = self.phases["end"].execute(self.battle_state)
                    self.action_executor.execute_actions(end_actions)
                    return {
                        "type": "battle_end",
                        "winner": winner,
                        "round_num": self.battle_state.round_num,
                        "message": f"战斗结束，获胜方: 队伍{winner}"
                    }

                # 记录回合结束状态
                self.recorder.capture_spirit_states(self.battle_state, "end")

                # 重置为下一回合
                self._current_spirit_index = 0
                self._round_started = False

                return {
                    "type": "round_end",
                    "round_num": self.battle_state.round_num,
                    "message": f"第{self.battle_state.round_num}回合结束"
                }

            # 执行当前精灵的回合
            current_spirit = action_queue[self._current_spirit_index]

            if not current_spirit.is_alive:
                # 精灵已死亡，跳过
                self._current_spirit_index += 1
                return {
                    "type": "spirit_skipped",
                    "spirit_name": getattr(current_spirit, 'name', 'Unknown'),
                    "reason": "精灵已死亡",
                    "message": f"{getattr(current_spirit, 'name', 'Unknown')} 已死亡，跳过回合"
                }

            battle_logger.info(f"执行 {current_spirit.name} 的回合")

            # 记录动作执行前的状态
            pre_action_hp = current_spirit.current_hp
            pre_action_energy = current_spirit.energy

            # 🔧 修复伤害显示：记录所有精灵的HP状态
            pre_action_all_hp = {}
            for spirit in self.battle_state.get_all_spirits():
                pre_action_all_hp[spirit.name] = spirit.current_hp

            # 执行精灵动作
            actions_generated = 0
            try:
                if hasattr(current_spirit, 'generate_actions'):
                    actions = current_spirit.generate_actions(self.battle_state)
                    if actions:
                        # 🔧 动态更新：逐个执行动作并记录状态变化
                        for i, action in enumerate(actions):
                            self.action_executor.execute_actions([action])
                            battle_logger.debug(f"精灵 {current_spirit.name} 执行动作 {i+1}/{len(actions)}: {type(action).__name__}")

                        actions_generated = len(actions)
                        battle_logger.debug(f"精灵 {current_spirit.name} 生成了 {len(actions)} 个动作")
                else:
                    battle_logger.warning(f"精灵 {current_spirit.name} 没有 generate_actions 方法")
            except Exception as e:
                battle_logger.error(f"精灵 {current_spirit.name} 动作生成失败: {e}")
                actions_generated = 0

            # 记录动作执行后的状态变化
            post_action_hp = current_spirit.current_hp
            post_action_energy = current_spirit.energy
            hp_change = post_action_hp - pre_action_hp
            energy_change = post_action_energy - pre_action_energy

            # 🔧 修复伤害显示：计算所有精灵的HP变化
            all_hp_changes = {}
            total_damage_dealt = 0
            total_healing_done = 0

            for spirit in self.battle_state.get_all_spirits():
                pre_hp = pre_action_all_hp.get(spirit.name, spirit.current_hp)
                current_hp = spirit.current_hp
                hp_diff = current_hp - pre_hp

                if hp_diff != 0:
                    all_hp_changes[spirit.name] = hp_diff

                    if hp_diff < 0:  # 受到伤害
                        total_damage_dealt += abs(hp_diff)
                    elif hp_diff > 0:  # 获得治疗
                        total_healing_done += hp_diff

            # 移动到下一只精灵
            self._current_spirit_index += 1

            return {
                "type": "spirit_turn",
                "spirit_name": current_spirit.name,
                "actions_generated": actions_generated,
                "current_index": self._current_spirit_index - 1,
                "total_spirits": len(action_queue),
                "message": f"{current_spirit.name} 执行了回合，生成了 {actions_generated} 个动作",
                # 🔧 动态更新：添加详细的状态变化信息
                "spirit_state": {
                    "hp": current_spirit.current_hp,
                    "max_hp": current_spirit.max_hp,
                    "energy": current_spirit.energy,
                    "max_energy": getattr(current_spirit, 'max_energy', 300),
                    "is_alive": current_spirit.is_alive,
                    "hp_change": hp_change,
                    "energy_change": energy_change
                },
                # 🔧 修复伤害显示：添加详细的伤害信息
                "damage_info": {
                    "total_damage_dealt": total_damage_dealt,
                    "total_healing_done": total_healing_done,
                    "all_hp_changes": all_hp_changes,
                    "damage_summary": f"造成 {total_damage_dealt:.0f} 点伤害" if total_damage_dealt > 0 else "无伤害"
                },
                "battle_state_snapshot": {
                    "round_num": self.battle_state.round_num,
                    "all_spirits_state": [
                        {
                            "name": spirit.name,
                            "hp": spirit.current_hp,
                            "max_hp": spirit.max_hp,
                            "energy": getattr(spirit, 'energy', 0),
                            "is_alive": spirit.is_alive,
                            "team": spirit.team
                        }
                        for spirit in self.battle_state.get_all_spirits()
                    ]
                }
            }

        except Exception as e:
            battle_logger.error(f"执行精灵回合失败: {e}")
            return {
                "type": "error",
                "message": f"执行精灵回合失败: {e}"
            }

    def _get_or_create_event_manager(self):
        """从统一系统管理器获取事件管理器"""
        try:
            from ...system_manager import get_system
            event_manager = get_system('event')
            if event_manager is None:
                raise RuntimeError("统一管理器中的事件系统不可用，无法创建战斗引擎")
            return event_manager
        except ImportError:
            # 如果系统管理器不可用，创建一个简单的事件管理器
            from ...event.unified_manager import unified_event_manager
            return unified_event_manager

    def _get_or_create_stats_tracker(self):
        """从统一系统管理器获取统计跟踪器"""
        try:
            from ...system_manager import get_system
            stats_tracker = get_system('statistics')
            if stats_tracker is None:
                raise RuntimeError("统一管理器中的统计系统不可用，无法创建战斗引擎")
            return stats_tracker
        except ImportError:
            # 如果系统管理器不可用，返回一个简单的统计跟踪器
            from ...monitoring.statistics import StatisticsTracker
            return StatisticsTracker()

    def _create_battle_state(self, formation1, formation2, unified_event_manager=None) -> BattleState:
        """创建战斗状态对象"""
        return BattleState(
            formation1,
            formation2,
            unified_event_manager=unified_event_manager
        )
        
    def _create_battle_phases(self) -> Dict[str, IBattlePhase]:
        """创建所有战斗阶段（不包含action_generation，使用重构协调器）"""
        phases_dict = {
            "init": PhaseInit(self.turn_order_strategy),
            "round_start": PhaseRoundStart(),
            "round_end": PhaseRoundEnd(),
            "end": PhaseBattleEnd(),
        }
        return phases_dict

    def _handle_extra_turns(self, spirit: IBattleEntity) -> None:
        """处理精灵的额外回合"""
        if not hasattr(spirit, 'extra_turns') or spirit.extra_turns <= 0:
            return

        from ..logging import battle_logger

        # 🆕 使用重构协调器处理额外回合（如果可用）
        if self.use_refactored_coordination and self.refactored_coordinator is not None:
            self._handle_extra_turns_refactored(spirit)
            return

        # 🔄 传统额外回合处理
        while hasattr(spirit, 'extra_turns') and spirit.extra_turns > 0 and spirit.is_alive:
            # 减少额外回合计数
            spirit.extra_turns -= 1

            battle_logger.info(f"{spirit.name} 执行额外回合（传统模式）")

            # 生成并执行额外回合的动作
            try:
                # 使用传统的动作生成（避免类型错误）
                if hasattr(self, 'phases') and 'action_generation' in self.phases:
                    extra_actions = self.phases['action_generation'].execute(self.battle_state)
                    if extra_actions:
                        self.action_executor.execute_actions(extra_actions)
                else:
                    battle_logger.warning(f"无法为 {spirit.name} 生成额外回合动作：缺少动作生成阶段")
            except Exception as e:
                battle_logger.error(f"处理 {spirit.name} 额外回合时出错: {e}")

            # 检查战斗是否结束
            if self.battle_state.winner is not None:
                break

    def _handle_extra_turns_refactored(self, spirit: IBattleEntity) -> None:
        """处理额外回合（直接执行模式）"""
        try:
            from ...logging import battle_logger
        except ImportError:
            import logging
            battle_logger = logging.getLogger(__name__)

        while hasattr(spirit, 'extra_turns') and spirit.extra_turns > 0 and spirit.is_alive:
            # 减少额外回合计数
            spirit.extra_turns -= 1

            battle_logger.info(f"{spirit.name} 执行额外回合（直接模式）")

            # 直接执行精灵动作，不使用协调器
            try:
                if hasattr(spirit, 'generate_actions'):
                    actions = spirit.generate_actions(self.battle_state)
                    if actions:
                        self.action_executor.execute_actions(actions)
                        battle_logger.debug(f"{spirit.name} 额外回合生成了 {len(actions)} 个动作")
                else:
                    battle_logger.warning(f"精灵 {spirit.name} 没有 generate_actions 方法")
            except Exception as e:
                battle_logger.error(f"{spirit.name} 额外回合执行失败: {e}")

            # 检查战斗是否结束
            if self.battle_state.winner is not None:
                break


__all__ = [
    'IBattlePhase',
    'BattleConditionChecker',
    'RefactoredBattleEngine'
]