#!/usr/bin/env python3
"""
调试被动技能问题

检查第一回合被动技能应用时发生了什么
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_passive_skills():
    """调试被动技能"""
    print("🔧 调试被动技能...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        
        spirit1_name = "神曜圣谕·女帝"
        spirit2_name = "神曜虚无·伏妖"
        
        spirit1 = spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
        
        print(f"✅ 精灵创建成功")
        print(f"  - 精灵1: {spirit1.name}, HP={spirit1.current_hp}")
        print(f"  - 精灵2: {spirit2.name}, HP={spirit2.current_hp}")
        
        # 检查精灵的被动技能
        print(f"\n📊 检查被动技能:")
        
        for i, spirit in enumerate([spirit1, spirit2], 1):
            print(f"\n精灵{i}: {spirit.name}")
            print(f"  - 技能数量: {len(spirit.skills)}")
            
            passive_skills = []
            for skill in spirit.skills:
                cast_type = getattr(skill.metadata, 'cast_type', None)
                if cast_type == 'PASSIVE':
                    passive_skills.append(skill)
                    print(f"    被动技能: {skill.metadata.name}")
                    print(f"      - 描述: {skill.metadata.description}")
                    print(f"      - 组件数量: {len(skill.components)}")
                    
                    # 检查技能组件
                    for j, component in enumerate(skill.components):
                        print(f"        组件{j+1}: {component.__class__.__name__}")
            
            print(f"  - 被动技能总数: {len(passive_skills)}")
            
            # 检查效果管理器
            if hasattr(spirit, 'effect_manager'):
                effects = spirit.effect_manager.effects
                print(f"  - 当前效果数量: {len(effects)}")
                for effect_id, effect in effects.items():
                    print(f"    效果: {getattr(effect, 'name', 'Unknown')} (ID: {effect_id})")
        
        # 手动模拟被动技能应用
        print(f"\n🎯 手动模拟被动技能应用...")
        
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 模拟RoundStartPhase的被动技能应用逻辑
        from core.skill.skills import Skill
        
        passive_skills = []
        for spirit in battle_state.get_all_spirits():
            if not spirit.is_alive:
                continue
            
            for skill in spirit.skills:
                if isinstance(skill, Skill):
                    skill.owner = spirit
                    cast_type = getattr(skill.metadata, 'cast_type', None)
                    if cast_type == 'PASSIVE':
                        passive_skills.append((spirit, skill))
        
        print(f"  - 找到 {len(passive_skills)} 个被动技能")
        
        # 逐个应用被动技能并检查精灵状态
        for i, (spirit, skill) in enumerate(passive_skills):
            print(f"\n  应用被动技能 {i+1}: {spirit.name} 的 {skill.metadata.name}")
            
            # 记录应用前状态
            hp_before = {s.name: s.current_hp for s in battle_state.get_all_spirits()}
            print(f"    应用前HP: {hp_before}")
            
            try:
                # 应用被动技能
                skill_actions = skill.cast(battle_state)
                print(f"    生成动作数量: {len(skill_actions) if skill_actions else 0}")
                
                # 执行动作
                if skill_actions:
                    from core.battle.executors.phased_executor import PhasedActionExecutor
                    executor = PhasedActionExecutor()
                    executor.execute_actions(skill_actions)
                
                # 记录应用后状态
                hp_after = {s.name: s.current_hp for s in battle_state.get_all_spirits()}
                print(f"    应用后HP: {hp_after}")
                
                # 检查HP变化
                for name in hp_before:
                    if hp_before[name] != hp_after[name]:
                        change = hp_after[name] - hp_before[name]
                        print(f"    ⚠️ {name} HP变化: {hp_before[name]} -> {hp_after[name]} (变化: {change})")
                        
                        if hp_after[name] <= 0:
                            print(f"    💀 {name} 死亡！")
                            return False
                
            except Exception as e:
                print(f"    ❌ 被动技能应用失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        print(f"\n✅ 所有被动技能应用完成，精灵状态正常")
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 被动技能调试")
    print("="*60)
    
    success = debug_passive_skills()
    
    print("\n" + "="*60)
    print("📊 调试结果")
    print("="*60)
    
    if success:
        print("✅ 被动技能正常")
        print("问题可能出在其他地方")
    else:
        print("❌ 发现被动技能问题")
        print("某个被动技能导致了精灵死亡")

if __name__ == "__main__":
    main()
