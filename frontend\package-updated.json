{"name": "spirit-battle-simulator", "version": "2.0.0", "description": "现代化回合制战斗模拟器前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.8.0", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.0", "socket.io-client": "^4.7.4", "three": "^0.168.0", "echarts": "^5.5.0", "vue-echarts": "^7.0.0", "gsap": "^3.12.2", "lodash-es": "^4.17.21", "@vueuse/core": "^11.0.0", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^22.0.0", "@types/three": "^0.168.0", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-typescript": "^13.0.0", "autoprefixer": "^10.4.20", "eslint": "^9.0.0", "eslint-plugin-vue": "^9.28.0", "postcss": "^8.4.47", "sass": "^1.79.0", "tailwindcss": "^3.4.0", "typescript": "~5.6.0", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.0", "vite": "^5.4.0", "vue-tsc": "^2.1.0"}}