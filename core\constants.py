"""Project-wide enumerations and magic constants.

Having a single place for enums / constants helps avoid "magic strings" scattered
in the codebase and eases maintenance.
"""
from __future__ import annotations

from enum import Enum, auto


class DamageType(str, Enum):
    PHYSICAL = "PHYSICAL"
    MAGICAL = "MAGICAL"
    TRUE = "TRUE"


class CastType(str, Enum):
    ACTIVE = "ACTIVE"
    ULTIMATE = "ULTIMATE"
    PASSIVE = "PASSIVE"


class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class BattleEndReason(Enum):
    TEAM_DEFEAT = auto()
    ROUND_LIMIT = auto()
    DRAW = auto() 