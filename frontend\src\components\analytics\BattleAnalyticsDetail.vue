<template>
  <div class="battle-analytics-detail">
    <!-- 战斗基本信息 -->
    <div class="battle-header bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white rounded-lg mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-bold">战斗 {{ battleRecord.id }}</h2>
          <p class="text-white/80">{{ formatDate(battleRecord.date) }}</p>
        </div>
        <div class="text-right">
          <el-tag :type="battleRecord.result === 'win' ? 'success' : 'danger'" size="large">
            {{ battleRecord.result === 'win' ? '胜利' : '失败' }}
          </el-tag>
          <div class="text-white/80 text-sm mt-2">
            {{ battleRecord.duration }}s | {{ battleRecord.rounds }}回合
          </div>
        </div>
      </div>
    </div>

    <!-- 战斗概览 -->
    <div class="battle-overview grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-red-400">{{ battleRecord.team1Score }}</div>
        <div class="text-slate-400 text-xs">队伍1得分</div>
      </div>
      
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-blue-400">{{ battleRecord.team2Score }}</div>
        <div class="text-slate-400 text-xs">队伍2得分</div>
      </div>
      
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-orange-400">{{ totalDamage }}</div>
        <div class="text-slate-400 text-xs">总伤害</div>
      </div>
      
      <div class="stat-card bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
        <div class="text-xl font-bold text-green-400">{{ totalHealing }}</div>
        <div class="text-slate-400 text-xs">总治疗</div>
      </div>
    </div>

    <!-- 详细分析选项卡 -->
    <el-tabs v-model="activeTab" class="analytics-tabs">
      <!-- 战斗流程 -->
      <el-tab-pane label="战斗流程" name="timeline">
        <div class="battle-timeline">
          <div class="timeline-header mb-4 flex items-center justify-between">
            <h3 class="text-white font-medium">战斗时间轴</h3>
            <div class="timeline-controls">
              <el-button size="small" @click="playTimeline">
                <el-icon><VideoPlay /></el-icon>
                播放
              </el-button>
              <el-slider
                v-model="timelinePosition"
                :max="battleEvents.length - 1"
                :step="1"
                style="width: 200px; margin-left: 12px;"
              />
            </div>
          </div>
          
          <div class="timeline-content bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <div class="events-list space-y-3 max-h-96 overflow-auto">
              <div
                v-for="(event, index) in visibleEvents"
                :key="index"
                class="event-item p-3 rounded border-l-4"
                :class="getEventClass(event.type)"
              >
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <el-icon :class="getEventIconClass(event.type)">
                      <component :is="getEventIcon(event.type)" />
                    </el-icon>
                    <span class="text-white font-medium">{{ event.title }}</span>
                  </div>
                  <span class="text-slate-400 text-sm">回合 {{ event.round }}</span>
                </div>
                <p class="text-slate-300 text-sm">{{ event.description }}</p>
                <div v-if="event.details" class="event-details mt-2 text-xs text-slate-400">
                  {{ event.details }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 伤害统计 -->
      <el-tab-pane label="伤害统计" name="damage">
        <div class="damage-analysis">
          <div class="damage-charts grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 伤害分布 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">伤害分布</h3>
              <div ref="damageDistributionChart" class="chart-container h-48"></div>
            </div>
            
            <!-- 伤害趋势 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">回合伤害趋势</h3>
              <div ref="damageTrendChart" class="chart-container h-48"></div>
            </div>
          </div>
          
          <!-- 精灵伤害排行 -->
          <div class="damage-ranking bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <h3 class="text-white font-medium mb-4">精灵伤害排行</h3>
            <div class="ranking-list space-y-3">
              <div
                v-for="(spirit, index) in damageRanking"
                :key="spirit.name"
                class="ranking-item flex items-center justify-between p-3 bg-slate-700/50 rounded"
              >
                <div class="flex items-center space-x-3">
                  <div class="rank-number w-8 h-8 rounded-full bg-gradient-to-br from-yellow-500 to-orange-500 flex items-center justify-center text-white font-bold text-sm">
                    {{ index + 1 }}
                  </div>
                  <div class="spirit-avatar w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
                    {{ spirit.name.charAt(0) }}
                  </div>
                  <div>
                    <div class="text-white font-medium">{{ spirit.name }}</div>
                    <div class="text-slate-400 text-sm">队伍 {{ spirit.team }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-red-400 font-bold text-lg">{{ spirit.damage }}</div>
                  <div class="text-slate-400 text-sm">{{ spirit.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 技能分析 -->
      <el-tab-pane label="技能分析" name="skills">
        <div class="skills-analysis">
          <div class="skills-overview grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- 技能使用次数 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">技能使用频率</h3>
              <div ref="skillUsageChart" class="chart-container h-48"></div>
            </div>
            
            <!-- 技能成功率 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">技能成功率</h3>
              <div ref="skillSuccessChart" class="chart-container h-48"></div>
            </div>
            
            <!-- 技能伤害贡献 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">伤害贡献</h3>
              <div ref="skillDamageChart" class="chart-container h-48"></div>
            </div>
          </div>
          
          <!-- 技能详细统计 -->
          <div class="skills-table bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <h3 class="text-white font-medium mb-4">技能详细统计</h3>
            <el-table :data="skillsStats" stripe style="width: 100%">
              <el-table-column prop="name" label="技能名称" width="200" />
              <el-table-column prop="caster" label="施法者" width="150" />
              <el-table-column prop="usageCount" label="使用次数" width="100" />
              <el-table-column prop="successCount" label="成功次数" width="100" />
              <el-table-column label="成功率" width="100">
                <template #default="{ row }">
                  <span :class="getSuccessRateColor(row.successRate)">{{ row.successRate }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="totalDamage" label="总伤害" width="120" />
              <el-table-column prop="avgDamage" label="平均伤害" width="120" />
              <el-table-column prop="criticalHits" label="暴击次数" width="100" />
            </el-table>
          </div>
        </div>
      </el-tab-pane>

      <!-- 效果分析 -->
      <el-tab-pane label="效果分析" name="effects">
        <div class="effects-analysis">
          <div class="effects-overview grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 效果类型分布 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">效果类型分布</h3>
              <div ref="effectTypesChart" class="chart-container h-48"></div>
            </div>
            
            <!-- 效果持续时间 -->
            <div class="chart-card bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">效果持续时间</h3>
              <div ref="effectDurationChart" class="chart-container h-48"></div>
            </div>
          </div>
          
          <!-- 关键效果列表 -->
          <div class="key-effects bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <h3 class="text-white font-medium mb-4">关键效果</h3>
            <div class="effects-list space-y-3">
              <div
                v-for="effect in keyEffects"
                :key="effect.id"
                class="effect-item p-3 bg-slate-700/50 rounded"
              >
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <el-tag :type="getEffectTypeColor(effect.type)" size="small">
                      {{ getEffectTypeLabel(effect.type) }}
                    </el-tag>
                    <span class="text-white font-medium">{{ effect.name }}</span>
                  </div>
                  <span class="text-slate-400 text-sm">持续 {{ effect.duration }} 回合</span>
                </div>
                <div class="effect-details grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span class="text-slate-400">施法者:</span>
                    <span class="text-white ml-2">{{ effect.caster }}</span>
                  </div>
                  <div>
                    <span class="text-slate-400">目标:</span>
                    <span class="text-white ml-2">{{ effect.target }}</span>
                  </div>
                  <div>
                    <span class="text-slate-400">触发次数:</span>
                    <span class="text-white ml-2">{{ effect.triggerCount }}</span>
                  </div>
                  <div>
                    <span class="text-slate-400">影响值:</span>
                    <span class="text-white ml-2">{{ effect.impact }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 战术分析 -->
      <el-tab-pane label="战术分析" name="tactics">
        <div class="tactics-analysis">
          <div class="tactics-summary grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 阵容分析 -->
            <div class="formation-analysis bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">阵容分析</h3>
              <div class="formation-comparison">
                <div class="team-formation mb-4">
                  <h4 class="text-blue-400 font-medium mb-2">队伍1阵容</h4>
                  <div class="formation-grid grid grid-cols-3 gap-2">
                    <div
                      v-for="(spirit, index) in team1Formation"
                      :key="index"
                      class="formation-cell p-2 bg-slate-700/50 rounded text-center"
                    >
                      <div v-if="spirit" class="text-white text-sm">{{ spirit.name }}</div>
                      <div v-else class="text-slate-500 text-sm">空</div>
                    </div>
                  </div>
                </div>
                
                <div class="team-formation">
                  <h4 class="text-red-400 font-medium mb-2">队伍2阵容</h4>
                  <div class="formation-grid grid grid-cols-3 gap-2">
                    <div
                      v-for="(spirit, index) in team2Formation"
                      :key="index"
                      class="formation-cell p-2 bg-slate-700/50 rounded text-center"
                    >
                      <div v-if="spirit" class="text-white text-sm">{{ spirit.name }}</div>
                      <div v-else class="text-slate-500 text-sm">空</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 战术评分 -->
            <div class="tactics-score bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <h3 class="text-white font-medium mb-4">战术评分</h3>
              <div class="score-items space-y-4">
                <div class="score-item">
                  <div class="flex justify-between mb-2">
                    <span class="text-slate-400">攻击协调</span>
                    <span class="text-white">{{ tacticsScore.attackCoordination }}/10</span>
                  </div>
                  <el-progress 
                    :percentage="tacticsScore.attackCoordination * 10" 
                    color="#ef4444"
                    :show-text="false"
                  />
                </div>
                
                <div class="score-item">
                  <div class="flex justify-between mb-2">
                    <span class="text-slate-400">防御配合</span>
                    <span class="text-white">{{ tacticsScore.defenseCoordination }}/10</span>
                  </div>
                  <el-progress 
                    :percentage="tacticsScore.defenseCoordination * 10" 
                    color="#3b82f6"
                    :show-text="false"
                  />
                </div>
                
                <div class="score-item">
                  <div class="flex justify-between mb-2">
                    <span class="text-slate-400">资源管理</span>
                    <span class="text-white">{{ tacticsScore.resourceManagement }}/10</span>
                  </div>
                  <el-progress 
                    :percentage="tacticsScore.resourceManagement * 10" 
                    color="#10b981"
                    :show-text="false"
                  />
                </div>
                
                <div class="score-item">
                  <div class="flex justify-between mb-2">
                    <span class="text-slate-400">时机把握</span>
                    <span class="text-white">{{ tacticsScore.timing }}/10</span>
                  </div>
                  <el-progress 
                    :percentage="tacticsScore.timing * 10" 
                    color="#f59e0b"
                    :show-text="false"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- 关键决策点 -->
          <div class="key-decisions bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
            <h3 class="text-white font-medium mb-4">关键决策点</h3>
            <div class="decisions-list space-y-3">
              <div
                v-for="decision in keyDecisions"
                :key="decision.id"
                class="decision-item p-3 bg-slate-700/50 rounded"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-white font-medium">{{ decision.title }}</span>
                  <el-tag :type="decision.impact === 'positive' ? 'success' : 'danger'" size="small">
                    {{ decision.impact === 'positive' ? '正面' : '负面' }}
                  </el-tag>
                </div>
                <p class="text-slate-300 text-sm mb-2">{{ decision.description }}</p>
                <div class="text-slate-400 text-xs">
                  回合 {{ decision.round }} | 影响度: {{ decision.impactScore }}/10
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  battleRecord: {
    id: string
    date: Date
    result: 'win' | 'lose'
    duration: number
    rounds: number
    team1Score: number
    team2Score: number
  }
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('timeline')
const timelinePosition = ref(0)

// 图表引用
const damageDistributionChart = ref()
const damageTrendChart = ref()
const skillUsageChart = ref()
const skillSuccessChart = ref()
const skillDamageChart = ref()
const effectTypesChart = ref()
const effectDurationChart = ref()

// 模拟数据
const totalDamage = ref(15420)
const totalHealing = ref(8930)

const battleEvents = ref([
  {
    round: 1,
    type: 'battle_start',
    title: '战斗开始',
    description: '双方精灵进入战场，战斗正式开始',
    details: '初始化精灵状态和位置'
  },
  {
    round: 1,
    type: 'skill_cast',
    title: '神曜王者·极 使用 万象归元',
    description: '为自己施加致命免疫效果',
    details: '消耗能量: 0 | 成功率: 100%'
  },
  {
    round: 2,
    type: 'damage',
    title: '神曜圣谕·女帝 受到伤害',
    description: '承受来自敌方的攻击，损失1200点生命值',
    details: '剩余生命值: 6800/8000'
  },
  {
    round: 3,
    type: 'heal',
    title: '希望之神·夏因 治疗队友',
    description: '为神曜圣谕·女帝恢复800点生命值',
    details: '当前生命值: 7600/8000'
  },
  {
    round: 4,
    type: 'death',
    title: '敌方精灵阵亡',
    description: '敌方一只精灵被击败',
    details: '剩余敌方精灵: 2只'
  },
  {
    round: 8,
    type: 'battle_end',
    title: '战斗结束',
    description: '我方获得胜利',
    details: '最终得分: 3-1'
  }
])

const damageRanking = ref([
  { name: '神曜王者·极', team: 1, damage: 4250, percentage: 27.6 },
  { name: '神曜圣谕·女帝', team: 1, damage: 3890, percentage: 25.2 },
  { name: '敌方精灵A', team: 2, damage: 3120, percentage: 20.2 },
  { name: '希望之神·夏因', team: 1, damage: 2340, percentage: 15.2 },
  { name: '敌方精灵B', team: 2, damage: 1820, percentage: 11.8 }
])

const skillsStats = ref([
  {
    name: '万象归元',
    caster: '神曜王者·极',
    usageCount: 1,
    successCount: 1,
    successRate: 100,
    totalDamage: 0,
    avgDamage: 0,
    criticalHits: 0
  },
  {
    name: '天剑凌云',
    caster: '神曜王者·极',
    usageCount: 3,
    successCount: 3,
    successRate: 100,
    totalDamage: 4250,
    avgDamage: 1417,
    criticalHits: 1
  },
  {
    name: '圣谕之盾',
    caster: '神曜圣谕·女帝',
    usageCount: 2,
    successCount: 2,
    successRate: 100,
    totalDamage: 0,
    avgDamage: 0,
    criticalHits: 0
  }
])

const keyEffects = ref([
  {
    id: 'effect_1',
    name: '致命免疫',
    type: 'buff',
    caster: '神曜王者·极',
    target: '神曜王者·极',
    duration: 3,
    triggerCount: 1,
    impact: '免疫1次致命伤害'
  },
  {
    id: 'effect_2',
    name: '护盾',
    type: 'buff',
    caster: '神曜圣谕·女帝',
    target: '神曜圣谕·女帝',
    duration: 2,
    triggerCount: 2,
    impact: '吸收1500点伤害'
  },
  {
    id: 'effect_3',
    name: '减速',
    type: 'debuff',
    caster: '神曜王者·极',
    target: '敌方精灵A',
    duration: 2,
    triggerCount: 0,
    impact: '速度降低30%'
  }
])

const team1Formation = ref([
  { name: '神曜王者·极' },
  null,
  { name: '希望之神·夏因' },
  null,
  { name: '神曜圣谕·女帝' },
  null,
  null,
  null,
  null
])

const team2Formation = ref([
  { name: '敌方精灵A' },
  null,
  { name: '敌方精灵B' },
  null,
  { name: '敌方精灵C' },
  null,
  null,
  null,
  null
])

const tacticsScore = ref({
  attackCoordination: 8.5,
  defenseCoordination: 7.2,
  resourceManagement: 6.8,
  timing: 9.1
})

const keyDecisions = ref([
  {
    id: 'decision_1',
    round: 1,
    title: '开局使用万象归元',
    description: '神曜王者·极在第一回合使用万象归元获得致命免疫，为后续战斗提供保障',
    impact: 'positive',
    impactScore: 9
  },
  {
    id: 'decision_2',
    round: 3,
    title: '及时治疗队友',
    description: '希望之神·夏因在关键时刻治疗神曜圣谕·女帝，避免了重要坦克的阵亡',
    impact: 'positive',
    impactScore: 8
  },
  {
    id: 'decision_3',
    round: 5,
    title: '错失击杀机会',
    description: '未能在敌方精灵残血时集火击杀，给对方留下了反击机会',
    impact: 'negative',
    impactScore: 6
  }
])

// 计算属性
const visibleEvents = computed(() => {
  return battleEvents.value.slice(0, timelinePosition.value + 1)
})

// 方法
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getEventClass = (type: string) => {
  const classMap = {
    battle_start: 'border-blue-500 bg-blue-500/10',
    battle_end: 'border-purple-500 bg-purple-500/10',
    skill_cast: 'border-yellow-500 bg-yellow-500/10',
    damage: 'border-red-500 bg-red-500/10',
    heal: 'border-green-500 bg-green-500/10',
    death: 'border-gray-500 bg-gray-500/10'
  }
  return classMap[type] || 'border-slate-500 bg-slate-500/10'
}

const getEventIconClass = (type: string) => {
  const classMap = {
    battle_start: 'text-blue-400',
    battle_end: 'text-purple-400',
    skill_cast: 'text-yellow-400',
    damage: 'text-red-400',
    heal: 'text-green-400',
    death: 'text-gray-400'
  }
  return classMap[type] || 'text-slate-400'
}

const getEventIcon = (type: string) => {
  const iconMap = {
    battle_start: 'VideoPlay',
    battle_end: 'Trophy',
    skill_cast: 'Magic',
    damage: 'Sword',
    heal: 'Heart',
    death: 'Close'
  }
  return iconMap[type] || 'InfoFilled'
}

const getSuccessRateColor = (rate: number) => {
  if (rate >= 90) return 'text-green-400'
  if (rate >= 70) return 'text-yellow-400'
  return 'text-red-400'
}

const getEffectTypeColor = (type: string) => {
  const colorMap = {
    buff: 'success',
    debuff: 'danger',
    neutral: 'info'
  }
  return colorMap[type] || ''
}

const getEffectTypeLabel = (type: string) => {
  const labelMap = {
    buff: '增益',
    debuff: '减益',
    neutral: '中性'
  }
  return labelMap[type] || type
}

const playTimeline = () => {
  const interval = setInterval(() => {
    if (timelinePosition.value < battleEvents.value.length - 1) {
      timelinePosition.value++
    } else {
      clearInterval(interval)
    }
  }, 1000)
}

const initCharts = () => {
  nextTick(() => {
    // 伤害分布图
    if (damageDistributionChart.value) {
      const chart1 = echarts.init(damageDistributionChart.value)
      chart1.setOption({
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 4250, name: '神曜王者·极', itemStyle: { color: '#8b5cf6' } },
            { value: 3890, name: '神曜圣谕·女帝', itemStyle: { color: '#3b82f6' } },
            { value: 3120, name: '敌方精灵A', itemStyle: { color: '#ef4444' } },
            { value: 2340, name: '希望之神·夏因', itemStyle: { color: '#10b981' } },
            { value: 1820, name: '敌方精灵B', itemStyle: { color: '#f59e0b' } }
          ]
        }]
      })
    }

    // 伤害趋势图
    if (damageTrendChart.value) {
      const chart2 = echarts.init(damageTrendChart.value)
      chart2.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['回合1', '回合2', '回合3', '回合4', '回合5', '回合6', '回合7', '回合8'] },
        yAxis: { type: 'value' },
        series: [
          {
            name: '队伍1',
            data: [1200, 800, 1500, 2100, 1800, 2200, 1900, 1650],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#3b82f6' }
          },
          {
            name: '队伍2',
            data: [900, 1100, 1300, 1600, 1200, 1400, 1100, 800],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#ef4444' }
          }
        ]
      })
    }

    // 技能使用频率图
    if (skillUsageChart.value) {
      const chart3 = echarts.init(skillUsageChart.value)
      chart3.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['万象归元', '天剑凌云', '圣谕之盾', '希望之光'] },
        yAxis: { type: 'value' },
        series: [{
          data: [1, 3, 2, 2],
          type: 'bar',
          itemStyle: { color: '#8b5cf6' }
        }]
      })
    }

    // 技能成功率图
    if (skillSuccessChart.value) {
      const chart4 = echarts.init(skillSuccessChart.value)
      chart4.setOption({
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 100, name: '成功', itemStyle: { color: '#10b981' } },
            { value: 0, name: '失败', itemStyle: { color: '#ef4444' } }
          ]
        }]
      })
    }

    // 技能伤害贡献图
    if (skillDamageChart.value) {
      const chart5 = echarts.init(skillDamageChart.value)
      chart5.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['天剑凌云', '圣谕攻击', '希望之光', '其他'] },
        yAxis: { type: 'value' },
        series: [{
          data: [4250, 3890, 2340, 1820],
          type: 'bar',
          itemStyle: { color: '#ec4899' }
        }]
      })
    }

    // 效果类型分布图
    if (effectTypesChart.value) {
      const chart6 = echarts.init(effectTypesChart.value)
      chart6.setOption({
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 5, name: '增益效果', itemStyle: { color: '#10b981' } },
            { value: 3, name: '减益效果', itemStyle: { color: '#ef4444' } },
            { value: 2, name: '中性效果', itemStyle: { color: '#6b7280' } }
          ]
        }]
      })
    }

    // 效果持续时间图
    if (effectDurationChart.value) {
      const chart7 = echarts.init(effectDurationChart.value)
      chart7.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['1回合', '2回合', '3回合', '4+回合'] },
        yAxis: { type: 'value' },
        series: [{
          data: [2, 4, 3, 1],
          type: 'bar',
          itemStyle: { color: '#f59e0b' }
        }]
      })
    }
  })
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped lang="scss">
.battle-analytics-detail {
  .stat-card {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      border-color: rgba(139, 92, 246, 0.3);
    }
  }
  
  .chart-container {
    width: 100%;
    height: 100%;
  }
  
  .event-item {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateX(4px);
    }
  }
  
  .ranking-item,
  .effect-item,
  .decision-item {
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(51, 65, 85, 0.7);
    }
  }
  
  .formation-cell {
    transition: all 0.3s ease;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background: rgba(139, 92, 246, 0.1);
    }
  }
  
  .timeline-content {
    .events-list {
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: rgba(148, 163, 184, 0.1);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(139, 92, 246, 0.3);
        border-radius: 3px;
        
        &:hover {
          background: rgba(139, 92, 246, 0.5);
        }
      }
    }
  }
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(148, 163, 184, 0.2);
}

:deep(.el-tabs__active-bar) {
  background-color: #8b5cf6;
}

:deep(.el-tabs__item.is-active) {
  color: #8b5cf6;
}

:deep(.el-table) {
  background: transparent;
  
  .el-table__body tr {
    background: rgba(51, 65, 85, 0.3);
    
    &:hover {
      background: rgba(139, 92, 246, 0.1);
    }
  }
  
  .el-table__header {
    background: rgba(30, 41, 59, 0.8);
  }
}

:deep(.el-slider__runway) {
  background-color: rgba(148, 163, 184, 0.3);
}

:deep(.el-slider__bar) {
  background: linear-gradient(to right, #8b5cf6, #ec4899);
}

:deep(.el-progress-bar__outer) {
  background-color: rgba(148, 163, 184, 0.2);
}
</style>