#!/usr/bin/env python3
"""
战斗记录模型

用于记录和管理战斗过程中的状态变化
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import copy


@dataclass
class SpiritSnapshot:
    """精灵状态快照"""
    name: str
    team: int
    position: tuple
    current_hp: float
    max_hp: float
    energy: int
    max_energy: int
    is_alive: bool
    
    # 详细属性
    actual_attack: float = 0.0
    actual_defense: float = 0.0
    actual_speed: float = 0.0
    actual_hit_rate: float = 0.0
    actual_dodge_rate: float = 0.0
    actual_crit_rate: float = 0.0
    actual_crit_damage: float = 1.5
    
    # 效果列表
    effects: List[Dict[str, Any]] = field(default_factory=list)
    
    # 状态变化
    hp_change: float = 0.0
    energy_change: int = 0
    effects_added: List[str] = field(default_factory=list)
    effects_removed: List[str] = field(default_factory=list)


@dataclass
class ActionRecord:
    """行动记录"""
    action_id: str
    action_type: str
    caster_name: str
    target_names: List[str]
    description: str
    timestamp: datetime
    
    # 行动结果
    damage_dealt: Dict[str, float] = field(default_factory=dict)
    healing_done: Dict[str, float] = field(default_factory=dict)
    effects_applied: Dict[str, List[str]] = field(default_factory=dict)
    
    # 额外信息
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RoundSnapshot:
    """回合状态快照"""
    round_num: int
    timestamp: datetime
    
    # 精灵状态
    spirits: Dict[str, SpiritSnapshot] = field(default_factory=dict)
    
    # 回合信息
    turn_order: List[str] = field(default_factory=list)
    actions_performed: List[ActionRecord] = field(default_factory=list)
    
    # 战斗状态
    winner: Optional[int] = None
    battle_ended: bool = False
    
    # 统计信息
    total_damage_dealt: Dict[int, float] = field(default_factory=dict)  # 按队伍统计
    total_healing_done: Dict[int, float] = field(default_factory=dict)  # 按队伍统计
    
    # 变化摘要
    changes_summary: str = ""


class BattleRecorder:
    """战斗记录器"""
    
    def __init__(self):
        self.snapshots: List[RoundSnapshot] = []
        self.current_round: int = 0
        self.battle_start_time: datetime = datetime.now()
        
    def create_snapshot(self, battle_state, round_num: int, actions: Optional[List[ActionRecord]] = None) -> RoundSnapshot:
        """创建回合快照"""
        snapshot = RoundSnapshot(
            round_num=round_num,
            timestamp=datetime.now()
        )
        
        # 记录精灵状态
        all_spirits = battle_state.get_all_spirits()
        for spirit in all_spirits:
            spirit_snapshot = self._create_spirit_snapshot(spirit)
            snapshot.spirits[spirit.name] = spirit_snapshot
        
        # 记录回合顺序
        if hasattr(battle_state, 'turn_order_strategy'):
            try:
                turn_order = battle_state.turn_order_strategy.create_action_queue(battle_state)
                snapshot.turn_order = [spirit.name for spirit in turn_order]
            except:
                snapshot.turn_order = []
        
        # 记录行动
        if actions:
            snapshot.actions_performed = actions
        else:
            # 如果没有提供行动记录，尝试从战斗状态中获取
            snapshot.actions_performed = self._extract_actions_from_battle_state(battle_state, round_num)
        
        # 记录战斗状态
        snapshot.winner = getattr(battle_state, 'winner', None)
        snapshot.battle_ended = snapshot.winner is not None
        
        # 计算统计信息
        self._calculate_statistics(snapshot)
        
        # 计算变化（如果有前一个快照）
        if self.snapshots:
            self._calculate_changes(snapshot, self.snapshots[-1])
        
        return snapshot

    def _extract_actions_from_battle_state(self, battle_state, round_num: int) -> List[ActionRecord]:
        """从战斗状态中提取行动记录"""
        actions = []

        # 尝试从战斗引擎的统计跟踪器中获取数据
        stats_data = self._get_stats_from_battle_state(battle_state)

        if round_num > 0:
            # 获取当前存活的精灵，假设它们都执行了行动
            all_spirits = battle_state.get_all_spirits()

            for i, spirit in enumerate(all_spirits):
                # 从统计数据中获取该精灵的伤害和治疗数据
                spirit_stats = stats_data.get(spirit.name, {})
                damage_dealt = spirit_stats.get('total_damage_dealt', 0)
                healing_done = spirit_stats.get('total_healing_done', 0)  # 如果有治疗统计的话

                # 获取该精灵对其他精灵造成的伤害
                damage_to_others = {}
                if 'damage_to' in spirit_stats:
                    for target_name, damage in spirit_stats['damage_to'].items():
                        if damage > 0:
                            damage_to_others[target_name] = damage

                # 创建动作记录
                action = ActionRecord(
                    action_id=f"round_{round_num}_action_{i}",
                    action_type="combat_action",
                    caster_name=spirit.name,
                    target_names=list(damage_to_others.keys()),
                    description=f"{spirit.name} 造成了 {damage_dealt:.0f} 点伤害",
                    timestamp=datetime.now(),
                    damage_dealt=damage_to_others,
                    healing_done={},  # 暂时没有治疗数据
                    effects_applied={}
                )
                actions.append(action)

        return actions

    def _get_stats_from_battle_state(self, battle_state) -> Dict[str, Any]:
        """从战斗状态中获取统计数据"""
        stats_data = {}

        try:
            # 尝试从战斗引擎中获取统计跟踪器
            if hasattr(battle_state, 'engine'):
                engine = battle_state.engine
                if hasattr(engine, 'stats_tracker') and engine.stats_tracker:
                    # 获取统计数据
                    raw_stats = engine.stats_tracker.data

                    # 转换统计数据格式
                    for spirit_id, spirit_data in raw_stats.items():
                        # 尝试通过ID找到精灵名称
                        spirit_name = self._get_spirit_name_by_id(battle_state, spirit_id)
                        if spirit_name:
                            stats_data[spirit_name] = {
                                'total_damage_dealt': spirit_data.get('total_damage_dealt', 0),
                                'total_damage_taken': spirit_data.get('total_damage_taken', 0),
                                'damage_to': dict(spirit_data.get('damage_to', {})),
                                'damage_taken_from': dict(spirit_data.get('damage_taken_from', {})),
                                'actions': spirit_data.get('actions', 0),
                                'crit': spirit_data.get('crit', 0),
                                'dodge': spirit_data.get('dodge', 0)
                            }

            # 如果没有找到统计数据，尝试其他方法
            if not stats_data:
                # 从全局统计系统获取
                try:
                    from core.system_manager import get_system
                    stats_tracker = get_system('statistics')
                    if stats_tracker and hasattr(stats_tracker, 'data'):
                        raw_stats = stats_tracker.data
                        for spirit_id, spirit_data in raw_stats.items():
                            spirit_name = self._get_spirit_name_by_id(battle_state, spirit_id)
                            if spirit_name:
                                stats_data[spirit_name] = {
                                    'total_damage_dealt': spirit_data.get('total_damage_dealt', 0),
                                    'total_damage_taken': spirit_data.get('total_damage_taken', 0),
                                    'damage_to': dict(spirit_data.get('damage_to', {})),
                                    'damage_taken_from': dict(spirit_data.get('damage_taken_from', {})),
                                    'actions': spirit_data.get('actions', 0),
                                    'crit': spirit_data.get('crit', 0),
                                    'dodge': spirit_data.get('dodge', 0)
                                }
                except:
                    pass

        except Exception as e:
            # 如果获取统计数据失败，记录错误但不影响主流程
            print(f"获取统计数据失败: {e}")

        return stats_data

    def _get_spirit_name_by_id(self, battle_state, spirit_id: str) -> Optional[str]:
        """通过ID获取精灵名称"""
        try:
            all_spirits = battle_state.get_all_spirits()
            for spirit in all_spirits:
                if hasattr(spirit, 'id') and spirit.id == spirit_id:
                    return spirit.name
                elif hasattr(spirit, 'name') and spirit.name == spirit_id:
                    return spirit.name
        except:
            pass
        return None

    def _create_spirit_snapshot(self, spirit) -> SpiritSnapshot:
        """创建精灵快照"""
        snapshot = SpiritSnapshot(
            name=spirit.name,
            team=spirit.team,
            position=getattr(spirit, 'position', (0, 0)),
            current_hp=spirit.current_hp,
            max_hp=spirit.max_hp,
            energy=getattr(spirit, 'energy', 0),
            max_energy=getattr(spirit, 'max_energy', 100),
            is_alive=spirit.is_alive
        )
        
        # 获取详细属性 - 使用正确的属性获取方法
        if hasattr(spirit, 'attributes'):
            attrs = spirit.attributes

            # 使用正确的方法获取实际属性值
            try:
                # 实际攻击力需要传入精灵实例
                if hasattr(attrs, 'get_actual_attack'):
                    snapshot.actual_attack = attrs.get_actual_attack(spirit)
                else:
                    snapshot.actual_attack = getattr(attrs, 'attack', 0.0)

                # 防御力 - 使用物理防御作为主要防御
                snapshot.actual_defense = getattr(attrs, 'pdef', 0.0)

                # 速度
                snapshot.actual_speed = getattr(attrs, 'speed', 0.0)

                # 命中率和闪避率
                snapshot.actual_hit_rate = getattr(attrs, 'hit_rate', 0.0)
                snapshot.actual_dodge_rate = getattr(attrs, 'dodge_rate', 0.0)

                # 暴击相关
                snapshot.actual_crit_rate = getattr(attrs, 'crit_rate', 0.0)
                snapshot.actual_crit_damage = getattr(attrs, 'crit_damage', 1.5)

            except Exception as e:
                # 如果获取失败，使用默认值
                snapshot.actual_attack = 0.0
                snapshot.actual_defense = 0.0
                snapshot.actual_speed = 0.0
                snapshot.actual_hit_rate = 0.0
                snapshot.actual_dodge_rate = 0.0
                snapshot.actual_crit_rate = 0.0
                snapshot.actual_crit_damage = 1.5
        
        # 获取效果信息 - 检查多种可能的效果存储方式
        effects_found = False

        # 方式1：检查 effect_manager.effects
        if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
            effects = spirit.effect_manager.effects
            if effects:  # 如果不为空
                for effect_id, effect in effects.items():
                    effect_info = {
                        'id': effect_id,
                        'name': getattr(effect, 'name', f'Effect_{effect_id}'),
                        'type': getattr(effect, 'effect_type', getattr(effect, 'type', 'Unknown')),
                        'duration': getattr(effect, 'duration', -1),
                        'stacks': getattr(effect, 'stacks', 1),
                        'description': getattr(effect, 'description', '')
                    }
                    snapshot.effects.append(effect_info)
                    effects_found = True

        # 方式2：检查 spirit.effects 列表
        if not effects_found and hasattr(spirit, 'effects') and spirit.effects:
            for i, effect in enumerate(spirit.effects):
                effect_info = {
                    'id': getattr(effect, 'id', f'effect_{i}'),
                    'name': getattr(effect, 'name', f'Effect_{i}'),
                    'type': getattr(effect, 'effect_type', getattr(effect, 'type', 'Unknown')),
                    'duration': getattr(effect, 'duration', -1),
                    'stacks': getattr(effect, 'stacks', 1),
                    'description': getattr(effect, 'description', '')
                }
                snapshot.effects.append(effect_info)
                effects_found = True

        # 方式3：检查被动技能作为"效果"
        if not effects_found and hasattr(spirit, 'skills'):
            for skill in spirit.skills:
                if hasattr(skill, 'metadata'):
                    cast_type = getattr(skill.metadata, 'cast_type', None)
                    if cast_type == 'PASSIVE':
                        effect_info = {
                            'id': f'passive_{skill.metadata.name}',
                            'name': f'被动: {skill.metadata.name}',
                            'type': 'PASSIVE',
                            'duration': -1,  # 永久
                            'stacks': 1,
                            'description': getattr(skill.metadata, 'description', '')
                        }
                        snapshot.effects.append(effect_info)
                        effects_found = True
        
        return snapshot
    
    def _calculate_statistics(self, snapshot: RoundSnapshot):
        """计算统计信息"""
        # 按队伍统计伤害和治疗
        for team in [0, 1]:
            total_damage = 0.0
            total_healing = 0.0

            for action in snapshot.actions_performed:
                # 检查施放者是否属于当前队伍
                caster_spirit = snapshot.spirits.get(action.caster_name)
                if caster_spirit and caster_spirit.team == team:
                    total_damage += sum(action.damage_dealt.values())
                    total_healing += sum(action.healing_done.values())

            snapshot.total_damage_dealt[team] = total_damage
            snapshot.total_healing_done[team] = total_healing
    
    def _calculate_changes(self, current: RoundSnapshot, previous: RoundSnapshot):
        """计算状态变化"""
        changes = []
        
        for name, current_spirit in current.spirits.items():
            if name in previous.spirits:
                prev_spirit = previous.spirits[name]
                
                # HP变化
                hp_change = current_spirit.current_hp - prev_spirit.current_hp
                if hp_change != 0:
                    current_spirit.hp_change = hp_change
                    if hp_change > 0:
                        changes.append(f"{name} 恢复了 {hp_change:.0f} HP")
                    else:
                        changes.append(f"{name} 失去了 {-hp_change:.0f} HP")
                
                # 气势变化
                energy_change = current_spirit.energy - prev_spirit.energy
                if energy_change != 0:
                    current_spirit.energy_change = energy_change
                    if energy_change > 0:
                        changes.append(f"{name} 获得了 {energy_change} 气势")
                    else:
                        changes.append(f"{name} 失去了 {-energy_change} 气势")
                
                # 效果变化
                prev_effect_names = {effect['name'] for effect in prev_spirit.effects}
                curr_effect_names = {effect['name'] for effect in current_spirit.effects}
                
                added_effects = curr_effect_names - prev_effect_names
                removed_effects = prev_effect_names - curr_effect_names
                
                current_spirit.effects_added = list(added_effects)
                current_spirit.effects_removed = list(removed_effects)
                
                for effect in added_effects:
                    changes.append(f"{name} 获得了效果: {effect}")
                for effect in removed_effects:
                    changes.append(f"{name} 失去了效果: {effect}")
        
        current.changes_summary = "; ".join(changes) if changes else "无变化"
    
    def add_snapshot(self, snapshot: RoundSnapshot):
        """添加快照"""
        self.snapshots.append(snapshot)
        self.current_round = snapshot.round_num
    
    def get_snapshot(self, round_num: int) -> Optional[RoundSnapshot]:
        """获取指定回合的快照"""
        for snapshot in self.snapshots:
            if snapshot.round_num == round_num:
                return snapshot
        return None
    
    def get_latest_snapshot(self) -> Optional[RoundSnapshot]:
        """获取最新快照"""
        return self.snapshots[-1] if self.snapshots else None
    
    def get_spirit_history(self, spirit_name: str) -> List[SpiritSnapshot]:
        """获取精灵的历史状态"""
        history = []
        for snapshot in self.snapshots:
            if spirit_name in snapshot.spirits:
                history.append(snapshot.spirits[spirit_name])
        return history
    
    def compare_snapshots(self, round1: int, round2: int) -> Dict[str, Any]:
        """比较两个回合的状态"""
        snapshot1 = self.get_snapshot(round1)
        snapshot2 = self.get_snapshot(round2)
        
        if not snapshot1 or not snapshot2:
            return {}
        
        comparison = {
            'round1': round1,
            'round2': round2,
            'spirit_changes': {},
            'team_stats_changes': {}
        }
        
        # 比较精灵状态
        for name in set(snapshot1.spirits.keys()) | set(snapshot2.spirits.keys()):
            spirit1 = snapshot1.spirits.get(name)
            spirit2 = snapshot2.spirits.get(name)
            
            if spirit1 and spirit2:
                changes = {
                    'hp_change': spirit2.current_hp - spirit1.current_hp,
                    'energy_change': spirit2.energy - spirit1.energy,
                    'alive_change': spirit2.is_alive != spirit1.is_alive,
                    'effects_change': len(spirit2.effects) - len(spirit1.effects)
                }
                comparison['spirit_changes'][name] = changes
        
        # 比较队伍统计
        for team in [0, 1]:
            damage1 = snapshot1.total_damage_dealt.get(team, 0)
            damage2 = snapshot2.total_damage_dealt.get(team, 0)
            healing1 = snapshot1.total_healing_done.get(team, 0)
            healing2 = snapshot2.total_healing_done.get(team, 0)
            
            comparison['team_stats_changes'][team] = {
                'damage_change': damage2 - damage1,
                'healing_change': healing2 - healing1
            }
        
        return comparison
    
    def export_summary(self) -> Dict[str, Any]:
        """导出战斗摘要"""
        if not self.snapshots:
            return {}
        
        latest = self.snapshots[-1]
        
        return {
            'battle_duration': (latest.timestamp - self.battle_start_time).total_seconds(),
            'total_rounds': len(self.snapshots),
            'winner': latest.winner,
            'final_spirits_status': {
                name: {
                    'hp': spirit.current_hp,
                    'max_hp': spirit.max_hp,
                    'alive': spirit.is_alive,
                    'effects_count': len(spirit.effects)
                }
                for name, spirit in latest.spirits.items()
            },
            'total_actions': sum(len(snapshot.actions_performed) for snapshot in self.snapshots),
            'team_final_stats': {
                team: {
                    'total_damage': latest.total_damage_dealt.get(team, 0),
                    'total_healing': latest.total_healing_done.get(team, 0),
                    'survivors': sum(1 for spirit in latest.spirits.values() 
                                   if spirit.team == team and spirit.is_alive)
                }
                for team in [0, 1]
            }
        }
