"""
增强动作执行器

处理AI行动生成系统创建的增强动作类型：
- EnhancedAttackAction: 包含条件性效果的攻击动作
- UnableToActEvent: 无法行动事件
- ActionDecisionAction: 行动决策动作
- ConditionalEffectTriggerAction: 条件性效果触发动作
"""

from __future__ import annotations
from typing import Optional, List, cast, Dict, Any

from core.battle.executor.executor import handler, UnifiedActionExecutor
from .decorators import validate_target, safe_execute
from core.action import (
    EnhancedAttackAction,
    UnableToActEvent,
    ActionDecisionAction,
    ConditionalEffectTriggerAction,
    BattleAction,
    LogAction,
    DamageAction,
    DispatchEventAction,
    DamageType
)
from core.logging import get_logger

logger = get_logger("battle.enhanced_actions")

@handler(EnhancedAttackAction)
@validate_target(alive_required=True)
@safe_execute()
def _handle_enhanced_attack(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理增强攻击动作"""
    enhanced_action = cast(EnhancedAttackAction, action)
    
    logger.debug(f"处理增强攻击: {getattr(enhanced_action.caster, 'name', 'Unknown')} -> {getattr(enhanced_action.target, 'name', 'Unknown')}")
    
    actions = []
    
    try:
        # 1. 计算增强后的伤害
        base_damage = enhanced_action.get_enhanced_damage()
        
        # 2. 检查是否暴击
        crit_rate = enhanced_action.get_enhanced_crit_rate()
        is_critical = _calculate_critical_hit(crit_rate)
        
        # 3. 应用暴击伤害
        final_damage = base_damage
        if is_critical:
            crit_multiplier = enhanced_action.get_enhanced_crit_damage()
            final_damage *= crit_multiplier
            
            # 添加暴击日志
            actions.append(LogAction(
                caster=enhanced_action.caster,
                message=f"💥 暴击！{getattr(enhanced_action.caster, 'name', 'Unknown')} 对 {getattr(enhanced_action.target, 'name', 'Unknown')} 造成暴击伤害！"
            ))
        
        # 4. 检查破击
        pierce_rate = enhanced_action.get_enhanced_pierce_rate()
        is_pierce = _calculate_pierce_hit(pierce_rate)
        
        if is_pierce:
            # 破击效果：忽略部分防御
            actions.append(LogAction(
                caster=enhanced_action.caster,
                message=f"🎯 破击！{getattr(enhanced_action.caster, 'name', 'Unknown')} 的攻击无视了部分防御！"
            ))
        
        # 5. 从技能中获取正确的参数
        skill_power_multiplier = 1.0
        skill_damage_type = DamageType.PHYSICAL

        # 检查技能组件获取正确的参数
        if hasattr(enhanced_action.skill, 'components'):
            for component in enhanced_action.skill.components:
                if hasattr(component, 'power_multiplier'):
                    skill_power_multiplier = component.power_multiplier
                if hasattr(component, 'damage_type'):
                    damage_type_str = component.damage_type.upper()
                    skill_damage_type = DamageType.MAGIC if damage_type_str == "MAGIC" else DamageType.PHYSICAL
                break

        # 应用条件性效果的倍率
        conditional_multiplier = enhanced_action.conditional_effects.get('damage_multiplier', 1.0)
        final_power_multiplier = skill_power_multiplier * conditional_multiplier

        # 创建伤害动作 - 让伤害计算器计算实际伤害
        damage_action = DamageAction(
            caster=enhanced_action.caster,
            target=enhanced_action.target,
            damage_value=None,  # 让伤害计算器计算
            power_multiplier=final_power_multiplier,  # 使用正确的倍率
            damage_type=skill_damage_type,  # 使用技能的伤害类型
            is_critical=is_critical,
            skill_name=getattr(enhanced_action.skill.metadata, 'name', 'Unknown') if hasattr(enhanced_action.skill, 'metadata') else 'Unknown',
            label=f"增强攻击{'(破击)' if is_pierce else ''}"
        )
        actions.append(damage_action)
        
        # 6. 处理气势获得
        energy_gain = enhanced_action.get_energy_gain()
        if energy_gain > 0:
            _apply_energy_gain(enhanced_action.caster, energy_gain, actions)
        
        # 7. 处理连击效果
        if enhanced_action.has_combo_effect():
            combo_actions = _handle_combo_effect(enhanced_action, self.battle_state)
            actions.extend(combo_actions)
        
        # 8. 记录条件性效果触发
        if enhanced_action.conditional_effects:
            _log_conditional_effects(enhanced_action, actions)
        
        logger.debug(f"增强攻击处理完成，生成了 {len(actions)} 个动作")
        
    except Exception as e:
        logger.error(f"处理增强攻击时出错: {e}")
        # 回退到基础伤害
        actions.append(DamageAction(
            caster=enhanced_action.caster,
            target=enhanced_action.target,
            damage_value=100,  # 默认伤害
            damage_type=DamageType.PHYSICAL,  # 使用物理伤害类型
            label="回退攻击"
        ))
    
    return actions

@handler(UnableToActEvent)
@safe_execute()
def _handle_unable_to_act(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理无法行动事件"""
    unable_action = cast(UnableToActEvent, action)
    
    logger.debug(f"{getattr(unable_action.caster, 'name', 'Unknown')} 无法行动: {unable_action.reason}")
    
    actions = []
    
    # 1. 记录无法行动的原因
    actions.append(LogAction(
        caster=unable_action.caster,
        message=f"⚠️ {getattr(unable_action.caster, 'name', 'Unknown')} {unable_action.reason}",
        level="WARNING"
    ))
    
    # 2. 发出无法行动事件（供其他系统监听）
    from core.event.events import GameEvent
    
    # 创建自定义无法行动事件
    unable_event = type('UnableToActGameEvent', (GameEvent,), {
        'spirit': unable_action.caster,
        'reason': unable_action.reason,
        'blocked_by': unable_action.blocked_by
    })()
    
    actions.append(DispatchEventAction(
        caster=unable_action.caster,
        event=unable_event
    ))
    
    return actions

@handler(ActionDecisionAction)
@safe_execute()
def _handle_action_decision(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理行动决策动作"""
    decision_action = cast(ActionDecisionAction, action)
    
    logger.debug(f"处理行动决策: {getattr(decision_action.caster, 'name', 'Unknown')}")
    
    actions = []
    
    # 记录决策过程（用于调试和分析）
    if decision_action.selected_skill:
        skill_name = getattr(decision_action.selected_skill.metadata, 'name', 'Unknown') if hasattr(decision_action.selected_skill, 'metadata') else 'Unknown'
        target_names = [getattr(t, 'name', 'Unknown') for t in decision_action.selected_targets]
        
        actions.append(LogAction(
            caster=decision_action.caster,
            message=f"🧠 {getattr(decision_action.caster, 'name', 'Unknown')} 决定使用 {skill_name} 攻击 {', '.join(target_names)}",
            level="DEBUG"
        ))
    
    return actions

@handler(ConditionalEffectTriggerAction)
@safe_execute()
def _handle_conditional_effect_trigger(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理条件性效果触发动作"""
    trigger_action = cast(ConditionalEffectTriggerAction, action)
    
    logger.debug(f"处理条件性效果触发: {trigger_action.effect_name}")
    
    actions = []
    
    # 记录效果触发
    actions.append(LogAction(
        caster=trigger_action.caster,
        message=f"✨ 触发条件性效果: {trigger_action.effect_name}",
        level="INFO"
    ))
    
    # 根据效果类型执行特定逻辑
    if trigger_action.effect_name == "spirit_wisdom_boost_triggered":
        actions.extend(_handle_spirit_wisdom_boost(trigger_action))
    elif trigger_action.effect_name == "critical_hit_triggered":
        actions.extend(_handle_critical_hit_trigger(trigger_action))
    elif trigger_action.effect_name == "execute_triggered":
        actions.extend(_handle_execute_trigger(trigger_action))
    elif trigger_action.effect_name == "combo_triggered":
        actions.extend(_handle_combo_trigger(trigger_action))
    
    return actions

# === 辅助函数 ===

def _calculate_critical_hit(crit_rate: float) -> bool:
    """计算是否暴击"""
    import random
    return random.random() < crit_rate

def _calculate_pierce_hit(pierce_rate: float) -> bool:
    """计算是否破击"""
    import random
    return random.random() < pierce_rate

def _apply_energy_gain(spirit, energy_gain: int, actions: List[BattleAction]):
    """应用气势获得"""
    try:
        if hasattr(spirit, 'current_energy') and hasattr(spirit, 'max_energy'):
            old_energy = spirit.current_energy
            new_energy = min(spirit.current_energy + energy_gain, spirit.max_energy)
            spirit.current_energy = new_energy
            
            actual_gain = new_energy - old_energy
            if actual_gain > 0:
                actions.append(LogAction(
                    caster=spirit,
                    message=f"⚡ {getattr(spirit, 'name', 'Unknown')} 获得 {actual_gain} 点气势！（当前: {new_energy}/{spirit.max_energy}）"
                ))
    except Exception as e:
        logger.warning(f"应用气势获得时出错: {e}")

def _handle_combo_effect(enhanced_action: EnhancedAttackAction, battle_state) -> List[BattleAction]:
    """处理连击效果"""
    actions = []
    
    try:
        actions.append(LogAction(
            caster=enhanced_action.caster,
            message=f"🔄 连击！{getattr(enhanced_action.caster, 'name', 'Unknown')} 发动连击攻击！"
        ))
        
        # 获取技能的伤害类型
        skill_damage_type = DamageType.PHYSICAL
        if hasattr(enhanced_action.skill, 'components'):
            for component in enhanced_action.skill.components:
                if hasattr(component, 'damage_type'):
                    damage_type_str = component.damage_type.upper()
                    skill_damage_type = DamageType.MAGIC if damage_type_str == "MAGIC" else DamageType.PHYSICAL
                    break

        actions.append(DamageAction(
            caster=enhanced_action.caster,
            target=enhanced_action.target,
            damage_value=None,  # 让伤害计算器计算
            power_multiplier=0.3,  # 连击30%伤害
            damage_type=skill_damage_type,  # 使用技能的伤害类型
            label="连击伤害"
        ))
        
    except Exception as e:
        logger.warning(f"处理连击效果时出错: {e}")
    
    return actions

def _log_conditional_effects(enhanced_action: EnhancedAttackAction, actions: List[BattleAction]):
    """记录条件性效果"""
    effects = enhanced_action.conditional_effects
    
    effect_messages = []
    for effect_name, value in effects.items():
        if effect_name.endswith('_bonus') and isinstance(value, (int, float)):
            percentage = int(value * 100) if value < 1 else int(value)
            effect_messages.append(f"{effect_name.replace('_', ' ').title()}: +{percentage}%")
        elif effect_name == 'energy_gain':
            effect_messages.append(f"气势获得: +{value}")
        elif effect_name == 'combo_attack' and value:
            effect_messages.append("连击效果")
    
    if effect_messages:
        actions.append(LogAction(
            caster=enhanced_action.caster,
            message=f"🎯 条件性效果: {', '.join(effect_messages)}",
            level="DEBUG"
        ))

def _handle_spirit_wisdom_boost(trigger_action: ConditionalEffectTriggerAction) -> List[BattleAction]:
    """处理灵目慧心战斗加成触发"""
    actions = []
    
    actions.append(LogAction(
        caster=trigger_action.caster,
        message=f"👁️ {getattr(trigger_action.caster, 'name', 'Unknown')} 的灵目慧心战斗加成生效！暴击率+40%、暴击伤害+40%、破击率+40%，获得30点气势！",
        level="INFO"
    ))
    
    return actions

def _handle_critical_hit_trigger(trigger_action: ConditionalEffectTriggerAction) -> List[BattleAction]:
    """处理暴击触发"""
    actions = []
    
    # 暴击可能触发额外效果
    actions.append(LogAction(
        caster=trigger_action.caster,
        message=f"💥 {getattr(trigger_action.caster, 'name', 'Unknown')} 的暴击触发了额外效果！",
        level="DEBUG"
    ))
    
    return actions

def _handle_execute_trigger(trigger_action: ConditionalEffectTriggerAction) -> List[BattleAction]:
    """处理斩杀触发"""
    actions = []
    
    actions.append(LogAction(
        caster=trigger_action.caster,
        message=f"⚔️ {getattr(trigger_action.caster, 'name', 'Unknown')} 的斩杀效果生效！对低血量目标造成额外伤害！",
        level="INFO"
    ))
    
    return actions

def _handle_combo_trigger(trigger_action: ConditionalEffectTriggerAction) -> List[BattleAction]:
    """处理连击触发"""
    actions = []
    
    actions.append(LogAction(
        caster=trigger_action.caster,
        message=f"🔄 {getattr(trigger_action.caster, 'name', 'Unknown')} 的连击效果生效！",
        level="INFO"
    ))
    
    return actions
