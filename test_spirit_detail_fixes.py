#!/usr/bin/env python3
"""
测试精灵详情修复效果

验证精灵属性是否正确显示
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_spirit_attributes():
    """测试精灵属性获取"""
    print("🔧 测试精灵属性获取...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"可用精灵: {available_spirits}")
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        # 测试每个精灵的属性
        for spirit_name in available_spirits[:2]:  # 只测试前两个
            print(f"\n📊 精灵: {spirit_name}")
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"  基本信息:")
            print(f"    名称: {spirit.name}")
            print(f"    生命值: {spirit.current_hp}/{spirit.max_hp}")
            print(f"    气势: {spirit.energy}/{spirit.max_energy}")
            
            # 测试属性获取
            if hasattr(spirit, 'attributes'):
                attrs = spirit.attributes
                print(f"  属性信息:")
                
                # 测试实际攻击力
                if hasattr(attrs, 'get_actual_attack'):
                    actual_attack = attrs.get_actual_attack(spirit)
                    print(f"    实际攻击力: {actual_attack}")
                else:
                    attack = getattr(attrs, 'attack', 0.0)
                    print(f"    面板攻击力: {attack}")
                
                # 测试其他属性
                pdef = getattr(attrs, 'pdef', 0.0)
                mdef = getattr(attrs, 'mdef', 0.0)
                speed = getattr(attrs, 'speed', 0.0)
                hit_rate = getattr(attrs, 'hit_rate', 0.0)
                dodge_rate = getattr(attrs, 'dodge_rate', 0.0)
                crit_rate = getattr(attrs, 'crit_rate', 0.0)
                crit_damage = getattr(attrs, 'crit_damage', 1.5)
                
                print(f"    物理防御: {pdef}")
                print(f"    魔法防御: {mdef}")
                print(f"    速度: {speed}")
                print(f"    命中率: {hit_rate}")
                print(f"    闪避率: {dodge_rate}")
                print(f"    暴击率: {crit_rate}")
                print(f"    暴击伤害: {crit_damage}")
            else:
                print("  ❌ 精灵没有 attributes 属性")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_spirit_snapshot_creation():
    """测试精灵快照创建"""
    print("\n🔧 测试精灵快照创建...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        # 使用修复后的快照创建逻辑
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        
        snapshot = recorder._create_spirit_snapshot(spirit)
        
        print(f"📊 修复后快照结果:")
        print(f"  名称: {snapshot.name}")
        print(f"  队伍: {snapshot.team}")
        print(f"  位置: {snapshot.position}")
        print(f"  生命值: {snapshot.current_hp}/{snapshot.max_hp}")
        print(f"  气势: {snapshot.energy}/{snapshot.max_energy}")
        print(f"  存活状态: {snapshot.is_alive}")
        print(f"  实际攻击力: {snapshot.actual_attack}")
        print(f"  实际防御力: {snapshot.actual_defense}")
        print(f"  速度: {snapshot.actual_speed}")
        print(f"  命中率: {snapshot.actual_hit_rate}")
        print(f"  闪避率: {snapshot.actual_dodge_rate}")
        print(f"  暴击率: {snapshot.actual_crit_rate}")
        print(f"  暴击伤害: {snapshot.actual_crit_damage}")
        print(f"  效果数量: {len(snapshot.effects)}")
        
        # 显示效果详情
        if snapshot.effects:
            print(f"  效果详情:")
            for effect in snapshot.effects:
                print(f"    - {effect['name']} ({effect['type']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 快照测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_detail_panel():
    """测试UI详情面板"""
    print("\n🔧 测试UI详情面板...")
    
    try:
        # 创建模拟的精灵快照
        from ui.ux.models.battle_record import SpiritSnapshot
        
        # 使用真实的精灵数据创建快照
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        snapshot = recorder._create_spirit_snapshot(spirit)
        
        # 测试精灵详情面板
        import tkinter as tk
        from ui.ux.components.spirit_detail_panel import SpiritDetailPanel
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        detail_panel = SpiritDetailPanel(root)
        detail_panel.update_spirit(snapshot)
        
        print("✅ 精灵详情面板测试成功")
        print(f"  精灵: {snapshot.name}")
        print(f"  实际攻击力: {snapshot.actual_attack}")
        print(f"  实际防御力: {snapshot.actual_defense}")
        print(f"  效果数量: {len(snapshot.effects)}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI详情面板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_battle_with_correct_attributes():
    """测试战斗中的正确属性显示"""
    print("\n🔧 测试战斗中的正确属性显示...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗创建成功")
        
        # 创建战斗记录器并测试
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        
        # 创建初始快照
        initial_snapshot = recorder.create_snapshot(engine.battle_state, 0)
        
        print(f"\n📝 战斗记录器测试:")
        print(f"  精灵数量: {len(initial_snapshot.spirits)}")
        
        for name, spirit_snapshot in initial_snapshot.spirits.items():
            print(f"    {name}:")
            print(f"      实际攻击力: {spirit_snapshot.actual_attack}")
            print(f"      实际防御力: {spirit_snapshot.actual_defense}")
            print(f"      速度: {spirit_snapshot.actual_speed}")
            print(f"      命中率: {spirit_snapshot.actual_hit_rate}")
            print(f"      闪避率: {spirit_snapshot.actual_dodge_rate}")
            print(f"      暴击率: {spirit_snapshot.actual_crit_rate}")
            print(f"      暴击伤害: {spirit_snapshot.actual_crit_damage}")
            print(f"      效果数量: {len(spirit_snapshot.effects)}")
            
            # 显示效果
            for effect in spirit_snapshot.effects:
                print(f"        - {effect['name']} ({effect['type']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 战斗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 精灵详情修复效果测试")
    print("="*60)
    
    tests = [
        ("精灵属性获取", test_spirit_attributes),
        ("精灵快照创建", test_spirit_snapshot_creation),
        ("UI详情面板", test_ui_detail_panel),
        ("战斗中属性显示", test_battle_with_correct_attributes),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！精灵详情修复成功")
        print("\n📋 修复内容:")
        print("  ✅ 实际攻击力正确获取（使用get_actual_attack方法）")
        print("  ✅ 防御力正确显示（使用pdef属性）")
        print("  ✅ 所有属性值正确计算")
        print("  ✅ 效果信息完整显示")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
