"""
动态更新调度器

管理效果和属性的动态更新，优化性能和避免循环依赖
"""
from __future__ import annotations
import time
import asyncio
import threading
from typing import Dict, List, Any, Callable, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum, IntEnum
from collections import deque, defaultdict
from concurrent.futures import ThreadPoolExecutor

from ...logging import get_logger

logger = get_logger("core.effect.reactive.update_scheduler")


class UpdatePriority(IntEnum):
    """更新优先级"""
    IMMEDIATE = 0      # 立即执行（如死亡检查）
    HIGH = 1          # 高优先级（如关键属性变化）
    NORMAL = 2        # 普通优先级（如一般效果更新）
    LOW = 3           # 低优先级（如统计更新）
    BACKGROUND = 4    # 后台执行（如日志记录）


class UpdateType(Enum):
    """更新类型"""
    ATTRIBUTE_CHANGE = "attribute_change"    # 属性变化
    EFFECT_UPDATE = "effect_update"          # 效果更新
    LIFECYCLE_EVENT = "lifecycle_event"      # 生命周期事件
    DEPENDENCY_CHECK = "dependency_check"    # 依赖检查
    BATCH_UPDATE = "batch_update"           # 批量更新


@dataclass
class UpdateTask:
    """更新任务"""
    task_id: str
    update_type: UpdateType
    priority: UpdatePriority
    callback: Callable[[], Any]
    target_id: str  # 目标对象ID
    created_time: float = field(default_factory=time.time)
    scheduled_time: float = 0  # 计划执行时间
    max_retries: int = 3
    retry_count: int = 0
    dependencies: Set[str] = field(default_factory=set)  # 依赖的任务ID
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.scheduled_time:
            self.scheduled_time = self.created_time
    
    @property
    def is_ready(self) -> bool:
        """检查任务是否准备执行"""
        return time.time() >= self.scheduled_time
    
    @property
    def age(self) -> float:
        """任务年龄（秒）"""
        return time.time() - self.created_time


class DynamicUpdateScheduler:
    """动态更新调度器
    
    管理所有的动态更新任务，提供优先级调度、批量处理、循环检测等功能
    """
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 任务队列（按优先级分组）
        self.task_queues: Dict[UpdatePriority, deque] = {
            priority: deque() for priority in UpdatePriority
        }
        
        # 任务管理
        self.pending_tasks: Dict[str, UpdateTask] = {}
        self.running_tasks: Dict[str, UpdateTask] = {}
        self.completed_tasks: deque = deque(maxlen=1000)  # 保留最近1000个完成的任务
        
        # 依赖关系图
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_dependencies: Dict[str, Set[str]] = defaultdict(set)
        
        # 批量处理
        self.batch_enabled = True
        self.batch_size = 10
        self.batch_timeout = 0.05  # 50ms
        self.batch_tasks: Dict[str, List[UpdateTask]] = defaultdict(list)  # 按target_id分组
        
        # 循环检测
        self.cycle_detection_enabled = True
        self.max_dependency_depth = 10
        
        # 调度器状态
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 性能统计
        self.stats = {
            "total_scheduled": 0,
            "total_executed": 0,
            "total_failed": 0,
            "total_retries": 0,
            "cycle_detections": 0,
            "batch_executions": 0,
            "average_execution_time": 0.0,
            "last_reset": time.time()
        }
    
    def start(self):
        """启动调度器"""
        if self.running:
            return
        
        self.running = True
        self.stop_event.clear()
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        logger.info("动态更新调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        self.stop_event.set()
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5.0)
        
        self.executor.shutdown(wait=True)
        logger.info("动态更新调度器已停止")
    
    def schedule_task(self, task: UpdateTask) -> bool:
        """调度任务
        
        Args:
            task: 更新任务
            
        Returns:
            是否成功调度
        """
        try:
            # 检查循环依赖
            if self.cycle_detection_enabled and self._has_cycle(task):
                logger.warning(f"检测到循环依赖，拒绝任务: {task.task_id}")
                self.stats["cycle_detections"] += 1
                return False
            
            # 添加到待处理队列
            self.pending_tasks[task.task_id] = task
            self.task_queues[task.priority].append(task)
            
            # 更新依赖关系图
            for dep_id in task.dependencies:
                self.dependency_graph[dep_id].add(task.task_id)
                self.reverse_dependencies[task.task_id].add(dep_id)
            
            self.stats["total_scheduled"] += 1
            logger.debug(f"任务已调度: {task.task_id} (优先级: {task.priority.name})")
            return True
            
        except Exception as e:
            logger.error(f"调度任务失败 {task.task_id}: {e}")
            return False
    
    def schedule_immediate(self, callback: Callable[[], Any], target_id: str, 
                          task_id: str = None, **metadata) -> str:
        """调度立即执行的任务
        
        Args:
            callback: 回调函数
            target_id: 目标对象ID
            task_id: 任务ID（可选）
            **metadata: 额外元数据
            
        Returns:
            任务ID
        """
        if not task_id:
            task_id = f"immediate_{target_id}_{int(time.time() * 1000000)}"
        
        task = UpdateTask(
            task_id=task_id,
            update_type=UpdateType.ATTRIBUTE_CHANGE,
            priority=UpdatePriority.IMMEDIATE,
            callback=callback,
            target_id=target_id,
            metadata=metadata
        )
        
        self.schedule_task(task)
        return task_id
    
    def schedule_batch(self, callbacks: List[Callable[[], Any]], target_id: str, 
                      priority: UpdatePriority = UpdatePriority.NORMAL, **metadata) -> str:
        """调度批量任务
        
        Args:
            callbacks: 回调函数列表
            target_id: 目标对象ID
            priority: 优先级
            **metadata: 额外元数据
            
        Returns:
            批量任务ID
        """
        batch_id = f"batch_{target_id}_{int(time.time() * 1000000)}"
        
        def batch_callback():
            results = []
            for callback in callbacks:
                try:
                    result = callback()
                    results.append(result)
                except Exception as e:
                    logger.error(f"批量任务中的回调失败: {e}")
                    results.append(None)
            return results
        
        task = UpdateTask(
            task_id=batch_id,
            update_type=UpdateType.BATCH_UPDATE,
            priority=priority,
            callback=batch_callback,
            target_id=target_id,
            metadata={**metadata, "batch_size": len(callbacks)}
        )
        
        self.schedule_task(task)
        return batch_id
    
    def _scheduler_loop(self):
        """调度器主循环"""
        logger.debug("调度器主循环开始")
        
        while self.running and not self.stop_event.is_set():
            try:
                # 处理批量任务
                if self.batch_enabled:
                    self._process_batch_tasks()
                
                # 执行准备好的任务
                self._execute_ready_tasks()
                
                # 清理完成的任务
                self._cleanup_completed_tasks()
                
                # 短暂休眠
                time.sleep(0.01)  # 10ms
                
            except Exception as e:
                logger.error(f"调度器循环异常: {e}")
                time.sleep(0.1)  # 异常时休眠更长时间
        
        logger.debug("调度器主循环结束")
    
    def _process_batch_tasks(self):
        """处理批量任务"""
        current_time = time.time()
        
        for target_id, tasks in list(self.batch_tasks.items()):
            if not tasks:
                continue
            
            # 检查批量条件
            should_execute = (
                len(tasks) >= self.batch_size or
                (tasks and current_time - tasks[0].created_time >= self.batch_timeout)
            )
            
            if should_execute:
                # 创建批量任务
                batch_callbacks = [task.callback for task in tasks]
                batch_id = self.schedule_batch(batch_callbacks, target_id)
                
                # 清空批量队列
                self.batch_tasks[target_id].clear()
                self.stats["batch_executions"] += 1
    
    def _execute_ready_tasks(self):
        """执行准备好的任务"""
        executed_count = 0
        max_executions_per_cycle = 20  # 限制每次循环的执行数量
        
        # 按优先级顺序处理
        for priority in UpdatePriority:
            queue = self.task_queues[priority]
            
            while queue and executed_count < max_executions_per_cycle:
                task = queue.popleft()
                
                # 检查任务是否准备好
                if not task.is_ready:
                    queue.append(task)  # 重新放回队列
                    break
                
                # 检查依赖是否满足
                if not self._dependencies_satisfied(task):
                    queue.append(task)  # 重新放回队列
                    continue
                
                # 执行任务
                self._execute_task(task)
                executed_count += 1
    
    def _dependencies_satisfied(self, task: UpdateTask) -> bool:
        """检查任务依赖是否满足"""
        for dep_id in task.dependencies:
            if dep_id in self.pending_tasks or dep_id in self.running_tasks:
                return False
        return True
    
    def _execute_task(self, task: UpdateTask):
        """执行单个任务"""
        try:
            # 移动到运行队列
            self.pending_tasks.pop(task.task_id, None)
            self.running_tasks[task.task_id] = task
            
            start_time = time.time()
            
            # 执行回调
            result = task.callback()
            
            execution_time = time.time() - start_time
            
            # 更新统计
            self.stats["total_executed"] += 1
            self._update_average_execution_time(execution_time)
            
            # 移动到完成队列
            self.running_tasks.pop(task.task_id, None)
            task.metadata["result"] = result
            task.metadata["execution_time"] = execution_time
            self.completed_tasks.append(task)
            
            logger.debug(f"任务执行完成: {task.task_id} ({execution_time:.3f}s)")
            
        except Exception as e:
            # 处理执行失败
            self._handle_task_failure(task, e)
    
    def _handle_task_failure(self, task: UpdateTask, error: Exception):
        """处理任务执行失败"""
        task.retry_count += 1
        self.stats["total_failed"] += 1
        
        logger.error(f"任务执行失败 {task.task_id} (重试 {task.retry_count}/{task.max_retries}): {error}")
        
        if task.retry_count < task.max_retries:
            # 重新调度
            task.scheduled_time = time.time() + (task.retry_count * 0.1)  # 指数退避
            self.running_tasks.pop(task.task_id, None)
            self.pending_tasks[task.task_id] = task
            self.task_queues[task.priority].append(task)
            self.stats["total_retries"] += 1
        else:
            # 放弃任务
            self.running_tasks.pop(task.task_id, None)
            task.metadata["error"] = str(error)
            self.completed_tasks.append(task)
    
    def _has_cycle(self, task: UpdateTask) -> bool:
        """检查是否存在循环依赖"""
        visited = set()
        rec_stack = set()
        
        def dfs(task_id: str, depth: int = 0) -> bool:
            if depth > self.max_dependency_depth:
                return True  # 深度过大，认为有循环
            
            if task_id in rec_stack:
                return True  # 发现循环
            
            if task_id in visited:
                return False
            
            visited.add(task_id)
            rec_stack.add(task_id)
            
            # 检查所有依赖
            for dep_id in self.dependency_graph.get(task_id, set()):
                if dfs(dep_id, depth + 1):
                    return True
            
            rec_stack.remove(task_id)
            return False
        
        return dfs(task.task_id)
    
    def _cleanup_completed_tasks(self):
        """清理完成的任务"""
        # 清理依赖关系图中的完成任务
        for task in list(self.completed_tasks):
            if task.task_id in self.dependency_graph:
                del self.dependency_graph[task.task_id]
            if task.task_id in self.reverse_dependencies:
                del self.reverse_dependencies[task.task_id]
    
    def _update_average_execution_time(self, execution_time: float):
        """更新平均执行时间"""
        current_avg = self.stats["average_execution_time"]
        total_executed = self.stats["total_executed"]
        
        if total_executed == 1:
            self.stats["average_execution_time"] = execution_time
        else:
            # 使用移动平均
            self.stats["average_execution_time"] = (current_avg * 0.9) + (execution_time * 0.1)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "pending_tasks": len(self.pending_tasks),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "dependency_graph_size": len(self.dependency_graph),
            "batch_queues": {target_id: len(tasks) for target_id, tasks in self.batch_tasks.items() if tasks}
        }
    
    def clear_all_tasks(self):
        """清空所有任务"""
        self.pending_tasks.clear()
        self.running_tasks.clear()
        self.completed_tasks.clear()
        for queue in self.task_queues.values():
            queue.clear()
        self.dependency_graph.clear()
        self.reverse_dependencies.clear()
        self.batch_tasks.clear()
        logger.info("已清空所有任务")

    def __del__(self):
        """析构函数"""
        self.stop()
