#!/usr/bin/env python3
"""
测试最终修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_final_fix():
    """测试最终修复"""
    print("🔧 测试最终修复...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=5, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 检查初始状态
        print(f"\n📋 检查初始状态:")
        print(f"  伏妖效果数量: {len(fuyao_spirit.effect_manager.effects)}")
        print(f"  目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 检查被动效果的首次攻击状态
        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
            if hasattr(effect, 'first_attack_used'):
                print(f"  {effect.name} 首次攻击已使用: {effect.first_attack_used}")
        
        # 执行几个回合
        print(f"\n📋 执行战斗回合:")
        
        for round_num in range(3):
            print(f"\n  === 回合 {round_num + 1} ===")
            
            # 执行一次精灵回合
            result = engine.execute_next_spirit_turn()
            
            if result.get("type") == "spirit_turn":
                spirit_name = result.get("spirit_name", "Unknown")
                print(f"    执行精灵: {spirit_name}")
                
                # 检查目标效果
                target_effects = len(other_spirit.effect_manager.effects)
                print(f"    目标效果数量: {target_effects}")
                
                if target_effects > 0:
                    print(f"    目标效果:")
                    for effect_id, effect in other_spirit.effect_manager.effects.items():
                        effect_name = getattr(effect, 'name', 'Unknown')
                        remaining = getattr(effect, 'remaining_duration', 'Unknown')
                        print(f"      - {effect_name} (剩余: {remaining})")
                
                # 检查被动效果状态
                for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                    if hasattr(effect, 'first_attack_used'):
                        print(f"    {effect.name} 首次攻击已使用: {effect.first_attack_used}")
                
                # 如果目标获得了效果，说明修复成功
                if target_effects > 0:
                    print(f"\n✅ 修复成功！被动效果成功触发并持续存在！")
                    return True
            
            elif result.get("type") == "battle_end":
                print(f"    战斗结束")
                break
            else:
                print(f"    其他结果: {result.get('type', 'Unknown')}")
        
        # 检查最终状态
        print(f"\n📋 检查最终状态:")
        target_effects_final = len(other_spirit.effect_manager.effects)
        print(f"  目标效果数量: {target_effects_final}")
        
        if target_effects_final > 0:
            print(f"  目标效果:")
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                remaining = getattr(effect, 'remaining_duration', 'Unknown')
                print(f"    - {effect_name} (剩余: {remaining})")
        
        # 检查被动效果状态
        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
            if hasattr(effect, 'first_attack_used'):
                print(f"  {effect.name} 首次攻击已使用: {effect.first_attack_used}")
        
        # 判断结果
        if target_effects_final > 0:
            print(f"\n✅ 最终修复测试成功！")
            print(f"  - 被动效果成功触发")
            print(f"  - 虚无效果成功应用并持续存在")
            return True
        else:
            print(f"\n❌ 最终修复测试失败")
            print(f"  - 被动效果没有触发或虚无效果被错误移除")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 最终修复测试")
    print("="*60)
    
    result = test_final_fix()
    
    print("\n" + "="*60)
    if result:
        print("✅ 最终修复测试成功")
        print("🎉 问题1和问题2都已完全解决！")
        print("")
        print("✅ 问题1修复：被动技能不再生成动作")
        print("✅ 问题2修复：攻击成功触发被动效果")
    else:
        print("❌ 最终修复测试失败")

if __name__ == "__main__":
    main()
