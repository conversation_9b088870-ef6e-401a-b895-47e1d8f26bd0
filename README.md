# 🎮 AoQiAI - 奥奇传说AI战斗系统

[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://python.org)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.0+-green.svg)](https://vuejs.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](#)

一个基于现代架构设计的奥奇传说AI战斗系统，采用模块化设计，支持实时战斗、AI对战、技能系统和效果管理。

## ✨ 特性

### 🏗️ 核心系统
- **🎯 战斗引擎**: 高性能的模块化战斗系统
- **🧠 AI系统**: 智能的战斗决策和策略
- **⚡ 技能系统**: 灵活的组件化技能架构
- **🔮 效果系统**: 完整的状态效果管理
- **📊 事件系统**: 高效的事件驱动架构

### 🎮 游戏功能
- **👾 精灵系统**: 丰富的精灵属性和能力
- **🏟️ 阵型系统**: 策略性的阵型布局
- **🎪 实时战斗**: 流畅的战斗体验
- **📈 数据统计**: 详细的战斗数据分析
- **🎬 战斗回放**: 完整的战斗记录系统

### 🛠️ 技术特性
- **🔧 模块化设计**: 高度解耦的系统架构
- **🚀 高性能**: 优化的算法和数据结构
- **🔒 类型安全**: 完整的类型注解和检查
- **🧪 测试覆盖**: 全面的单元测试和集成测试
- **📚 文档完整**: 详细的API文档和使用指南

## 🚀 快速开始

### 环境要求

- Python 3.12+
- Node.js 16+
- Vue.js 3.0+

### 安装

1. **克隆项目**
```bash
git clone https://github.com/your-username/aoqiai.git
cd aoqiai
```

2. **安装Python依赖**
```bash
pip install -r requirements.txt
```

3. **安装前端依赖**
```bash
cd frontend
npm install
cd ..
```

4. **初始化系统**
```bash
python -c "from core.system_manager import initialize_core_systems; initialize_core_systems()"
```

### 运行示例

#### 🎮 简单战斗演示
```bash
python simple_battle.py
```

#### 🌐 启动Web服务器
```bash
python run_server.py
```

#### 🧪 运行测试
```bash
python run_tests.py
```

## 📖 文档

### 📚 核心文档
- [🏗️ 系统架构](docs/architecture.md) - 整体架构设计
- [⚔️ 战斗系统](docs/battle-system.md) - 战斗引擎详解
- [🎯 技能系统](docs/skill-system.md) - 技能组件架构
- [🔮 效果系统](docs/effect-system.md) - 状态效果管理
- [📊 事件系统](docs/event-system.md) - 事件驱动架构

### 🛠️ 开发文档
- [🚀 快速开始](docs/getting-started.md) - 开发环境搭建
- [📝 API参考](docs/api-reference.md) - 完整API文档
- [🧪 测试指南](docs/testing.md) - 测试框架和规范
- [🔧 配置说明](docs/configuration.md) - 系统配置详解

### 🎮 游戏文档
- [👾 精灵系统](docs/spirit-system.md) - 精灵属性和能力
- [🏟️ 阵型系统](docs/formation-system.md) - 阵型布局策略
- [🧠 AI系统](docs/ai-system.md) - AI决策机制
- [📈 数据分析](docs/data-analysis.md) - 战斗数据统计

## 🏗️ 项目结构

```
aoqiai/
├── 📁 src/                    # 源代码
│   ├── 📁 core/              # 核心系统
│   │   ├── 📁 battle/        # 战斗系统
│   │   ├── 📁 skill/         # 技能系统
│   │   ├── 📁 effect/        # 效果系统
│   │   ├── 📁 event/         # 事件系统
│   │   └── 📁 spirit/        # 精灵系统
│   ├── 📁 api/               # API接口
│   └── 📁 ai/                # AI系统
├── 📁 spirits_data/          # 精灵数据
├── 📁 frontend/              # 前端界面
├── 📁 docs/                  # 项目文档
├── 📁 tests/                 # 测试代码
├── 📁 config/                # 配置文件
└── 📁 data/                  # 游戏数据
```

## 🎯 核心系统概览

### ⚔️ 战斗系统
- **RefactoredBattleEngine**: 重构的战斗引擎
- **ActionExecutor**: 动作执行器
- **BattleState**: 战斗状态管理
- **DamageCalculator**: 伤害计算系统

### 🎯 技能系统
- **Skill**: 灵活的技能架构
- **SkillComponent**: 技能组件系统
- **TargetSelector**: 目标选择器
- **SkillFactory**: 技能工厂模式

### 🔮 效果系统
- **IEffect**: 效果接口
- **EffectManager**: 效果管理器
- **TriggerCondition**: 触发条件
- **EffectFactory**: 效果工厂

### 📊 事件系统
- **TriggerManager**: 事件管理器
- **GameEvent**: 游戏事件基类
- **EventDispatcher**: 事件分发器

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

### 🐛 报告问题
- 使用 [GitHub Issues](https://github.com/your-username/aoqiai/issues)
- 提供详细的错误信息和复现步骤

### 💡 功能建议
- 在 Issues 中提出功能请求
- 详细描述功能需求和使用场景

### 🔧 代码贡献
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和社区成员！

## 📞 联系我们

- 📧 Email: <EMAIL>
- 💬 Discord: [项目Discord](https://discord.gg/your-server)
- 🐦 Twitter: [@your-twitter](https://twitter.com/your-twitter)

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
