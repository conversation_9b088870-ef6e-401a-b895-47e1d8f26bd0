<template>
  <div class="battle-grid">
    <div
      v-for="(position, index) in gridPositions"
      :key="index"
      class="grid-cell"
      :class="getCellClass(position)"
      @click="onCellClick(position)"
      @dragover.prevent="onDragOver($event, position)"
      @drop="onDrop($event, position)"
      @dragenter.prevent
      @dragleave="onDragLeave($event, position)"
    >
      <!-- 精灵简版显示 -->
      <div
        v-if="getSpiritAtPosition(position)"
        class="spirit-display"
        draggable="true"
        @click.stop="onSpiritClick(getSpiritAtPosition(position)!)"
        @contextmenu.prevent.stop="onSpiritContextMenu(getSpiritAtPosition(position)!)"
        @dragstart.stop="onSpiritDragStart(getSpiritAtPosition(position)!, $event)"
        @dragend.stop="onSpiritDragEnd(getSpiritAtPosition(position)!)"
      >
        <div class="spirit-avatar">
          {{ getAvatarText(getSpiritAtPosition(position)!) }}
        </div>
        <span class="spirit-name">{{ getSpiritAtPosition(position)!.name }}</span>
      </div>
      
      <!-- 空位置编号 -->
      <div
        v-else
        class="empty-slot"
        :class="{ 'highlight': isHighlighted(position) }"
      >
        <span class="cell-number">{{ getCellNumber(position) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Spirit } from '../../types/battle'

interface Props {
  team: 1 | 2
  spirits: Spirit[]
  selectedSpirit?: Spirit | null
  highlightedPositions?: [number, number][]
}

interface Emits {
  (e: 'spirit-select', payload: { spirit: Spirit; team: 1 | 2 }): void
  (e: 'position-click', payload: { position: [number, number]; team: 1 | 2 }): void
  (e: 'spirit-move', payload: { spirit: Spirit; newPosition: [number, number] }): void
  (e: 'spirit-remove', payload: { spirit: Spirit; team: 1 | 2 }): void
}

const props = withDefaults(defineProps<Props>(), {
  selectedSpirit: null,
  highlightedPositions: () => []
})

const emit = defineEmits<Emits>()

// 3x2网格位置
const gridPositions = computed(() => {
  const positions: [number, number][] = []
  for (let row = 0; row < 3; row++) {
    for (let col = 0; col < 3; col++) {
      positions.push([row, col])
    }
  }
  return positions
})

// 方法
const getSpiritAtPosition = (position: [number, number]) => {
  return props.spirits.find(spirit => 
    spirit.position && spirit.position[0] === position[0] && spirit.position[1] === position[1]
  )
}

const isSelected = (spirit: Spirit | undefined) => {
  if (!spirit || !props.selectedSpirit) return false
  return spirit.id === props.selectedSpirit.id
}

const isHighlighted = (position: [number, number]) => {
  return props.highlightedPositions.some(pos => 
    pos[0] === position[0] && pos[1] === position[1]
  )
}

/**
 * 获取精灵头像中显示的文本，默认取名称首字符。
 * @param spirit 目标精灵实例
 */
const getAvatarText = (spirit: Spirit) => {
  return spirit.name ? spirit.name.charAt(0) : ''
}

const getCellClass = (position: [number, number]) => {
  const spirit = getSpiritAtPosition(position)
  return {
    'has-spirit': !!spirit,
    'spirit-alive': spirit?.isAlive,
    'spirit-dead': spirit && !spirit.isAlive,
    'team-1': props.team === 1,
    'team-2': props.team === 2
  }
}

/**
 * 计算空格编号：按列优先（自右向左），列内从上到下。
 * 公式: (2 - col) * 3 + row + 1  (行列均0-2)
 */
const getCellNumber = (position: [number, number]) => {
  const [row, col] = position
  if (props.team === 1) {
    // 队伍1：右→左列序
    return (2 - col) * 3 + row + 1
  } else {
    // 队伍2：左→右列序
    return col * 3 + row + 1
  }
}

const onCellClick = (position: [number, number]) => {
  const spirit = getSpiritAtPosition(position)
  if (spirit) {
    onSpiritClick(spirit)
  } else {
    emit('position-click', { position: position, team: props.team })
  }
}

const onSpiritClick = (spirit: Spirit) => {
  emit('spirit-select', { spirit: spirit, team: props.team })
}

const onSpiritRemove = (spirit: Spirit) => {
  emit('spirit-remove', { spirit: spirit, team: props.team })
}

const onSpiritContextMenu = (spirit: Spirit) => {
  // 右键菜单处理 - 直接移除精灵
  emit('spirit-remove', { spirit: spirit, team: props.team })
}

// 拖拽功能
const onSpiritDragStart = (spirit: Spirit, event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    const payload = {
      spiritId: spirit.id,
      spiritName: spirit.name,
      spiritTeam: spirit.team,
    };
    event.dataTransfer.setData('application/json', JSON.stringify(payload));
  }
}

const onSpiritDragEnd = (spirit: Spirit) => {
  // 精灵拖拽结束时的处理
  console.log('Spirit drag end:', spirit.name)
}

const onDragOver = (event: DragEvent, position: [number, number]) => {
  event.preventDefault()
  // 检查是否可以放置在此位置
  const existingSpirit = getSpiritAtPosition(position)
  if (!existingSpirit) {
    event.dataTransfer!.dropEffect = 'move'
  } else {
    event.dataTransfer!.dropEffect = 'none'
  }
}

const onDragLeave = (event: DragEvent, position: [number, number]) => {
  // 拖拽离开格子时的处理
}

const onDrop = (event: DragEvent, position: [number, number]) => {
  event.preventDefault()
  console.log('Drop event triggered at position:', position)
  
  if (event.dataTransfer) {
    const spiritData = event.dataTransfer.getData('application/json')
    console.log('Received drag data:', spiritData)
    
    if (spiritData) {
      try {
        const dragData = JSON.parse(spiritData)
        console.log('Parsed drag data:', dragData)
        
        // 检查目标位置是否为空（只检查当前队伍）
        const existingSpirit = getSpiritAtPosition(position)
        if (existingSpirit) {
          console.log('Position occupied by same team spirit:', existingSpirit.name)
          return
        }
        
        const spirit = props.spirits.find(s => s.id === dragData.spiritId)
        console.log('Found spirit:', spirit?.name)
        
        if (spirit) {
          console.log('Emitting spirit-move event')
          emit('spirit-move', { spirit: spirit, newPosition: position })
        } else {
          console.log('Spirit not found in current team spirits')
        }
      } catch (error) {
        console.error('Error parsing drag data:', error)
      }
    } else {
      console.log('No drag data received')
    }
  }
}
</script>

<style scoped>
/* 本地样式最小化，主要使用全局样式 */
.grid-cell:hover {
  @apply border-purple-400 bg-purple-500/20;
}

.grid-cell.has-spirit {
  @apply border-solid;
}

.grid-cell.team-1.has-spirit {
  @apply border-blue-400 bg-blue-500/20;
}

.grid-cell.team-2.has-spirit {
  @apply border-red-400 bg-red-500/20;
}

.grid-cell.spirit-dead {
  @apply border-gray-500 bg-gray-500/20;
}

.empty-slot.highlight {
  @apply bg-yellow-500/20 border-yellow-400 text-yellow-400;
}

.spirit-chip {
  @apply w-full h-full flex items-center justify-center p-2 rounded-lg text-white font-bold text-center cursor-pointer transition-colors;
}

.spirit-chip .spirit-name {
  @apply text-sm leading-tight;
}

/* 拖拽样式 */
.grid-cell[draggable="true"] {
  cursor: grab;
}

.grid-cell[draggable="true"]:active {
  cursor: grabbing;
}
</style>

<style scoped>
.cell-number {
  @apply text-slate-400 text-base font-medium;
}
</style>