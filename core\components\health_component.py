"""
生命值组件

管理精灵的生命值相关功能，包括当前生命值、最大生命值、伤害处理、治疗和复活。
"""
from __future__ import annotations
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..spirit.spirit import Spirit

from ..logging import spirit_logger


class HealthComponent:
    """生命值组件 - 管理精灵的生命值系统"""
    
    def __init__(self, owner: 'Spirit', max_hp: float):
        """
        初始化生命值组件
        
        Args:
            owner: 拥有此组件的精灵
            max_hp: 最大生命值
        """
        self.owner = owner
        self._max_hp = max_hp
        self._current_hp = max_hp
    
    @property
    def current_hp(self) -> float:
        """当前生命值"""
        return self._current_hp
    
    @current_hp.setter
    def current_hp(self, value: float) -> None:
        """设置当前生命值"""
        self._current_hp = max(0.0, min(value, self._max_hp))
    
    @property
    def max_hp(self) -> float:
        """最大生命值"""
        return self._max_hp
    
    @max_hp.setter
    def max_hp(self, value: float) -> None:
        """设置最大生命值"""
        self._max_hp = max(1.0, value)  # 最大生命值至少为1
        # 如果当前生命值超过新的最大值，调整当前生命值
        if self._current_hp > self._max_hp:
            self._current_hp = self._max_hp
    
    @property
    def hp_percentage(self) -> float:
        """生命值百分比 (0.0 - 1.0)"""
        return self._current_hp / self._max_hp if self._max_hp > 0 else 0.0
    
    @property
    def is_alive(self) -> bool:
        """是否存活"""
        return self._current_hp > 0
    
    @property
    def is_full_hp(self) -> bool:
        """是否满血"""
        return self._current_hp >= self._max_hp
    
    def take_damage(self, damage: float, is_destruction: bool = False) -> float:
        """
        承受伤害

        Args:
            damage: 伤害值
            is_destruction: 是否为毁灭伤害（无视减伤和免疫）

        Returns:
            实际造成的伤害值
        """
        if damage <= 0:
            return 0.0

        # 记录伤害前的生命值
        old_hp = self._current_hp

        # 应用伤害
        self._current_hp = max(0.0, self._current_hp - damage)

        # 计算实际伤害
        actual_damage = old_hp - self._current_hp

        # 记录日志
        if actual_damage > 0:
            spirit_logger.debug(
                f"{self.owner.name} 受到 {actual_damage:.1f} 点伤害 "
                f"(HP: {old_hp:.1f} -> {self._current_hp:.1f})"
            )

            # 🆕 动态死亡检测
            self._trigger_dynamic_death_check(old_hp, self._current_hp)

        return actual_damage

    def _trigger_dynamic_death_check(self, old_hp: float, new_hp: float):
        """触发动态死亡检测"""
        try:
            from ..death import monitor_spirit_hp_change
            monitor_spirit_hp_change(self.owner, old_hp, new_hp)
        except ImportError:
            # 如果动态死亡系统不可用，回退到传统日志
            if new_hp <= 0 and old_hp > 0:
                spirit_logger.info(f"{self.owner.name} 死亡")
    
    def heal(self, amount: float) -> float:
        """
        治疗
        
        Args:
            amount: 治疗量
            
        Returns:
            实际治疗量
        """
        if amount <= 0 or self._current_hp <= 0:
            return 0.0
        
        # 记录治疗前的生命值
        old_hp = self._current_hp
        
        # 应用治疗
        self._current_hp = min(self._max_hp, self._current_hp + amount)
        
        # 计算实际治疗量
        actual_heal = self._current_hp - old_hp
        
        # 记录日志
        if actual_heal > 0:
            spirit_logger.debug(
                f"{self.owner.name} 恢复 {actual_heal:.1f} 点生命值 "
                f"(HP: {old_hp:.1f} -> {self._current_hp:.1f})"
            )

            # 🆕 动态死亡检测（可能从死亡复活）
            self._trigger_dynamic_death_check(old_hp, self._current_hp)

        return actual_heal
    
    def revive(self, hp_percent: float = 1.0) -> None:
        """
        复活
        
        Args:
            hp_percent: 复活后的生命值百分比 (0.0 - 1.0)
        """
        hp_percent = max(0.0, min(1.0, hp_percent))
        old_hp = self._current_hp
        self._current_hp = self._max_hp * hp_percent
        
        spirit_logger.info(
            f"{self.owner.name} 复活 "
            f"(HP: {old_hp:.1f} -> {self._current_hp:.1f})"
        )
    
    def set_hp_directly(self, value: float) -> None:
        """
        直接设置生命值（用于特殊情况，不记录日志）
        
        Args:
            value: 新的生命值
        """
        self._current_hp = max(0.0, min(value, self._max_hp))
    
    def get_missing_hp(self) -> float:
        """获取缺失的生命值"""
        return self._max_hp - self._current_hp
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"HealthComponent(HP: {self._current_hp:.1f}/{self._max_hp:.1f})"
