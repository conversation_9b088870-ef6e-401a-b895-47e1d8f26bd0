// 组件样式

// 按钮样式
.btn-gradient {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border: none;
  color: white;
  transition: all $transition-normal;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

// 卡片样式
.card-glass {
  @extend .glass-effect;
  border-radius: $radius-lg;
  padding: $spacing-lg;
  transition: all $transition-normal;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-lg;
  }
}

// 精灵卡片
.spirit-card {
  @extend .card-glass;
  position: relative;
  overflow: hidden;
  
  &.selected {
    border-color: $primary-color;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  
  &.dead {
    opacity: 0.5;
    filter: grayscale(100%);
  }
  
  .spirit-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, $primary-color, $secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: $spacing-sm;
  }
  
  .spirit-name {
    font-weight: 600;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }
  
  .spirit-level {
    font-size: 0.875rem;
    color: $text-secondary;
  }
  
  .hp-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-top: $spacing-sm;
    
    .hp-fill {
      height: 100%;
      background: linear-gradient(90deg, $success-color, $warning-color, $error-color);
      transition: width $transition-normal;
    }
  }
}

// 战斗网格
.battle-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $spacing-md;
  padding: $spacing-lg;
  background: rgba(0, 0, 0, 0.2);
  border-radius: $radius-lg;
  border: 2px solid $border-color;
  
  .grid-cell {
    aspect-ratio: 1;
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: $radius-md;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all $transition-fast;
    cursor: pointer;
    
    &:hover {
      border-color: $primary-color;
      background: rgba(139, 92, 246, 0.1);
    }
    
    &.occupied {
      border-style: solid;
      border-color: $primary-color;
    }
    
    &.valid-drop {
      border-color: $success-color;
      background: rgba(16, 185, 129, 0.1);
    }
    
    &.invalid-drop {
      border-color: $error-color;
      background: rgba(239, 68, 68, 0.1);
    }
  }
}

// 技能面板
.skill-panel {
  .skill-item {
    @extend .card-glass;
    margin-bottom: $spacing-sm;
    padding: $spacing-sm;
    cursor: pointer;
    
    &.on-cooldown {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.active {
      border-color: $primary-color;
      background: rgba(139, 92, 246, 0.2);
    }
    
    .skill-icon {
      width: 32px;
      height: 32px;
      border-radius: $radius-sm;
      background: $primary-color;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
    }
    
    .skill-name {
      font-weight: 600;
      color: $text-primary;
    }
    
    .skill-description {
      font-size: 0.875rem;
      color: $text-secondary;
      margin-top: $spacing-xs;
    }
    
    .cooldown-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      border-radius: $radius-sm;
    }
  }
}

// 效果指示器
.effect-indicator {
  display: inline-flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-sm;
  font-size: 0.75rem;
  font-weight: 600;
  margin: 2px;
  
  &.buff {
    background: rgba(16, 185, 129, 0.2);
    color: $success-color;
    border: 1px solid $success-color;
  }
  
  &.debuff {
    background: rgba(239, 68, 68, 0.2);
    color: $error-color;
    border: 1px solid $error-color;
  }
  
  &.neutral {
    background: rgba(59, 130, 246, 0.2);
    color: $info-color;
    border: 1px solid $info-color;
  }
  
  .effect-duration {
    margin-left: $spacing-xs;
    opacity: 0.8;
  }
}