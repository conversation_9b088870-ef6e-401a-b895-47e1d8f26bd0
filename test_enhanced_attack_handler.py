#!/usr/bin/env python3
"""
测试EnhancedAttackAction处理器注册

验证EnhancedAttackAction处理器是否正确注册和工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_attack_handler_registration():
    """测试EnhancedAttackAction处理器注册"""
    print("🔧 测试EnhancedAttackAction处理器注册...")
    
    try:
        # 导入必要的模块
        from core.battle.execution import _handler_registry, enhanced_actions
        from core.action import EnhancedAttackAction
        
        print(f"✅ 成功导入enhanced_actions模块")
        print(f"📊 当前注册的处理器数量: {len(_handler_registry)}")
        
        # 检查EnhancedAttackAction是否已注册
        if EnhancedAttackAction in _handler_registry:
            handler_func = _handler_registry[EnhancedAttackAction]
            print(f"✅ EnhancedAttackAction处理器已注册: {handler_func.__name__}")
            return True
        else:
            print("❌ EnhancedAttackAction处理器未注册")
            print("📋 已注册的动作类型:")
            for action_type in _handler_registry.keys():
                print(f"  - {action_type.__name__}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_attack_creation():
    """测试EnhancedAttackAction创建"""
    print("\n🔧 测试EnhancedAttackAction创建...")
    
    try:
        from core.action import EnhancedAttackAction
        
        # 创建模拟精灵
        class MockSpirit:
            def __init__(self, name):
                self.name = name
                self.id = name.lower()
                self.team = 0
                self.current_hp = 1000
                self.max_hp = 1000
                self.is_alive = True
        
        # 创建模拟技能
        class MockSkill:
            def __init__(self, name):
                self.name = name
                self.base_damage = 100
            
            def get_base_damage(self, caster):
                return self.base_damage
        
        caster = MockSpirit("攻击者")
        target = MockSpirit("目标")
        skill = MockSkill("测试技能")
        
        # 创建EnhancedAttackAction
        enhanced_action = EnhancedAttackAction(
            caster=caster,
            target=target,
            skill=skill,
            conditional_effects={
                'damage_multiplier': 1.5,
                'critical_hit': True,
                'pierce_hit': True
            }
        )
        
        print(f"✅ EnhancedAttackAction创建成功")
        print(f"  - 攻击者: {enhanced_action.caster.name}")
        print(f"  - 目标: {enhanced_action.target.name}")
        print(f"  - 技能: {enhanced_action.skill.name}")
        print(f"  - 条件性效果: {enhanced_action.conditional_effects}")
        print(f"  - 增强伤害: {enhanced_action.get_enhanced_damage()}")
        
        return True
        
    except Exception as e:
        print(f"❌ EnhancedAttackAction创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_attack_execution():
    """测试EnhancedAttackAction执行"""
    print("\n🔧 测试EnhancedAttackAction执行...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗状态
        from core.battle.models import BattleState
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        battle_state = BattleState(formation1, formation2)
        
        # 创建执行器
        from core.battle.execution import UnifiedActionExecutor
        from core.battle.conditions import KnockoutCondition
        
        executor = UnifiedActionExecutor(
            battle_state=battle_state,
            condition_checker=KnockoutCondition(),
            battle_log=[]
        )
        
        # 创建测试精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        if not spirit1 or not spirit2:
            print("❌ 精灵创建失败")
            return False
        
        # 创建模拟技能
        class MockSkill:
            def __init__(self, name):
                self.name = name
                self.base_damage = 200
            
            def get_base_damage(self, caster):
                return self.base_damage
        
        skill = MockSkill("测试增强攻击")
        
        # 创建EnhancedAttackAction
        from core.action import EnhancedAttackAction
        enhanced_action = EnhancedAttackAction(
            caster=spirit1,
            target=spirit2,
            skill=skill,
            conditional_effects={
                'damage_multiplier': 1.2,
                'critical_hit': True
            }
        )
        
        print(f"✅ 创建增强攻击动作")
        print(f"  - 攻击者: {spirit1.name} (HP: {spirit1.current_hp})")
        print(f"  - 目标: {spirit2.name} (HP: {spirit2.current_hp})")
        print(f"  - 增强伤害: {enhanced_action.get_enhanced_damage()}")
        
        # 执行动作
        result_actions = executor.execute_actions([enhanced_action])
        
        print(f"✅ EnhancedAttackAction执行成功")
        if result_actions:
            print(f"  - 生成了 {len(result_actions)} 个后续动作")
            for i, action in enumerate(result_actions, 1):
                print(f"    {i}. {action.__class__.__name__}")
        else:
            print("  - 没有生成后续动作")
        
        return True
        
    except Exception as e:
        print(f"❌ EnhancedAttackAction执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 EnhancedAttackAction处理器测试")
    print("="*60)
    
    tests = [
        ("处理器注册测试", test_enhanced_attack_handler_registration),
        ("动作创建测试", test_enhanced_attack_creation),
        ("动作执行测试", test_enhanced_attack_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！EnhancedAttackAction处理器工作正常！")
        print("\n📋 验证结果:")
        print("  ✅ EnhancedAttackAction处理器已正确注册")
        print("  ✅ EnhancedAttackAction可以正常创建")
        print("  ✅ EnhancedAttackAction可以正常执行")
        print("  ✅ 增强动作系统完全集成")
        print("\n🚀 AI战斗系统中的EnhancedAttackAction现在完全可用！")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
