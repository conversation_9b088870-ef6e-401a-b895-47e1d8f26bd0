#!/usr/bin/env python3
"""
效果验证器

提供效果验证和错误检查功能。
"""

from typing import List, Dict, Any, Optional, Tuple
from .system import IEffect, EffectManager, EffectResult


class EffectValidationError(Exception):
    """效果验证错误"""
    def __init__(self, message: str, effect_id: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.effect_id = effect_id
        self.details = details or {}


class EffectValidator:
    """效果验证器"""
    
    def __init__(self):
        self.validation_rules: Dict[str, callable] = {}
        self._register_default_rules()
    
    def _register_default_rules(self):
        """注册默认验证规则"""
        self.validation_rules.update({
            'effect_id_not_empty': self._validate_effect_id,
            'effect_name_not_empty': self._validate_effect_name,
            'duration_valid': self._validate_duration,
            'stacks_valid': self._validate_stacks,
            'target_compatible': self._validate_target_compatibility,
            'no_conflicts': self._validate_no_conflicts,
            'dependencies_met': self._validate_dependencies,
        })
    
    def _validate_effect_id(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证效果ID不为空"""
        if not effect.id or not effect.id.strip():
            return False, "效果ID不能为空"
        return True, ""
    
    def _validate_effect_name(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证效果名称不为空"""
        if not effect.name or not effect.name.strip():
            return False, "效果名称不能为空"
        return True, ""
    
    def _validate_duration(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证持续时间有效"""
        if effect.duration < -1:
            return False, "持续时间不能小于-1"
        if effect.remaining_duration < -1:
            return False, "剩余持续时间不能小于-1"
        if effect.duration > 0 and effect.remaining_duration > effect.duration:
            return False, "剩余持续时间不能大于总持续时间"
        return True, ""
    
    def _validate_stacks(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证层数有效"""
        if effect.current_stacks < 0:
            return False, "当前层数不能小于0"
        if effect.max_stacks < 1:
            return False, "最大层数不能小于1"
        if effect.current_stacks > effect.max_stacks:
            return False, "当前层数不能大于最大层数"
        return True, ""
    
    def _validate_target_compatibility(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证目标兼容性"""
        if target is None:
            return True, ""  # 没有目标时跳过验证
        
        if not effect.can_apply_to(target):
            return False, f"效果 {effect.name} 无法应用到目标 {getattr(target, 'name', 'Unknown')}"
        return True, ""
    
    def _validate_no_conflicts(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证无冲突效果"""
        if target is None or not hasattr(target, 'effect_manager'):
            return True, ""
        
        effect_manager = getattr(target, 'effect_manager', None)
        if not effect_manager or not hasattr(effect_manager, 'effects'):
            return True, ""
        
        for existing_effect in effect_manager.effects.values():
            if (existing_effect.id in effect.conflicts or 
                effect.id in existing_effect.conflicts):
                return False, f"效果 {effect.name} 与现有效果 {existing_effect.name} 冲突"
        
        return True, ""
    
    def _validate_dependencies(self, effect: IEffect, target=None, battle_state=None) -> Tuple[bool, str]:
        """验证依赖关系"""
        if not effect.dependencies:
            return True, ""
        
        if target is None or not hasattr(target, 'effect_manager'):
            return False, "目标缺少效果管理器，无法验证依赖关系"
        
        effect_manager = getattr(target, 'effect_manager', None)
        if not effect_manager or not hasattr(effect_manager, 'effects'):
            return False, "效果管理器无效，无法验证依赖关系"
        
        existing_effect_ids = set(effect_manager.effects.keys())
        missing_dependencies = effect.dependencies - existing_effect_ids
        
        if missing_dependencies:
            return False, f"缺少依赖效果: {', '.join(missing_dependencies)}"
        
        return True, ""
    
    def validate_effect(self, effect: IEffect, target=None, battle_state=None, 
                       rules: List[str] = None) -> EffectResult:
        """验证单个效果"""
        if rules is None:
            rules = list(self.validation_rules.keys())
        
        errors = []
        warnings = []
        
        for rule_name in rules:
            if rule_name not in self.validation_rules:
                warnings.append(f"未知的验证规则: {rule_name}")
                continue
            
            try:
                is_valid, message = self.validation_rules[rule_name](effect, target, battle_state)
                if not is_valid:
                    errors.append(f"{rule_name}: {message}")
            except Exception as e:
                errors.append(f"{rule_name}: 验证过程中发生错误 - {str(e)}")
        
        if errors:
            return EffectResult.failure(f"效果验证失败: {'; '.join(errors)}")
        
        result_data = {"validated": True, "effect_id": effect.id}
        if warnings:
            result_data["warnings"] = warnings
        
        return EffectResult.success_with_data(result_data, "效果验证通过")
    
    def validate_effect_manager(self, effect_manager: EffectManager) -> EffectResult:
        """验证效果管理器"""
        errors = []
        warnings = []
        validated_effects = []
        
        # 验证每个效果
        for effect_id, effect in effect_manager.effects.items():
            try:
                result = self.validate_effect(effect, effect_manager.owner)
                if not result.success:
                    errors.append(f"效果 {effect_id}: {result.message}")
                else:
                    validated_effects.append(effect_id)
                    if "warnings" in result.data:
                        warnings.extend([f"效果 {effect_id}: {w}" for w in result.data["warnings"]])
            except Exception as e:
                errors.append(f"效果 {effect_id}: 验证过程中发生异常 - {str(e)}")
        
        # 验证效果间的关系
        self._validate_effect_relationships(effect_manager, errors, warnings)
        
        if errors:
            return EffectResult.failure(f"效果管理器验证失败: {'; '.join(errors)}")
        
        result_data = {
            "validated": True,
            "total_effects": len(effect_manager.effects),
            "validated_effects": validated_effects
        }
        if warnings:
            result_data["warnings"] = warnings
        
        return EffectResult.success_with_data(result_data, "效果管理器验证通过")
    
    def _validate_effect_relationships(self, effect_manager: EffectManager, 
                                     errors: List[str], warnings: List[str]):
        """验证效果间的关系"""
        effects = list(effect_manager.effects.values())
        
        # 检查循环依赖
        for effect in effects:
            if self._has_circular_dependency(effect, effect_manager.effects):
                errors.append(f"效果 {effect.id} 存在循环依赖")
        
        # 检查冲突效果
        for i, effect1 in enumerate(effects):
            for effect2 in effects[i+1:]:
                if (effect1.id in effect2.conflicts or effect2.id in effect1.conflicts):
                    errors.append(f"效果 {effect1.id} 与 {effect2.id} 存在冲突")
        
        # 检查重复效果
        effect_names = [e.name for e in effects]
        duplicates = [name for name in set(effect_names) if effect_names.count(name) > 1]
        for duplicate in duplicates:
            duplicate_effects = [e for e in effects if e.name == duplicate]
            if not all(e.stackable for e in duplicate_effects):
                warnings.append(f"存在重复的不可叠加效果: {duplicate}")
    
    def _has_circular_dependency(self, effect: IEffect, all_effects: Dict[str, IEffect], 
                                visited: set = None) -> bool:
        """检查是否存在循环依赖"""
        if visited is None:
            visited = set()
        
        if effect.id in visited:
            return True
        
        visited.add(effect.id)
        
        for dep_id in effect.dependencies:
            if dep_id in all_effects:
                if self._has_circular_dependency(all_effects[dep_id], all_effects, visited.copy()):
                    return True
        
        return False
    
    def add_validation_rule(self, name: str, rule_func: callable):
        """添加自定义验证规则"""
        self.validation_rules[name] = rule_func
    
    def remove_validation_rule(self, name: str):
        """移除验证规则"""
        if name in self.validation_rules:
            del self.validation_rules[name]
    
    def get_available_rules(self) -> List[str]:
        """获取可用的验证规则"""
        return list(self.validation_rules.keys())


class EffectCompatibilityChecker:
    """效果兼容性检查器"""
    
    def __init__(self):
        self.compatibility_matrix: Dict[str, Dict[str, str]] = {}
        self._setup_default_compatibility()
    
    def _setup_default_compatibility(self):
        """设置默认兼容性规则"""
        # 这里可以定义不同效果类型之间的兼容性
        pass
    
    def check_compatibility(self, effect1: IEffect, effect2: IEffect) -> Tuple[bool, str]:
        """检查两个效果的兼容性"""
        # 检查直接冲突
        if effect1.id in effect2.conflicts or effect2.id in effect1.conflicts:
            return False, "效果直接冲突"
        
        # 检查同名效果的叠加性
        if effect1.name == effect2.name:
            if not effect1.stackable or not effect2.stackable:
                return False, "同名效果不可叠加"
        
        # 检查类型兼容性
        type_key = f"{effect1.type.value}_{effect2.type.value}"
        if type_key in self.compatibility_matrix:
            compatibility = self.compatibility_matrix[type_key]
            if compatibility.get("compatible", True) is False:
                return False, compatibility.get("reason", "类型不兼容")
        
        return True, "兼容"
    
    def add_compatibility_rule(self, type1: str, type2: str, compatible: bool, reason: str = ""):
        """添加兼容性规则"""
        key = f"{type1}_{type2}"
        self.compatibility_matrix[key] = {
            "compatible": compatible,
            "reason": reason
        }
        
        # 添加反向规则
        reverse_key = f"{type2}_{type1}"
        self.compatibility_matrix[reverse_key] = {
            "compatible": compatible,
            "reason": reason
        }


# 全局验证器实例
effect_validator = EffectValidator()
compatibility_checker = EffectCompatibilityChecker()


def validate_effect(effect: IEffect, target=None, battle_state=None, rules: List[str] = None) -> EffectResult:
    """便捷函数：验证效果"""
    return effect_validator.validate_effect(effect, target, battle_state, rules)


def validate_effect_manager(effect_manager: EffectManager) -> EffectResult:
    """便捷函数：验证效果管理器"""
    return effect_validator.validate_effect_manager(effect_manager)


def check_compatibility(effect1: IEffect, effect2: IEffect) -> Tuple[bool, str]:
    """便捷函数：检查效果兼容性"""
    return compatibility_checker.check_compatibility(effect1, effect2)


__all__ = [
    'EffectValidationError',
    'EffectValidator',
    'EffectCompatibilityChecker',
    'effect_validator',
    'compatibility_checker',
    'validate_effect',
    'validate_effect_manager',
    'check_compatibility'
]