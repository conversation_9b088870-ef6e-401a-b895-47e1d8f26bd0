/* Battle Log Styling */
.battle-log-container {
  height: 30vh;
  min-height: 200px;
  max-height: 40vh;
  display: flex;
  flex-direction: column;
  border-top: 1px solid rgba(139, 92, 246, 0.2);
  background-color: transparent;
}

.battle-log-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.battle-log-pane {
  height: 100%;
  overflow: hidden;
}

.battle-log-content {
  height: 100%;
  overflow-y: auto;
}

/* Log Entry Styling */
.log-entry {
  margin-bottom: 4px;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(30, 41, 59, 0.4);
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
}

.log-entry:hover {
  background-color: rgba(30, 41, 59, 0.6);
}

.log-entry.entry-damage {
  border-left-color: #ef4444;
}

.log-entry.entry-heal {
  border-left-color: #10b981;
}

.log-entry.entry-apply_effect {
  border-left-color: #3b82f6;
}

.log-entry.entry-remove_effect {
  border-left-color: #f59e0b;
}

.log-entry.entry-skill_cast {
  border-left-color: #8b5cf6;
}

.log-entry.entry-log {
  border-left-color: #6b7280;
}

/* Custom Scrollbar */
.log-content::-webkit-scrollbar {
  width: 8px;
}

.log-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

/* Card-type tabs styling */
.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none !important;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border: none !important;
  background-color: rgba(30, 41, 59, 0.4) !important;
  margin-right: 4px !important;
  border-radius: 6px 6px 0 0 !important;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background-color: rgba(139, 92, 246, 0.2) !important;
  color: #a78bfa !important;
  border-bottom: 2px solid #a78bfa !important;
}

/* Empty state styling */
.log-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #94a3b8;
  background-color: rgba(15, 23, 42, 0.3);
  border-radius: 8px;
}

.log-empty .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
  color: #94a3b8;
} 