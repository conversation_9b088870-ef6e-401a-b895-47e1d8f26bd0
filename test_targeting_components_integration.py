#!/usr/bin/env python3
"""
Targeting和Components系统集成测试

验证AI行动生成系统与现有的targeting系统和components系统的集成是否正常工作。
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_targeting_integration():
    """测试targeting系统集成"""
    
    print("=== Targeting系统集成测试 ===")
    
    try:
        # 1. 测试增强目标选择器导入
        print("\n1. 测试增强目标选择器导入...")
        
        from core.ai.targeting_integration import EnhancedTargetSelector, EnhancedTargetSelectionResult
        print("✅ EnhancedTargetSelector 导入成功")
        
        # 2. 创建测试对象
        print("\n2. 创建测试对象...")
        
        class MockSpirit:
            def __init__(self, name, team=0, hp=1000):
                self.name = name
                self.id = name.lower()
                self.team = team
                self.current_hp = hp
                self.max_hp = 1000
                self.current_energy = 100
                self.max_energy = 300
                self.is_alive = hp > 0
                self.attack = 200
                self.defense = 100
        
        class MockBattleState:
            def __init__(self):
                self.round_num = 5
                self.team0_spirits = [MockSpirit("队友A", 0), MockSpirit("队友B", 0)]
                self.team1_spirits = [MockSpirit("敌人A", 1, 300), MockSpirit("敌人B", 1, 800)]
            
            def get_living_spirits(self, team):
                if team == 0:
                    return [s for s in self.team0_spirits if s.is_alive]
                else:
                    return [s for s in self.team1_spirits if s.is_alive]
        
        class MockSkill:
            def __init__(self, name, skill_type='ACTIVE', target_selector=None):
                self.metadata = type('Metadata', (), {
                    'name': name,
                    'cast_type': skill_type,
                    'energy_cost': 50,
                    'tags': []
                })()
                self.target_selector = target_selector
        
        # 3. 测试目标选择器
        print("\n3. 测试目标选择器...")
        
        selector = EnhancedTargetSelector()
        attacker = MockSpirit("攻击者", 0)
        skill = MockSkill("测试技能")
        battle_state = MockBattleState()
        
        # 测试智能目标选择
        result = selector.select_targets_with_ai(attacker, skill, battle_state)
        
        print(f"✅ 目标选择结果:")
        print(f"  - 选择的目标数量: {len(result.targets)}")
        print(f"  - 选择原因: {result.reason}")
        print(f"  - 选择器类型: {result.selector_type}")
        
        if result.targets:
            target_names = [t.name for t in result.targets]
            print(f"  - 目标名称: {', '.join(target_names)}")
        
        # 4. 测试不同技能类型的目标选择
        print("\n4. 测试不同技能类型...")
        
        # 治疗技能
        heal_skill = MockSkill("治疗术", 'ACTIVE')
        heal_result = selector._infer_target_selector(attacker, heal_skill, battle_state)
        print(f"✅ 治疗技能目标选择: {len(heal_result.targets)} 个目标 - {heal_result.reason}")
        
        # 群攻技能
        aoe_skill = MockSkill("群体攻击", 'ACTIVE')
        aoe_result = selector._infer_target_selector(attacker, aoe_skill, battle_state)
        print(f"✅ 群攻技能目标选择: {len(aoe_result.targets)} 个目标 - {aoe_result.reason}")
        
        print("\n🎉 Targeting系统集成测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ Targeting集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_components_integration():
    """测试components系统集成"""
    
    print("\n=== Components系统集成测试 ===")
    
    try:
        # 1. 测试components集成导入
        print("\n1. 测试components集成导入...")
        
        from core.ai.components_integration import ComponentsIntegratedSkillSelector, SkillAvailabilityResult
        print("✅ ComponentsIntegratedSkillSelector 导入成功")
        
        # 2. 创建测试对象
        print("\n2. 创建测试对象...")
        
        class MockSkill:
            def __init__(self, name, energy_cost=50):
                self.metadata = type('Metadata', (), {
                    'name': name,
                    'cast_type': 'ACTIVE',
                    'energy_cost': energy_cost
                })()
                self.current_level = 1
        
        class MockSkillComponent:
            def __init__(self):
                self.skills = [
                    MockSkill("普通攻击", 0),
                    MockSkill("火球术", 30),
                    MockSkill("治疗术", 40),
                    MockSkill("大招", 100)  # 能量不足
                ]
            
            def can_use_skill(self, skill, battle_state):
                # 简单的能量检查
                return skill.metadata.energy_cost <= 50
            
            def is_skill_on_cooldown(self, skill_name):
                return skill_name == "大招"  # 大招在冷却中
            
            def is_skill_disabled(self, skill_name):
                return False
        
        class MockComponents:
            def __init__(self):
                self.skill_component = MockSkillComponent()
            
            def get_component(self, component_type):
                if component_type.__name__ == 'SkillComponent':
                    return self.skill_component
                return None
        
        class MockSpirit:
            def __init__(self, name):
                self.name = name
                self.id = name.lower()
                self.current_energy = 50
                self.max_energy = 100
                self.current_hp = 800
                self.max_hp = 1000
                self.components = MockComponents()
        
        class MockBattleState:
            def __init__(self):
                self.round_num = 3
        
        # 3. 测试技能可用性检查
        print("\n3. 测试技能可用性检查...")
        
        selector = ComponentsIntegratedSkillSelector()
        spirit = MockSpirit("测试精灵")
        battle_state = MockBattleState()
        
        result = selector.get_available_skills_from_components(spirit, battle_state)
        
        print(f"✅ 技能可用性检查结果:")
        print(f"  - 总技能数: {result.total_skills}")
        print(f"  - 可用技能数: {len(result.available_skills)}")
        print(f"  - 不可用技能数: {len(result.unavailable_skills)}")
        
        if result.available_skills:
            available_names = [s.metadata.name for s in result.available_skills]
            print(f"  - 可用技能: {', '.join(available_names)}")
        
        if result.unavailable_skills:
            unavailable_info = [f"{s.metadata.name}({reason})" for s, reason in result.unavailable_skills]
            print(f"  - 不可用技能: {', '.join(unavailable_info)}")
        
        # 4. 测试技能优先级计算
        print("\n4. 测试技能优先级计算...")
        
        for skill in result.available_skills:
            priority = selector.calculate_skill_priority(spirit, skill, battle_state)
            print(f"  - {skill.metadata.name}: 优先级 {priority:.2f}")
        
        print("\n🎉 Components系统集成测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ Components集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_action_generator():
    """测试集成后的行动生成器"""
    
    print("\n=== 集成行动生成器测试 ===")
    
    try:
        # 1. 测试集成后的行动生成器
        print("\n1. 测试集成后的行动生成器...")
        
        from core.ai import get_action_generator
        
        generator = get_action_generator()
        print(f"✅ 行动生成器: {type(generator).__name__}")
        print(f"  - 目标选择器: {type(generator.target_selector).__name__}")
        
        # 2. 创建完整的测试场景
        print("\n2. 创建完整的测试场景...")
        
        class MockSkill:
            def __init__(self, name):
                self.metadata = type('Metadata', (), {
                    'name': name,
                    'cast_type': 'ACTIVE',
                    'energy_cost': 30
                })()
                self.target_selector = None  # 没有目标选择器，使用AI推断
        
        class MockSkillComponent:
            def __init__(self):
                self.skills = [MockSkill("测试攻击")]
            
            def can_use_skill(self, skill, battle_state):
                return True
        
        class MockComponents:
            def get_component(self, component_type):
                if hasattr(component_type, '__name__') and component_type.__name__ == 'SkillComponent':
                    return MockSkillComponent()
                return None
        
        class MockSpirit:
            def __init__(self, name, team=0):
                self.name = name
                self.id = name.lower()
                self.team = team
                self.current_hp = 1000
                self.max_hp = 1000
                self.current_energy = 100
                self.max_energy = 300
                self.is_alive = True
                self.attack = 200
                self.defense = 100
                self.components = MockComponents()
        
        class MockBattleState:
            def __init__(self):
                self.round_num = 5
                self.spirits = {
                    0: [MockSpirit("队友", 0)],
                    1: [MockSpirit("敌人", 1)]
                }
            
            def get_living_spirits(self, team):
                return [s for s in self.spirits.get(team, []) if s.is_alive]
            
            def dispatch_event(self, event):
                return []
        
        # 3. 测试完整的行动生成流程
        print("\n3. 测试完整的行动生成流程...")
        
        spirit = MockSpirit("测试精灵", 0)
        battle_state = MockBattleState()
        
        # 测试行动能力检查
        capability = generator.capability_checker.can_act(spirit, battle_state)
        print(f"✅ 行动能力检查: {capability.can_act} - {capability.reason}")
        
        # 测试技能选择
        skill_result = generator.skill_selector.select_skill(spirit, battle_state)
        print(f"✅ 技能选择: {skill_result.skill.metadata.name if skill_result.skill else 'None'} - {skill_result.reason}")
        
        # 测试目标选择
        if skill_result.skill:
            target_result = generator.target_selector.select_targets_with_ai(spirit, skill_result.skill, battle_state)
            print(f"✅ 目标选择: {len(target_result.targets)} 个目标 - {target_result.reason}")
            
            if target_result.targets:
                target_names = [t.name for t in target_result.targets]
                print(f"  - 选择的目标: {', '.join(target_names)}")
        
        print("\n🎉 集成行动生成器测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 集成行动生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始Targeting和Components系统集成测试...\n")
    
    # 运行各项测试
    test_results = []
    
    test_results.append(("Targeting系统集成", test_targeting_integration()))
    test_results.append(("Components系统集成", test_components_integration()))
    test_results.append(("集成行动生成器", test_integrated_action_generator()))
    
    # 总结结果
    print("\n" + "="*60)
    print("集成测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有集成测试通过！")
        print("\n📋 集成功能总结:")
        print("  ✅ Targeting系统集成 - 智能目标选择")
        print("  ✅ Components系统集成 - 准确技能获取")
        print("  ✅ 增强目标选择器 - 支持技能目标选择器")
        print("  ✅ 智能技能选择 - 基于components的可用性检查")
        print("  ✅ 优先级计算 - 考虑技能组件信息")
        print("  ✅ 错误处理 - 完善的回退机制")
        
        print("\n🚀 AI行动生成系统现在完全集成了Targeting和Components系统！")
        print("  📖 系统能够：")
        print("    - 使用技能自带的目标选择器")
        print("    - 根据技能类型智能推断目标")
        print("    - 通过components系统准确获取可用技能")
        print("    - 考虑技能冷却、能量消耗等因素")
        print("    - 提供详细的不可用原因")
        
    else:
        print("\n❌ 部分集成测试失败，请检查系统配置")
    
    print("="*60)
