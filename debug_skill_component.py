#!/usr/bin/env python3
"""
调试技能组件的get_skills_by_type方法
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_get_skills_by_type():
    """调试get_skills_by_type方法"""
    print("🔧 调试get_skills_by_type方法...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        print(f"📊 测试精灵: {spirit.name}")
        
        # 获取技能组件
        if hasattr(spirit, 'components'):
            from core.components import SkillComponent
            skill_component = spirit.components.get_component(SkillComponent)
            
            if skill_component:
                print(f"  技能组件存在: ✅")
                
                # 获取所有技能
                all_skills = skill_component._skills
                print(f"  总技能数: {len(all_skills)}")
                
                # 检查每个技能的详细信息
                for i, skill in enumerate(all_skills):
                    print(f"\n  技能 {i}: {getattr(skill, 'name', 'Unknown')}")
                    
                    if hasattr(skill, 'metadata'):
                        metadata = skill.metadata
                        print(f"    metadata存在: ✅")
                        
                        # 检查cast_type
                        if hasattr(metadata, 'cast_type'):
                            cast_type = metadata.cast_type
                            print(f"    cast_type: '{cast_type}' (类型: {type(cast_type)})")
                        else:
                            print(f"    cast_type: 不存在")
                        
                        # 检查tags
                        if hasattr(metadata, 'tags'):
                            tags = metadata.tags
                            print(f"    tags: {tags} (类型: {type(tags)})")
                        else:
                            print(f"    tags: 不存在")
                    else:
                        print(f"    metadata: 不存在")
                
                # 测试get_skills_by_type方法
                print(f"\n🎯 测试get_skills_by_type方法:")
                
                test_types = ['ULTIMATE', 'ultimate', 'ACTIVE', 'active', 'PASSIVE', 'passive']
                
                for test_type in test_types:
                    try:
                        result = skill_component.get_skills_by_type(test_type)
                        print(f"  get_skills_by_type('{test_type}'): {len(result)} 个技能")
                        
                        for skill in result:
                            skill_name = getattr(skill, 'name', 'Unknown')
                            print(f"    - {skill_name}")
                    except Exception as e:
                        print(f"  get_skills_by_type('{test_type}'): 出错 - {e}")
                
                # 手动检查ULTIMATE技能匹配逻辑
                print(f"\n🔍 手动检查ULTIMATE技能匹配:")
                
                for i, skill in enumerate(all_skills):
                    skill_name = getattr(skill, 'name', f'技能{i}')
                    
                    # 检查是否有metadata
                    if not hasattr(skill, 'metadata'):
                        print(f"  {skill_name}: 没有metadata - 跳过")
                        continue
                    
                    metadata = skill.metadata
                    
                    # 检查tags匹配
                    if hasattr(metadata, 'tags'):
                        tags = metadata.tags
                        if 'ULTIMATE' in tags:
                            print(f"  {skill_name}: 通过tags匹配 (tags包含'ULTIMATE')")
                            continue
                    
                    # 检查cast_type匹配
                    if hasattr(metadata, 'cast_type'):
                        cast_type = metadata.cast_type
                        print(f"  {skill_name}: cast_type='{cast_type}'")
                        
                        # 检查各种匹配条件
                        if 'ULTIMATE' == cast_type:
                            print(f"    ✅ 匹配: 'ULTIMATE' == '{cast_type}'")
                        elif 'ULTIMATE'.upper() == cast_type:
                            print(f"    ✅ 匹配: 'ULTIMATE'.upper() == '{cast_type}'")
                        else:
                            print(f"    ❌ 不匹配: 'ULTIMATE' != '{cast_type}'")
                    else:
                        print(f"  {skill_name}: 没有cast_type")
            else:
                print(f"  ❌ 技能组件不存在")
        else:
            print(f"  ❌ 精灵没有components属性")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 技能组件get_skills_by_type方法调试")
    print("="*60)
    
    debug_get_skills_by_type()

if __name__ == "__main__":
    main()
