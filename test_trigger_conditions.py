#!/usr/bin/env python3
"""
测试触发条件
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_trigger_conditions():
    """测试触发条件"""
    print("🔧 测试触发条件...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        print(f"✅ 创建伏妖精灵: {fuyao_spirit.name}")
        
        # 检查被动效果
        print(f"\n📋 检查被动效果:")
        if hasattr(fuyao_spirit, 'effect_manager') and fuyao_spirit.effect_manager:
            effects_count = len(fuyao_spirit.effect_manager.effects)
            print(f"  效果数量: {effects_count}")
            
            for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"  效果: {effect_name}")
                
                # 检查触发条件
                if hasattr(effect, 'get_trigger_conditions'):
                    try:
                        conditions = effect.get_trigger_conditions()
                        print(f"    触发条件数量: {len(conditions)}")
                        
                        for i, condition in enumerate(conditions):
                            condition_type = type(condition).__name__
                            event_type = getattr(condition, 'event_type', 'Unknown')
                            print(f"      条件{i+1}: {condition_type} -> {event_type}")
                            
                            # 检查条件的具体属性
                            if hasattr(condition, 'target'):
                                target = getattr(condition, 'target', 'any')
                                print(f"        target: {target}")
                        
                        # 测试事件匹配
                        print(f"    测试事件匹配:")
                        
                        # 测试BEFORE_ATTACK事件
                        test_event_data = {
                            "event_type": "BEFORE_ATTACK",
                            "attacker": fuyao_spirit,
                            "target": spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1)),
                            "skill_name": "咒缚锁妖"
                        }
                        
                        print(f"      测试BEFORE_ATTACK事件:")
                        print(f"        事件数据: {list(test_event_data.keys())}")
                        
                        # 检查是否有匹配的触发条件
                        from core.effect.triggers import EventType
                        
                        matching_conditions = []
                        for condition in conditions:
                            if hasattr(condition, 'event_type') and condition.event_type == EventType.BEFORE_ATTACK:
                                matching_conditions.append(condition)
                        
                        print(f"        匹配的条件数量: {len(matching_conditions)}")
                        
                        if matching_conditions:
                            print(f"        ✅ 找到匹配的BEFORE_ATTACK条件")
                            
                            # 测试效果的on_triggered方法
                            print(f"      测试效果触发:")
                            try:
                                # 创建简单的战斗状态
                                from core.formation import Formation
                                from core.battle.engines.factory import create_battle_engine
                                
                                other_spirit = test_event_data["target"]
                                formation1 = Formation()
                                formation2 = Formation()
                                formation1.add_spirit(fuyao_spirit, 1, 1)
                                formation2.add_spirit(other_spirit, 3, 1)
                                
                                engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
                                battle_state = engine.battle_state
                                
                                # 调用on_triggered方法
                                result = effect.on_triggered(test_event_data, battle_state)
                                
                                if result and hasattr(result, 'success'):
                                    success = result.success if callable(result.success) else result.success
                                    print(f"        触发结果: {'成功' if success else '失败'}")
                                    
                                    if hasattr(result, 'actions') and result.actions:
                                        print(f"        生成动作数量: {len(result.actions)}")
                                        for j, action in enumerate(result.actions):
                                            action_type = type(action).__name__
                                            print(f"          动作{j+1}: {action_type}")
                                    else:
                                        print(f"        生成动作数量: 0")
                                else:
                                    print(f"        触发失败或无结果")
                                    
                            except Exception as trigger_error:
                                print(f"        ❌ 触发测试失败: {trigger_error}")
                                import traceback
                                traceback.print_exc()
                        else:
                            print(f"        ❌ 没有找到匹配的BEFORE_ATTACK条件")
                        
                    except Exception as e:
                        print(f"    ❌ 触发条件检查失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"    ❌ 效果没有get_trigger_conditions方法")
        else:
            print(f"  ❌ 没有效果管理器")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 触发条件测试")
    print("="*60)
    
    result = test_trigger_conditions()
    
    print("\n" + "="*60)
    if result:
        print("✅ 触发条件测试成功")
        print("被动效果的触发条件正常工作")
    else:
        print("❌ 触发条件测试失败")

if __name__ == "__main__":
    main()
