"""属性系统核心。

提供用于管理和计算属性修改的基础设施。
这包括属性修改器、计算器和管理器的定义。
"""
from __future__ import annotations
from collections import defaultdict
from typing import Dict, List

class AttributeModifier:
    """代表一个对属性的单一修改。

    Attributes:
        source_id (str): 修改的来源标识（如效果ID、技能ID），用于追踪和移除。
        value (float): 修改的数值。可以是固定值，也可以是百分比（以小数形式表示）。
    """
    
    def __init__(self, source_id: str, value: float):
        """初始化一个属性修改器。
        
        Args:
            source_id: 修改器的来源ID。
            value: 修改值。
        """
        self.source_id = source_id
        self.value = value
    
    def __repr__(self) -> str:
        """返回修改器的字符串表示形式。"""
        return f"AttributeModifier(source={self.source_id}, value={self.value})"


class AttributeCalculator:
    """静态工具类，提供计算最终属性值的纯函数。"""
    
    @staticmethod
    def calculate_modified_value(
        base_value: float, 
        percentage_modifiers: List[AttributeModifier], 
        flat_modifiers: List[AttributeModifier]
    ) -> float:
        """
        根据标准公式计算最终属性值。

        公式： `base_value * (1 + sum(percentage_modifiers)) + sum(flat_modifiers)`

        Args:
            base_value: 属性的基础值。
            percentage_modifiers: 应用于该属性的所有百分比修改器列表。
            flat_modifiers: 应用于该属性的所有固定值修改器列表。
            
        Returns:
            计算得出的最终属性值。
        """
        percentage_sum = sum(mod.value for mod in percentage_modifiers)
        flat_sum = sum(mod.value for mod in flat_modifiers)
        
        return base_value * (1 + percentage_sum) + flat_sum
    
    @staticmethod
    def calculate_simple_modified_value(
        base_value: float, modifiers: List[AttributeModifier]
    ) -> float:
        """
        计算只受固定值加成影响的属性值。

        公式： `base_value + sum(modifiers)`

        Args:
            base_value: 属性的基础值。
            modifiers: 应用于该属性的所有修改器列表。
            
        Returns:
            计算得出的最终属性值。
        """
        modifier_sum = sum(mod.value for mod in modifiers)
        return base_value + modifier_sum
    
    @staticmethod
    def calculate_product_modifier(modifiers: List[AttributeModifier]) -> float:
        """
        计算累积乘积修改，常用于各类增伤/减伤系数。

        公式： `product(1 + modifier.value)`

        Args:
            modifiers: 修改器列表。
            
        Returns:
            所有修改器累积相乘的结果。如果列表为空，则返回 1.0。
        """
        product = 1.0
        for mod in modifiers:
            product *= (1 + mod.value)
        return product


class AttributeManager:
    """
    管理一个实体所有属性的修改器。

    负责处理属性修改器的添加、设置、移除和查询。
    """
    
    def __init__(self):
        """初始化属性管理器。"""
        self.modifiers: Dict[str, List[AttributeModifier]] = defaultdict(list)
    
    def add_modifier(self, source_id: str, attribute: str, value: float) -> None:
        """
        添加一个新的属性修改器。

        Args:
            source_id: 修改的来源ID。
            attribute: 要修改的属性名称 (例如, "attack_p")。
            value: 修改的数值。
        """
        self.modifiers[attribute].append(AttributeModifier(source_id, value))
    
    def set_modifier(self, source_id: str, attribute: str, value: float) -> None:
        """
        设置一个属性修改器，会覆盖来自同一来源的对同一属性的旧修改。

        Args:
            source_id: 修改的来源ID。
            attribute: 要修改的属性名称。
            value: 新的修改数值。
        """
        # 移除该来源对该属性的旧修改器
        self.modifiers[attribute] = [
            mod for mod in self.modifiers[attribute] if mod.source_id != source_id
        ]
        # 添加新的修改器
        self.add_modifier(source_id, attribute, value)
    
    def remove_modifier(self, source_id: str) -> None:
        """
        移除由特定来源ID添加的所有属性修改器。

        Args:
            source_id: 要移除的修改器的来源ID。
        """
        for attribute in self.modifiers:
            self.modifiers[attribute] = [
                mod for mod in self.modifiers[attribute] if mod.source_id != source_id
            ]
    
    def get_modifiers(self, attribute: str) -> List[AttributeModifier]:
        """
        获取应用于特定属性的所有修改器。

        Args:
            attribute: 要查询的属性名称。
        
        Returns:
            一个包含所有应用于该属性的修改器的列表。
        """
        return self.modifiers.get(attribute, [])
    
    def clear_all_modifiers(self) -> None:
        """清除此管理器中的所有属性修改器。"""
        self.modifiers.clear()