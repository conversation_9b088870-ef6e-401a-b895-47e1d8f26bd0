#!/usr/bin/env python3
"""
测试动作执行流程
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_action_execution():
    """测试动作执行流程"""
    print("🔧 测试动作执行流程...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 获取动作执行器
        from core.battle.execution import UnifiedActionExecutor
        executor = UnifiedActionExecutor(battle_state, None, None)
        
        # 监控动作执行
        original_execute_single = executor._execute_single_action
        
        executed_actions = []
        
        def monitored_execute_single(action):
            """监控单个动作执行"""
            action_type = type(action).__name__
            executed_actions.append({
                'action_type': action_type,
                'action': action
            })
            print(f"    🎯 执行动作: {action_type}")
            
            # 调用原始方法
            result = original_execute_single(action)
            
            if result:
                print(f"      生成新动作数量: {len(result)}")
                for i, new_action in enumerate(result):
                    new_action_type = type(new_action).__name__
                    print(f"        新动作{i+1}: {new_action_type}")
            else:
                print(f"      生成新动作数量: 0")
            
            return result
        
        # 替换动作执行方法
        executor._execute_single_action = monitored_execute_single
        
        # 手动创建并执行DispatchEventAction
        print(f"\n📋 手动测试DispatchEventAction:")
        
        from core.action import DispatchEventAction
        from core.event.events import BeforeAttackEvent
        
        # 创建攻击事件
        attack_event = BeforeAttackEvent(
            attacker=fuyao_spirit,
            target=other_spirit,
            skill_name="咒缚锁妖"
        )
        
        # 创建分发动作
        dispatch_action = DispatchEventAction(
            caster=fuyao_spirit,
            event=attack_event
        )
        
        print(f"  创建DispatchEventAction: {type(dispatch_action).__name__}")
        print(f"  事件类型: {type(attack_event).__name__}")
        print(f"  执行前目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 执行动作
        print(f"  执行DispatchEventAction...")
        executor.execute_actions([dispatch_action])
        
        print(f"  执行后目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 显示目标的效果
        if len(other_spirit.effect_manager.effects) > 0:
            print(f"  目标效果:")
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"    - {effect_name}")
        
        # 显示执行的动作
        print(f"\n📋 执行的动作总结:")
        print(f"  总动作数量: {len(executed_actions)}")
        
        for i, action_info in enumerate(executed_actions):
            action_type = action_info['action_type']
            print(f"    动作{i+1}: {action_type}")
        
        # 检查是否有ApplyEffectAction被执行
        apply_effect_actions = [a for a in executed_actions if a['action_type'] == 'ApplyEffectAction']
        print(f"  ApplyEffectAction执行数量: {len(apply_effect_actions)}")
        
        # 判断测试结果
        target_effects_after = len(other_spirit.effect_manager.effects)
        apply_effect_count = len(apply_effect_actions)
        
        if target_effects_after > 0 and apply_effect_count > 0:
            print(f"\n✅ 动作执行流程测试成功！")
            print(f"  - ApplyEffectAction正确执行: {apply_effect_count} 个")
            print(f"  - 效果成功应用: 目标获得了效果")
            return True
        elif apply_effect_count > 0:
            print(f"\n⚠️ 动作执行流程部分成功")
            print(f"  - ApplyEffectAction已执行: {apply_effect_count} 个")
            print(f"  - 但效果没有应用: 目标没有获得效果")
            return False
        else:
            print(f"\n❌ 动作执行流程测试失败")
            print(f"  - ApplyEffectAction没有被执行")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 动作执行流程测试")
    print("="*60)
    
    result = test_action_execution()
    
    print("\n" + "="*60)
    if result:
        print("✅ 动作执行流程测试成功")
        print("DispatchEventAction正确执行并应用效果")
    else:
        print("❌ 动作执行流程测试失败")

if __name__ == "__main__":
    main()
