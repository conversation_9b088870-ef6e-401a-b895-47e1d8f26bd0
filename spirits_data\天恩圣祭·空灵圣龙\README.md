# 天恩圣祭·空灵圣龙

## 🌟 **精灵概览**

**属性**: 空  
**职业**: 魔法  
**定位**: 隐身控制型魔法输出  

## 📊 **基础属性**

| 属性 | 数值 | 说明 |
|------|------|------|
| 生命值 | 2300 | 中等生存能力 |
| 攻击力 | 220 | 较高魔法攻击力 |
| 物理防御 | 85 | 较低物防 |
| 魔法防御 | 120 | 较高魔防 |
| 速度 | 95 | 中等速度 |
| 暴击率 | 8% | 较低暴击率 |
| 闪避率 | 12% | 较高闪避率（配合隐身） |

## 🎯 **技能详解**

### 🔮 **藏隙匿形** (被动 Lv.1)

**效果**:
1. **隐身机制**: 获得隐身，直到同横排其他精灵全部死亡
2. **增伤效果**: 隐身状态或神使状态下，该精灵获得40%增伤

**机制说明**:
- 战斗开始时，如果同横排有其他存活精灵，则获得隐身
- 隐身状态下无法被敌方直接选择为目标
- 当同横排其他精灵全部死亡时，失去隐身效果
- 隐身或神使状态下，所有伤害增加40%

### ⚔️ **牵丝引线** (普攻 Lv.1)

**类型**: 加气普攻  
**消耗**: 0气势  
**效果**: 攻击对手，造成攻击120%的魔法伤害，攻击后获得30点气势

**特点**:
- 标准的魔法普攻
- 提供稳定的气势积累
- 无法攻击隐身目标

### 💥 **缄言净缚** (超杀 Lv.1)

**类型**: 加气群攻超杀  
**消耗**: 150气势  
**效果**: 
- **基础伤害**: 造成攻击*300%的魔法伤害
- **免疫效果**: 攻击后令各横排最靠前的精灵获得免疫(持续1次攻击)
- **特殊机制**: 若该次攻击在隐身或神使状态下，则更改本次超杀目标为敌阵各横排最靠前的精灵

**目标选择逻辑**:
- **正常状态**: 选择敌方单体目标
- **隐身/神使状态**: 选择敌阵各横排最靠前的精灵（群攻效果）

## 🎮 **战术运用**

### 🛡️ **防守阶段**
1. **隐身保护**: 利用同排精灵作为掩护，避免被直接攻击
2. **增伤积累**: 在隐身状态下积累气势，准备爆发
3. **免疫支援**: 超杀后为前排精灵提供免疫保护

### ⚡ **进攻阶段**
1. **隐身爆发**: 在隐身状态下使用超杀，享受40%增伤和群攻效果
2. **目标切换**: 隐身状态下的超杀可以攻击敌方多个前排目标
3. **控制节奏**: 通过免疫效果保护关键精灵

### 🎯 **配队建议**
1. **前排保护**: 需要前排精灵提供隐身条件
2. **神使激活**: 配合神使状态精灵，触发增伤效果
3. **控制链**: 与其他控制型精灵形成控制链

## 🔧 **技术实现**

### 📁 **文件结构**
```
天恩圣祭·空灵圣龙/
├── __init__.py              # 模块导出
├── spirit.py                # 精灵主体定义
├── skills.py                # 技能定义
├── skill_components.py      # 技能组件
├── passive_effects.py       # 被动效果
├── effects.py               # 特殊效果
└── README.md               # 说明文档
```

### 🎨 **核心特性**
1. **隐身系统**: 实现了完整的隐身机制和条件检查
2. **动态目标选择**: 根据状态改变技能目标选择逻辑
3. **状态增伤**: 基于多种状态的伤害加成系统
4. **免疫分发**: 智能的免疫效果分配机制

### 🔍 **关键组件**
- `CangXiNiXingPassiveEffect`: 藏隙匿形被动效果
- `JianYanJingFuTargetSelector`: 动态目标选择器
- `InvisibilityEffect`: 隐身效果实现
- `ImmunityEffect`: 免疫效果实现

## 🎉 **使用示例**

```python
from spirits_data.天恩圣祭·空灵圣龙 import create_kongling_shenglong_spirit

# 创建精灵
kongling = create_kongling_shenglong_spirit()

# 查看精灵信息
print(f"精灵名称: {kongling.name}")
print(f"属性: {kongling.element.value}")
print(f"职业: {kongling.profession.value}")
print(f"技能数量: {len(kongling.skills)}")
```

## 🌟 **设计亮点**

1. **创新的隐身机制**: 基于同排精灵存活状态的动态隐身
2. **状态联动**: 隐身和神使状态的协同增伤效果
3. **智能目标选择**: 根据自身状态动态改变攻击模式
4. **团队支援**: 通过免疫效果保护关键队友

这个精灵设计体现了高度的战术深度和团队协作价值！🚀
