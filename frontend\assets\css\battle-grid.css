/* 战斗网格全局样式 */
.battle-grid {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保网格容器始终保持正方形 */
.grid-container {
  aspect-ratio: 1/1;
  max-height: 100%;
  max-width: 100%;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.grid-container h3 {
  margin-bottom: 10px;
  flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .battle-arena .flex-1.flex {
    flex-direction: column;
  }
  
  .battle-arena .w-1\/3 {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .battle-arena .flex-1.p-4 {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
}

/* 确保网格单元格始终保持正方形 */
.grid-cell {
  aspect-ratio: 1/1 !important;
  position: relative;
  overflow: hidden;
  border: 2px dashed rgba(203, 213, 225, 0.4) !important;
  border-radius: 8px !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.1) !important;
}

.grid-cell:hover {
  border-color: rgb(139, 92, 246) !important;
  background-color: rgba(139, 92, 246, 0.2) !important;
  box-shadow: inset 0 0 10px rgba(139, 92, 246, 0.2) !important;
}

.team-1 .grid-cell {
  border-color: rgba(96, 165, 250, 0.4) !important;
}

.team-2 .grid-cell {
  border-color: rgba(248, 113, 113, 0.4) !important;
}

.grid-cell.has-spirit {
  border-style: solid !important;
}

.grid-cell.team-1.has-spirit {
  border-color: rgb(96, 165, 250) !important;
  background-color: rgba(59, 130, 246, 0.2) !important;
  box-shadow: inset 0 0 10px rgba(59, 130, 246, 0.2) !important;
}

.grid-cell.team-2.has-spirit {
  border-color: rgb(248, 113, 113) !important;
  background-color: rgba(239, 68, 68, 0.2) !important;
  box-shadow: inset 0 0 10px rgba(239, 68, 68, 0.2) !important;
}

/* 确保内容居中 */
.grid-wrapper {
  display: flex;
  align-items: stretch;
  justify-content: center;
  width: 100%;
  height: 100%;
  flex: 1;
  min-height: 0;
}

/* 网格容器内的BattleGrid组件应该填满整个空间 */
.grid-wrapper .battle-grid {
  width: 100%;
  height: 100%;
}

/* 网格容器内的grid-container应该填满整个空间 */
.grid-wrapper .grid-container {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 10px;
}

/* 空位置提示样式 */
.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: rgba(203, 213, 225, 0.7) !important;
}

.empty-slot .el-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

/* 战斗网格专用样式 */

/* ==================== 战斗网格布局 ==================== */

/* 队伍容器 */
.team-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.team-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 16px 16px 0 0;
}

.team-container.team-1::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.team-container.team-2::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* 队伍标题 */
.team-title {
  font-size: 1.25rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 16px;
  color: var(--gray-800);
  position: relative;
}

.team-title.team-1 {
  color: #059669;
}

.team-title.team-2 {
  color: #dc2626;
}

.team-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: currentColor;
  border-radius: 1px;
}

/* 3x3 网格 */
.battle-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8px;
  aspect-ratio: 1;
  max-width: 300px;
  margin: 0 auto;
  padding: 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  border: 2px solid var(--gray-200);
}

/* 网格单元格 */
.grid-cell {
  aspect-ratio: 1;
  border: 2px dashed var(--gray-300);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.grid-cell:hover {
  border-color: var(--primary-blue);
  background: rgba(59, 130, 246, 0.1);
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.grid-cell.occupied {
  border: 2px solid var(--success-green);
  background: rgba(16, 185, 129, 0.1);
  border-style: solid;
}

.grid-cell.occupied:hover {
  border-color: var(--success-green);
  background: rgba(16, 185, 129, 0.2);
  animation: glow 2s infinite;
}

/* 位置标签 */
.position-label {
  position: absolute;
  top: 2px;
  left: 2px;
  background: var(--gray-600);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 500;
  opacity: 0.7;
}

/* 精灵显示 */
.spirit-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  position: relative;
}

.spirit-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
  box-shadow: var(--shadow-sm);
}

.spirit-name {
  font-size: 10px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9); /* 更高对比度 */
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 精灵类型特定样式 */
.spirit-avatar.type-beast {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.spirit-avatar.type-shenyao {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.spirit-avatar.type-hero {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.spirit-avatar.type-summoner {
  background: linear-gradient(135deg, #10b981, #059669);
}

/* 契约标识 */
.contract-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.contract-indicator.summoner {
  background: var(--accent-gold);
}

.contract-indicator.beast {
  background: var(--secondary-purple);
}

/* 空单元格提示 */
.empty-cell-hint {
  color: var(--gray-400);
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grid-cell:hover .empty-cell-hint {
  opacity: 1;
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 768px) {
  .battle-grid {
    max-width: 250px;
    gap: 6px;
    padding: 8px;
  }
  
  .spirit-avatar {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
  
  .spirit-name {
    font-size: 9px;
  }
  
  .position-label {
    font-size: 8px;
    padding: 1px 3px;
  }
}

@media (max-width: 480px) {
  .team-container {
    padding: 12px;
  }
  
  .battle-grid {
    max-width: 200px;
    gap: 4px;
    padding: 6px;
  }
  
  .spirit-avatar {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
  
  .spirit-name {
    font-size: 8px;
  }
}

/* ==================== 动画效果 ==================== */

/* 精灵放置动画 */
@keyframes spiritPlace {
  0% {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.spirit-display.newly-placed {
  animation: spiritPlace 0.6s ease-out;
}

/* 网格悬停效果 */
.grid-cell::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: 0;
}

.grid-cell:hover::before {
  width: 100%;
  height: 100%;
}

/* 战斗准备状态 */
.battle-grid.battle-ready {
  border-color: var(--success-green);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.battle-grid.battle-ready .grid-cell.occupied {
  animation: pulse 2s infinite;
}