"""
超杀技能基类

提供自动气势消耗和溢出气势处理的超杀技能基类
"""

from typing import List, Any, Optional
from abc import ABC, abstractmethod

from ..interfaces import IBattleEntity, IBattleState
from ..logging import get_logger
from .ultimate_energy_handler import create_ultimate_energy_actions

logger = get_logger("core.skill.ultimate_skill_base")


class UltimateSkillComponent(ABC):
    """
    超杀技能组件基类
    
    自动处理：
    1. 气势消耗（消耗所有气势）
    2. 溢出气势计算
    3. 伤害动作增强
    """
    
    def __init__(self, skill_name: str):
        """
        初始化超杀技能组件
        
        Args:
            skill_name: 技能名称
        """
        self.skill_name = skill_name
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        """
        执行超杀技能
        
        自动处理气势消耗和溢出气势计算
        """
        logger.info(f"{caster.name} 开始执行超杀技能: {self.skill_name}")
        
        # 1. 执行具体的超杀技能逻辑
        damage_actions = self.execute_ultimate_skill(caster, targets, battle_state, context)
        
        # 2. 创建完整的气势处理动作
        all_actions = create_ultimate_energy_actions(
            caster=caster,
            skill_name=self.skill_name,
            damage_actions=damage_actions
        )
        
        logger.info(f"{caster.name} 超杀技能 {self.skill_name} 执行完成，生成 {len(all_actions)} 个动作")
        
        return all_actions
    
    @abstractmethod
    def execute_ultimate_skill(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        """
        执行具体的超杀技能逻辑
        
        子类需要实现这个方法，返回伤害动作列表
        气势消耗和溢出气势处理会自动完成
        
        Args:
            caster: 施法者
            targets: 目标列表
            battle_state: 战斗状态
            context: 技能上下文
            
        Returns:
            伤害动作列表（不包含气势消耗动作）
        """
        pass


class SimpleDamageUltimateComponent(UltimateSkillComponent):
    """
    简单伤害型超杀技能组件
    
    适用于大部分只造成伤害的超杀技能
    """
    
    def __init__(
        self,
        skill_name: str,
        damage_multiplier: float = 3.0,
        damage_type: str = "PHYSICAL",
        additional_effects: Optional[List[Any]] = None
    ):
        """
        初始化简单伤害型超杀技能
        
        Args:
            skill_name: 技能名称
            damage_multiplier: 伤害倍率（基于攻击力）
            damage_type: 伤害类型
            additional_effects: 额外效果列表
        """
        super().__init__(skill_name)
        self.damage_multiplier = damage_multiplier
        self.damage_type = damage_type
        self.additional_effects = additional_effects or []
    
    def execute_ultimate_skill(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        """执行简单伤害型超杀技能"""
        from ..action import DamageAction, DamageType, LogAction
        
        actions = []
        
        # 计算基础伤害
        base_attack = getattr(caster, 'attack', 0)
        damage_value = int(base_attack * self.damage_multiplier)
        
        # 确定伤害类型
        if self.damage_type.upper() == "PHYSICAL":
            damage_type = DamageType.PHYSICAL
        else:
            damage_type = DamageType.MAGIC
        
        # 对每个目标造成伤害
        for target in targets:
            if target.is_alive:
                # 添加技能释放日志
                actions.append(LogAction(
                    caster=caster,
                    target=target,
                    message=f"💥 [{self.skill_name}] {caster.name} 释放超杀技能攻击 {target.name}！"
                ))
                
                # 创建伤害动作
                damage_action = DamageAction(
                    caster=caster,
                    target=target,
                    damage_value=damage_value,
                    damage_type=damage_type,
                    skill_name=self.skill_name,
                    is_ultimate=True  # 标记为超杀技能
                )
                actions.append(damage_action)
        
        # 添加额外效果
        for effect in self.additional_effects:
            if callable(effect):
                effect_actions = effect(caster, targets, battle_state, context)
                if effect_actions:
                    actions.extend(effect_actions)
        
        return actions


class HPBasedUltimateComponent(UltimateSkillComponent):
    """
    基于生命值的超杀技能组件
    
    伤害基于攻击力和生命值百分比
    """
    
    def __init__(
        self,
        skill_name: str,
        attack_multiplier: float = 2.0,
        hp_percentage: float = 0.3,
        damage_type: str = "PHYSICAL"
    ):
        """
        初始化基于生命值的超杀技能
        
        Args:
            skill_name: 技能名称
            attack_multiplier: 攻击力倍率
            hp_percentage: 生命值百分比
            damage_type: 伤害类型
        """
        super().__init__(skill_name)
        self.attack_multiplier = attack_multiplier
        self.hp_percentage = hp_percentage
        self.damage_type = damage_type
    
    def execute_ultimate_skill(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        """执行基于生命值的超杀技能"""
        from ..action import DamageAction, DamageType, LogAction
        
        actions = []
        
        # 计算伤害
        base_attack = getattr(caster, 'attack', 0)
        max_hp = getattr(caster, 'max_hp', 0)
        
        attack_damage = base_attack * self.attack_multiplier
        hp_damage = max_hp * self.hp_percentage
        total_damage = int(attack_damage + hp_damage)
        
        # 确定伤害类型
        if self.damage_type.upper() == "PHYSICAL":
            damage_type = DamageType.PHYSICAL
        else:
            damage_type = DamageType.MAGIC
        
        # 对每个目标造成伤害
        for target in targets:
            if target.is_alive:
                # 添加技能释放日志
                actions.append(LogAction(
                    caster=caster,
                    target=target,
                    message=f"💥 [{self.skill_name}] {caster.name} 释放生命力超杀技能！"
                ))
                
                # 创建伤害动作
                damage_action = DamageAction(
                    caster=caster,
                    target=target,
                    damage_value=total_damage,
                    damage_type=damage_type,
                    skill_name=self.skill_name,
                    is_ultimate=True
                )
                actions.append(damage_action)
        
        return actions


# 便捷函数
def create_simple_ultimate_component(
    skill_name: str,
    damage_multiplier: float = 3.0,
    damage_type: str = "PHYSICAL"
) -> SimpleDamageUltimateComponent:
    """创建简单伤害型超杀技能组件"""
    return SimpleDamageUltimateComponent(skill_name, damage_multiplier, damage_type)


def create_hp_based_ultimate_component(
    skill_name: str,
    attack_multiplier: float = 2.0,
    hp_percentage: float = 0.3,
    damage_type: str = "PHYSICAL"
) -> HPBasedUltimateComponent:
    """创建基于生命值的超杀技能组件"""
    return HPBasedUltimateComponent(skill_name, attack_multiplier, hp_percentage, damage_type)
