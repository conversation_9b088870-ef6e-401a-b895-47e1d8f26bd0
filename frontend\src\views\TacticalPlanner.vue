<template>
  <div class="tactical-planner h-full flex flex-col">
    <!-- 页面头部 -->
    <div class="page-header bg-slate-800/50 backdrop-blur-sm border-b border-purple-500/20 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-white mb-2">
            <el-icon class="mr-3"><Guide /></el-icon>
            战术规划
          </h1>
          <p class="text-slate-400">制定战术策略，优化阵容配置，提升战斗胜率</p>
        </div>
        <div class="flex items-center space-x-3">
          <el-button type="primary" @click="showCreateTacticDialog = true">
            <el-icon class="mr-2"><Plus /></el-icon>
            新建战术
          </el-button>
          <el-button @click="importTactics">
            <el-icon class="mr-2"><Upload /></el-icon>
            导入战术
          </el-button>
          <el-button @click="exportTactics">
            <el-icon class="mr-2"><Download /></el-icon>
            导出战术
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content flex-1 flex">
      <!-- 左侧：战术列表 -->
      <div class="tactics-sidebar w-80 bg-slate-800/30 border-r border-slate-600/30 p-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-white">我的战术</h3>
          <el-button size="small" @click="refreshTactics">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>

        <!-- 搜索和过滤 -->
        <div class="search-filter mb-4">
          <el-input
            v-model="searchQuery"
            placeholder="搜索战术..."
            clearable
            class="mb-3"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="filterCategory" placeholder="战术类型" clearable class="w-full">
            <el-option label="攻击型" value="offensive" />
            <el-option label="防守型" value="defensive" />
            <el-option label="平衡型" value="balanced" />
            <el-option label="特殊型" value="special" />
          </el-select>
        </div>

        <!-- 战术列表 -->
        <div class="tactics-list space-y-3 max-h-96 overflow-auto">
          <div
            v-for="tactic in filteredTactics"
            :key="tactic.id"
            class="tactic-item p-4 bg-slate-700/50 rounded-lg border border-slate-600/30 cursor-pointer transition-all hover:border-purple-500/50"
            :class="{ 'border-purple-500 bg-purple-500/10': selectedTactic?.id === tactic.id }"
            @click="selectTactic(tactic)"
          >
            <div class="flex items-center justify-between mb-2">
              <h4 class="text-white font-medium">{{ tactic.name }}</h4>
              <el-tag :type="getTacticTypeColor(tactic.type)" size="small">
                {{ getTacticTypeLabel(tactic.type) }}
              </el-tag>
            </div>
            
            <p class="text-slate-400 text-sm mb-3">{{ tactic.description }}</p>
            
            <div class="tactic-stats flex items-center justify-between text-xs">
              <span class="text-slate-400">胜率: <span class="text-green-400">{{ tactic.winRate }}%</span></span>
              <span class="text-slate-400">使用: {{ tactic.usageCount }}次</span>
            </div>
            
            <div class="tactic-actions mt-3 flex justify-end space-x-2">
              <el-button size="small" @click.stop="editTactic(tactic)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" @click.stop="duplicateTactic(tactic)">
                <el-icon><CopyDocument /></el-icon>
              </el-button>
              <el-button size="small" type="danger" @click.stop="deleteTactic(tactic)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 快速战术模板 -->
        <div class="quick-templates mt-6">
          <h4 class="text-white font-medium mb-3">快速模板</h4>
          <div class="space-y-2">
            <el-button
              v-for="template in tacticTemplates"
              :key="template.id"
              size="small"
              class="w-full justify-start"
              @click="applyTemplate(template)"
            >
              {{ template.name }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧：战术详情和编辑器 -->
      <div class="tactics-editor flex-1 p-6">
        <div v-if="selectedTactic" class="h-full flex flex-col">
          <!-- 战术信息头部 -->
          <div class="tactic-header bg-slate-800/50 rounded-lg p-6 mb-6 border border-slate-600/30">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h2 class="text-2xl font-bold text-white">{{ selectedTactic.name }}</h2>
                <p class="text-slate-400 mt-1">{{ selectedTactic.description }}</p>
              </div>
              <div class="flex items-center space-x-3">
                <el-tag :type="getTacticTypeColor(selectedTactic.type)">
                  {{ getTacticTypeLabel(selectedTactic.type) }}
                </el-tag>
                <el-button type="primary" @click="testTactic">
                  <el-icon class="mr-2"><VideoPlay /></el-icon>
                  测试战术
                </el-button>
              </div>
            </div>
            
            <!-- 战术统计 -->
            <div class="tactic-stats grid grid-cols-4 gap-4">
              <div class="stat-item text-center">
                <div class="text-2xl font-bold text-green-400">{{ selectedTactic.winRate }}%</div>
                <div class="text-slate-400 text-sm">胜率</div>
              </div>
              <div class="stat-item text-center">
                <div class="text-2xl font-bold text-blue-400">{{ selectedTactic.usageCount }}</div>
                <div class="text-slate-400 text-sm">使用次数</div>
              </div>
              <div class="stat-item text-center">
                <div class="text-2xl font-bold text-purple-400">{{ selectedTactic.avgDuration }}s</div>
                <div class="text-slate-400 text-sm">平均时长</div>
              </div>
              <div class="stat-item text-center">
                <div class="text-2xl font-bold text-orange-400">{{ selectedTactic.difficulty }}/5</div>
                <div class="text-slate-400 text-sm">难度等级</div>
              </div>
            </div>
          </div>

          <!-- 战术配置选项卡 -->
          <div class="tactic-config flex-1">
            <el-tabs v-model="activeConfigTab" class="h-full">
              <!-- 阵容配置 -->
              <el-tab-pane label="阵容配置" name="formation" class="h-full">
                <FormationEditor
                  v-model="selectedTactic.formation"
                  @formation-change="onFormationChange"
                />
              </el-tab-pane>

              <!-- 战术规则 -->
              <el-tab-pane label="战术规则" name="rules" class="h-full">
                <TacticRulesEditor
                  v-model="selectedTactic.rules"
                  @rules-change="onRulesChange"
                />
              </el-tab-pane>

              <!-- 条件触发 -->
              <el-tab-pane label="条件触发" name="triggers" class="h-full">
                <TacticTriggersEditor
                  v-model="selectedTactic.triggers"
                  @triggers-change="onTriggersChange"
                />
              </el-tab-pane>

              <!-- 优先级设置 -->
              <el-tab-pane label="优先级设置" name="priorities" class="h-full">
                <TacticPrioritiesEditor
                  v-model="selectedTactic.priorities"
                  @priorities-change="onPrioritiesChange"
                />
              </el-tab-pane>

              <!-- 测试结果 -->
              <el-tab-pane label="测试结果" name="results" class="h-full">
                <TacticTestResults
                  v-if="testResults"
                  :results="testResults"
                  @retest="testTactic"
                />
                <div v-else class="flex items-center justify-center h-full">
                  <div class="text-center">
                    <el-icon class="text-6xl text-slate-500 mb-4"><DataAnalysis /></el-icon>
                    <p class="text-slate-400 mb-4">暂无测试结果</p>
                    <el-button type="primary" @click="testTactic">
                      <el-icon class="mr-2"><VideoPlay /></el-icon>
                      开始测试
                    </el-button>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state flex items-center justify-center h-full">
          <div class="text-center">
            <el-icon class="text-6xl text-slate-500 mb-4"><Guide /></el-icon>
            <h3 class="text-xl text-slate-400 mb-2">选择或创建战术</h3>
            <p class="text-slate-500 mb-4">从左侧选择一个战术进行编辑，或创建新的战术方案</p>
            <el-button type="primary" @click="showCreateTacticDialog = true">
              <el-icon class="mr-2"><Plus /></el-icon>
              创建新战术
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑战术对话框 -->
    <el-dialog
      v-model="showCreateTacticDialog"
      :title="editingTactic ? '编辑战术' : '创建战术'"
      width="600px"
      @close="resetTacticForm"
    >
      <TacticForm
        v-model="tacticForm"
        :is-editing="!!editingTactic"
        @submit="handleTacticSubmit"
        @cancel="showCreateTacticDialog = false"
      />
    </el-dialog>

    <!-- 战术测试对话框 -->
    <el-dialog
      v-model="showTestDialog"
      title="战术测试"
      width="800px"
    >
      <TacticTestDialog
        v-if="selectedTactic"
        :tactic="selectedTactic"
        @test-complete="onTestComplete"
        @close="showTestDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormationEditor from '@/components/tactics/FormationEditor.vue'
import TacticRulesEditor from '@/components/tactics/TacticRulesEditor.vue'
import TacticTriggersEditor from '@/components/tactics/TacticTriggersEditor.vue'
import TacticPrioritiesEditor from '@/components/tactics/TacticPrioritiesEditor.vue'
import TacticTestResults from '@/components/tactics/TacticTestResults.vue'
import TacticForm from '@/components/tactics/TacticForm.vue'
import TacticTestDialog from '@/components/tactics/TacticTestDialog.vue'

// 响应式数据
const searchQuery = ref('')
const filterCategory = ref('')
const selectedTactic = ref(null)
const activeConfigTab = ref('formation')
const showCreateTacticDialog = ref(false)
const showTestDialog = ref(false)
const editingTactic = ref(null)
const testResults = ref(null)
const tacticForm = ref({})

// 战术数据
const tactics = ref([
  {
    id: 'tactic_1',
    name: '雷霆突击',
    description: '以高攻击力精灵为核心的快速突击战术',
    type: 'offensive',
    winRate: 78.5,
    usageCount: 45,
    avgDuration: 32.4,
    difficulty: 3,
    formation: {
      team1: [],
      team2: []
    },
    rules: [],
    triggers: [],
    priorities: []
  },
  {
    id: 'tactic_2',
    name: '铁壁防守',
    description: '以防御和治疗为主的稳健防守战术',
    type: 'defensive',
    winRate: 65.2,
    usageCount: 38,
    avgDuration: 48.7,
    difficulty: 2,
    formation: {
      team1: [],
      team2: []
    },
    rules: [],
    triggers: [],
    priorities: []
  },
  {
    id: 'tactic_3',
    name: '均衡发展',
    description: '攻守兼备的平衡型战术配置',
    type: 'balanced',
    winRate: 71.8,
    usageCount: 52,
    avgDuration: 41.2,
    difficulty: 2,
    formation: {
      team1: [],
      team2: []
    },
    rules: [],
    triggers: [],
    priorities: []
  }
])

// 战术模板
const tacticTemplates = ref([
  { id: 'template_1', name: '速攻模板', type: 'offensive' },
  { id: 'template_2', name: '防守模板', type: 'defensive' },
  { id: 'template_3', name: '平衡模板', type: 'balanced' },
  { id: 'template_4', name: '控制模板', type: 'special' }
])

// 计算属性
const filteredTactics = computed(() => {
  let result = tactics.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(tactic =>
      tactic.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      tactic.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 类型过滤
  if (filterCategory.value) {
    result = result.filter(tactic => tactic.type === filterCategory.value)
  }

  return result
})

// 方法
const selectTactic = (tactic) => {
  selectedTactic.value = tactic
  activeConfigTab.value = 'formation'
  testResults.value = null
}

const editTactic = (tactic) => {
  editingTactic.value = tactic
  tacticForm.value = { ...tactic }
  showCreateTacticDialog.value = true
}

const duplicateTactic = (tactic) => {
  const newTactic = {
    ...tactic,
    id: `tactic_${Date.now()}`,
    name: `${tactic.name} (副本)`,
    usageCount: 0,
    winRate: 0
  }
  tactics.value.push(newTactic)
  ElMessage.success('战术复制成功')
}

const deleteTactic = async (tactic) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除战术 "${tactic.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = tactics.value.findIndex(t => t.id === tactic.id)
    if (index > -1) {
      tactics.value.splice(index, 1)
      if (selectedTactic.value?.id === tactic.id) {
        selectedTactic.value = null
      }
      ElMessage.success('战术删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const testTactic = () => {
  if (!selectedTactic.value) return
  showTestDialog.value = true
}

const onTestComplete = (results) => {
  testResults.value = results
  activeConfigTab.value = 'results'
  showTestDialog.value = false
  ElMessage.success('战术测试完成')
}

const refreshTactics = () => {
  ElMessage.info('正在刷新战术列表...')
  // TODO: 实际的刷新逻辑
}

const importTactics = () => {
  ElMessage.info('导入功能开发中...')
}

const exportTactics = () => {
  const data = JSON.stringify(tactics.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'tactics.json'
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('战术数据导出成功')
}

const applyTemplate = (template) => {
  ElMessage.info(`应用模板: ${template.name}`)
  // TODO: 实现模板应用逻辑
}

const handleTacticSubmit = (formData) => {
  if (editingTactic.value) {
    // 编辑现有战术
    const index = tactics.value.findIndex(t => t.id === editingTactic.value.id)
    if (index > -1) {
      tactics.value[index] = { ...formData }
      if (selectedTactic.value?.id === editingTactic.value.id) {
        selectedTactic.value = tactics.value[index]
      }
      ElMessage.success('战术更新成功')
    }
  } else {
    // 创建新战术
    const newTactic = {
      ...formData,
      id: `tactic_${Date.now()}`,
      winRate: 0,
      usageCount: 0,
      avgDuration: 0,
      formation: { team1: [], team2: [] },
      rules: [],
      triggers: [],
      priorities: []
    }
    tactics.value.push(newTactic)
    selectedTactic.value = newTactic
    ElMessage.success('战术创建成功')
  }
  
  showCreateTacticDialog.value = false
  resetTacticForm()
}

const resetTacticForm = () => {
  editingTactic.value = null
  tacticForm.value = {}
}

const onFormationChange = (formation) => {
  if (selectedTactic.value) {
    selectedTactic.value.formation = formation
  }
}

const onRulesChange = (rules) => {
  if (selectedTactic.value) {
    selectedTactic.value.rules = rules
  }
}

const onTriggersChange = (triggers) => {
  if (selectedTactic.value) {
    selectedTactic.value.triggers = triggers
  }
}

const onPrioritiesChange = (priorities) => {
  if (selectedTactic.value) {
    selectedTactic.value.priorities = priorities
  }
}

// 辅助函数
const getTacticTypeLabel = (type) => {
  const typeMap = {
    offensive: '攻击型',
    defensive: '防守型',
    balanced: '平衡型',
    special: '特殊型'
  }
  return typeMap[type] || type
}

const getTacticTypeColor = (type) => {
  const colorMap = {
    offensive: 'danger',
    defensive: 'primary',
    balanced: 'success',
    special: 'warning'
  }
  return colorMap[type] || ''
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.tactical-planner {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.tactic-item {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  }
}

.tactics-list {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(148, 163, 184, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(139, 92, 246, 0.5);
    }
  }
}

:deep(.el-tabs__content) {
  height: calc(100% - 40px);
  padding: 16px 0;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(148, 163, 184, 0.2);
}

:deep(.el-tabs__active-bar) {
  background-color: #8b5cf6;
}

:deep(.el-tabs__item.is-active) {
  color: #8b5cf6;
}
</style>