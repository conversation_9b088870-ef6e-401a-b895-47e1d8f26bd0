#!/usr/bin/env python3
"""
回合历史面板

显示和管理战斗回合历史记录
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable, List
from ui.ux.models.battle_record import BattleRecorder, RoundSnapshot


class RoundHistoryPanel:
    """回合历史面板"""
    
    def __init__(self, parent, on_round_selected: Optional[Callable[[int], None]] = None):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        self.on_round_selected = on_round_selected
        self.recorder: Optional[BattleRecorder] = None
        self.selected_round: int = 0
        
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 控制区域
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 回合选择
        ttk.Label(control_frame, text="选择回合:").pack(side=tk.LEFT)
        
        self.round_var = tk.StringVar()
        self.round_combo = ttk.Combobox(control_frame, textvariable=self.round_var, 
                                       width=10, state="readonly")
        self.round_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.round_combo.bind('<<ComboboxSelected>>', self.on_round_combo_changed)
        
        # 导航按钮
        ttk.Button(control_frame, text="上一回合", command=self.prev_round).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="下一回合", command=self.next_round).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="最新回合", command=self.goto_latest).pack(side=tk.LEFT, padx=(0, 10))
        
        # 比较按钮
        ttk.Button(control_frame, text="比较回合", command=self.show_comparison).pack(side=tk.LEFT)
        
        # 回合详情区域
        details_frame = ttk.LabelFrame(self.frame, text="回合详情", padding=10)
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页
        notebook = ttk.Notebook(details_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 回合概览标签页
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="回合概览")
        self.overview_text = tk.Text(overview_frame, height=10, wrap=tk.WORD)
        overview_scroll = ttk.Scrollbar(overview_frame, orient="vertical", command=self.overview_text.yview)
        self.overview_text.configure(yscrollcommand=overview_scroll.set)
        self.overview_text.pack(side="left", fill="both", expand=True)
        overview_scroll.pack(side="right", fill="y")
        
        # 行动记录标签页
        actions_frame = ttk.Frame(notebook)
        notebook.add(actions_frame, text="行动记录")
        self.actions_text = tk.Text(actions_frame, height=10, wrap=tk.WORD)
        actions_scroll = ttk.Scrollbar(actions_frame, orient="vertical", command=self.actions_text.yview)
        self.actions_text.configure(yscrollcommand=actions_scroll.set)
        self.actions_text.pack(side="left", fill="both", expand=True)
        actions_scroll.pack(side="right", fill="y")
        
        # 状态变化标签页
        changes_frame = ttk.Frame(notebook)
        notebook.add(changes_frame, text="状态变化")
        self.changes_text = tk.Text(changes_frame, height=10, wrap=tk.WORD)
        changes_scroll = ttk.Scrollbar(changes_frame, orient="vertical", command=self.changes_text.yview)
        self.changes_text.configure(yscrollcommand=changes_scroll.set)
        self.changes_text.pack(side="left", fill="both", expand=True)
        changes_scroll.pack(side="right", fill="y")
    
    def set_recorder(self, recorder: BattleRecorder):
        """设置战斗记录器"""
        self.recorder = recorder
        self.update_round_list()
    
    def update_round_list(self):
        """更新回合列表"""
        if not self.recorder or not self.recorder.snapshots:
            self.round_combo['values'] = []
            return
        
        rounds = [f"回合 {snapshot.round_num}" for snapshot in self.recorder.snapshots]
        self.round_combo['values'] = rounds
        
        # 默认选择最新回合
        if rounds:
            self.round_combo.set(rounds[-1])
            self.selected_round = self.recorder.snapshots[-1].round_num
            self.update_display()
    
    def on_round_combo_changed(self, event):
        """回合选择改变"""
        selected = self.round_var.get()
        if selected and selected.startswith("回合 "):
            round_num = int(selected.split(" ")[1])
            self.selected_round = round_num
            self.update_display()
            
            if self.on_round_selected:
                self.on_round_selected(round_num)
    
    def prev_round(self):
        """上一回合"""
        if not self.recorder or not self.recorder.snapshots:
            return
        
        current_index = self.get_current_round_index()
        if current_index > 0:
            new_snapshot = self.recorder.snapshots[current_index - 1]
            self.selected_round = new_snapshot.round_num
            self.round_combo.set(f"回合 {self.selected_round}")
            self.update_display()
            
            if self.on_round_selected:
                self.on_round_selected(self.selected_round)
    
    def next_round(self):
        """下一回合"""
        if not self.recorder or not self.recorder.snapshots:
            return
        
        current_index = self.get_current_round_index()
        if current_index < len(self.recorder.snapshots) - 1:
            new_snapshot = self.recorder.snapshots[current_index + 1]
            self.selected_round = new_snapshot.round_num
            self.round_combo.set(f"回合 {self.selected_round}")
            self.update_display()
            
            if self.on_round_selected:
                self.on_round_selected(self.selected_round)
    
    def goto_latest(self):
        """跳转到最新回合"""
        if not self.recorder or not self.recorder.snapshots:
            return
        
        latest = self.recorder.snapshots[-1]
        self.selected_round = latest.round_num
        self.round_combo.set(f"回合 {self.selected_round}")
        self.update_display()
        
        if self.on_round_selected:
            self.on_round_selected(self.selected_round)
    
    def get_current_round_index(self) -> int:
        """获取当前回合索引"""
        if not self.recorder:
            return 0
        
        for i, snapshot in enumerate(self.recorder.snapshots):
            if snapshot.round_num == self.selected_round:
                return i
        return 0
    
    def update_display(self):
        """更新显示"""
        if not self.recorder:
            return
        
        snapshot = self.recorder.get_snapshot(self.selected_round)
        if not snapshot:
            return
        
        self.update_overview(snapshot)
        self.update_actions(snapshot)
        self.update_changes(snapshot)
    
    def update_overview(self, snapshot: RoundSnapshot):
        """更新回合概览"""
        self.overview_text.delete(1.0, tk.END)
        
        info = []
        info.append(f"=== 回合 {snapshot.round_num} 概览 ===")
        info.append(f"时间: {snapshot.timestamp.strftime('%H:%M:%S')}")
        info.append(f"战斗状态: {'已结束' if snapshot.battle_ended else '进行中'}")
        
        if snapshot.winner is not None:
            if snapshot.winner == -1:
                info.append(f"结果: 平局")
            else:
                info.append(f"获胜方: 队伍{snapshot.winner}")
        
        info.append("")
        info.append("=== 精灵状态 ===")
        
        for name, spirit in snapshot.spirits.items():
            status = "存活" if spirit.is_alive else "死亡"
            hp_percent = spirit.current_hp / spirit.max_hp * 100 if spirit.max_hp > 0 else 0
            energy_percent = spirit.energy / spirit.max_energy * 100 if spirit.max_energy > 0 else 0
            
            info.append(f"{name} (队伍{spirit.team}): {status}")
            info.append(f"  HP: {spirit.current_hp:.0f}/{spirit.max_hp:.0f} ({hp_percent:.1f}%)")
            info.append(f"  气势: {spirit.energy}/{spirit.max_energy} ({energy_percent:.1f}%)")
            info.append(f"  效果数量: {len(spirit.effects)}")
        
        info.append("")
        info.append("=== 回合统计 ===")
        info.append(f"执行行动数: {len(snapshot.actions_performed)}")
        
        for team in [0, 1]:
            damage = snapshot.total_damage_dealt.get(team, 0)
            healing = snapshot.total_healing_done.get(team, 0)
            info.append(f"队伍{team}: 造成伤害 {damage:.0f}, 治疗 {healing:.0f}")
        
        if snapshot.turn_order:
            info.append("")
            info.append("=== 行动顺序 ===")
            info.append(" -> ".join(snapshot.turn_order))
        
        self.overview_text.insert(tk.END, "\n".join(info))
        self.overview_text.config(state=tk.DISABLED)
    
    def update_actions(self, snapshot: RoundSnapshot):
        """更新行动记录"""
        self.actions_text.delete(1.0, tk.END)

        if not snapshot.actions_performed:
            self.actions_text.insert(tk.END, "本回合无行动记录")
            self.actions_text.config(state=tk.DISABLED)
        else:
            info = []
            info.append(f"=== 回合 {snapshot.round_num} 行动记录 ===")
            info.append("")

            for i, action in enumerate(snapshot.actions_performed, 1):
                info.append(f"行动 {i}: {action.description}")
                info.append(f"  施放者: {action.caster_name}")
                info.append(f"  目标: {', '.join(action.target_names) if action.target_names else '无'}")
                info.append(f"  时间: {action.timestamp.strftime('%H:%M:%S.%f')[:-3]}")

                if action.damage_dealt:
                    info.append(f"  造成伤害: {', '.join(f'{name}:{dmg:.0f}' for name, dmg in action.damage_dealt.items())}")

                if action.healing_done:
                    info.append(f"  治疗量: {', '.join(f'{name}:{heal:.0f}' for name, heal in action.healing_done.items())}")

                if action.effects_applied:
                    info.append(f"  应用效果: {', '.join(f'{name}:{effects}' for name, effects in action.effects_applied.items())}")

                info.append("")

            self.actions_text.insert(tk.END, "\n".join(info))
            self.actions_text.config(state=tk.DISABLED)
    
    def update_changes(self, snapshot: RoundSnapshot):
        """更新状态变化"""
        self.changes_text.delete(1.0, tk.END)
        
        info = []
        info.append(f"=== 回合 {snapshot.round_num} 状态变化 ===")
        info.append("")
        
        if snapshot.changes_summary:
            info.append("变化摘要:")
            info.append(snapshot.changes_summary)
            info.append("")
        
        info.append("详细变化:")
        
        for name, spirit in snapshot.spirits.items():
            changes = []
            
            if spirit.hp_change != 0:
                if spirit.hp_change > 0:
                    changes.append(f"HP +{spirit.hp_change:.0f}")
                else:
                    changes.append(f"HP {spirit.hp_change:.0f}")
            
            if spirit.energy_change != 0:
                if spirit.energy_change > 0:
                    changes.append(f"气势 +{spirit.energy_change}")
                else:
                    changes.append(f"气势 {spirit.energy_change}")
            
            if spirit.effects_added:
                changes.append(f"新增效果: {', '.join(spirit.effects_added)}")
            
            if spirit.effects_removed:
                changes.append(f"移除效果: {', '.join(spirit.effects_removed)}")
            
            if changes:
                info.append(f"{name}: {'; '.join(changes)}")
        
        if len(info) == 3:  # 只有标题，没有实际变化
            info.append("本回合无状态变化")
        
        self.changes_text.insert(tk.END, "\n".join(info))
        self.changes_text.config(state=tk.DISABLED)
    
    def show_comparison(self):
        """显示回合比较"""
        if not self.recorder or len(self.recorder.snapshots) < 2:
            return
        
        # 创建比较窗口
        comparison_window = tk.Toplevel(self.parent)
        comparison_window.title("回合比较")
        comparison_window.geometry("800x600")
        
        # 比较控制
        control_frame = ttk.Frame(comparison_window)
        control_frame.pack(fill=tk.X, pady=10, padx=10)
        
        ttk.Label(control_frame, text="比较回合:").pack(side=tk.LEFT)
        
        round1_var = tk.StringVar()
        round1_combo = ttk.Combobox(control_frame, textvariable=round1_var, width=10, state="readonly")
        round1_combo['values'] = [f"回合 {s.round_num}" for s in self.recorder.snapshots]
        round1_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(control_frame, text="vs").pack(side=tk.LEFT)
        
        round2_var = tk.StringVar()
        round2_combo = ttk.Combobox(control_frame, textvariable=round2_var, width=10, state="readonly")
        round2_combo['values'] = [f"回合 {s.round_num}" for s in self.recorder.snapshots]
        round2_combo.pack(side=tk.LEFT, padx=(10, 10))
        
        # 比较结果显示
        result_text = tk.Text(comparison_window, wrap=tk.WORD)
        result_scroll = ttk.Scrollbar(comparison_window, orient="vertical", command=result_text.yview)
        result_text.configure(yscrollcommand=result_scroll.set)
        result_text.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=(0, 10))
        result_scroll.pack(side="right", fill="y", padx=(0, 10), pady=(0, 10))
        
        def update_comparison():
            round1_text = round1_var.get()
            round2_text = round2_var.get()
            
            if round1_text and round2_text:
                round1 = int(round1_text.split(" ")[1])
                round2 = int(round2_text.split(" ")[1])
                
                comparison = self.recorder.compare_snapshots(round1, round2)
                
                if comparison:
                    result_text.delete(1.0, tk.END)
                    
                    info = []
                    info.append(f"=== 回合 {round1} vs 回合 {round2} 比较 ===")
                    info.append("")
                    
                    info.append("精灵状态变化:")
                    for name, changes in comparison['spirit_changes'].items():
                        change_list = []
                        if changes['hp_change'] != 0:
                            change_list.append(f"HP变化: {changes['hp_change']:+.0f}")
                        if changes['energy_change'] != 0:
                            change_list.append(f"气势变化: {changes['energy_change']:+d}")
                        if changes['alive_change']:
                            change_list.append("生死状态改变")
                        if changes['effects_change'] != 0:
                            change_list.append(f"效果数量变化: {changes['effects_change']:+d}")
                        
                        if change_list:
                            info.append(f"  {name}: {'; '.join(change_list)}")
                    
                    info.append("")
                    info.append("队伍统计变化:")
                    for team, changes in comparison['team_stats_changes'].items():
                        info.append(f"  队伍{team}:")
                        info.append(f"    伤害变化: {changes['damage_change']:+.0f}")
                        info.append(f"    治疗变化: {changes['healing_change']:+.0f}")
                    
                    result_text.insert(tk.END, "\n".join(info))
        
        round1_combo.bind('<<ComboboxSelected>>', lambda e: update_comparison())
        round2_combo.bind('<<ComboboxSelected>>', lambda e: update_comparison())
        
        # 设置默认值
        if len(self.recorder.snapshots) >= 2:
            round1_combo.set(f"回合 {self.recorder.snapshots[0].round_num}")
            round2_combo.set(f"回合 {self.recorder.snapshots[-1].round_num}")
            update_comparison()
    
    def clear(self):
        """清空显示"""
        self.recorder = None
        self.round_combo['values'] = []
        self.round_var.set("")
        
        for text_widget in [self.overview_text, self.actions_text, self.changes_text]:
            text_widget.config(state=tk.NORMAL)
            text_widget.delete(1.0, tk.END)
            text_widget.config(state=tk.DISABLED)
    
    def pack(self, **kwargs):
        """打包布局"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)

    def add_round(self, snapshot: RoundSnapshot):
        """添加新回合"""
        if self.recorder:
            self.recorder.add_snapshot(snapshot)
            self.update_round_list()
            # 自动跳转到最新回合
            self.goto_latest()
