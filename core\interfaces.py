# 核心接口定义模块 - 核心功能已完善
"""
核心接口定义模块

通过定义抽象接口来解决循环依赖问题，提供清晰的契约定义。
"""
from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Protocol, runtime_checkable, Dict, List, Any, Optional, Tuple
from enum import Enum


@runtime_checkable
class ICharacter(Protocol):
    """
    角色接口，定义了作为可玩或NPC角色所具备的特性。
    这比 IBattleEntity 更具体，适用于需要查询职业、标签等元数据的场景。
    """
    def has_profession(self, profession: Any) -> bool:
        """检查角色是否拥有指定职业。"""
        ...
    
    def has_tag(self, tag: str) -> bool:
        """检查角色是否拥有指定标签。"""
        ...


@runtime_checkable
class Identifiable(Protocol):
    """可识别的实体协议"""
    id: str
    name: str


@runtime_checkable
class Positionable(Protocol):
    """可定位的实体协议"""
    @property
    def position(self) -> Tuple[int, int]:
        """获取位置"""
        ...
    
    @position.setter
    def position(self, value: Tuple[int, int]) -> None:
        """设置位置"""
        ...
    
    team: int


@runtime_checkable
class Attributable(Protocol):
    """具有属性的实体协议"""
    def get_attribute(self, attr_name: str) -> float:
        """获取属性值"""
        ...
    
    def set_attribute(self, attr_name: str, value: float) -> None:
        """设置属性值"""
        ...


@runtime_checkable
class Effectible(Protocol):
    """可应用效果的实体协议"""
    def apply_effect(self, effect: Any, battle_state: "IBattleState") -> bool:
        """应用效果"""
        ...
    
    def remove_effect(self, effect_id: str, battle_state: "IBattleState") -> Tuple[Any, Optional[List[Any]]]:
        """移除效果，返回被移除的效果和可能产生的动作列表"""
        ...

    def has_effect(self, effect_name: str) -> bool:
        """检查实体是否拥有指定名称的效果。"""
        ...


@runtime_checkable
class Skillable(Protocol):
    """具有技能的实体协议"""
    skills: List[Any]
    
    def can_cast_skill(self, skill_name: str, battle_state: "IBattleState") -> bool:
        """检查是否可以释放技能"""
        ...


class IBattleEntity(Identifiable, Positionable, Attributable, Effectible, Skillable, Protocol):
    """战斗实体的完整接口"""
    # 从父协议继承的属性需要重新声明以确保类型兼容性
    id: str
    name: str
    team: int
    skills: List[Any]
    
    # 位置属性（从 Positionable 协议继承）
    @property
    def position(self) -> Tuple[int, int]:
        """获取位置"""
        ...
    
    @position.setter
    def position(self, value: Tuple[int, int]) -> None:
        """设置位置"""
        ...
    
    # IBattleEntity 特有的属性
    current_hp: float
    max_hp: float
    is_alive: bool
    energy: int
    max_energy: int
    effect_manager: Any # 允许访问效果管理器
    is_targetable: bool # 角色是否可被攻击
    attributes: Any # 允许访问属性系统
    metadata: Any # 允许访问精灵元数据
    extra_turns: int # 额外回合数，用于连击等机制
    life_state: Any # 生命状态枚举

    def take_damage(self, damage: float) -> float:
        """实体承受伤害并返回实际伤害值。"""
        ...

    def consume_energy(self, amount: int) -> None:
        """
        消耗能量。
        
        如果能量不足，应抛出 InsufficientEnergyException。
        """
        ...

    def set_hp(self, value: float) -> None:
        """设置当前生命值"""
        ...

    def set_energy(self, value: int) -> None:
        """设置当前能量值"""
        ...

    def gain_energy(self, amount: int) -> int:
        """为实体增加能量。"""
        ...

    def on_event(self, event_data: Any, battle_state: "IBattleState") -> List[Any]:
        """处理分发给实体的事件。"""
        ...
        
    def revive(self, hp_percent: float = 1.0) -> None:
        """复活实体。"""
        ...


@runtime_checkable
class IBattleState(Protocol):
    """战斗状态接口"""
    round_num: int
    winner: Optional[int]
    
    def get_spirit_by_id(self, spirit_id: str) -> Optional[IBattleEntity]:
        """根据ID获取精灵"""
        ...
    
    def get_living_spirits(self, team_id: int) -> List[IBattleEntity]:
        """获取指定队伍的存活精灵"""
        ...

    def get_all_living_spirits(self) -> List[IBattleEntity]:
        """获取所有存活的精灵"""
        ...

    def get_all_spirits(self) -> List[IBattleEntity]:
        """获取战场上的所有精灵"""
        ...
        
    def add_log(self, message: str, level: str = "INFO") -> None:
        """添加一条战斗日志"""
        ...

    def get_targetable_living_spirits(self, team_id: int) -> List[IBattleEntity]:
        """获取指定队伍的可被选中的存活精灵"""
        ...


# IEffect已移至src.core.effect.system模块，避免重复定义
# 如需使用IEffect，请从src.core.effect导入
# 为了向后兼容，这里提供一个导入别名
from .effect.system import IEffect


class ISkill(Protocol):
    """技能接口"""
    name: str
    owner: Optional[IBattleEntity]
    
    def can_cast(self, battle_state: IBattleState) -> bool:
        """检查是否可以释放"""
        ...
    
    def cast(self, battle_state: IBattleState, forced_targets: Optional[List[IBattleEntity]] = None) -> List[Any]:
        """释放技能"""
        ...


class IAction(Protocol):
    """动作接口"""
    caster: Optional[IBattleEntity]
    
    def execute(self, battle_state: IBattleState) -> None:
        """执行动作"""
        ...


__all__ = [
    # Protocols
    'Identifiable', 'Positionable', 'Attributable', 'Effectible', 'Skillable',
    # Main Interfaces
    'IBattleEntity', 'IBattleState', 'ISkill', 'IAction',
    # Re-exported from effect module
    'IEffect'
]