# 战斗数据模型 - 核心功能已完善
"""战斗数据模型。

定义了战斗系统的核心数据结构，包括：
- `Battle`: 用于在数据库中存储战斗记录的 SQLAlchemy 模型。
- `BattleState`: 维护战斗过程中的所有运行时状态，如精灵、阵型、回合数等。
"""
from __future__ import annotations

import datetime
from typing import List, Dict, Optional, Tuple, Any, TYPE_CHECKING

try:
    from src.database import Base
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    Base = object

from ...event.events import SpiritSummonedEvent
from ...event.unified_manager import UnifiedEventManager, unified_event_manager
from ...interfaces import IBattleEntity, IBattleState
from ...formation import Formation

if TYPE_CHECKING:
    from ...spirit.refactored_spirit import RefactoredSpirit
    from ...skill import Skill


class BattleState(IBattleState):
    """
    Manages all dynamic runtime state during a battle.
    """
    def __init__(
        self,
        formation_p1: "Formation",
        formation_p2: "Formation",
        unified_event_manager: Optional[UnifiedEventManager] = None,
        global_skills: Optional[List["Skill"]] = None,
    ):
        """Initializes a new battle state."""
        self.spirits: Dict[str, "Spirit"] = {}
        self.formations: Dict[int, "Formation"] = {0: formation_p1, 1: formation_p2}

        # 使用统一事件管理器
        self.unified_event_manager: UnifiedEventManager = unified_event_manager or self._get_global_unified_event_manager()

        self.global_skills: List["Skill"] = global_skills or []
        
        self.round_num: int = 0
        self.action_history: List = []
        self.event_history: List = []
        self.damage_log: List[Dict[str, Any]] = []
        self.winner: Optional[int] = None
        self.log: List[Dict[str, Any]] = []

        self._populate_spirits_from_formations()

    def _populate_spirits_from_formations(self):
        """Initializes spirits from the formations and injects the trigger manager."""
        for team_id, formation in self.formations.items():
            for pos, spirit in formation.grid.items():
                if spirit:
                    spirit.id = f"t{team_id}_{pos[0]}{pos[1]}_{spirit.name}"
                    spirit.team = team_id
                    spirit.position = pos
                    # [NEW] Inject the unified event manager
                    if hasattr(spirit, 'set_unified_event_manager'):
                        spirit.set_unified_event_manager(self.unified_event_manager)
                    self.spirits[spirit.id] = spirit

    def _get_global_unified_event_manager(self) -> UnifiedEventManager:
        """从统一系统管理器获取全局统一事件管理器"""
        from ...system_manager import get_system
        event_manager = get_system('event')
        if event_manager is None:
            raise RuntimeError("统一管理器中的事件系统不可用，无法创建战斗状态")
        return event_manager

    def dispatch_event(self, event, battle_state=None):
        """
        统一事件分发方法
        """
        battle_state = battle_state or self
        return self.unified_event_manager.dispatch(event, battle_state)

    def get_spirit_by_id(self, spirit_id: str) -> Optional[IBattleEntity]:
        """
        根据ID获取一个精灵实例。

        Args:
            spirit_id: 要获取的精灵的唯一ID。

        Returns:
            如果找到，则返回 Spirit 实例，否则返回 None。
        """
        return self.spirits.get(spirit_id)
    
    def get_all_spirits(self) -> List[IBattleEntity]:
        """获取战场上的所有精灵"""
        return list(self.spirits.values())
        
    def get_spirit_position(self, spirit_id: str) -> Optional[Tuple[int, int]]:
        """根据ID获取精灵的位置。"""
        for formation in self.formations.values():
            for pos, spirit in formation.grid.items():
                if spirit and spirit.id == spirit_id:
                    return pos
        return None        
    def get_spirit_at_position(self, pos: Tuple[int, int], team_id: int) -> Optional["Spirit"]:
        """获取指定队伍指定位置的精灵。"""
        formation = self.get_formation_by_team_id(team_id)
        return formation.get_spirit_at(pos[0], pos[1])

    def get_formation_by_team_id(self, team_id: int) -> "Formation":
        """
        通过队伍ID获取阵型。
        """
        if team_id not in self.formations:
            raise ValueError(f"无效的队伍ID: {team_id}")
        return self.formations[team_id]

    def get_living_spirits(self, team_id: int) -> List[IBattleEntity]:
        """
        获取指定队伍中所有存活的精灵。

        Args:
            team_id: 队伍的ID (0或1)。

        Returns:
            一个包含该队伍所有存活精灵的列表。
        """
        formation = self.get_formation_by_team_id(team_id)
        living_spirits: List[IBattleEntity] = []
        # 按标准优先级排序
        for r in range(1, 4):
            for c in range(1, 4):
                spirit = formation.get_spirit_at(r, c)
                if spirit and spirit.is_alive:
                    living_spirits.append(spirit)
        return living_spirits

    def get_targetable_living_spirits(self, team_id: int) -> List[IBattleEntity]:
        """获取指定队伍的可被选中的存活精灵"""
        all_living = self.get_living_spirits(team_id)
        return [spirit for spirit in all_living if spirit.is_targetable]

    def get_all_living_spirits(self) -> List[IBattleEntity]:
        """获取战场上所有存活的精灵。"""
        return self.get_living_spirits(0) + self.get_living_spirits(1)

    def add_log(self, message: str, level: str = "INFO") -> None:
        """Adds a record to the battle log."""
        self.log.append({
            "round": self.round_num,
            "level": level,
            "message": message,
            "timestamp": datetime.datetime.now().isoformat()
        })

    def add_spirit(
        self,
        spirit: "Spirit",
        team_id: int,
        position: Optional[Tuple[int, int]] = None,
        is_summoned: bool = False
    ) -> str:
        """
        在战斗过程中动态地向指定队伍添加一个新精灵。

        通常用于召唤物。

        Args:
            spirit: 要添加的精灵实例。
            team_id: 目标队伍的ID。
            position: 要将精灵放置的位置。如果为None，则自动寻找空位。
            is_summoned: 标记此精灵是否为召唤物。

        Returns:
            分配给新精灵的唯一ID。
            
        Raises:
            ValueError: 如果队伍ID无效或阵型已满且未提供位置。
        """
        if team_id not in (0, 1):
            raise ValueError("team_id 必须是 0 或 1")

        formation = self.get_formation_by_team_id(team_id)

        if position is None:
            position = formation.find_first_empty_slot()
            if position is None:
                raise ValueError(f"队伍 {team_id} 的阵型已满，无法添加新精灵。")

        # 生成唯一ID
        base_id = f"t{team_id}_summon_{position[0]}{position[1]}"
        spirit_id = base_id
        counter = 1
        while spirit_id in self.spirits:
            spirit_id = f"{base_id}_{counter}"
            counter += 1
        
        spirit.id = spirit_id
        spirit.team = team_id
        spirit.position = position
        if is_summoned:
            spirit.metadata.tags.add("summoned")

        self.spirits[spirit_id] = spirit
        formation.grid[position] = spirit

        # 被动效果由精灵的自动发现机制处理，无需手动注册
        
        # 触发召唤事件
        summon_event = SpiritSummonedEvent(spirit=spirit, battle_state=self)
        self.dispatch_event(summon_event, self)
        
        return spirit_id

    # _register_passives 方法已移除，统一使用精灵的自动发现机制

    def _register_global_skills(self) -> None:
        """将全局技能（如契约）注册到事件管理器中。"""
        # 全局被动技能的事件监听已移至Effect内部
        # 通过统一的效果系统处理，无需在此处注册
        pass

    def to_dict(self) -> Dict[str, Any]:
        """Serializes the battle state to a dictionary."""
        return {
            "round_num": self.round_num,
            "winner": self.winner,
            "log": self.log,
            "formations": {
                team_id: formation.to_dict()
                for team_id, formation in self.formations.items()
            },
            "spirits": {
                spirit_id: spirit.to_dict()
                for spirit_id, spirit in self.spirits.items()
            }
        }
