<template>
  <div class="notification-center">
    <TransitionGroup name="notification" tag="div" class="notifications-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification"
        :class="[
          `notification-${notification.type}`,
          { 'notification-persistent': notification.persistent }
        ]"
        @click="removeNotification(notification.id)"
      >
        <!-- 图标 -->
        <div class="notification-icon">
          <el-icon>
            <component :is="getNotificationIcon(notification.type)" />
          </el-icon>
        </div>

        <!-- 内容 -->
        <div class="notification-content">
          <div class="notification-title" v-if="notification.title">
            {{ notification.title }}
          </div>
          <div class="notification-message">
            {{ notification.message }}
          </div>
          <div class="notification-time">
            {{ formatTime(notification.timestamp) }}
          </div>
        </div>

        <!-- 关闭按钮 -->
        <div class="notification-close" v-if="!notification.persistent">
          <el-icon><Close /></el-icon>
        </div>

        <!-- 进度条 -->
        <div 
          class="notification-progress" 
          v-if="!notification.persistent && notification.duration"
          :style="{ 
            animationDuration: `${notification.duration}ms`,
            animationPlayState: notification.paused ? 'paused' : 'running'
          }"
        ></div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { useNotificationStore } from '@/stores/notification'
import dayjs from 'dayjs'

const notificationStore = useNotificationStore()

// 获取通知列表
const notifications = computed(() => notificationStore.notifications)

// 移除通知
const removeNotification = (id: string) => {
  notificationStore.removeNotification(id)
}

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    success: 'CircleCheck',
    error: 'CircleClose',
    warning: 'Warning',
    info: 'InfoFilled',
    battle: 'Sword',
    system: 'Setting'
  }
  return iconMap[type] || 'InfoFilled'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).format('HH:mm:ss')
}
</script>

<style scoped lang="scss">
.notification-center {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;

  .notifications-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 400px;
  }

  .notification {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateX(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    // 类型样式
    &.notification-success {
      border-left: 4px solid #10b981;
      
      .notification-icon {
        color: #10b981;
      }
    }

    &.notification-error {
      border-left: 4px solid #ef4444;
      
      .notification-icon {
        color: #ef4444;
      }
    }

    &.notification-warning {
      border-left: 4px solid #f59e0b;
      
      .notification-icon {
        color: #f59e0b;
      }
    }

    &.notification-info {
      border-left: 4px solid #3b82f6;
      
      .notification-icon {
        color: #3b82f6;
      }
    }

    &.notification-battle {
      border-left: 4px solid #8b5cf6;
      
      .notification-icon {
        color: #8b5cf6;
      }
    }

    &.notification-system {
      border-left: 4px solid #6b7280;
      
      .notification-icon {
        color: #6b7280;
      }
    }

    &.notification-persistent {
      cursor: default;
      
      &:hover {
        transform: none;
      }
    }

    .notification-icon {
      font-size: 20px;
      flex-shrink: 0;
      margin-top: 2px;
    }

    .notification-content {
      flex: 1;
      min-width: 0;

      .notification-title {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .notification-message {
        color: #4b5563;
        font-size: 13px;
        line-height: 1.4;
        word-break: break-word;
      }

      .notification-time {
        color: #9ca3af;
        font-size: 11px;
        margin-top: 4px;
      }
    }

    .notification-close {
      color: #9ca3af;
      cursor: pointer;
      padding: 2px;
      border-radius: 4px;
      transition: all 0.2s ease;
      flex-shrink: 0;

      &:hover {
        color: #6b7280;
        background: rgba(0, 0, 0, 0.05);
      }
    }

    .notification-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 3px;
      background: currentColor;
      opacity: 0.3;
      animation: notificationProgress linear forwards;
    }
  }
}

// 暗色主题
.dark .notification {
  background: rgba(30, 41, 59, 0.95) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;

  .notification-title {
    color: #f8fafc !important;
  }

  .notification-message {
    color: #cbd5e1 !important;
  }

  .notification-time {
    color: #64748b !important;
  }

  .notification-close {
    color: #64748b !important;

    &:hover {
      color: #94a3b8 !important;
      background: rgba(255, 255, 255, 0.1) !important;
    }
  }
}

// 动画
.notification-enter-active {
  transition: all 0.4s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.notification-move {
  transition: transform 0.3s ease;
}

@keyframes notificationProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
</style>