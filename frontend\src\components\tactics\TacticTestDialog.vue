<template>
  <el-dialog v-model="visible" title="战术测试" width="600px">
    <el-empty description="战术测试功能开发中…" image-size="120" />
    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElDialog, ElButton } from 'element-plus'

const props = defineProps<{ modelValue: boolean }>()
const emit = defineEmits<{
  (e: 'update:modelValue', v: boolean): void
}>()

const visible = ref(false)

watch(
  () => visible.value,
  (val) => emit('update:modelValue', val)
)

watch(
  () => props.modelValue,
  (val) => (visible.value = val),
  { immediate: true }
)

function close() {
  visible.value = false
}
</script> 