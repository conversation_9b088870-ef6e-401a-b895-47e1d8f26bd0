"""
天恩圣祭·空灵圣龙 - 技能定义

技能：
1. 牵丝引线 (普攻): 120%魔法伤害，获得30点气势
2. 缄言净缚 (超杀): 300%魔法伤害，为各排最前精灵提供免疫，隐身/神使状态下改变目标选择
"""

from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from core.spirit.spirit import Spirit

from core.skill.skills import (
    Skill, SkillMetadata,
    DamageComponent, SkillEnergyComponent
)
from .skill_components import (
    QianSiYinXianTargetSelector,
    JianYanJingFuTargetSelector,
    JianYanJingFuImmunityComponent
)


def create_qiansi_yinxian_skill(owner_spirit: Spirit) -> Skill:
    """
    创建牵丝引线技能 (普攻)
    
    攻击对手，造成攻击120%的魔法伤害，攻击后获得30点气势
    """
    return Skill(
        owner=owner_spirit,
        metadata=SkillMetadata(
            name="牵丝引线",
            description="攻击对手，造成攻击120%的魔法伤害，攻击后获得30点气势",
            cast_type="ACTIVE",
            category="ATTACK",
            energy_cost=0,
            cooldown=0
        ),
        target_selector=QianSiYinXianTargetSelector(),
        components=[
            DamageComponent(
                power_multiplier=1.2,
                damage_type="magic"
            ),
            SkillEnergyComponent(
                energy_change=30
            )
        ]
    )


def create_jianyan_jingfu_skill(owner_spirit: Spirit) -> Skill:
    """
    创建缄言净缚技能 (超杀)
    
    攻击目标，造成攻击*300%的魔法伤害，攻击后令各横排最靠前的精灵获得免疫(持续1次攻击)，
    若该次攻击在隐身或神使状态下，则更改本次超杀目标为敌阵各横排最靠前的精灵
    """
    return Skill(
        owner=owner_spirit,
        metadata=SkillMetadata(
            name="缄言净缚",
            description="攻击目标，造成攻击*300%的魔法伤害，攻击后令各横排最靠前的精灵获得免疫(持续1次攻击)，若该次攻击在隐身或神使状态下，则更改本次超杀目标为敌阵各横排最靠前的精灵",
            cast_type="ULTIMATE",
            category="ATTACK",
            energy_cost=0,  # 超杀技能不需要固定消耗，只需要阈值判断
            cooldown=0
        ),
        target_selector=JianYanJingFuTargetSelector(),
        components=[
            DamageComponent(
                power_multiplier=3.0,
                damage_type="magic"
            ),
            JianYanJingFuImmunityComponent()
        ]
    )


def create_kongling_skills(owner_spirit: Spirit) -> List[Skill]:
    """创建空灵圣龙的所有技能"""
    return [
        create_qiansi_yinxian_skill(owner_spirit),
        create_jianyan_jingfu_skill(owner_spirit)
    ]


# 导出所有技能创建函数
__all__ = [
    'create_qiansi_yinxian_skill',
    'create_jianyan_jingfu_skill', 
    'create_kongling_skills'
]
