#!/usr/bin/env python3
"""
调试超杀系统问题

检查为什么达到超杀阈值后没有释放超杀技能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_spirit_ultimate_skills():
    """调试精灵的超杀技能配置"""
    print("🔧 调试精灵的超杀技能配置...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        for spirit_name in available_spirits:
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"\n🎯 {spirit.name} 超杀技能分析:")
            
            # 检查精灵的技能
            if hasattr(spirit, 'skills'):
                print(f"  总技能数: {len(spirit.skills)}")
                
                ultimate_skills = []
                for i, skill in enumerate(spirit.skills):
                    if hasattr(skill, 'metadata'):
                        cast_type = getattr(skill.metadata, 'cast_type', 'UNKNOWN')
                        skill_name = getattr(skill.metadata, 'name', f'技能{i}')
                        energy_cost = getattr(skill.metadata, 'energy_cost', 0)
                        
                        print(f"    技能 {i}: {skill_name}")
                        print(f"      类型: {cast_type}")
                        print(f"      气势消耗: {energy_cost}")
                        
                        if cast_type == 'ULTIMATE':
                            ultimate_skills.append(skill)
                            print(f"      ✅ 这是超杀技能！")
                        else:
                            print(f"      ❌ 不是超杀技能")
                
                print(f"  超杀技能数量: {len(ultimate_skills)}")
            else:
                print(f"  ❌ 精灵没有技能属性")
            
            # 检查技能组件
            if hasattr(spirit, 'components'):
                from core.components import SkillComponent
                skill_component = spirit.components.get_component(SkillComponent)
                
                if skill_component:
                    print(f"  技能组件存在: ✅")
                    
                    # 检查get_skills_by_type方法
                    if hasattr(skill_component, 'get_skills_by_type'):
                        ultimate_skills_by_type = skill_component.get_skills_by_type('ULTIMATE')
                        print(f"  通过类型获取的超杀技能: {len(ultimate_skills_by_type)}")
                        
                        for skill in ultimate_skills_by_type:
                            skill_name = getattr(skill.metadata, 'name', 'Unknown') if hasattr(skill, 'metadata') else 'Unknown'
                            print(f"    - {skill_name}")
                    else:
                        print(f"  ❌ 技能组件没有get_skills_by_type方法")
                else:
                    print(f"  ❌ 精灵没有技能组件")
            
            # 检查超杀管理器
            if hasattr(spirit, 'ultimate_manager'):
                print(f"  超杀管理器存在: ✅")
                ultimate_manager = spirit.ultimate_manager
                
                # 检查当前气势
                current_energy = getattr(spirit, 'energy', 0)
                print(f"  当前气势: {current_energy}")
                
                # 检查超杀阈值
                if hasattr(ultimate_manager, 'get_lowest_threshold'):
                    threshold = ultimate_manager.get_lowest_threshold()
                    print(f"  超杀阈值: {threshold}")
                    
                    # 检查是否可以使用超杀
                    can_use = ultimate_manager.can_use_any_ultimate(current_energy)
                    print(f"  可以使用超杀: {can_use}")
                    
                    if current_energy >= threshold:
                        print(f"  ✅ 气势足够使用超杀 ({current_energy} >= {threshold})")
                    else:
                        print(f"  ❌ 气势不足使用超杀 ({current_energy} < {threshold})")
                else:
                    print(f"  ❌ 超杀管理器没有get_lowest_threshold方法")
            else:
                print(f"  ❌ 精灵没有超杀管理器")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_ai_ultimate_selection():
    """调试AI的超杀技能选择"""
    print("\n🔧 调试AI的超杀技能选择...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        print(f"📊 测试精灵: {spirit.name}")
        
        # 手动设置高气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300  # 设置为最大气势
                print(f"  设置气势为: {energy_component._current_energy}")
        
        # 创建AI生成器
        from core.ai.generator import get_ai_generator
        ai_generator = get_ai_generator()
        
        print(f"  AI生成器类型: {type(ai_generator).__name__}")
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        formation1.add_spirit(spirit, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 生成动作
        print(f"\n🎯 为 {spirit.name} 生成动作:")
        print(f"  当前气势: {spirit.energy}")
        
        actions = ai_generator.generate_actions_for_spirit(spirit, battle_state)
        
        print(f"  生成的动作数量: {len(actions)}")
        
        for i, action in enumerate(actions):
            print(f"\n  动作 {i}: {type(action).__name__}")
            
            if hasattr(action, 'skill'):
                skill = action.skill
                if hasattr(skill, 'metadata'):
                    skill_name = getattr(skill.metadata, 'name', 'Unknown')
                    cast_type = getattr(skill.metadata, 'cast_type', 'Unknown')
                    energy_cost = getattr(skill.metadata, 'energy_cost', 0)
                    
                    print(f"    技能名称: {skill_name}")
                    print(f"    技能类型: {cast_type}")
                    print(f"    气势消耗: {energy_cost}")
                    
                    if cast_type == 'ULTIMATE':
                        print(f"    ✅ 这是超杀技能！")
                    else:
                        print(f"    ❌ 不是超杀技能")
            
            if hasattr(action, 'target'):
                print(f"    目标: {action.target.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI超杀选择调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_ultimate_skill_execution():
    """调试超杀技能的执行"""
    print("\n🔧 调试超杀技能的执行...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        print(f"📊 测试精灵: {spirit.name}")
        
        # 查找超杀技能
        ultimate_skill = None
        if hasattr(spirit, 'skills'):
            for skill in spirit.skills:
                if hasattr(skill, 'metadata') and skill.metadata.cast_type == 'ULTIMATE':
                    ultimate_skill = skill
                    break
        
        if not ultimate_skill:
            print(f"  ❌ 没有找到超杀技能")
            return False
        
        skill_name = getattr(ultimate_skill.metadata, 'name', 'Unknown')
        print(f"  找到超杀技能: {skill_name}")
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 设置足够的气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置气势为: {energy_component._current_energy}")
        
        # 检查技能是否可以释放
        print(f"\n🎯 检查技能释放条件:")
        
        # 检查气势消耗
        energy_cost = getattr(ultimate_skill.metadata, 'energy_cost', 0)
        current_energy = spirit.energy
        print(f"  技能气势消耗: {energy_cost}")
        print(f"  当前气势: {current_energy}")
        
        if current_energy >= energy_cost:
            print(f"  ✅ 气势足够")
        else:
            print(f"  ❌ 气势不足")
        
        # 检查技能的can_cast方法
        if hasattr(ultimate_skill, 'can_cast'):
            try:
                can_cast = ultimate_skill.can_cast(battle_state)
                print(f"  技能can_cast结果: {can_cast}")
            except Exception as e:
                print(f"  技能can_cast出错: {e}")
        
        # 尝试直接执行技能
        print(f"\n🎯 尝试直接执行超杀技能:")
        
        try:
            # 选择目标
            targets = [spirit2]  # 简单选择敌方精灵作为目标
            
            # 执行技能
            actions = ultimate_skill.execute(targets, battle_state)
            
            print(f"  技能执行成功！")
            print(f"  生成的动作数量: {len(actions)}")
            
            for i, action in enumerate(actions):
                print(f"    动作 {i}: {type(action).__name__}")
                if hasattr(action, 'damage_value'):
                    print(f"      伤害值: {action.damage_value}")
                if hasattr(action, 'is_ultimate'):
                    print(f"      是否超杀: {action.is_ultimate}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 技能执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 超杀技能执行调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_battle_ultimate_trigger():
    """调试战斗中的超杀触发"""
    print("\n🔧 调试战斗中的超杀触发...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 设置高气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置 {spirit1.name} 气势为: {energy_component._current_energy}")
        
        print(f"📊 战斗前状态:")
        print(f"  {spirit1.name}: 气势 {spirit1.energy}")
        print(f"  {spirit2.name}: 气势 {spirit2.energy}")
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=3,
            turn_order_bonus_energy=50
        )
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        print(f"  回合结果: {result}")
        
        # 检查是否有超杀技能被使用
        if hasattr(engine, 'battle_state') and hasattr(engine.battle_state, 'action_history'):
            history = engine.battle_state.action_history
            print(f"  行动历史记录数: {len(history)}")
            
            ultimate_used = False
            for i, action in enumerate(history):
                print(f"    行动 {i}: {action}")
                
                # 检查是否是超杀动作
                if hasattr(action, 'is_ultimate') and action.is_ultimate:
                    ultimate_used = True
                    print(f"      ✅ 发现超杀动作！")
                elif hasattr(action, 'skill_name'):
                    skill_name = action.skill_name
                    if '超杀' in skill_name or 'ULTIMATE' in skill_name:
                        ultimate_used = True
                        print(f"      ✅ 发现超杀技能: {skill_name}")
            
            if ultimate_used:
                print(f"  ✅ 超杀技能被使用了")
            else:
                print(f"  ❌ 没有使用超杀技能")
        
        return True
        
    except Exception as e:
        print(f"❌ 战斗超杀触发调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 超杀系统问题调试")
    print("="*60)
    
    tests = [
        ("精灵超杀技能配置", debug_spirit_ultimate_skills),
        ("AI超杀技能选择", debug_ai_ultimate_selection),
        ("超杀技能执行", debug_ultimate_skill_execution),
        ("战斗中超杀触发", debug_battle_ultimate_trigger),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
