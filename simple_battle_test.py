#!/usr/bin/env python3
"""
简化战斗测试程序

用于快速测试战斗系统是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_system_initialization():
    """测试系统初始化"""
    print("🚀 测试系统初始化...")
    
    try:
        # 初始化核心系统
        print("1. 初始化核心系统管理器...")
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        print("✅ 核心系统管理器初始化成功")
        
        # 初始化精灵服务
        print("2. 初始化精灵服务...")
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        print(f"✅ 发现 {len(available_spirits)} 个可用精灵")
        
        # 显示前几个精灵
        if available_spirits:
            print("前5个可用精灵:")
            for i, spirit_id in enumerate(available_spirits[:5], 1):
                print(f"  {i}. {spirit_id}")
        
        # 初始化AI系统
        print("3. 初始化AI系统...")
        from core.ai import get_action_generator
        ai_generator = get_action_generator()
        print("✅ AI行动生成系统初始化成功")
        
        return True, spirit_service, available_spirits
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, []

def create_simple_formations(spirit_service, available_spirits):
    """创建简单的测试阵型"""
    print("\n🏗️ 创建测试阵型...")
    
    if len(available_spirits) < 2:
        print("❌ 可用精灵不足2个")
        return None, None
    
    try:
        from core.formation import Formation
        
        # 创建阵型1 - 使用第一个精灵
        formation1 = Formation()
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        if spirit1:
            formation1.add_spirit(spirit1, 1, 1)
            print(f"✅ 队伍1: {spirit1.name} (HP: {spirit1.current_hp})")
        else:
            print("❌ 创建队伍1精灵失败")
            return None, None
        
        # 创建阵型2 - 使用第二个精灵
        formation2 = Formation()
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        if spirit2:
            formation2.add_spirit(spirit2, 3, 1)
            print(f"✅ 队伍2: {spirit2.name} (HP: {spirit2.current_hp})")
        else:
            print("❌ 创建队伍2精灵失败")
            return None, None
        
        return formation1, formation2
        
    except Exception as e:
        print(f"❌ 创建阵型失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_battle_engine(formation1, formation2):
    """测试战斗引擎"""
    print("\n⚔️ 测试战斗引擎...")
    
    try:
        from core.battle.engines import create_battle_engine
        
        # 创建战斗引擎
        battle_engine = create_battle_engine(
            formation1=formation1,
            formation2=formation2,
            victory="ko",
            round_limit=10,  # 明确设置回合限制为10
            executor_type="phased"
        )
        print("✅ 战斗引擎创建成功")
        
        # 运行一回合测试
        print("🎮 运行战斗测试...")
        round_count = 0
        
        for round_result in battle_engine.run_battle():
            round_count += 1
            print(f"📊 第{round_count}回合完成")
            
            if round_result.get("type") == "battle_end":
                winner = round_result.get("winner")
                if winner is not None:
                    team_name = "队伍1" if winner == 0 else "队伍2"
                    print(f"🏆 战斗结束！获胜方: {team_name}")
                else:
                    print("🤝 战斗平局")
                break
            
            # 限制测试回合数
            if round_count >= 5:
                print("⏰ 测试回合数达到限制，停止战斗")
                break
        
        print("✅ 战斗引擎测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 战斗引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🎮 简化战斗系统测试")
    print("="*60)
    
    # 1. 测试系统初始化
    success, spirit_service, available_spirits = test_system_initialization()
    if not success:
        print("❌ 系统初始化失败，测试终止")
        return
    
    # 2. 创建测试阵型
    formation1, formation2 = create_simple_formations(spirit_service, available_spirits)
    if not formation1 or not formation2:
        print("❌ 阵型创建失败，测试终止")
        return
    
    # 3. 测试战斗引擎
    battle_success = test_battle_engine(formation1, formation2)
    
    # 4. 总结
    print("\n" + "="*60)
    if battle_success:
        print("🎉 所有测试通过！战斗系统工作正常！")
        print("✅ 系统初始化: 成功")
        print("✅ 精灵创建: 成功")
        print("✅ 阵型创建: 成功")
        print("✅ 战斗引擎: 成功")
        print("✅ AI系统: 集成成功")
    else:
        print("❌ 部分测试失败")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
