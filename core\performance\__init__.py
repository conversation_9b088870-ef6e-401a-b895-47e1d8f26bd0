from __future__ import annotations
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any, Callable, TypeVar

# 第三方库导入
import functools
from collections import defaultdict, deque
import functools
import threading
import time

"""
性能监控系统

提供性能分析、监控和优化工具。
"""


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"        # 计数器
    GAUGE = "gauge"           # 仪表盘
    HISTOGRAM = "histogram"   # 直方图
    TIMER = "timer"          # 计时器


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    metric_type: MetricType
    value: float = 0.0
    count: int = 0
    min_value: float = float('inf')
    max_value: float = float('-inf')
    sum_value: float = 0.0
    timestamp: float = field(default_factory=time.time)
    
    def update(self, value: float) -> None:
        """更新指标"""
        self.value = value
        self.count += 1
        self.sum_value += value
        self.min_value = min(self.min_value, value)
        self.max_value = max(self.max_value, value)
        self.timestamp = time.time()
    
    @property
    def average(self) -> float:
        """平均值"""
        return self.sum_value / self.count if self.count > 0 else 0.0


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.samples: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_samples))
        self.active_timers: Dict[str, float] = {}
        self._lock = threading.Lock()
    
    def counter(self, name: str, value: float = 1.0) -> None:
        """记录计数器指标"""
        with self._lock:
            if name not in self.metrics:
                self.metrics[name] = PerformanceMetric(name, MetricType.COUNTER)
            
            self.metrics[name].update(value)
            self.samples[name].append((time.time(), value))
    
    def gauge(self, name: str, value: float) -> None:
        """记录仪表盘指标"""
        with self._lock:
            if name not in self.metrics:
                self.metrics[name] = PerformanceMetric(name, MetricType.GAUGE)
            
            self.metrics[name].update(value)
            self.samples[name].append((time.time(), value))
    
    def timer_start(self, name: str) -> None:
        """开始计时"""
        with self._lock:
            self.active_timers[name] = time.time()
    
    def timer_end(self, name: str) -> float:
        """结束计时并记录"""
        with self._lock:
            if name not in self.active_timers:
                return 0.0
            
            duration = time.time() - self.active_timers[name]
            del self.active_timers[name]
            
            if name not in self.metrics:
                self.metrics[name] = PerformanceMetric(name, MetricType.TIMER)
            
            self.metrics[name].update(duration)
            self.samples[name].append((time.time(), duration))
            
            return duration
    
    def histogram(self, name: str, value: float) -> None:
        """记录直方图指标"""
        with self._lock:
            if name not in self.metrics:
                self.metrics[name] = PerformanceMetric(name, MetricType.HISTOGRAM)
            
            self.metrics[name].update(value)
            self.samples[name].append((time.time(), value))
    
    def get_metric(self, name: str) -> Optional[PerformanceMetric]:
        """获取指标"""
        return self.metrics.get(name)
    
    def get_all_metrics(self) -> Dict[str, PerformanceMetric]:
        """获取所有指标"""
        return self.metrics.copy()
    
    def get_samples(self, name: str, limit: Optional[int] = None) -> List[tuple]:
        """获取样本数据"""
        samples = list(self.samples[name])
        if limit:
            samples = samples[-limit:]
        return samples
    
    def reset(self) -> None:
        """重置所有指标"""
        with self._lock:
            self.metrics.clear()
            self.samples.clear()
            self.active_timers.clear()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {}
        for name, metric in self.metrics.items():
            summary[name] = {
                'type': metric.metric_type.value,
                'current': metric.value,
                'count': metric.count,
                'average': metric.average,
                'min': metric.min_value if metric.min_value != float('inf') else None,
                'max': metric.max_value if metric.max_value != float('-inf') else None,
                'total': metric.sum_value
            }
        return summary


# 性能监控装饰器
def performance_monitor(metric_name: Optional[str] = None, metric_type: MetricType = MetricType.TIMER):
    """
    性能监控装饰器
    
    Args:
        metric_name: 指标名称，默认使用函数名
        metric_type: 指标类型
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        name = metric_name or f"{func.__module__}.{func.__qualname__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if metric_type == MetricType.TIMER:
                profiler.timer_start(name)
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    profiler.timer_end(name)
            elif metric_type == MetricType.COUNTER:
                profiler.counter(name)
                return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


def monitor_battle_performance(func: Callable[..., Any]) -> Callable[..., Any]:
    """战斗性能监控装饰器"""
    return performance_monitor(f"battle.{func.__name__}", MetricType.TIMER)(func)


def monitor_skill_performance(func: Callable[..., Any]) -> Callable[..., Any]:
    """技能性能监控装饰器"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return performance_monitor(f"skill.{func.__name__}", MetricType.TIMER)(func)(*args, **kwargs)

    return wrapper


def monitor_effect_performance(func: Callable[..., Any]) -> Callable[..., Any]:
    """效果性能监控装饰器"""
    return performance_monitor(f"effect.{func.__name__}", MetricType.TIMER)(func)


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, profiler: PerformanceProfiler):
        self.profiler = profiler
    
    def analyze_bottlenecks(self, threshold: float = 0.1) -> List[Dict[str, Any]]:
        """分析性能瓶颈"""
        bottlenecks = []
        
        for name, metric in self.profiler.get_all_metrics().items():
            if metric.metric_type == MetricType.TIMER and metric.average > threshold:
                bottlenecks.append({
                    'name': name,
                    'average_time': metric.average,
                    'max_time': metric.max_value,
                    'call_count': metric.count,
                    'total_time': metric.sum_value
                })
        
        # 按平均时间排序
        bottlenecks.sort(key=lambda x: x['average_time'], reverse=True)
        return bottlenecks
    
    def get_top_consumers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取耗时最多的操作"""
        consumers = []
        
        for name, metric in self.profiler.get_all_metrics().items():
            if metric.metric_type == MetricType.TIMER:
                consumers.append({
                    'name': name,
                    'total_time': metric.sum_value,
                    'average_time': metric.average,
                    'call_count': metric.count
                })
        
        # 按总耗时排序
        consumers.sort(key=lambda x: x['total_time'], reverse=True)
        return consumers[:limit]
    
    def generate_report(self) -> str:
        """生成性能报告"""
        summary = self.profiler.get_summary()
        bottlenecks = self.analyze_bottlenecks()
        top_consumers = self.get_top_consumers()
        
        report = ["=== 性能分析报告 ===\n"]
        
        # 总体统计
        report.append("总体统计:")
        report.append(f"  监控指标数量: {len(summary)}")
        
        timer_metrics = [m for m in summary.values() if m['type'] == 'timer']
        if timer_metrics:
            total_time = sum(m['total'] for m in timer_metrics)
            report.append(f"  总执行时间: {total_time:.4f}s")
        
        # 性能瓶颈
        if bottlenecks:
            report.append("\n性能瓶颈 (平均耗时 > 0.1s):")
            for bottleneck in bottlenecks[:5]:
                report.append(f"  {bottleneck['name']}: {bottleneck['average_time']:.4f}s (调用{bottleneck['call_count']}次)")
        
        # 耗时最多的操作
        if top_consumers:
            report.append("\n耗时最多的操作:")
            for consumer in top_consumers[:5]:
                report.append(f"  {consumer['name']}: {consumer['total_time']:.4f}s (平均{consumer['average_time']:.4f}s)")
        
        return "\n".join(report)


# 全局性能分析器实例
profiler = PerformanceProfiler()
analyzer = PerformanceAnalyzer(profiler)


__all__ = [
    'MetricType',
    'PerformanceMetric',
    'PerformanceProfiler',
    'PerformanceAnalyzer',
    'performance_monitor',
    'monitor_battle_performance',
    'monitor_skill_performance', 
    'monitor_effect_performance',
    'profiler',
    'analyzer'
]