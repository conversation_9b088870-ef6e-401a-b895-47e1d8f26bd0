# 本地导入
from .base import IBattlePhase
from .battle_end import BattleEndPhase
from .initialization import BattleInitializationPhase
from .round_end import RoundEndPhase
from .round_start import RoundStartPhase

"""Battle phase modules package.

This package groups individual **battle phases** to keep responsibilities isolated.
External code should import phase classes from this package for convenience:

```python
    BattleInitializationPhase,
    RoundStartPhase,
    ActionGenerationPhase,
    RoundEndPhase,
    BattleEndPhase,
)
```
"""


# Concrete phases

__all__ = [
    "IBattlePhase",
    "BattleInitializationPhase",
    "RoundStartPhase",
    "RoundEndPhase",
    "BattleEndPhase",
]