"""
高级Action处理器

处理奥奇传说游戏中的高级Action类型，包括护盾、反射、传送、能量管理和特殊状态。
"""
from __future__ import annotations

from typing import List, Optional
from core.battle.executor.executor import handler, UnifiedActionExecutor
from .decorators import validate_target, safe_execute, monitor_performance
from ...action import (
    BattleAction,
    # 护盾系统
    CreateShieldAction, UpdateShieldAction, RemoveShieldAction,
    # 反射系统
    ReflectDamageAction, ReflectEffectAction,
    # 传送系统
    TeleportAction, SwapPositionAction,  # 能量系统
    TransferEnergyAction, DrainEnergyAction,  # 特殊状态
    InvincibilityAction, StealthAction, ForbidAction, TransformAction
)


# ============================================================================
# 护盾系统处理器
# ============================================================================

@handler(CreateShieldAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
@monitor_performance(slow_threshold=0.1)
def _handle_create_shield(
    self: UnifiedActionExecutor, action: CreateShieldAction
) -> Optional[List[BattleAction]]:
    """处理创建护盾动作"""
    target = action.target
    
    # 检查目标是否有护盾管理器
    if not hasattr(target, 'shield_manager'):
        # 如果没有，创建一个简单的护盾存储
        target.shields = getattr(target, 'shields', {})
    
    # 生成护盾ID
    shield_id = action.shield_id or f"shield_{len(getattr(target, 'shields', {}))}"
    
    # 创建护盾数据
    shield_data = {
        'id': shield_id,
        'value': action.shield_value,
        'type': action.shield_type,
        'duration': action.duration,
        'can_stack': action.can_stack,
        'reflect_damage': action.reflect_damage,
        'reflect_ratio': action.reflect_ratio,
        'created_round': self.battle_state.round_num
    }
    
    # 检查是否可以叠加
    if not action.can_stack and shield_id in getattr(target, 'shields', {}):
        # 替换现有护盾
        target.shields[shield_id] = shield_data
    else:
        # 添加新护盾
        if not hasattr(target, 'shields'):
            target.shields = {}
        target.shields[shield_id] = shield_data
    
    # 记录日志
    from ...action import LogAction
    log_msg = f"{target.name} 获得了 {action.shield_value} 点{action.shield_type}护盾"
    
    return [LogAction(caster=action.caster, message=log_msg)]


@handler(UpdateShieldAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_update_shield(
    self: ActionExecutor, action: UpdateShieldAction
) -> Optional[List[BattleAction]]:
    """处理更新护盾动作"""
    target = action.target
    shields = getattr(target, 'shields', {})
    
    if action.shield_id not in shields:
        return None
    
    shield = shields[action.shield_id]
    
    # 更新护盾值
    if action.value_change is not None:
        shield['value'] = max(0, shield['value'] + action.value_change)
        
        # 如果护盾值为0，移除护盾
        if shield['value'] <= 0:
            del shields[action.shield_id]
            from ...action import LogAction
            return [LogAction(
                caster=action.caster,
                message=f"{target.name} 的护盾被破坏了"
            )]
    
    # 更新持续时间
    if action.duration_change is not None:
        shield['duration'] += action.duration_change
        
        # 如果持续时间为0，移除护盾
        if shield['duration'] <= 0:
            del shields[action.shield_id]
    
    # 更新其他属性
    if action.new_properties:
        shield.update(action.new_properties)
    
    return None


@handler(RemoveShieldAction)
@validate_target(alive_required=False)  # 死亡精灵也可能需要移除护盾
@safe_execute(log_errors=True)
def _handle_remove_shield(
    self: ActionExecutor, action: RemoveShieldAction
) -> Optional[List[BattleAction]]:
    """处理移除护盾动作"""
    target = action.target
    shields = getattr(target, 'shields', {})
    
    if not shields:
        return None
    
    removed_shields = []
    
    if action.shield_id:
        # 移除特定护盾
        if action.shield_id in shields:
            removed_shields.append(shields.pop(action.shield_id))
    elif action.shield_type:
        # 移除特定类型的护盾
        to_remove = [sid for sid, shield in shields.items() 
                    if shield['type'] == action.shield_type]
        for sid in to_remove:
            removed_shields.append(shields.pop(sid))
    else:
        # 移除所有护盾
        removed_shields = list(shields.values())
        shields.clear()
    
    if removed_shields:
        from ...action import LogAction
        return [LogAction(
            caster=action.caster,
            message=f"{target.name} 失去了 {len(removed_shields)} 个护盾"
        )]
    
    return None


# ============================================================================
# 反射系统处理器
# ============================================================================

@handler(ReflectDamageAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
@monitor_performance(slow_threshold=0.1)
def _handle_reflect_damage(
    self: ActionExecutor, action: ReflectDamageAction
) -> Optional[List[BattleAction]]:
    """处理反射伤害动作"""
    from ...action import DamageAction, LogAction
    
    # 创建反射伤害
    reflect_damage_action = DamageAction(
        caster=action.original_target,  # 反射者作为施法者
        target=action.reflect_target,
        damage_value=action.reflect_damage,
        damage_type=action.original_damage.damage_type if hasattr(action, 'original_damage') else "physical",
        label="反射伤害"
    )
    
    # 如果无视防御，设置特殊标记
    if action.ignore_defense:
        reflect_damage_action.power_multiplier = 999.0  # 高倍数表示无视防御
    
    log_action = LogAction(
        caster=action.caster,
        message=f"{action.original_target.name} 反射了 {action.reflect_damage} 点伤害给 {action.reflect_target.name}"
    )
    
    return [reflect_damage_action, log_action]


@handler(ReflectEffectAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_reflect_effect(
    self: ActionExecutor, action: ReflectEffectAction
) -> Optional[List[BattleAction]]:
    """处理反射效果动作"""
    if not action.reflect_success:
        return None
    
    from ...action import ApplyEffectAction, RemoveEffectAction, LogAction
    
    actions = []
    
    # 将效果施加给反射目标
    reflect_apply = ApplyEffectAction(
        caster=action.original_target,
        target=action.reflect_target,
        effect=action.effect,
        from_attack=True
    )
    actions.append(reflect_apply)
    
    # 如果需要，移除原始目标的效果
    if action.original_effect_removed:
        remove_original = RemoveEffectAction(
            caster=action.caster,
            target=action.original_target,
            effect_id=action.effect.effect_id
        )
        actions.append(remove_original)
    
    # 记录日志
    log_action = LogAction(
        caster=action.caster,
        message=f"{action.original_target.name} 反射了效果 {action.effect.name} 给 {action.reflect_target.name}"
    )
    actions.append(log_action)
    
    return actions


# ============================================================================
# 传送系统处理器
# ============================================================================

@handler(TeleportAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_teleport(
    self: ActionExecutor, action: TeleportAction
) -> Optional[List[BattleAction]]:
    """处理传送动作"""
    target = action.target
    old_position = target.position
    
    # 检查新位置是否有效
    if not self._is_valid_position(action.new_position):
        return None
    
    # 检查是否可以被阻挡
    if not action.can_be_blocked or self._can_teleport_to(target, action.new_position):
        # 执行传送
        target.position = action.new_position
        
        from ...action import LogAction
        log_action = LogAction(
            caster=action.caster,
            message=f"{target.name} 从 {old_position} 传送到 {action.new_position}"
        )
        
        # 如果需要触发位置效果
        if action.trigger_effects:
            # 这里可以添加位置相关的效果触发逻辑
            pass
        
        return [log_action]
    
    return None


@handler(SwapPositionAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_swap_position(
    self: ActionExecutor, action: SwapPositionAction
) -> Optional[List[BattleAction]]:
    """处理位置交换动作"""
    target1, target2 = action.target1, action.target2
    
    # 检查是否可以交换
    if not action.force_swap:
        if not (self._can_teleport_to(target1, target2.position) and 
                self._can_teleport_to(target2, target1.position)):
            return None
    
    # 执行位置交换
    pos1, pos2 = target1.position, target2.position
    target1.position = pos2
    target2.position = pos1
    
    from ...action import LogAction
    return [LogAction(
        caster=action.caster,
        message=f"{target1.name} 和 {target2.name} 交换了位置"
    )]


def _is_valid_position(self, position: tuple[int, int]) -> bool:
    """检查位置是否有效"""
    # 这里应该根据实际的战场大小来判断
    x, y = position
    return 0 <= x < 3 and 0 <= y < 3  # 假设3x3战场


def _can_teleport_to(self, spirit, position: tuple[int, int]) -> bool:
    """检查是否可以传送到指定位置"""
    # 检查位置是否被占用
    for other_spirit in self.battle_state.get_all_spirits():
        if other_spirit != spirit and other_spirit.position == position and other_spirit.is_alive:
            return False
    return True


# ============================================================================
# 能量系统处理器
# ============================================================================

@handler(TransferEnergyAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_transfer_energy(
    self: ActionExecutor, action: TransferEnergyAction
) -> Optional[List[BattleAction]]:
    """处理能量转移动作"""
    source, target = action.source, action.target

    # 计算实际转移量
    available_energy = getattr(source, 'energy', 0)
    actual_transfer = min(action.amount, available_energy)

    if actual_transfer <= 0:
        return None

    # 计算转移效率
    effective_transfer = int(actual_transfer * action.efficiency)

    # 执行能量转移
    source.energy = max(0, source.energy - actual_transfer)

    if action.can_exceed_max:
        target.energy += effective_transfer
    else:
        max_energy = getattr(target, 'max_energy', 150)
        target.energy = min(max_energy, target.energy + effective_transfer)

    from ...action import LogAction
    return [LogAction(
        caster=action.caster,
        message=f"{source.name} 转移了 {effective_transfer} 点能量给 {target.name}"
    )]


@handler(DrainEnergyAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_drain_energy(
    self: ActionExecutor, action: DrainEnergyAction
) -> Optional[List[BattleAction]]:
    """处理能量吸取动作"""
    target = action.target
    caster = action.caster

    # 计算吸取量
    if action.drain_type == "percentage":
        drain_amount = int(target.energy * action.drain_amount / 100)
    else:
        drain_amount = min(action.drain_amount, target.energy)

    if drain_amount <= 0:
        return None

    # 执行能量吸取
    target.energy = max(0, target.energy - drain_amount)

    # 转化为其他资源
    converted_amount = int(drain_amount * action.conversion_ratio)

    actions = []

    if action.convert_to == "energy" and caster:
        caster.energy = min(getattr(caster, 'max_energy', 150),
                           caster.energy + converted_amount)
    elif action.convert_to == "hp" and caster:
        # 使用SetHPAction来安全地修改HP
        from ...action import SetHPAction
        current_hp = getattr(caster, 'hp', 0)
        max_hp = getattr(caster, 'max_hp', 1000)
        new_hp = min(max_hp, current_hp + converted_amount)
        hp_action = SetHPAction(caster=action.caster, target=caster, hp=new_hp)
        actions.append(hp_action)
    elif action.convert_to == "shield" and caster:
        # 创建护盾
        from ...action import CreateShieldAction
        shield_action = CreateShieldAction(
            caster=caster,
            target=caster,
            shield_value=converted_amount,
            shield_type="energy_shield"
        )
        actions.append(shield_action)

    from ...action import LogAction
    actions.append(LogAction(
        caster=action.caster,
        message=f"{caster.name if caster else '未知'} 吸取了 {target.name} 的 {drain_amount} 点能量"
    ))

    return actions


# ============================================================================
# 特殊状态处理器
# ============================================================================

@handler(InvincibilityAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_invincibility(
    self: ActionExecutor, action: InvincibilityAction
) -> Optional[List[BattleAction]]:
    """处理无敌状态动作"""
    target = action.target

    # 使用属性修改来实现无敌状态
    from ...action import ModifyAttributeAction, LogAction

    # 设置一个极高的防御值来模拟无敌
    invincibility_modifier = ModifyAttributeAction(
        caster=action.caster,
        target=target,
        attribute_name="defense",
        value=99999.0,  # 极高防御值
        source_id=f"invincibility_{action.duration}",
        is_remove=False
    )

    log_action = LogAction(
        caster=action.caster,
        message=f"{target.name} 获得了 {action.duration} 回合的无敌状态"
    )

    return [invincibility_modifier, log_action]


@handler(StealthAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_stealth(
    self: ActionExecutor, action: StealthAction
) -> Optional[List[BattleAction]]:
    """处理隐身状态动作"""
    target = action.target

    # 使用属性修改来实现隐身状态
    from ...action import ModifyAttributeAction, LogAction

    # 设置一个特殊标记来表示隐身状态
    stealth_modifier = ModifyAttributeAction(
        caster=action.caster,
        target=target,
        attribute_name="stealth_level",
        value=float(action.duration),
        source_id=f"stealth_{action.stealth_type}",
        is_remove=False
    )

    log_action = LogAction(
        caster=action.caster,
        message=f"{target.name} 进入了隐身状态"
    )

    return [stealth_modifier, log_action]


@handler(ForbidAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_forbid(
    self: ActionExecutor, action: ForbidAction
) -> Optional[List[BattleAction]]:
    """处理禁止状态动作"""
    target = action.target

    from ...action import ModifyAttributeAction, LogAction

    # 使用属性修改来标记禁止状态
    forbid_modifier = ModifyAttributeAction(
        caster=action.caster,
        target=target,
        attribute_name=f"forbid_{action.forbid_type}",
        value=float(action.duration),
        source_id=f"forbid_{action.forbid_type}_{action.duration}",
        is_remove=False
    )

    log_action = LogAction(
        caster=action.caster,
        message=f"{target.name} 被禁止使用 {action.forbid_type} {action.duration} 回合"
    )

    return [forbid_modifier, log_action]


@handler(TransformAction)
@validate_target(alive_required=True)
@safe_execute(log_errors=True)
def _handle_transform(
    self: ActionExecutor, action: TransformAction
) -> Optional[List[BattleAction]]:
    """处理变身状态动作"""
    target = action.target

    from ...action import ModifyAttributeAction, LogAction

    actions = []

    # 应用属性变化
    for attr_name, change_value in action.attribute_changes.items():
        attr_modifier = ModifyAttributeAction(
            caster=action.caster,
            target=target,
            attribute_name=attr_name,
            value=change_value,
            source_id=f"transform_{action.new_form_id}",
            is_remove=False
        )
        actions.append(attr_modifier)

    # 记录变身
    log_action = LogAction(
        caster=action.caster,
        message=f"{target.name} 变身为 {action.new_form_id}"
    )
    actions.append(log_action)

    return actions


# 注意：辅助方法已在处理器中直接实现，无需绑定到ActionExecutor类
