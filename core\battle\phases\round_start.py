"""RoundStartPhase – handles start-of-round logic."""
from __future__ import annotations

from typing import List, Any

from .base import IBattlePhase
from ..models import BattleState  # type: ignore
from ...performance import monitor_battle_performance


class RoundStartPhase(IBattlePhase):
    """回合开始阶段 – 递增回合计数并分发 RoundStartEvent。"""

    @monitor_battle_performance
    def execute(self, battle_state: BattleState) -> List[Any]:  # noqa: D401
        from ...event.events import RoundStartEvent

        actions: List[Any] = []
        battle_state.round_num += 1

        # 第一回合开始时，并发应用所有精灵的被动技能
        # 🔧 重新启用被动技能应用 - 修复了精灵异常死亡的问题
        if battle_state.round_num == 1:
            try:
                passive_actions = self._apply_passive_skills_concurrently(battle_state)
                actions.extend(passive_actions)
            except Exception as e:
                from ...logging import get_logger
                logger = get_logger("battle.phases")
                logger.error(f"被动技能应用失败: {e}")
                # 继续执行，不让被动技能错误中断战斗

        # 直接分发回合开始事件（精简版本）
        round_start_event = RoundStartEvent(round_num=battle_state.round_num)

        # 直接调用事件管理器，避免 DispatchEventAction 的包装开销
        try:
            event_actions = battle_state.dispatch_event(round_start_event, battle_state)
            if event_actions:
                actions.extend(event_actions)
        except Exception as e:
            # 错误处理：记录但不中断流程
            from ...logging import get_logger
            logger = get_logger("battle.phases")
            logger.warning(f"回合开始事件分发失败: {e}")

        return actions

    def _apply_passive_skills_concurrently(self, battle_state: BattleState) -> List[Any]:
        """在第一回合开始时并发应用所有精灵的被动技能"""
        from ...skill.skills import Skill
        from ...logging import battle_logger
        from ...action import LogAction

        actions: List[Any] = []

        # 收集所有精灵的被动技能
        passive_skills = []
        for spirit in battle_state.get_all_spirits():
            if not spirit.is_alive:
                continue

            for skill in spirit.skills:
                if isinstance(skill, Skill):
                    # 确保技能有owner
                    skill.owner = spirit

                    # 检查是否是被动技能
                    cast_type = getattr(skill.metadata, 'cast_type', None)
                    if cast_type == 'PASSIVE':
                        passive_skills.append((spirit, skill))

        # 记录被动技能应用开始
        if passive_skills:
            actions.append(LogAction(
                caster=None,
                message=f"🔮 第一回合开始，并发应用 {len(passive_skills)} 个被动技能",
                level="INFO"
            ))

        # 🔧 安全地并发触发所有被动技能
        for spirit, skill in passive_skills:
            try:
                # 检查精灵是否仍然存活
                if not getattr(spirit, 'is_alive', False):
                    continue

                # 直接调用技能的cast方法
                skill_actions = skill.cast(battle_state)

                if skill_actions:
                    # 🔧 安全检查：过滤可能导致异常死亡的动作
                    safe_actions = self._filter_safe_passive_actions(skill_actions, spirit)
                    actions.extend(safe_actions)

                # 记录成功应用的被动技能
                actions.append(LogAction(
                    caster=spirit,
                    message=f"✨ {spirit.name} 的被动技能 {skill.metadata.name} 已应用",
                    level="INFO"
                ))

            except Exception as e:
                battle_logger.error(f"应用 {spirit.name} 的被动技能 {skill.metadata.name} 失败: {e}")
                actions.append(LogAction(
                    caster=spirit,
                    message=f"❌ {spirit.name} 的被动技能 {skill.metadata.name} 应用失败: {e}",
                    level="ERROR"
                ))

        return actions

    def _filter_safe_passive_actions(self, actions: List[Any], spirit: Any) -> List[Any]:
        """过滤被动技能动作，确保不会导致异常死亡"""
        from ...action import DamageAction, ApplyEffectAction, LogAction

        safe_actions = []

        for action in actions:
            try:
                # 允许日志动作
                if isinstance(action, LogAction):
                    safe_actions.append(action)
                    continue

                # 允许效果应用动作（被动技能主要是应用效果）
                if isinstance(action, ApplyEffectAction):
                    # 检查效果是否安全
                    if self._is_safe_effect(action, spirit):
                        safe_actions.append(action)
                    continue

                # 🔧 谨慎处理伤害动作 - 检查是否是安全的被动伤害
                if isinstance(action, DamageAction):
                    # 检查是否是安全的被动伤害（如神曜技能的初始伤害）
                    if self._is_safe_passive_damage(action, spirit):
                        safe_actions.append(action)
                    else:
                        # 跳过可能导致异常死亡的伤害
                        from ...logging import battle_logger
                        battle_logger.warning(f"跳过被动技能 {spirit.name} 的不安全伤害动作")
                    continue

                # 其他动作类型默认允许
                safe_actions.append(action)

            except Exception as e:
                from ...logging import battle_logger
                battle_logger.warning(f"过滤被动技能动作时出错: {e}")
                # 出错时跳过该动作
                continue

        return safe_actions

    def _is_safe_effect(self, action: Any, spirit: Any) -> bool:
        """检查效果是否安全"""
        try:
            effect = getattr(action, 'effect', None)
            if not effect:
                return True

            # 检查效果类型
            effect_name = getattr(effect, 'name', '')

            # 允许的安全效果类型
            safe_effect_types = [
                '彼岸殊沙',  # 伏妖被动
                '藏隙匿形',  # 空灵圣龙被动
                '御神之怒',  # 御神被动
                '隐身',
                '护盾',
                '增益',
                '被动效果'
            ]

            # 检查是否是安全的效果
            for safe_type in safe_effect_types:
                if safe_type in effect_name:
                    return True

            # 默认允许，但记录警告
            from ...logging import battle_logger
            battle_logger.debug(f"未知效果类型 {effect_name}，默认允许")
            return True

        except Exception:
            # 出错时默认不允许
            return False

    def _is_safe_passive_damage(self, action: Any, spirit: Any) -> bool:
        """检查被动伤害是否安全"""
        try:
            # 获取伤害量
            damage_amount = getattr(action, 'damage', 0)
            target = getattr(action, 'target', None)

            # 如果没有目标或伤害为0，认为是安全的
            if not target or damage_amount <= 0:
                return True

            # 检查目标的当前HP
            target_hp = getattr(target, 'current_hp', 0)

            # 如果伤害不会导致目标死亡，认为是安全的
            if target_hp > damage_amount * 2:  # 留有安全余量
                return True

            # 检查是否是特定的安全被动技能
            spirit_name = getattr(spirit, 'name', '')
            if '神曜' in spirit_name or '通灵' in spirit_name:
                # 神曜技能的被动伤害通常是安全的
                return True

            # 默认不安全
            return False

        except Exception:
            # 出错时默认不安全
            return False