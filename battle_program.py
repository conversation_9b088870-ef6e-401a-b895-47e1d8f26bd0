#!/usr/bin/env python3
"""
战斗程序 - 完整的战斗系统

提供交互式的战斗体验，包括：
1. 精灵选择和阵型配置
2. 战斗执行和实时显示
3. 战斗结果分析
4. AI行动生成系统集成
"""

import sys
import os
import time
from typing import List, Dict, Any, Optional, Tuple

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志系统"""
    try:
        from core.logging import get_logger
        logger = get_logger("battle_program")
        logger.info("战斗程序启动")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None

class BattleProgram:
    """战斗程序主类"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.available_spirits = []
        self.battle_engine = None
        self.battle_result = None
        
    def log(self, level: str, message: str):
        """统一日志输出"""
        print(f"[{level}] {message}")
        if self.logger:
            getattr(self.logger, level.lower(), self.logger.info)(message)
    
    def initialize_system(self):
        """初始化战斗系统"""
        self.log("INFO", "🚀 初始化战斗系统...")

        try:
            # 初始化核心系统管理器
            self.log("INFO", "初始化核心系统管理器...")
            try:
                from core.system_manager import initialize_core_systems, get_system
                initialize_core_systems()
                self.log("INFO", "✅ 核心系统管理器初始化成功")
            except Exception as e:
                self.log("WARNING", f"核心系统管理器初始化失败: {e}")
                # 继续尝试其他初始化

            # 初始化精灵系统
            from core.spirit.spirit_service import get_spirit_service
            self.spirit_service = get_spirit_service()

            # 获取可用精灵列表
            self.available_spirits = self.spirit_service.list_available_spirits()
            self.log("INFO", f"✅ 发现 {len(self.available_spirits)} 个可用精灵")

            # 初始化AI系统
            from core.ai import get_action_generator
            self.ai_generator = get_action_generator()
            self.log("INFO", "✅ AI行动生成系统已初始化")

            return True

        except Exception as e:
            self.log("ERROR", f"❌ 系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def display_available_spirits(self):
        """显示可用精灵列表"""
        print("\n" + "="*60)
        print("🎭 可用精灵列表")
        print("="*60)
        
        if not self.available_spirits:
            print("❌ 没有可用的精灵")
            return
        
        for i, spirit_id in enumerate(self.available_spirits, 1):
            print(f"{i:2d}. {spirit_id}")
        
        print("="*60)
    
    def select_spirits_for_team(self, team_name: str, team_id: int) -> List[Dict]:
        """为队伍选择精灵"""
        print(f"\n🏗️ 配置{team_name} (队伍{team_id})")

        # 询问要选择多少个精灵
        while True:
            try:
                max_spirits = min(len(self.available_spirits), 6)  # 最多6个精灵
                spirit_count = input(f"请输入要选择的精灵数量 (1-{max_spirits}): ").strip()
                spirit_count = int(spirit_count)
                if 1 <= spirit_count <= max_spirits:
                    break
                else:
                    print(f"❌ 精灵数量必须在1-{max_spirits}之间")
            except ValueError:
                print("❌ 请输入有效数字")

        print(f"请选择{spirit_count}个精灵组成队伍")

        team_config = []
        positions = [(1, 1), (1, 2), (1, 3), (2, 1), (2, 2), (2, 3)] if team_id == 0 else [(3, 1), (3, 2), (3, 3), (2, 1), (2, 2), (2, 3)]

        for i in range(spirit_count):
            while True:
                try:
                    print(f"\n选择第{i+1}个精灵:")
                    choice = input(f"输入精灵编号 (1-{len(self.available_spirits)}) 或精灵名称: ").strip()
                    
                    # 尝试按编号选择
                    if choice.isdigit():
                        choice_idx = int(choice) - 1
                        if 0 <= choice_idx < len(self.available_spirits):
                            spirit_id = self.available_spirits[choice_idx]
                        else:
                            print("❌ 编号超出范围，请重新输入")
                            continue
                    else:
                        # 尝试按名称选择
                        if choice in self.available_spirits:
                            spirit_id = choice
                        else:
                            print("❌ 精灵不存在，请重新输入")
                            continue
                    
                    team_config.append({
                        "spirit_id": spirit_id,
                        "position": positions[i]
                    })
                    
                    print(f"✅ 已选择: {spirit_id} 位置{positions[i]}")
                    break
                    
                except (ValueError, KeyboardInterrupt):
                    print("❌ 输入无效，请重新输入")
        
        return team_config
    
    def create_formation(self, team_config: List[Dict], team_id: int):
        """创建阵型"""
        try:
            from core.formation import Formation
            
            formation = Formation()
            spirits_created = []
            
            for config in team_config:
                spirit_id = config["spirit_id"]
                position = config["position"]
                
                # 创建精灵
                spirit = self.spirit_service.create_spirit(spirit_id, team_id, position)
                if spirit:
                    formation.add_spirit(spirit, position[0], position[1])
                    spirits_created.append(spirit)
                    self.log("DEBUG", f"创建精灵: {spirit_id} 队伍{team_id} 位置{position}")
                else:
                    self.log("ERROR", f"创建精灵失败: {spirit_id}")
            
            self.log("INFO", f"✅ 队伍{team_id}阵型创建完成，共{len(spirits_created)}个精灵")
            return formation
            
        except Exception as e:
            self.log("ERROR", f"创建阵型失败: {e}")
            return None
    
    def display_battle_setup(self, formation1, formation2):
        """显示战斗配置"""
        print("\n" + "="*60)
        print("⚔️ 战斗配置")
        print("="*60)
        
        print("\n🔵 队伍1:")
        for position, spirit in formation1.grid.items():
            if spirit:
                print(f"  位置{position}: {spirit.name} (HP: {spirit.current_hp}/{spirit.max_hp})")
        
        print("\n🔴 队伍2:")
        for position, spirit in formation2.grid.items():
            if spirit:
                print(f"  位置{position}: {spirit.name} (HP: {spirit.current_hp}/{spirit.max_hp})")
        
        print("="*60)
    
    def create_battle_engine(self, formation1, formation2):
        """创建战斗引擎"""
        try:
            from core.battle.engines import create_battle_engine
            
            self.battle_engine = create_battle_engine(
                formation1=formation1,
                formation2=formation2,
                victory="ko",  # 击倒制胜利
                round_limit=10,  # 最大回合数设置为10
                executor_type="phased"  # 使用阶段化执行器
            )
            
            self.log("INFO", "✅ 战斗引擎创建成功")
            return True
            
        except Exception as e:
            self.log("ERROR", f"创建战斗引擎失败: {e}")
            return False
    
    def run_battle(self):
        """运行战斗"""
        if not self.battle_engine:
            self.log("ERROR", "战斗引擎未初始化")
            return False
        
        print("\n" + "="*60)
        print("⚔️ 战斗开始！")
        print("="*60)
        
        try:
            round_count = 0
            
            # 运行战斗循环
            for round_result in self.battle_engine.run_battle():
                round_count += 1
                
                # 显示回合信息
                if round_result.get("type") == "round_complete":
                    self.display_round_result(round_result, round_count)
                elif round_result.get("type") == "battle_end":
                    self.display_battle_end(round_result)
                    self.battle_result = round_result
                    break
                
                # 添加延迟以便观察
                time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.log("ERROR", f"战斗执行失败: {e}")
            return False
    
    def display_round_result(self, round_result: Dict, round_count: int):
        """显示回合结果"""
        print(f"\n📊 第{round_count}回合结果:")
        
        # 显示精灵状态
        battle_state = round_result.get("state", {})
        if battle_state:
            self.display_spirits_status(battle_state)
    
    def display_spirits_status(self, battle_state: Dict):
        """显示精灵状态"""
        spirits = battle_state.get("spirits", {})
        
        team0_spirits = []
        team1_spirits = []
        
        for spirit_id, spirit_data in spirits.items():
            if spirit_data.get("team") == 0:
                team0_spirits.append(spirit_data)
            else:
                team1_spirits.append(spirit_data)
        
        print("  🔵 队伍1:")
        for spirit in team0_spirits:
            status = "💀" if not spirit.get("is_alive", True) else "💚"
            hp = spirit.get("current_hp", 0)
            max_hp = spirit.get("max_hp", 1)
            energy = spirit.get("current_energy", 0)
            print(f"    {status} {spirit.get('name', 'Unknown')}: HP {hp}/{max_hp}, 能量 {energy}")
        
        print("  🔴 队伍2:")
        for spirit in team1_spirits:
            status = "💀" if not spirit.get("is_alive", True) else "💚"
            hp = spirit.get("current_hp", 0)
            max_hp = spirit.get("max_hp", 1)
            energy = spirit.get("current_energy", 0)
            print(f"    {status} {spirit.get('name', 'Unknown')}: HP {hp}/{max_hp}, 能量 {energy}")
    
    def display_battle_end(self, battle_result: Dict):
        """显示战斗结束"""
        print("\n" + "="*60)
        print("🏆 战斗结束！")
        print("="*60)
        
        winner = battle_result.get("winner")
        round_num = battle_result.get("round_num", 0)
        
        if winner is not None:
            team_name = "队伍1" if winner == 0 else "队伍2"
            print(f"🎉 获胜方: {team_name} (队伍{winner})")
        else:
            print("🤝 平局")
        
        print(f"📊 总回合数: {round_num}")
        
        # 显示最终状态
        battle_state = battle_result.get("state", {})
        if battle_state:
            print("\n📋 最终状态:")
            self.display_spirits_status(battle_state)
        
        print("="*60)
    
    def main_menu(self):
        """主菜单"""
        while True:
            print("\n" + "="*60)
            print("⚔️ 战斗程序主菜单")
            print("="*60)
            print("1. 开始新战斗")
            print("2. 快速战斗 (自动选择精灵)")
            print("3. 查看可用精灵")
            print("4. 查看AI系统状态")
            print("5. 退出程序")
            print("="*60)
            
            try:
                choice = input("请选择操作 (1-5): ").strip()

                if choice == "1":
                    self.start_new_battle()
                elif choice == "2":
                    self.quick_battle()
                elif choice == "3":
                    self.display_available_spirits()
                elif choice == "4":
                    self.display_ai_status()
                elif choice == "5":
                    print("👋 感谢使用战斗程序！")
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                self.log("ERROR", f"菜单操作失败: {e}")
    
    def start_new_battle(self):
        """开始新战斗"""
        print("\n🚀 开始新战斗")
        
        # 显示可用精灵
        self.display_available_spirits()
        
        # 配置队伍
        team1_config = self.select_spirits_for_team("队伍1", 0)
        team2_config = self.select_spirits_for_team("队伍2", 1)
        
        # 创建阵型
        formation1 = self.create_formation(team1_config, 0)
        formation2 = self.create_formation(team2_config, 1)
        
        if not formation1 or not formation2:
            print("❌ 阵型创建失败")
            return
        
        # 显示战斗配置
        self.display_battle_setup(formation1, formation2)
        
        # 确认开始战斗
        confirm = input("\n是否开始战斗？(y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 战斗已取消")
            return
        
        # 创建战斗引擎
        if not self.create_battle_engine(formation1, formation2):
            print("❌ 战斗引擎创建失败")
            return
        
        # 运行战斗
        self.run_battle()

    def quick_battle(self):
        """快速战斗 - 自动选择精灵"""
        print("\n🚀 快速战斗模式")

        if len(self.available_spirits) < 2:
            print("❌ 可用精灵不足2个，无法进行快速战斗")
            return

        # 自动选择精灵，每队至少1个，最多3个
        spirits_per_team = min(3, len(self.available_spirits) // 2)
        if spirits_per_team == 0:
            spirits_per_team = 1

        team1_spirits = self.available_spirits[:spirits_per_team]
        team2_spirits = self.available_spirits[spirits_per_team:spirits_per_team*2]

        # 如果第二队精灵不够，从剩余精灵中选择
        if len(team2_spirits) == 0:
            team2_spirits = [self.available_spirits[-1]]  # 至少选择最后一个精灵

        print(f"🔵 队伍1: {', '.join(team1_spirits)}")
        print(f"🔴 队伍2: {', '.join(team2_spirits)}")

        # 创建队伍配置
        team1_config = [
            {"spirit_id": spirit_id, "position": (1, i+1)}
            for i, spirit_id in enumerate(team1_spirits)
        ]
        team2_config = [
            {"spirit_id": spirit_id, "position": (3, i+1)}
            for i, spirit_id in enumerate(team2_spirits)
        ]

        # 创建阵型
        formation1 = self.create_formation(team1_config, 0)
        formation2 = self.create_formation(team2_config, 1)

        if not formation1 or not formation2:
            print("❌ 阵型创建失败")
            return

        # 显示战斗配置
        self.display_battle_setup(formation1, formation2)

        # 创建战斗引擎
        if not self.create_battle_engine(formation1, formation2):
            print("❌ 战斗引擎创建失败")
            return

        # 运行战斗
        self.run_battle()

    def display_ai_status(self):
        """显示AI系统状态"""
        print("\n" + "="*60)
        print("🤖 AI系统状态")
        print("="*60)
        
        try:
            if hasattr(self, 'ai_generator') and self.ai_generator:
                print("✅ AI行动生成器: 已初始化")
                print(f"  - 行动能力检查器: {len(self.ai_generator.capability_checker.checkers)} 个检查器")
                print(f"  - 动态条件评估器: {len(self.ai_generator.condition_evaluator.evaluators)} 个评估器")
                print(f"  - 条件性效果计算器: {len(self.ai_generator.effect_calculator.calculators)} 个计算器")
                print(f"  - 技能选择器: {type(self.ai_generator.skill_selector).__name__}")
            else:
                print("❌ AI行动生成器: 未初始化")
            
            # 显示扩展系统状态
            from core.ai.extensions import ExtensionManager
            stats = ExtensionManager.get_extension_stats()
            print(f"\n📊 扩展系统统计: {stats}")
            
        except Exception as e:
            print(f"❌ 获取AI状态失败: {e}")
        
        print("="*60)
    
    def run(self):
        """运行战斗程序"""
        print("⚔️ 欢迎使用战斗程序！")
        
        # 初始化系统
        if not self.initialize_system():
            print("❌ 系统初始化失败，程序退出")
            return
        
        # 进入主菜单
        self.main_menu()

def main():
    """主函数"""
    try:
        program = BattleProgram()
        program.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
