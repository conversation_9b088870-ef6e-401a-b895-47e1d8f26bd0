<template>
  <div class="battle-log">
    <!-- 头部 -->
    <div class="log-header">
      <h3 class="log-title">
        <el-icon><Document /></el-icon>
        战斗日志
      </h3>
      <div class="log-controls">
        <el-button size="small" @click="toggleAutoScroll" type="text">
          <el-icon><component :is="autoScroll ? 'VideoPause' : 'VideoPlay'" /></el-icon>
          {{ autoScroll ? '暂停滚动' : '自动滚动' }}
        </el-button>
        <el-button size="small" @click="clearLog" type="text">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        <el-button size="small" @click="exportLog" type="text">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 过滤器 -->
    <div class="log-filters" v-if="showFilters">
      <el-checkbox-group v-model="activeFilters" size="small">
        <el-checkbox label="ACTION">动作</el-checkbox>
        <el-checkbox label="EVENT">事件</el-checkbox>
        <el-checkbox label="SYSTEM">系统</el-checkbox>
        <el-checkbox label="INFO">信息</el-checkbox>
        <el-checkbox label="DEBUG">调试</el-checkbox>
        <el-checkbox label="WARNING">警告</el-checkbox>
        <el-checkbox label="ERROR">错误</el-checkbox>
        <el-checkbox label="CRITICAL">严重</el-checkbox>
      </el-checkbox-group>
    </div>

    <!-- 日志内容 -->
    <div 
      ref="logContainer" 
      class="log-content"
      @scroll="onScroll"
    >
      <div class="log-entries">
        <TransitionGroup name="log-entry" tag="div">
          <div
            v-for="entry in filteredEntries"
            :key="entry.id"
            class="log-entry"
            :class="[
              `entry-${entry.type}`,
              { 'entry-highlighted': entry.highlighted }
            ]"
          >
            <!-- 回合信息 -->
            <div class="entry-round" v-if="entry.round">
              <el-tag size="small" type="info" round>回合 {{ entry.round }}</el-tag>
            </div>
            
            <!-- 时间戳 -->
            <div class="entry-timestamp" v-if="showTimestamp">
              {{ formatTimestamp(entry.timestamp) }}
            </div>

            <!-- 图标 -->
            <div class="entry-icon">
              <el-icon>
                <component :is="getEntryIcon(entry.type)" />
              </el-icon>
            </div>

            <!-- 内容 -->
            <div class="entry-content">
              <div class="entry-description" v-html="formatDescription(entry.description)"></div>
              <div v-if="showDetails && (entry.caster_id || entry.target_id)" class="entry-details">
                <el-collapse>
                  <el-collapse-item title="详细信息">
                    <div v-if="entry.caster_id">施放者ID: {{ entry.caster_id }}</div>
                    <div v-if="entry.target_id">目标ID: {{ entry.target_id }}</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
        </TransitionGroup>
      </div>

      <!-- 空状态 -->
      <div class="log-empty" v-if="filteredEntries.length === 0">
        <el-icon class="empty-icon"><Document /></el-icon>
        <p>暂无战斗日志</p>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="log-footer">
      <span class="log-count">共 {{ props.logEntries.length }} 条记录</span>
      <div class="log-footer-controls">
        <el-button 
          text 
          size="small" 
          @click="showFilters = !showFilters"
        >
          <el-icon><Filter /></el-icon>
          过滤器
        </el-button>
        <el-button 
          text 
          size="small" 
          @click="showDetails = !showDetails"
        >
          <el-icon><View /></el-icon>
          详情
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { LogEntry } from '../../types/battle'

interface Props {
  logEntries: LogEntry[]
  maxEntries?: number
  autoScrollDefault?: boolean
  showTimestamp?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  logEntries: () => [],
  maxEntries: 1000, // 增加默认最大条目数
  autoScrollDefault: true,
  showTimestamp: true
})

const logContainer = ref<HTMLElement>()

const autoScroll = ref(props.autoScrollDefault)
const showFilters = ref(false)
const showDetails = ref(false)
const activeFilters = ref(['ACTION', 'EVENT', 'SYSTEM', 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']) // 包含后端可能的日志类型

const filteredEntries = computed(() => {
  if (!props.logEntries) {
    return []
  }
  
  // 处理日志并添加唯一ID和其他必要字段
  const processedLogs = props.logEntries.map((entry, index) => {
    return {
      id: `log-${index}-${Date.now()}`, // 为了动画和key追踪添加唯一ID
      type: entry.type.toLowerCase(), // 转小写与icon映射匹配
      description: entry.message, // 使用message字段作为描述
      round: entry.round,
      caster_id: entry.caster_id,
      target_id: entry.target_id,
      timestamp: Date.now() - (props.logEntries.length - index) * 1000, // 模拟时间戳
      highlighted: false // 可以根据需要高亮特定日志
    }
  })
  
  return processedLogs
    .filter(entry => activeFilters.value.includes(entry.type.toUpperCase()))
    .slice(-props.maxEntries)
})

watch(() => props.logEntries, () => {
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}, { deep: true })

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

// 处理滚动事件
const onScroll = () => {
  if (!logContainer.value) return
  
  const { scrollTop, scrollHeight, clientHeight } = logContainer.value
  const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
  
  // 如果用户手动滚动到非底部，暂停自动滚动
  if (!isAtBottom && autoScroll.value) {
    autoScroll.value = false
  }
}

// 切换自动滚动
const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value
  if (autoScroll.value) {
    scrollToBottom()
  }
}

const emit = defineEmits(['clear-log', 'export-log'])

const clearLog = () => {
  emit('clear-log')
}

const exportLog = () => {
  emit('export-log', filteredEntries.value)
}

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 获取条目图标
const getEntryIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    // 动作类型
    damage: 'Sword',
    heal: 'Heart',
    apply_effect: 'Plus',
    remove_effect: 'Minus',
    modify_attribute: 'Edit',
    skill_cast: 'MagicStick',
    move: 'Rank',
    die: 'CircleClose',
    revive: 'Refresh',
    
    // 日志类型
    log: 'ChatDotSquare',
    action: 'Lightning', 
    event: 'Bell',
    system: 'Monitor',
    info: 'InfoFilled',
    debug: 'Opportunity',
    warning: 'Warning',
    error: 'CircleCloseFilled',
    critical: 'WarnTriangleFilled'
  }
  return iconMap[type.toLowerCase()] || 'InfoFilled'
}

// 格式化描述文本
const formatDescription = (description: string) => {
  if (!description) return '';
  
  // 高亮精灵名称、技能名称和效果
  return description
    // 精灵名称 (匹配常见命名规则)
    .replace(/\b([A-Za-z0-9_]+(?:_[A-Za-z0-9_]+)+)\b/g, '<strong class="highlight-spirit">$1</strong>')
    // 技能名称 (可能包含中文)
    .replace(/(释放了|使用了|发动了)([^，。！？\n]+)(技能|效果)/g, '$1<em class="highlight-skill">$2</em>$3')
    // 效果名称 (方括号)
    .replace(/\[([^\]]+)\]/g, '<span class="highlight-effect">$1</span>')
    // 伤害和治疗数值
    .replace(/(\d+)(点伤害|点生命值)/g, '<span class="highlight-damage">$1</span>$2')
    .replace(/恢复了(\d+)点/g, '恢复了<span class="highlight-heal">$1</span>点')
    // 回合数
    .replace(/(第\s*\d+\s*回合)/g, '<span class="highlight-round">$1</span>')
    // 突出显示重要状态变化
    .replace(/(死亡|复活|昏迷|眩晕|免疫|抵抗|闪避)/g, '<span class="highlight-status">$1</span>')
}

// 格式化数值
const formatValue = (type: string, value: number) => {
  switch (type) {
    case 'damage':
      return `-${value}`
    case 'heal':
      return `+${value}`
    default:
      return value.toString()
  }
}

// 获取数值样式类
const getValueClass = (type: string, value: number) => {
  switch (type) {
    case 'damage':
      return 'value-damage'
    case 'heal':
      return 'value-heal'
    default:
      return 'value-neutral'
  }
}
</script>

<style scoped lang="scss">
.battle-log {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: transparent;
  border-radius: 8px;
  overflow: hidden;

  .log-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(15, 23, 42, 0.6);
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);

    .log-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: #f8fafc;
      font-size: 16px;
      font-weight: 600;
    }

    .log-controls {
      display: flex;
      gap: 8px;
    }
  }

  .log-filters {
    padding: 8px 12px;
    background: rgba(15, 23, 42, 0.4);
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  }

  .log-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    min-height: 100px;

    .log-entries {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

          .log-entry {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 6px 8px;
      border-radius: 6px;
      transition: all 0.2s ease;
      font-size: 14px;

      &:hover {
        background: rgba(30, 41, 59, 0.6);
      }

      &.entry-highlighted {
        background: rgba(139, 92, 246, 0.2);
        border: 1px solid rgba(139, 92, 246, 0.5);
      }

      // 条目类型样式
      &.entry-damage {
        border-left: 3px solid #ef4444;
      }

      &.entry-heal {
        border-left: 3px solid #10b981;
      }

      &.entry-apply_effect {
        border-left: 3px solid #3b82f6;
      }

      &.entry-remove_effect {
        border-left: 3px solid #f59e0b;
      }

      &.entry-skill_cast {
        border-left: 3px solid #8b5cf6;
      }

      &.entry-action {
        border-left: 3px solid #8b5cf6;
      }

      &.entry-event {
        border-left: 3px solid #3b82f6;
      }
      
      &.entry-system {
        border-left: 3px solid #6b7280;
      }

      &.entry-log {
        border-left: 3px solid #6b7280;
      }

      .entry-round {
        margin-right: 6px;
        
        .el-tag {
          font-size: 10px;
          height: 20px;
          line-height: 20px;
          background-color: rgba(99, 102, 241, 0.2);
          border-color: rgba(99, 102, 241, 0.2);
          color: #a5b4fc;
        }
      }

      .entry-timestamp {
        font-size: 12px;
        color: #94a3b8;
        white-space: nowrap;
        font-family: 'Courier New', monospace;
        min-width: 60px;
      }

      .entry-icon {
        color: #cbd5e1;
        font-size: 14px;
        margin-top: 1px;
      }

      .entry-content {
        flex: 1;
        min-width: 0;

                  .entry-description {
          color: #e2e8f0;
          line-height: 1.4;
          word-break: break-word;

          :deep(.highlight-spirit) {
            color: #fbbf24; // 金黄色
            font-weight: 600;
          }

          :deep(.highlight-skill) {
            color: #a78bfa; // 紫色
            font-style: normal;
            font-weight: 600;
          }

          :deep(.highlight-effect) {
            color: #34d399; // 绿色
            font-weight: 500;
          }
          
          :deep(.highlight-damage) {
            color: #ef4444; // 红色
            font-weight: 600;
          }
          
          :deep(.highlight-heal) {
            color: #10b981; // 绿色
            font-weight: 600;
          }
          
          :deep(.highlight-round) {
            color: #60a5fa; // 蓝色
            font-weight: 600;
          }
          
          :deep(.highlight-status) {
            color: #f59e0b; // 橙色
            font-weight: 600;
          }
        }

        .entry-details {
          margin-top: 8px;
          
          pre {
            font-size: 11px;
            color: #94a3b8;
            background: rgba(15, 23, 42, 0.6);
            padding: 8px;
            border-radius: 4px;
            overflow-x: auto;
          }
        }
      }

      .entry-value {
        font-weight: 600;
        font-family: 'Courier New', monospace;
        white-space: nowrap;

        .value-damage {
          color: #ef4444;
        }

        .value-heal {
          color: #10b981;
        }

        .value-neutral {
          color: #94a3b8;
        }
      }
    }

    .log-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #64748b;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }

  .log-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(15, 23, 42, 0.6);
    border-top: 1px solid rgba(139, 92, 246, 0.2);

    .log-count {
      font-size: 12px;
      color: #94a3b8;
    }
    
    .log-footer-controls {
      display: flex;
      gap: 8px;
    }
  }
}

// 动画
.log-entry-enter-active {
  transition: all 0.3s ease;
}

.log-entry-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.log-entry-move {
  transition: transform 0.3s ease;
}
</style>