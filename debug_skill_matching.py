#!/usr/bin/env python3
"""
调试技能匹配问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_skill_matching():
    """调试技能匹配过程"""
    print("🔧 调试技能匹配过程...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        print(f"📊 测试精灵: {spirit.name}")
        
        # 获取技能组件
        if hasattr(spirit, 'components'):
            from core.components import SkillComponent
            skill_component = spirit.components.get_component(SkillComponent)
            
            if skill_component:
                print(f"  技能组件存在: ✅")
                
                # 获取所有技能
                all_skills = skill_component._skills
                print(f"  总技能数: {len(all_skills)}")
                
                # 手动模拟get_skills_by_type的匹配过程
                print(f"\n🔍 手动模拟get_skills_by_type('ULTIMATE')匹配过程:")
                
                skill_type = 'ULTIMATE'
                matching_skills = []
                
                for i, skill in enumerate(all_skills):
                    skill_name = getattr(skill, 'name', f'技能{i}')
                    print(f"\n  检查技能 {i}: {skill_name}")
                    
                    # 检查是否有metadata
                    if not hasattr(skill, 'metadata'):
                        print(f"    ❌ 没有metadata - 跳过")
                        continue
                    
                    metadata = skill.metadata
                    print(f"    ✅ 有metadata")
                    
                    # 检查tags匹配
                    if hasattr(metadata, 'tags'):
                        tags = metadata.tags
                        print(f"    tags: {tags} (类型: {type(tags)})")
                        if skill_type in tags:
                            print(f"    ✅ 通过tags匹配: '{skill_type}' in {tags}")
                            matching_skills.append(skill)
                            continue
                        else:
                            print(f"    ❌ tags不匹配: '{skill_type}' not in {tags}")
                    else:
                        print(f"    ❌ 没有tags")
                    
                    # 检查cast_type匹配
                    if hasattr(metadata, 'cast_type'):
                        cast_type = metadata.cast_type
                        print(f"    cast_type: '{cast_type}' (类型: {type(cast_type)})")
                        
                        # 直接匹配
                        if skill_type == cast_type:
                            print(f"    ✅ 直接匹配: '{skill_type}' == '{cast_type}'")
                            matching_skills.append(skill)
                            continue
                        
                        # 大小写不敏感匹配
                        if skill_type.upper() == cast_type.upper():
                            print(f"    ✅ 大小写不敏感匹配: '{skill_type}'.upper() == '{cast_type}'.upper()")
                            matching_skills.append(skill)
                            continue
                        
                        # 特殊兼容性匹配
                        if skill_type.lower() == 'ultimate' and cast_type == 'ULTIMATE':
                            print(f"    ✅ 特殊兼容性匹配: '{skill_type}'.lower() == 'ultimate' and '{cast_type}' == 'ULTIMATE'")
                            matching_skills.append(skill)
                            continue
                        
                        print(f"    ❌ cast_type不匹配: '{skill_type}' != '{cast_type}'")
                    else:
                        print(f"    ❌ 没有cast_type")
                
                print(f"\n📊 手动匹配结果: {len(matching_skills)} 个技能")
                for skill in matching_skills:
                    skill_name = getattr(skill, 'name', 'Unknown')
                    print(f"  - {skill_name}")
                
                # 对比实际的get_skills_by_type结果
                print(f"\n🔍 对比实际的get_skills_by_type结果:")
                actual_result = skill_component.get_skills_by_type('ULTIMATE')
                print(f"  get_skills_by_type('ULTIMATE'): {len(actual_result)} 个技能")
                
                if len(matching_skills) != len(actual_result):
                    print(f"  ❌ 结果不一致！手动匹配{len(matching_skills)}个，实际返回{len(actual_result)}个")
                    
                    # 检查get_skills_by_type的实际代码执行
                    print(f"\n🔍 检查get_skills_by_type的实际执行:")
                    
                    # 直接调用并添加调试信息
                    print(f"    调用 skill_component.get_skills_by_type('ULTIMATE')...")
                    
                    # 手动执行get_skills_by_type的逻辑
                    debug_matching_skills = []
                    for skill in skill_component._skills:
                        print(f"    检查技能: {getattr(skill, 'name', 'Unknown')}")
                        
                        # 检查tags
                        if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'tags'):
                            tags = skill.metadata.tags
                            if 'ULTIMATE' in tags:
                                print(f"      ✅ 通过tags匹配")
                                debug_matching_skills.append(skill)
                                continue
                        
                        # 检查cast_type
                        if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'cast_type'):
                            cast_type = skill.metadata.cast_type
                            print(f"      cast_type: '{cast_type}'")
                            
                            # 直接匹配
                            if 'ULTIMATE' == cast_type:
                                print(f"      ✅ 直接匹配")
                                debug_matching_skills.append(skill)
                                continue
                            
                            # 大小写不敏感匹配
                            if 'ULTIMATE'.upper() == cast_type.upper():
                                print(f"      ✅ 大小写匹配")
                                debug_matching_skills.append(skill)
                                continue
                            
                            # 特殊匹配
                            if 'ULTIMATE'.lower() == 'ultimate' and cast_type == 'ULTIMATE':
                                print(f"      ✅ 特殊匹配")
                                debug_matching_skills.append(skill)
                                continue
                            
                            print(f"      ❌ 不匹配")
                    
                    print(f"    调试执行结果: {len(debug_matching_skills)} 个技能")
                else:
                    print(f"  ✅ 结果一致")
                
                return len(matching_skills) > 0
            else:
                print(f"  ❌ 技能组件不存在")
                return False
        else:
            print(f"  ❌ 精灵没有components属性")
            return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 技能匹配问题调试")
    print("="*60)
    
    debug_skill_matching()

if __name__ == "__main__":
    main()
