"""
Battle Engine Factory Helpers
============================

本模块提供一组便捷函数，用于**一行代码**即可创建所需的战斗引擎实例。
设计目标：
    1. *零学习成本* —— 通过字符串配置选择常见胜负条件。
    2. *灵活扩展* —— 允许直接传入自定义 `IBattleEndCondition` 子类。
    3. *统一入口* —— 可通过 `use_stream=True` 一键切换到流式引擎。

示例::

    from src.core.battle.factory import create_battle_engine
    engine = create_battle_engine(
        formation1, formation2,
        victory="round_limit",    # 或 KnockoutCondition()/CompositeCondition() 等
        round_limit=30,
        use_stream=False,
        executor_type="optimized"  # 选择优化版执行器
    )

"""
from __future__ import annotations

from typing import Any, Dict, Optional, Sequence, cast

# 核心类/策略
from .battle_engine import RefactoredBattleEngine
from ..conditions import (
    IBattleEndCondition,
    KnockoutCondition,
    RoundLimitCondition,
)
from .streaming_engine import (
    StreamLevel,
    create_streaming_engine,
)
from ...turn_order import FixedGridTurnOrderStrategy
from ...formation import Formation

__all__ = ["create_battle_engine"]


def _resolve_condition(victory: str | IBattleEndCondition | None, **kwargs) -> IBattleEndCondition:
    """根据用户输入构造胜负判定策略。"""
    if victory is None or victory == "ko":
        return KnockoutCondition()
    if isinstance(victory, IBattleEndCondition):
        return victory
    if isinstance(victory, str):
        victory_lower = victory.lower()
        if victory_lower in {"round_limit", "round", "limit"}:
            max_rounds = int(kwargs.get("round_limit", 20))
            return RoundLimitCondition(max_rounds=max_rounds)
        raise ValueError(f"不支持的胜负类型: {victory}")
    raise TypeError("victory 必须是 str、IBattleEndCondition 或 None")


def create_battle_engine(
    formation1: "Formation",
    formation2: "Formation",
    *,
    victory: str | IBattleEndCondition | None = "ko",
    round_limit: int = 20,
    turn_order_strategy: Optional[Any] = None,
    use_stream: bool = False,
    stream_level: StreamLevel = StreamLevel.STANDARD,
    executor_type: str = "phased",  # 将默认值从"registry"改为"phased"
    turn_order_bonus_energy: int = 50,  # 🆕 顺位加气数量（默认逻辑）
    # 注意：spirit_turn 系统已被移除
    **kwargs: Dict,
):
    """高层工厂函数，快速创建战斗引擎。

    参数
    ------
    formation1, formation2
        阵型定义，兼容 `RefactoredBattleEngine` 现有格式。
    victory
        * "ko" (默认)：全灭即胜。
        * "round_limit"         ：达到回合上限后比较 HP。
        * `IBattleEndCondition` 子类实例：自定义策略。
    round_limit
        与 "round_limit" 胜负类型配合使用。
    turn_order_strategy
        行动顺序策略，默认 `FixedGridTurnOrderStrategy()`。
    use_stream
        若为 ``True`` 则返回 `StreamingBattleEngine`。
    stream_level
        流式事件详细级别。
    executor_type
        执行器类型选择：
        * "phased" (默认): 阶段化执行器，按阶段处理动作
    # 注意：spirit_turn 相关参数已被移除
    kwargs
        其余关键字参数原样透传给底层引擎。
    """

    # 1. 解析胜负判定策略
    condition = _resolve_condition(victory, round_limit=round_limit)

    # 2. 解析行动顺序
    turn_strategy = turn_order_strategy or FixedGridTurnOrderStrategy()

    # 3. 准备统一事件管理器
    from ...event.unified_manager import unified_event_manager
    event_manager_kwargs = {'unified_event_manager': unified_event_manager}

    # 4. 创建引擎
    if use_stream:
        return cast(Any, create_streaming_engine)(
            formation1,
            formation2,
            level=stream_level,
            turn_order_strategy=turn_strategy,
            condition_strategy=condition,
            executor_type=executor_type,  # 传递执行器类型
            **event_manager_kwargs,
            **kwargs,
        )  # type: ignore[arg-type]

    return cast(Any, RefactoredBattleEngine)(
        formation1,
        formation2,
        turn_order_strategy=turn_strategy,
        condition_strategy=condition,
        executor_type=executor_type,  # 传递执行器类型
        round_limit=round_limit,  # 传递回合限制参数
        turn_order_bonus_energy=turn_order_bonus_energy,  # 🆕 传递顺位加气数量（默认逻辑）
        **event_manager_kwargs,
        **kwargs,
    )  # type: ignore[arg-type]