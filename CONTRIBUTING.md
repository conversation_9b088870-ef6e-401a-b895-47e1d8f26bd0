# 🤝 贡献指南

感谢你对AoQiAI项目的关注！我们欢迎所有形式的贡献，无论是代码、文档、测试还是反馈。

## 📋 贡献方式

### 🐛 报告问题
- 使用GitHub Issues报告bug
- 提供详细的错误信息和复现步骤
- 包含系统环境信息

### 💡 功能建议
- 在Issues中提出新功能请求
- 详细描述功能需求和使用场景
- 讨论实现方案的可行性

### 📝 改进文档
- 修正文档中的错误
- 添加缺失的文档内容
- 改进代码注释和示例

### 🔧 代码贡献
- 修复已知bug
- 实现新功能
- 优化性能
- 增加测试覆盖率

## 🚀 开发流程

### 1. 准备工作

```bash
# Fork项目到你的GitHub账户
# 克隆你的fork
git clone https://github.com/your-username/aoqiai.git
cd aoqiai

# 添加上游仓库
git remote add upstream https://github.com/original-owner/aoqiai.git

# 创建开发分支
git checkout -b feature/your-feature-name
```

### 2. 开发环境设置

```bash
# 创建虚拟环境
python -m venv aoqiai-dev
source aoqiai-dev/bin/activate  # Linux/macOS
# aoqiai-dev\Scripts\activate  # Windows

# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install
```

### 3. 代码开发

```bash
# 确保代码符合规范
black src/  # 代码格式化
flake8 src/  # 代码检查
mypy src/   # 类型检查

# 运行测试
pytest tests/ -v

# 添加新测试
# 在tests/目录下创建对应的测试文件
```

### 4. 提交代码

```bash
# 添加更改
git add .

# 提交更改（遵循提交信息规范）
git commit -m "feat: 添加新的技能组件系统"

# 推送到你的fork
git push origin feature/your-feature-name
```

### 5. 创建Pull Request

1. 在GitHub上打开你的fork
2. 点击"New Pull Request"
3. 选择目标分支（通常是main）
4. 填写PR描述模板
5. 等待代码审查

## 📝 代码规范

### Python代码风格

我们使用以下工具确保代码质量：

- **Black**: 代码格式化
- **Flake8**: 代码检查
- **MyPy**: 类型检查
- **isort**: 导入排序

### 代码规范要求

```python
# 1. 使用类型注解
def calculate_damage(
    attacker: IBattleEntity,
    target: IBattleEntity,
    skill_power: float
) -> int:
    """计算伤害值"""
    pass

# 2. 使用文档字符串
class SkillComponent:
    """
    技能组件基类
    
    Args:
        name: 组件名称
        priority: 执行优先级
        
    Attributes:
        is_active: 组件是否激活
    """
    
# 3. 使用数据类
@dataclass
class SkillMetadata:
    """技能元数据"""
    name: str
    description: str
    energy_cost: int = 0

# 4. 错误处理
try:
    result = risky_operation()
except SpecificException as e:
    logger.error(f"操作失败: {e}")
    raise
```

### 命名规范

- **类名**: PascalCase (`BattleEngine`)
- **函数名**: snake_case (`calculate_damage`)
- **常量**: UPPER_SNAKE_CASE (`MAX_ENERGY`)
- **私有成员**: 前缀下划线 (`_internal_method`)

## 🧪 测试规范

### 测试结构

```
tests/
├── core/
│   ├── test_battle.py
│   ├── test_skill.py
│   └── test_effect.py
├── integration/
│   └── test_full_battle.py
└── fixtures/
    └── test_data.py
```

### 测试示例

```python
import pytest
from src.core.battle import BattleEngine
from src.core.formation import Formation

class TestBattleEngine:
    """战斗引擎测试"""
    
    def test_battle_initialization(self):
        """测试战斗初始化"""
        formation1 = Formation()
        formation2 = Formation()
        
        engine = BattleEngine(formation1, formation2)
        
        assert engine.battle_state is not None
        assert engine.battle_state.round_num == 0
    
    @pytest.mark.parametrize("team1_size,team2_size", [
        (1, 1),
        (2, 2),
        (3, 3)
    ])
    def test_different_team_sizes(self, team1_size, team2_size):
        """测试不同队伍规模"""
        # 测试逻辑
        pass
    
    def test_battle_end_conditions(self):
        """测试战斗结束条件"""
        # 测试逻辑
        pass
```

### 测试覆盖率

我们要求：
- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **关键路径覆盖率**: 100%

```bash
# 运行覆盖率测试
pytest --cov=src tests/ --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

## 📋 提交信息规范

我们使用[Conventional Commits](https://www.conventionalcommits.org/)规范：

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 添加测试
- `chore`: 构建过程或辅助工具的变动

### 提交格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 示例

```bash
# 新功能
git commit -m "feat(skill): 添加技能组件系统"

# 修复bug
git commit -m "fix(battle): 修复伤害计算溢出问题"

# 文档更新
git commit -m "docs: 更新API参考文档"

# 重大变更
git commit -m "feat!: 重构战斗引擎API

BREAKING CHANGE: BattleEngine构造函数参数已更改"
```

## 🔍 代码审查

### 审查清单

#### 功能性
- [ ] 代码实现了预期功能
- [ ] 边界条件处理正确
- [ ] 错误处理完善
- [ ] 性能影响可接受

#### 代码质量
- [ ] 代码风格符合规范
- [ ] 命名清晰易懂
- [ ] 注释和文档完整
- [ ] 没有重复代码

#### 测试
- [ ] 包含充分的单元测试
- [ ] 测试覆盖关键路径
- [ ] 测试用例有意义
- [ ] 所有测试通过

#### 兼容性
- [ ] 向后兼容性
- [ ] API变更已文档化
- [ ] 依赖变更合理

### 审查流程

1. **自动检查**: CI/CD流水线自动运行测试和代码检查
2. **人工审查**: 至少一名维护者审查代码
3. **讨论**: 在PR中讨论改进建议
4. **修改**: 根据反馈修改代码
5. **合并**: 审查通过后合并到主分支

## 🏷️ 发布流程

### 版本号规范

我们使用[语义化版本](https://semver.org/)：

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布步骤

1. **更新版本号**: 在`__version__.py`中更新版本
2. **更新CHANGELOG**: 记录本版本的变更
3. **创建标签**: `git tag v1.2.3`
4. **推送标签**: `git push origin v1.2.3`
5. **GitHub Release**: 创建GitHub发布页面
6. **发布包**: 发布到PyPI（如适用）

## 🎯 贡献者指南

### 新贡献者

1. **阅读文档**: 熟悉项目架构和设计理念
2. **从小做起**: 从修复文档错误或小bug开始
3. **参与讨论**: 在Issues和PR中积极参与讨论
4. **寻求帮助**: 遇到问题时及时寻求帮助

### 经验贡献者

1. **指导新人**: 帮助新贡献者熟悉项目
2. **架构设计**: 参与重要功能的架构设计
3. **代码审查**: 审查其他贡献者的代码
4. **维护文档**: 保持文档的准确性和完整性

## 📞 联系方式

### 获取帮助

- **GitHub Issues**: 技术问题和bug报告
- **GitHub Discussions**: 功能讨论和问答
- **Discord**: 实时交流和讨论
- **Email**: 私人问题和合作咨询

### 维护者

- **@maintainer1**: 项目负责人
- **@maintainer2**: 核心开发者
- **@maintainer3**: 文档维护者

## 🙏 致谢

感谢所有为AoQiAI项目做出贡献的开发者！

### 贡献者名单

- **核心开发者**: [贡献者列表]
- **文档贡献者**: [贡献者列表]
- **测试贡献者**: [贡献者列表]
- **社区贡献者**: [贡献者列表]

### 特别感谢

- 感谢所有提供反馈和建议的用户
- 感谢开源社区的支持和帮助
- 感谢相关技术栈的开发者

---

🎉 **再次感谢你的贡献！让我们一起打造更好的AoQiAI！**
