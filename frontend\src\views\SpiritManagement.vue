<template>
  <div class="spirit-management-container">
    <header class="page-header">
      <h1>精灵管理</h1>
      <p>在这里查看、筛选和管理所有精灵。</p>
    </header>

    <!-- 筛选栏 -->
    <div class="filter-bar" v-if="!isLoading && !error">
      <el-input
        v-model="searchQuery"
        placeholder="搜索名称或标签"
        clearable
        class="filter-item"
      />

      <!-- 元素多选 -->
      <div class="filter-group">
        <el-icon class="filter-icon"><Sunny /></el-icon>
        <span class="filter-label">元素类型</span>
        <div class="filter-options">
          <el-button
            v-for="opt in elementTypes"
            :key="opt.value"
            size="small"
            round
            :type="selectedElements.includes(opt.value) ? 'primary' : 'default'"
            @click="toggleElement(opt.value)"
          >
            {{ opt.label }}
          </el-button>
        </div>
      </div>

      <!-- 职业多选 -->
      <div class="filter-group">
        <el-icon class="filter-icon"><UserFilled /></el-icon>
        <span class="filter-label">职业类型</span>
        <div class="filter-options profession-options">
          <el-button
            v-for="opt in professionTypes"
            :key="opt.value"
            size="small"
            round
            :type="selectedProfessions.includes(opt.value) ? 'primary' : 'default'"
            @click="toggleProfession(opt.value)"
          >
            {{ opt.label }}
          </el-button>
        </div>
      </div>

      <el-button @click="resetFilters" class="filter-item">重置</el-button>
    </div>

    <div v-if="isLoading" class="loading-indicator">
      正在加载精灵数据...
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-if="!isLoading && !error" class="spirit-grid">
      <SpiritCard
        v-for="spirit in filteredSpirits"
        :key="spirit.name_prefix"
        :spirit="spirit"
        :tooltip-disabled="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import type { SpiritPrototype } from '../types/spirit';
import { getSpirits } from '../api/spirits';
import SpiritCard from '../components/spirit/SpiritCard.vue';
import { Sunny, UserFilled } from '@element-plus/icons-vue';

const spirits = ref<SpiritPrototype[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// 筛选条件
const searchQuery = ref('')
const selectedElements = ref<string[]>([])
const selectedProfessions = ref<string[]>([])

// 选项列表（与 SpiritSelector 保持一致）
const elementTypes = [
  { label: '火', value: '火' },
  { label: '水', value: '水' },
  { label: '草', value: '草' },
  { label: '光', value: '光' },
  { label: '暗', value: '暗' },
  { label: '创', value: '创' },
  { label: '空', value: '空' },
]

const professionTypes = [
  { label: '英雄', value: '英雄' },
  { label: '魔法', value: '魔法' },
  { label: '平衡', value: '平衡' },
  { label: '利爪', value: '利爪' },
  { label: '肉盾', value: '肉盾' },
  { label: '神曜', value: '神曜' },
  { label: '通灵师', value: '通灵师' },
  { label: '召唤师', value: '召唤师' },
  { label: '疾速', value: '疾速' },
  { label: '治疗', value: '治疗' },
  { label: '控制', value: '控制' },
  { label: '神启', value: '神启' },
  { label: '天觉者', value: '天觉者' },
]

// 计算筛选后的精灵
const filteredSpirits = computed(() => {
  return spirits.value.filter(s => {
    // 搜索
    if (searchQuery.value && !s.name_prefix.toLowerCase().includes(searchQuery.value.toLowerCase())) {
      return false
    }
    // 元素筛选
    if (selectedElements.value.length > 0 && !selectedElements.value.includes(s.element)) {
      return false
    }
    // 职业筛选
    if (selectedProfessions.value.length > 0 && !selectedProfessions.value.some(p => s.professions.includes(p))) {
      return false
    }
    return true
  })
})

// 重置函数
const resetFilters = () => {
  searchQuery.value = ''
  selectedElements.value = []
  selectedProfessions.value = []
}

// 切换选中状态
const toggleElement = (val: string) => {
  const idx = selectedElements.value.indexOf(val)
  if (idx === -1) {
    selectedElements.value.push(val)
  } else {
    selectedElements.value.splice(idx, 1)
  }
}

const toggleProfession = (val: string) => {
  const idx = selectedProfessions.value.indexOf(val)
  if (idx === -1) {
    selectedProfessions.value.push(val)
  } else {
    selectedProfessions.value.splice(idx, 1)
  }
}

onMounted(async () => {
  try {
    isLoading.value = true;
    spirits.value = await getSpirits();
  } catch (err) {
    error.value = '加载精灵数据失败。';
    console.error(err);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
/* 主要容器布局 */
.spirit-management-container {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-header {
  margin-bottom: 1.5rem;
  flex-shrink: 0;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* 新样式 */
.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.9rem;
  color: #cbd5e1;
  margin-right: 0.25rem;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-icon {
  color: #cbd5e1;
}

/* 按钮样式微调 */
.el-button--default {
  background: rgba(255,255,255,0.05);
  border-color: rgba(255,255,255,0.15);
  color: #cbd5e1;
}

.el-button.is-round {
  padding: 4px 14px;
}

.spirit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  overflow-y: auto;
  padding-right: 1rem;
}

.loading-indicator,
.error-message {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
}
</style>