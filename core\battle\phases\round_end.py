"""RoundEndPhase – applies end-of-round effect updates and dispatches RoundEndEvent."""
from __future__ import annotations

from typing import List, Any

from .base import IBattlePhase
from ..models import BattleState  # type: ignore
from ...performance import monitor_battle_performance


class RoundEndPhase(IBattlePhase):
    """回合结束阶段。"""

    @monitor_battle_performance
    def execute(self, battle_state: BattleState) -> List[Any]:  # noqa: D401
        from ...event.events import RoundEndEvent

        actions: List[Any] = []

        # 🔧 修复效果系统：处理效果的回合结束逻辑
        for spirit in battle_state.get_all_spirits():
            if hasattr(spirit, "effect_manager") and spirit.effect_manager:
                try:
                    # 更新所有效果（包括持续时间递减和触发逻辑）
                    effect_results = spirit.effect_manager.update_effects(battle_state)

                    # 收集所有效果产生的动作
                    for result in effect_results:
                        if hasattr(result, 'actions') and result.actions:
                            actions.extend(result.actions)

                    # 🔧 特别处理：触发回合结束事件给所有效果
                    from ...event.events import RoundEndEvent
                    round_end_event = RoundEndEvent(round_num=battle_state.round_num)

                    # 为每个效果分发回合结束事件
                    for effect in spirit.effect_manager.effects.values():
                        if hasattr(effect, 'get_trigger_conditions'):
                            conditions = effect.get_trigger_conditions()
                            for condition in conditions:
                                if hasattr(condition, 'event_type') and condition.event_type.value == "round_end":
                                    try:
                                        event_data = {
                                            "event_type": "ROUND_END",
                                            "round_num": battle_state.round_num,
                                            "spirit": spirit
                                        }
                                        trigger_result = effect.on_triggered(event_data, battle_state)
                                        if trigger_result and hasattr(trigger_result, 'actions') and trigger_result.actions:
                                            actions.extend(trigger_result.actions)
                                    except Exception as e:
                                        from ...logging import get_logger
                                        logger = get_logger("battle.phases")
                                        logger.warning(f"效果 {effect.name} 回合结束触发失败: {e}")

                except Exception as e:
                    from ...logging import get_logger
                    logger = get_logger("battle.phases")
                    logger.warning(f"精灵 {spirit.name} 效果更新失败: {e}")

        # 直接分发回合结束事件（精简版本）
        round_end_event = RoundEndEvent(round_num=battle_state.round_num)

        # 直接调用事件管理器，避免 DispatchEventAction 的包装开销
        try:
            event_actions = battle_state.dispatch_event(round_end_event, battle_state)
            if event_actions:
                actions.extend(event_actions)
        except Exception as e:
            # 错误处理：记录但不中断流程
            from ...logging import get_logger
            logger = get_logger("battle.phases")
            logger.warning(f"回合结束事件分发失败: {e}")

        return actions