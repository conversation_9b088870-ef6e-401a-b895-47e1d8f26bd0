"""
超杀技能气势处理器

处理超杀技能的气势消耗和溢出气势计算
"""

from typing import List, Optional, Any
from ..logging import get_logger
from ..interfaces import IBattleEntity, IBattleState
from ..action import ConsumeEnergyAction, LogAction

logger = get_logger("core.skill.ultimate_energy_handler")


class UltimateEnergyHandler:
    """超杀技能气势处理器"""
    
    @staticmethod
    def calculate_overflow_energy(caster: IBattleEntity, ultimate_threshold: int) -> int:
        """
        计算溢出气势
        
        Args:
            caster: 施法者
            ultimate_threshold: 超杀技能阈值
            
        Returns:
            溢出的气势数量
        """
        current_energy = getattr(caster, 'energy', 0)
        overflow = max(0, current_energy - ultimate_threshold)
        
        logger.debug(f"{caster.name} 气势计算: 当前{current_energy}, 阈值{ultimate_threshold}, 溢出{overflow}")
        
        return overflow
    
    @staticmethod
    def create_energy_consumption_actions(
        caster: IBattleEntity, 
        ultimate_threshold: int,
        skill_name: str = "超杀技能"
    ) -> List[Any]:
        """
        创建气势消耗动作
        
        Args:
            caster: 施法者
            ultimate_threshold: 超杀技能阈值
            skill_name: 技能名称
            
        Returns:
            气势消耗相关的动作列表
        """
        actions = []
        
        current_energy = getattr(caster, 'energy', 0)
        
        if current_energy < ultimate_threshold:
            logger.warning(f"{caster.name} 气势不足释放超杀技能 {skill_name}: {current_energy} < {ultimate_threshold}")
            return actions
        
        # 计算溢出气势
        overflow_energy = UltimateEnergyHandler.calculate_overflow_energy(caster, ultimate_threshold)
        
        # 创建气势消耗动作 - 消耗所有气势
        consume_action = ConsumeEnergyAction(
            caster=caster,
            target=caster,
            amount=current_energy  # 消耗所有气势
        )
        actions.append(consume_action)
        
        # 创建日志动作
        if overflow_energy > 0:
            log_message = (
                f"💥 [{skill_name}] {caster.name} 消耗所有气势({current_energy})释放超杀技能！"
                f"溢出气势: {overflow_energy}"
            )
        else:
            log_message = (
                f"💥 [{skill_name}] {caster.name} 消耗所有气势({current_energy})释放超杀技能！"
            )
        
        log_action = LogAction(
            caster=caster,
            message=log_message
        )
        actions.append(log_action)
        
        logger.info(f"{caster.name} 释放超杀技能 {skill_name}: 消耗{current_energy}气势, 溢出{overflow_energy}")
        
        return actions
    
    @staticmethod
    def enhance_damage_action_with_overflow(
        damage_action: Any,
        caster: IBattleEntity,
        ultimate_threshold: int
    ) -> Any:
        """
        为伤害动作添加溢出气势信息
        
        Args:
            damage_action: 伤害动作
            caster: 施法者
            ultimate_threshold: 超杀技能阈值
            
        Returns:
            增强后的伤害动作
        """
        # 计算溢出气势
        overflow_energy = UltimateEnergyHandler.calculate_overflow_energy(caster, ultimate_threshold)
        
        # 为伤害动作添加溢出气势信息
        if hasattr(damage_action, 'overflow_energy'):
            damage_action.overflow_energy = overflow_energy
        
        # 添加超杀阈值信息（用于伤害计算）
        if hasattr(damage_action, 'ultimate_threshold'):
            damage_action.ultimate_threshold = ultimate_threshold
        
        logger.debug(f"为伤害动作添加溢出气势信息: {overflow_energy}")
        
        return damage_action
    
    @staticmethod
    def get_ultimate_threshold(caster: IBattleEntity, skill_name: str = None) -> int:
        """
        获取超杀技能阈值
        
        Args:
            caster: 施法者
            skill_name: 技能名称（可选）
            
        Returns:
            超杀技能阈值
        """
        # 方法1: 从超杀管理器获取
        if hasattr(caster, 'ultimate_manager') and caster.ultimate_manager:
            if skill_name and hasattr(caster.ultimate_manager, 'get_ultimate_skill'):
                skill_config = caster.ultimate_manager.get_ultimate_skill(skill_name)
                if skill_config:
                    return skill_config.energy_threshold
            
            # 获取最低阈值
            if hasattr(caster.ultimate_manager, 'get_lowest_threshold'):
                return caster.ultimate_manager.get_lowest_threshold()
        
        # 方法2: 从属性获取
        if hasattr(caster, 'attributes') and hasattr(caster.attributes, 'ultimate_threshold'):
            return caster.attributes.ultimate_threshold
        
        # 方法3: 从气势组件获取
        if hasattr(caster, 'components'):
            from ..components import EnergyComponent
            energy_component = caster.components.get_component(EnergyComponent)
            if energy_component and hasattr(energy_component, 'get_ultimate_threshold'):
                return energy_component.get_ultimate_threshold()
        
        # 默认阈值
        return 300
    
    @staticmethod
    def can_cast_ultimate(caster: IBattleEntity, ultimate_threshold: int = None) -> bool:
        """
        检查是否可以释放超杀技能
        
        Args:
            caster: 施法者
            ultimate_threshold: 超杀技能阈值（可选）
            
        Returns:
            是否可以释放超杀技能
        """
        if ultimate_threshold is None:
            ultimate_threshold = UltimateEnergyHandler.get_ultimate_threshold(caster)
        
        current_energy = getattr(caster, 'energy', 0)
        can_cast = current_energy >= ultimate_threshold
        
        logger.debug(f"{caster.name} 超杀技能检查: {current_energy} >= {ultimate_threshold} = {can_cast}")
        
        return can_cast


def create_ultimate_energy_actions(
    caster: IBattleEntity,
    skill_name: str,
    damage_actions: List[Any]
) -> List[Any]:
    """
    为超杀技能创建完整的气势处理动作
    
    Args:
        caster: 施法者
        skill_name: 技能名称
        damage_actions: 伤害动作列表
        
    Returns:
        包含气势处理的完整动作列表
    """
    all_actions = []
    
    # 获取超杀阈值
    ultimate_threshold = UltimateEnergyHandler.get_ultimate_threshold(caster, skill_name)
    
    # 检查是否可以释放
    if not UltimateEnergyHandler.can_cast_ultimate(caster, ultimate_threshold):
        logger.warning(f"{caster.name} 无法释放超杀技能 {skill_name}: 气势不足")
        return []
    
    # 1. 创建气势消耗动作
    energy_actions = UltimateEnergyHandler.create_energy_consumption_actions(
        caster, ultimate_threshold, skill_name
    )
    all_actions.extend(energy_actions)
    
    # 2. 增强伤害动作
    enhanced_damage_actions = []
    for damage_action in damage_actions:
        enhanced_action = UltimateEnergyHandler.enhance_damage_action_with_overflow(
            damage_action, caster, ultimate_threshold
        )
        enhanced_damage_actions.append(enhanced_action)
    
    all_actions.extend(enhanced_damage_actions)
    
    return all_actions
