
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

class ActionRecord:
    """
    记录单个战斗动作的详细信息。
    """
    def __init__(self, action_type: str, caster_id: Optional[str], caster_name: str, target_ids: List[str], data: Dict[str, Any]):
        self.action_type = action_type
        self.caster_id = caster_id
        self.caster_name = caster_name
        self.target_ids = target_ids
        self.timestamp = datetime.now().isoformat()
        self.data = data

    def to_dict(self) -> Dict[str, Any]:
        return self.__dict__


class RoundRecord:
    """
    记录一个完整回合的所有信息。
    """
    def __init__(self, round_num: int):
        self.round_num = round_num
        self.start_time = datetime.now().isoformat()
        self.end_time: Optional[str] = None
        self.actions: List[ActionRecord] = []
        self.spirit_states_start: Dict[str, Any] = {}
        self.spirit_states_end: Dict[str, Any] = {}

    def add_action(self, action: ActionRecord):
        self.actions.append(action)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "round_num": self.round_num,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "actions": [action.to_dict() for action in self.actions],
            "spirit_states_start": self.spirit_states_start,
            "spirit_states_end": self.spirit_states_end,
        }


class BattleRecord:
    """
    存储整场战斗的完整记录。
    """
    def __init__(self, battle_id: Optional[str] = None):
        self.battle_id = battle_id or str(uuid.uuid4())
        self.start_time = datetime.now().isoformat()
        self.end_time: Optional[str] = None
        self.winner: Optional[int] = None
        self.initial_state: Dict[str, Any] = {}
        self.rounds: List[RoundRecord] = []
        self.global_events: List[Dict[str, Any]] = []

    def start_round(self, round_num: int) -> RoundRecord:
        round_record = RoundRecord(round_num)
        self.rounds.append(round_record)
        return round_record

    def end_battle(self, winner: Optional[int]):
        self.winner = winner
        self.end_time = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        return {
            "battle_id": self.battle_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "winner": self.winner,
            "initial_state": self.initial_state,
            "rounds": [round_record.to_dict() for round_record in self.rounds],
            "global_events": self.global_events,
        }

class BattleRecorder:
    """
    负责管理和生成战斗记录。
    """
    def __init__(self):
        self.record = BattleRecord()
        self.current_round_record: Optional[RoundRecord] = None

    def start_battle(self, initial_state: Dict[str, Any]):
        self.record.initial_state = initial_state

    def start_round(self, round_num: int):
        self.current_round_record = self.record.start_round(round_num)

    def record_action(self, action: Any):
        if not self.current_round_record:
            return

        from core.action import BattleAction
        if not isinstance(action, BattleAction):
            return

        action_data = {
            "action_class": action.__class__.__name__,
            **action.to_dict()
        }
        
        # 移除不可序列化的字段
        action_data.pop('caster', None)
        action_data.pop('target', None)
        action_data.pop('targets', None)
        action_data.pop('event', None)
        action_data.pop('original_action', None)


        action_record = ActionRecord(
            action_type=action.__class__.__name__,
            caster_id=getattr(action.caster, 'id', None),
            caster_name=getattr(action.caster, 'name', 'System'),
            target_ids=[t.id for t in action.get_targets() if t and hasattr(t, 'id')],
            data=action_data
        )
        self.current_round_record.add_action(action_record)

    def capture_spirit_states(self, battle_state: Any, stage: str):
        if not self.current_round_record:
            return
            
        states = {}
        for spirit in battle_state.get_all_spirits():
            states[spirit.id] = self._serialize_spirit(spirit)

        if stage == "start":
            self.current_round_record.spirit_states_start = states
        elif stage == "end":
            self.current_round_record.spirit_states_end = states

    def end_battle(self, winner: Optional[int]):
        self.record.end_battle(winner)

    def get_record(self) -> BattleRecord:
        return self.record

    @staticmethod
    def _serialize_spirit(spirit: Any) -> Dict[str, Any]:
        return {
            "id": spirit.id,
            "name": spirit.name,
            "team": spirit.team,
            "position": spirit.position,
            "is_alive": spirit.is_alive,
            "current_hp": spirit.current_hp,
            "max_hp": spirit.max_hp,
            "energy": getattr(spirit, "energy", 0),
            "effects": [
                {"name": getattr(e, "name", str(e)), "duration": getattr(e, "duration", -1), "stacks": getattr(e, "stacks", 1)} 
                for e in (spirit.effect_manager.effects.values() if hasattr(spirit.effect_manager, 'effects') and hasattr(spirit.effect_manager.effects, 'values') else [])
            ] if hasattr(spirit, 'effect_manager') else [],
        }

__all__ = ["BattleRecorder", "BattleRecord", "RoundRecord", "ActionRecord"] 