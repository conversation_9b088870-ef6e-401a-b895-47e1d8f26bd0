#!/usr/bin/env python3
"""
调试被动技能问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_passive_debug():
    """调试被动技能问题"""
    print("🔧 调试被动技能问题...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        fuyao_spirit = None
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            print("❌ 未找到伏妖精灵")
            return False
        
        # 检查被动技能
        print(f"\n📋 检查被动技能详情:")
        
        passive_skills = []
        for skill in fuyao_spirit.skills:
            if hasattr(skill, 'metadata') and getattr(skill.metadata, 'cast_type', '') == 'PASSIVE':
                passive_skills.append(skill)
                print(f"  找到被动技能: {skill.metadata.name}")
                print(f"    技能ID: {skill.id}")
                print(f"    目标选择器: {type(skill.target_selector).__name__}")
                print(f"    组件数量: {len(skill.components)}")
                
                for i, component in enumerate(skill.components):
                    print(f"      组件{i+1}: {type(component).__name__}")
                    if hasattr(component, 'effect_factory'):
                        print(f"        有effect_factory: True")
                        try:
                            effect = component.effect_factory()
                            print(f"        效果类型: {type(effect).__name__}")
                            print(f"        效果名称: {getattr(effect, 'name', 'Unknown')}")
                            print(f"        效果ID: {getattr(effect, 'id', 'Unknown')}")
                        except Exception as e:
                            print(f"        效果创建失败: {e}")
        
        if not passive_skills:
            print("❌ 没有找到被动技能")
            return False
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        other_spirit = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 测试被动技能的cast方法
        print(f"\n📋 测试被动技能的cast方法:")
        
        for skill in passive_skills:
            print(f"\n  测试技能: {skill.metadata.name}")
            
            # 记录应用前状态
            effects_before = len(fuyao_spirit.effect_manager.effects)
            print(f"    应用前效果数量: {effects_before}")
            
            # 调用cast方法
            try:
                skill_actions = skill.cast(battle_state)
                print(f"    cast方法生成动作数量: {len(skill_actions) if skill_actions else 0}")
                
                # 详细检查每个动作
                if skill_actions:
                    for i, action in enumerate(skill_actions):
                        action_type = type(action).__name__
                        print(f"      动作{i+1}: {action_type}")
                        
                        if hasattr(action, 'target'):
                            target_name = getattr(action.target, 'name', 'Unknown')
                            print(f"        目标: {target_name}")
                        
                        if hasattr(action, 'effect'):
                            effect_name = getattr(action.effect, 'name', 'Unknown')
                            effect_type = type(action.effect).__name__
                            print(f"        效果: {effect_name} ({effect_type})")
                        
                        if hasattr(action, 'caster'):
                            caster_name = getattr(action.caster, 'name', 'Unknown')
                            print(f"        施法者: {caster_name}")
                
                # 手动执行动作
                if skill_actions:
                    print(f"    手动执行动作:")
                    from core.battle.execution import UnifiedActionExecutor
                    executor = UnifiedActionExecutor(battle_state, None, None)
                    
                    for i, action in enumerate(skill_actions):
                        try:
                            print(f"      执行动作{i+1}: {type(action).__name__}")
                            executor.execute_actions([action])
                            print(f"        执行成功")
                        except Exception as action_error:
                            print(f"        执行失败: {action_error}")
                            import traceback
                            traceback.print_exc()
                
                # 检查应用后状态
                effects_after = len(fuyao_spirit.effect_manager.effects)
                print(f"    应用后效果数量: {effects_after}")
                
                if effects_after > effects_before:
                    print(f"    ✅ 成功创建了 {effects_after - effects_before} 个效果")
                    
                    # 显示新增的效果
                    for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                        effect_name = getattr(effect, 'name', 'Unknown')
                        effect_type = type(effect).__name__
                        print(f"      - {effect_name} ({effect_type})")
                else:
                    print(f"    ❌ 没有创建效果")
                
            except Exception as e:
                print(f"    ❌ cast方法失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 检查被动效果是否应该直接添加到精灵
        print(f"\n📋 检查被动效果的正确应用方式:")
        
        # 直接创建被动效果并添加
        try:
            from spirits_data.神曜虚无·伏妖.passive_effects import create_bian_shusha_passive_effect
            
            print(f"  直接创建彼岸殊沙被动效果:")
            passive_effect = create_bian_shusha_passive_effect(fuyao_spirit)
            print(f"    效果名称: {passive_effect.name}")
            print(f"    效果类型: {type(passive_effect).__name__}")
            print(f"    效果ID: {passive_effect.id}")
            
            # 直接添加到效果管理器
            effects_before = len(fuyao_spirit.effect_manager.effects)
            print(f"    添加前效果数量: {effects_before}")
            
            result = fuyao_spirit.effect_manager.add_effect(passive_effect, battle_state)
            print(f"    添加结果: {result.success if result else False}")
            
            effects_after = len(fuyao_spirit.effect_manager.effects)
            print(f"    添加后效果数量: {effects_after}")
            
            if effects_after > effects_before:
                print(f"    ✅ 直接添加被动效果成功！")
                
                # 显示效果
                for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                    effect_name = getattr(effect, 'name', 'Unknown')
                    print(f"      - {effect_name}")
                
                return True
            else:
                print(f"    ❌ 直接添加被动效果失败")
                return False
                
        except Exception as e:
            print(f"  ❌ 直接创建被动效果失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 被动技能调试")
    print("="*50)
    
    result = test_passive_debug()
    
    print("\n" + "="*50)
    if result:
        print("✅ 被动技能调试成功")
        print("找到了被动技能问题的根本原因")
    else:
        print("❌ 被动技能调试失败")

if __name__ == "__main__":
    main()
