from __future__ import annotations

"""精灵（英雄）包
初始化时自动遍历并导入其下所有子包与模块，确保精灵及其技能被注册。
"""

import pkgutil
import importlib

# 导入现有的精灵（只导入存在的）
try:
    from .赤妖王·御神 import create_yushen_spirit
except ImportError:
    create_yushen_spirit = None

try:
    from .神曜虚无·伏妖 import create_fuyao_spirit
except ImportError:
    create_fuyao_spirit = None

try:
    from .神曜圣谕·女帝 import create_nudi_spirit
except ImportError:
    create_nudi_spirit = None

# 导入新创建的精灵
try:
    from .天恩圣祭·空灵圣龙 import create_kongling_shenglong_spirit
except ImportError:
    create_kongling_shenglong_spirit = None

# 精灵创建函数字典 - 优化版本，优先使用 JSON 配置
ALL_SPIRIT_CREATORS = {}

def get_spirit_creator(spirit_id: str):
    """获取精灵创建函数，优先使用 JSON 配置系统"""
    # 优先尝试使用 JSON 配置系统
    try:
        from core.spirit.json_factory import get_json_spirit_factory
        factory = get_json_spirit_factory()

        # 检查 JSON 配置是否存在
        if spirit_id in factory.list_available_spirits():
            return lambda: factory.create_spirit(spirit_id)
    except Exception:
        pass

    # 回退到传统的创建函数
    return ALL_SPIRIT_CREATORS.get(spirit_id)

# 添加存在的精灵创建函数（保留用于向后兼容）
if create_yushen_spirit:
    ALL_SPIRIT_CREATORS["chiyaowang_yushen"] = create_yushen_spirit

if create_fuyao_spirit:
    ALL_SPIRIT_CREATORS["shen_yao_xu_wu_fu_yao"] = create_fuyao_spirit

if create_nudi_spirit:
    ALL_SPIRIT_CREATORS["shen_yao_sheng_yu_nu_di"] = create_nudi_spirit

if create_kongling_shenglong_spirit:
    ALL_SPIRIT_CREATORS["tianen_shengji_kongling_shenglong"] = create_kongling_shenglong_spirit
