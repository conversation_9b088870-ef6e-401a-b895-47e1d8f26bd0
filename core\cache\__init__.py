from __future__ import annotations
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, Optional, Callable, TypeVar, Generic, Union

# 第三方库导入
import functools
from collections import OrderedDict
import hashlib
import pickle  # 使用标准pickle，避免dill依赖
import threading
import time
import weakref

# 本地导入 - monitor 将在 cache_manager 定义后导入

"""
缓存系统

提供多层次的缓存机制，优化性能和减少重复计算。
"""

T = TypeVar('T')

# 全局缓存开关
CACHE_ENABLED = True


class CachePolicy(Enum):
    """缓存策略"""
    LRU = "lru"           # 最近最少使用
    LFU = "lfu"           # 最少使用频率
    TTL = "ttl"           # 生存时间
    WEAK_REF = "weak_ref" # 弱引用


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    created_at: float = field(default_factory=time.time)
    ttl: Optional[float] = None
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self) -> None:
        """更新访问信息"""
        self.access_count += 1
        self.last_access = time.time()


class Cache(Generic[T]):
    """通用缓存类"""
    
    def __init__(
        self, 
        max_size: int = 1000,
        policy: CachePolicy = CachePolicy.LRU,
        default_ttl: Optional[float] = None
    ):
        self.max_size = max_size
        self.policy = policy
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: OrderedDict[str, None] = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self.hits = 0
        self.misses = 0
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = (args, tuple(sorted(kwargs.items())))
        try:
            key_bytes = pickle.dumps(key_data)
        except Exception:
            # 对无法 pickle 的对象，使用更安全的字符串表示
            try:
                # 尝试使用repr获得更准确的表示
                key_str = repr(key_data)
            except Exception:
                # 最后的备选方案
                key_str = str(key_data)
            key_bytes = key_str.encode("utf-8")
        return hashlib.sha256(key_bytes).hexdigest()[:16]  # 使用SHA256并截取前16位
    
    def get(self, key: str) -> Optional[T]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self.misses += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired:
                del self._cache[key]
                if key in self._access_order:
                    del self._access_order[key]
                self.misses += 1
                return None
            
            # 更新访问信息
            entry.touch()
            
            # 更新访问顺序（LRU）
            if self.policy == CachePolicy.LRU:
                if key in self._access_order:
                    del self._access_order[key]
                self._access_order[key] = None
            
            self.hits += 1
            return entry.value
    
    def set(self, key: str, value: T, ttl: Optional[float] = None) -> None:
        """设置缓存值 (put的别名)"""
        self.put(key, value, ttl)
    
    def put(self, key: str, value: T, ttl: Optional[float] = None) -> None:
        """存储缓存值"""
        with self._lock:
            # 使用默认TTL
            if ttl is None:
                ttl = self.default_ttl
            
            # 创建缓存条目
            entry = CacheEntry(key, value, ttl=ttl)
            
            # 如果已存在，更新
            if key in self._cache:
                self._cache[key] = entry
                if self.policy == CachePolicy.LRU:
                    if key in self._access_order:
                        del self._access_order[key]
                    self._access_order[key] = None
                return
            
            # 检查容量限制
            if len(self._cache) >= self.max_size:
                self._evict()
            
            # 添加新条目
            self._cache[key] = entry
            if self.policy == CachePolicy.LRU:
                self._access_order[key] = None
    
    def _evict(self) -> None:
        """根据策略驱逐缓存条目"""
        if not self._cache:
            return
        
        if self.policy == CachePolicy.LRU:
            # 移除最近最少使用的
            oldest_key = next(iter(self._access_order))
            del self._cache[oldest_key]
            del self._access_order[oldest_key]
        
        elif self.policy == CachePolicy.LFU:
            # 移除使用频率最低的
            min_access = min(entry.access_count for entry in self._cache.values())
            for key, entry in self._cache.items():
                if entry.access_count == min_access:
                    del self._cache[key]
                    if key in self._access_order:
                        del self._access_order[key]
                    break
        
        elif self.policy == CachePolicy.TTL:
            # 移除最早过期的
            now = time.time()
            oldest_key = None
            oldest_time = float('inf')
            
            for key, entry in self._cache.items():
                if entry.is_expired:
                    oldest_key = key
                    break
                elif entry.created_at < oldest_time:
                    oldest_time = entry.created_at
                    oldest_key = key
            
            if oldest_key:
                del self._cache[oldest_key]
                if oldest_key in self._access_order:
                    del self._access_order[oldest_key]
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self.hits = 0
            self.misses = 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
    
    def hit_rate(self) -> float:
        """获取命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                if key in self._access_order:
                    del self._access_order[key]
                return True
            return False
    
    def cleanup_expired(self) -> int:
        """清理过期条目，返回清理数量"""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items() 
                if entry.is_expired
            ]
            
            for key in expired_keys:
                del self._cache[key]
                if key in self._access_order:
                    del self._access_order[key]
            
            return len(expired_keys)


class WeakCache:
    """弱引用缓存，适用于大对象"""
    
    def __init__(self):
        self._cache: Dict[str, weakref.ref] = {}
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                return None
            
            ref = self._cache[key]
            value = ref()
            
            if value is None:
                # 对象已被垃圾回收
                del self._cache[key]
                return None
            
            return value
    
    def put(self, key: str, value: Any) -> None:
        """存储缓存值"""
        with self._lock:
            def cleanup(ref):
                # 对象被垃圾回收时的清理回调
                if key in self._cache and self._cache[key] is ref:
                    del self._cache[key]
            
            self._cache[key] = weakref.ref(value, cleanup)
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()


def cached(
    cache: Optional[Cache] = None,
    key_func: Optional[Callable] = None,
    ttl: Optional[float] = None
):
    """
    缓存装饰器

    :param cache: 使用的缓存实例
    :param key_func: 自定义缓存键生成函数
    :param ttl: 缓存时间
    """
    
    def decorator(func: Callable) -> Callable:
        # 在装饰器内部初始化缓存
        _cache = cache if cache is not None else Cache()
        
        def wrapper(*args, **kwargs):
            # 全局开关检查
            if not CACHE_ENABLED:
                return func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = _cache._generate_key(func.__name__, *args, **kwargs)
            
            # 尝试从缓存获取
            result = _cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            _cache.put(cache_key, result, ttl)
            
            return result
        
        # 添加缓存管理方法
        wrapper.cache = _cache
        wrapper.clear_cache = _cache.clear
        wrapper.cache_info = lambda: {
            'hits': _cache.hits,
            'misses': _cache.misses,
            'hit_rate': _cache.hit_rate(),
            'size': _cache.size()
        }
        
        return wrapper
    return decorator


# 专用缓存实例
class GameCacheManager:
    """游戏缓存管理器"""
    
    def __init__(self):
        # 不同类型的缓存
        self.attribute_cache = Cache[float](max_size=5000, policy=CachePolicy.LRU)
        self.damage_cache = Cache[float](max_size=2000, policy=CachePolicy.LRU)
        self.skill_cache = Cache[Any](max_size=1000, policy=CachePolicy.LRU)
        self.formation_cache = Cache[Any](max_size=500, policy=CachePolicy.LRU)
        
        # 弱引用缓存，用于大对象
        self.spirit_cache = WeakCache()
        self.battle_state_cache = WeakCache()
    
    def clear_all(self) -> None:
        """清空所有缓存"""
        self.attribute_cache.clear()
        self.damage_cache.clear()
        self.skill_cache.clear()
        self.formation_cache.clear()
        self.spirit_cache.clear()
        self.battle_state_cache.clear()
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """设置缓存值 - 通用接口"""
        # 默认使用通用缓存
        self.skill_cache.set(key, value, ttl)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值 - 通用接口"""
        return self.skill_cache.get(key)
    
    def delete(self, key: str) -> bool:
        """删除缓存值 - 通用接口"""
        return self.skill_cache.delete(key)
    
    def has(self, key: str) -> bool:
        """检查缓存是否存在"""
        return self.skill_cache.get(key) is not None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'attribute_cache': {
                'size': self.attribute_cache.size(),
                'hit_rate': self.attribute_cache.hit_rate(),
                'hits': self.attribute_cache.hits,
                'misses': self.attribute_cache.misses
            },
            'damage_cache': {
                'size': self.damage_cache.size(),
                'hit_rate': self.damage_cache.hit_rate(),
                'hits': self.damage_cache.hits,
                'misses': self.damage_cache.misses
            },
            'skill_cache': {
                'size': self.skill_cache.size(),
                'hit_rate': self.skill_cache.hit_rate(),
                'hits': self.skill_cache.hits,
                'misses': self.skill_cache.misses
            },
            'formation_cache': {
                'size': self.formation_cache.size(),
                'hit_rate': self.formation_cache.hit_rate(),
                'hits': self.formation_cache.hits,
                'misses': self.formation_cache.misses
            }
        }


# 全局缓存管理器
cache_manager = GameCacheManager()

# 现在可以安全导入监控模块
try:
    from .monitor import cache_monitor, get_cache_monitor
except ImportError:
    # 如果监控模块不可用，创建空的占位符
    cache_monitor = None
    get_cache_monitor = lambda: None

# 便捷的缓存装饰器
def cache_attribute_calculation(func: Callable) -> Callable:
    """属性计算缓存装饰器"""
    return cached(cache_manager.attribute_cache)(func)

def cache_damage_calculation(func: Callable) -> Callable:
    """伤害计算缓存装饰器"""
    return cached(cache_manager.damage_cache)(func)

def cache_skill_result(func: Callable) -> Callable:
    """技能结果缓存装饰器"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return cached(cache_manager.skill_cache)(func)(*args, **kwargs)

    return wrapper

def cache_result(cache_key: str, ttl: Optional[float] = None):
    """通用结果缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        def key_func(*args, **kwargs):
            return f"{cache_key}_{cache_manager.skill_cache._generate_key(*args, **kwargs)}"
        return cached(cache_manager.skill_cache, key_func=key_func, ttl=ttl)(func)
    return decorator


# 添加简单的LRU缓存实现
class LRUCache:
    """简单的LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_order = []
        self.hits = 0
        self.misses = 0
    
    def get(self, key):
        """获取缓存值"""
        if key in self.cache:
            # 更新访问顺序
            self.access_order.remove(key)
            self.access_order.append(key)
            self.hits += 1
            return self.cache[key]
        else:
            self.misses += 1
            return None
    
    def set(self, key, value, ttl=None):
        """设置缓存值"""
        if key in self.cache:
            self.access_order.remove(key)
        elif len(self.cache) >= self.max_size:
            # 移除最久未使用的项
            oldest = self.access_order.pop(0)
            del self.cache[oldest]
        
        self.cache[key] = value
        self.access_order.append(key)
    
    def get_stats(self):
        """获取缓存统计"""
        total = self.hits + self.misses
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate": self.hits / total if total > 0 else 0.0
        }

__all__ = [
    'CachePolicy',
    'CacheEntry',
    'Cache',
    'WeakCache',
    'GameCacheManager',
    'cached',
    'cache_manager',
    'cache_attribute_calculation',
    'cache_damage_calculation',
    'cache_skill_result',
    'cache_result',
    'LRUCache',
    'cache_monitor',
    'get_cache_monitor'
]