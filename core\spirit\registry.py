#!/usr/bin/env python3
"""
精灵注册系统

提供统一的精灵自动注册机制，避免手动维护多个注册点。
"""

import os
import importlib
import importlib.util
import inspect
from typing import Dict, Callable, Any, List, Optional
from pathlib import Path

from ..logging import get_logger

logger = get_logger("spirit.registry")


class SpiritRegistry:
    """精灵注册表 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.creators: Dict[str, Callable] = {}
            self.metadata: Dict[str, Dict[str, Any]] = {}
            self.prototypes: List[Dict[str, Any]] = []
            self._initialized = True
            logger.info("精灵注册表初始化完成")
    
    def register_spirit(self, 
                       spirit_id: str, 
                       creator_func: Callable,
                       name: str = None,
                       skills: List[str] = None,
                       category: str = "NORMAL",
                       **metadata):
        """
        注册精灵
        
        Args:
            spirit_id: 精灵唯一ID
            creator_func: 精灵创建函数
            name: 精灵显示名称
            skills: 技能列表
            category: 精灵分类
            **metadata: 其他元数据
        """
        if spirit_id in self.creators:
            logger.warning(f"精灵 {spirit_id} 已存在，将被覆盖")
        
        self.creators[spirit_id] = creator_func
        
        # 如果没有提供名称，尝试从创建函数获取
        if name is None:
            try:
                temp_spirit = creator_func()
                name = temp_spirit.name
            except Exception as e:
                logger.warning(f"无法获取精灵 {spirit_id} 的名称: {e}")
                name = spirit_id
        
        # 存储元数据
        self.metadata[spirit_id] = {
            "id": spirit_id,
            "name": name,
            "creator": creator_func,
            "skills": skills or [],
            "category": category,
            **metadata
        }
        
        # 更新原型列表（用于API）
        prototype = {
            "name": name,
            "creator": creator_func,
            "skills": skills or []
        }
        
        # 移除旧的原型（如果存在）
        self.prototypes = [p for p in self.prototypes if p.get("name") != name]
        self.prototypes.append(prototype)
        
        logger.info(f"精灵注册成功: {spirit_id} -> {name}")
    
    def get_creator(self, spirit_id: str) -> Optional[Callable]:
        """获取精灵创建函数"""
        return self.creators.get(spirit_id)
    
    def get_all_creators(self) -> Dict[str, Callable]:
        """获取所有精灵创建函数"""
        return self.creators.copy()
    
    def get_metadata(self, spirit_id: str) -> Optional[Dict[str, Any]]:
        """获取精灵元数据"""
        return self.metadata.get(spirit_id)
    
    def get_all_metadata(self) -> Dict[str, Dict[str, Any]]:
        """获取所有精灵元数据"""
        return self.metadata.copy()
    
    def get_prototypes(self) -> List[Dict[str, Any]]:
        """获取精灵原型列表（用于API）"""
        return self.prototypes.copy()
    
    def list_spirits(self) -> List[str]:
        """列出所有已注册的精灵ID"""
        return list(self.creators.keys())
    
    def exists(self, spirit_id: str) -> bool:
        """检查精灵是否已注册"""
        return spirit_id in self.creators
    
    def unregister(self, spirit_id: str) -> bool:
        """注销精灵"""
        if spirit_id in self.creators:
            del self.creators[spirit_id]
            if spirit_id in self.metadata:
                del self.metadata[spirit_id]
            # 从原型列表中移除
            spirit_name = self.metadata.get(spirit_id, {}).get("name", "")
            self.prototypes = [p for p in self.prototypes if p.get("name") != spirit_name]
            logger.info(f"精灵注销成功: {spirit_id}")
            return True
        return False
    
    def clear(self):
        """清空注册表"""
        self.creators.clear()
        self.metadata.clear()
        self.prototypes.clear()
        logger.info("精灵注册表已清空")


# 全局注册表实例
spirit_registry = SpiritRegistry()


def register_spirit(spirit_id: str, 
                   creator_func: Callable = None,
                   name: str = None,
                   skills: List[str] = None,
                   category: str = "NORMAL",
                   **metadata):
    """
    精灵注册装饰器
    
    用法:
    @register_spirit("my_spirit_id", name="我的精灵", skills=["skill1", "skill2"])
    def create_my_spirit():
        return MySpirit(...)
    
    或者直接调用:
    register_spirit("my_spirit_id", create_my_spirit, name="我的精灵")
    """
    def decorator(func):
        spirit_registry.register_spirit(
            spirit_id=spirit_id,
            creator_func=func,
            name=name,
            skills=skills,
            category=category,
            **metadata
        )
        return func
    
    if creator_func is not None:
        # 直接调用模式
        return decorator(creator_func)
    else:
        # 装饰器模式
        return decorator


def auto_discover_spirits(spirits_dir: str = "spirits_data") -> int:
    """
    自动发现并注册精灵
    
    Args:
        spirits_dir: 精灵数据目录
        
    Returns:
        注册的精灵数量
    """
    logger.info(f"开始自动发现精灵: {spirits_dir}")
    
    registered_count = 0
    spirits_path = Path(spirits_dir)
    
    if not spirits_path.exists():
        logger.warning(f"精灵目录不存在: {spirits_dir}")
        return 0
    
    # 遍历精灵文件
    for py_file in spirits_path.glob("*.py"):
        if py_file.name.startswith("_"):
            continue  # 跳过私有文件
        
        module_name = py_file.stem
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                f"spirits_data.{module_name}", 
                py_file
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找创建函数 - 只查找精灵创建函数，排除被动效果和技能创建函数
            creator_functions = []
            for name, obj in inspect.getmembers(module):
                if (inspect.isfunction(obj) and
                    name.startswith("create_") and
                    not name.startswith("create_test") and  # 排除测试函数
                    not name.endswith("_passive_effects") and  # 排除被动效果创建函数
                    not name.endswith("_skills") and  # 排除技能创建函数
                    not name.endswith("_effect") and  # 排除单个效果创建函数
                    not name.endswith("_effect_for_spirit") and  # 排除精灵效果创建函数
                    not name.endswith("_effect_for_war_god") and  # 排除战神效果创建函数
                    name != "create_passive_effects"):  # 排除通用被动效果函数
                    creator_functions.append((name, obj))
            
            # 注册找到的精灵
            for func_name, func in creator_functions:
                try:
                    # 尝试创建精灵实例获取信息
                    temp_spirit = func()
                    spirit_id = temp_spirit.id
                    spirit_name = temp_spirit.name
                    
                    # 推断技能列表
                    skills = []
                    if hasattr(temp_spirit, 'skills'):
                        skills = [skill.metadata.name for skill in temp_spirit.skills]
                    
                    # 推断分类
                    category = "NORMAL"
                    if "test" in module_name.lower():
                        category = "TEST"
                    elif hasattr(temp_spirit, 'metadata') and temp_spirit.metadata.tags:
                        if "HERO" in str(temp_spirit.metadata.tags):
                            category = "HERO"
                        elif "SUPPORT" in str(temp_spirit.metadata.tags):
                            category = "SUPPORT"
                    
                    # 注册精灵
                    spirit_registry.register_spirit(
                        spirit_id=spirit_id,
                        creator_func=func,
                        name=spirit_name,
                        skills=skills,
                        category=category,
                        module=module_name,
                        function=func_name
                    )
                    
                    registered_count += 1
                    logger.info(f"自动注册精灵: {spirit_id} ({spirit_name})")
                    
                except Exception as e:
                    logger.error(f"注册精灵失败 {func_name}: {e}")
                    
        except Exception as e:
            logger.error(f"导入模块失败 {module_name}: {e}")
    
    logger.info(f"自动发现完成，注册了 {registered_count} 个精灵")
    return registered_count


def get_spirit_creators() -> Dict[str, Callable]:
    """获取所有精灵创建函数（用于API）"""
    return spirit_registry.get_all_creators()


def get_spirit_prototypes() -> List[Dict[str, Any]]:
    """获取精灵原型列表（用于API）"""
    return spirit_registry.get_prototypes()


def create_spirit_by_id(spirit_id: str, team: int = 0, position: tuple = (1, 1)):
    """
    通过ID创建精灵（用于API）- 优化版本，优先使用 JSON 配置

    Args:
        spirit_id: 精灵ID
        team: 队伍编号
        position: 位置

    Returns:
        精灵实例

    Raises:
        ValueError: 精灵不存在
    """
    # 优先尝试使用 JSON 配置系统创建精灵
    try:
        from .json_factory import get_json_spirit_factory
        factory = get_json_spirit_factory()
        spirit = factory.create_spirit(spirit_id, team, position)
        if spirit:
            logger.info(f"通过 JSON 配置创建精灵: {spirit_id}")
            return spirit
    except Exception as e:
        logger.warning(f"JSON 配置创建精灵失败 {spirit_id}: {e}")

    # 回退到传统的注册表方式
    creator = spirit_registry.get_creator(spirit_id)
    if not creator:
        # 尝试列出可用的精灵（包括 JSON 配置的）
        available_spirits = spirit_registry.list_spirits()
        try:
            from .json_factory import get_json_spirit_factory
            factory = get_json_spirit_factory()
            json_spirits = factory.list_available_spirits()
            available_spirits.extend(json_spirits)
        except:
            pass

        raise ValueError(f"名为 '{spirit_id}' 的精灵不存在。可用精灵: {available_spirits}")

    spirit = creator()
    spirit.team = team
    spirit.position = position
    logger.info(f"通过注册表创建精灵: {spirit_id}")
    return spirit


# 初始化精灵注册表（延迟自动发现）
def initialize_spirit_registry():
    """初始化精灵注册表"""
    logger.info("初始化精灵注册表...")
    # 🔧 延迟自动发现，避免循环依赖
    # 自动发现将在系统完全初始化后进行
    logger.info(f"精灵注册表初始化完成，自动发现将延迟执行")
    return 0  # 暂时返回0，实际注册数量将在延迟发现后更新


if __name__ == "__main__":
    # 测试自动发现
    initialize_spirit_registry()
    
    logger.info("已注册的精灵:")
    for spirit_id in spirit_registry.list_spirits():
        metadata = spirit_registry.get_metadata(spirit_id)
        logger.info(f"  {spirit_id}: {metadata['name']} ({metadata['category']})")