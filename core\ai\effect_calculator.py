"""
条件性效果计算器

根据动态条件计算额外效果，包括：
- 御神英雄技等复杂条件效果
- 暴击率、暴击伤害、破击率加成
- 气势获得、伤害加成等
- 连击、斩杀等特殊效果
"""

from __future__ import annotations
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, TYPE_CHECKING, Callable
from abc import ABC, abstractmethod

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill
    from .condition_evaluator import AttackConditionResult

from core.logging import get_logger

logger = get_logger("ai.effect")

@dataclass
class ConditionalEffectResult:
    """条件性效果结果"""
    effects: Dict[str, Any] = field(default_factory=dict)
    trigger_events: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_effect(self, effect_name: str, value: Any, metadata: Dict[str, Any] = None):
        """添加效果"""
        self.effects[effect_name] = value
        if metadata:
            self.metadata[effect_name] = metadata
    
    def add_trigger_event(self, event_name: str, event_data: Dict[str, Any] = None):
        """添加触发事件"""
        self.trigger_events.append(event_name)
        if event_data:
            self.metadata[f"{event_name}_data"] = event_data
    
    def get_effect(self, effect_name: str, default: Any = None) -> Any:
        """获取效果值"""
        return self.effects.get(effect_name, default)
    
    def has_effect(self, effect_name: str) -> bool:
        """检查是否有指定效果"""
        return effect_name in self.effects
    
    def merge(self, other: 'ConditionalEffectResult'):
        """合并另一个效果结果"""
        # 合并效果（相同效果名称的值会相加）
        for effect_name, value in other.effects.items():
            if effect_name in self.effects:
                # 尝试相加，如果不能相加则覆盖
                try:
                    if isinstance(self.effects[effect_name], (int, float)) and isinstance(value, (int, float)):
                        self.effects[effect_name] += value
                    else:
                        self.effects[effect_name] = value
                except:
                    self.effects[effect_name] = value
            else:
                self.effects[effect_name] = value
        
        # 合并事件
        self.trigger_events.extend(other.trigger_events)
        
        # 合并元数据
        self.metadata.update(other.metadata)
    
    def apply_multiplier(self, multiplier: float):
        """对数值效果应用倍率"""
        for effect_name, value in self.effects.items():
            if isinstance(value, (int, float)):
                self.effects[effect_name] = value * multiplier

class IEffectCalculator(ABC):
    """效果计算器接口"""
    
    @abstractmethod
    def calculate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算条件性效果"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取计算器名称"""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取计算器优先级（数字越小优先级越高）"""
        pass

class SpiritWisdomEffectCalculator(IEffectCalculator):
    """灵目慧心效果计算器"""
    
    def get_name(self) -> str:
        return "spirit_wisdom"
    
    def get_priority(self) -> int:
        return 10  # 高优先级
    
    def calculate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算灵目慧心效果"""
        
        result = ConditionalEffectResult()
        
        # 检查是否有灵目慧心战斗加成效果
        if not conditions.get_condition('attacker_has_spirit_wisdom', False):
            return result
        
        # 检查目标是否无法行动
        if not conditions.get_condition('target_unable_to_act', False):
            return result
        
        logger.debug(f"{getattr(attacker, 'name', 'Unknown')} 触发灵目慧心战斗加成")
        
        # 添加战斗加成效果
        result.add_effect('crit_rate_bonus', 0.4, {'source': 'spirit_wisdom', 'description': '+40%暴击率'})
        result.add_effect('crit_damage_bonus', 0.4, {'source': 'spirit_wisdom', 'description': '+40%暴击伤害'})
        result.add_effect('pierce_rate_bonus', 0.4, {'source': 'spirit_wisdom', 'description': '+40%破击率'})
        result.add_effect('energy_gain', 30, {'source': 'spirit_wisdom', 'description': '+30气势'})
        
        # 添加触发事件
        result.add_trigger_event('spirit_wisdom_boost_triggered', {
            'attacker': attacker,
            'target': target,
            'skill': skill
        })
        
        return result

class CriticalHitEffectCalculator(IEffectCalculator):
    """暴击效果计算器"""
    
    def get_name(self) -> str:
        return "critical_hit"
    
    def get_priority(self) -> int:
        return 20  # 中等优先级
    
    def calculate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算暴击相关效果"""
        
        result = ConditionalEffectResult()
        
        # 检查是否会暴击
        if not conditions.get_condition('will_critical_hit', False):
            return result
        
        # 基础暴击伤害加成
        base_crit_damage = 0.5  # 50%基础暴击伤害
        result.add_effect('crit_damage_multiplier', base_crit_damage)
        
        # 检查是否有暴击增益
        if conditions.get_condition('attacker_has_crit_buff', False):
            result.add_effect('crit_damage_bonus', 0.2, {'source': 'crit_buff', 'description': '暴击增益+20%'})
        
        # 暴击触发事件
        result.add_trigger_event('critical_hit_triggered', {
            'attacker': attacker,
            'target': target
        })
        
        return result

class ExecuteEffectCalculator(IEffectCalculator):
    """斩杀效果计算器"""
    
    def get_name(self) -> str:
        return "execute"
    
    def get_priority(self) -> int:
        return 15  # 高优先级
    
    def calculate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算斩杀效果"""
        
        result = ConditionalEffectResult()
        
        # 检查目标是否为低血量
        target_hp_percentage = conditions.get_condition('target_hp_percentage', 1.0)
        
        if target_hp_percentage <= 0.3:  # 目标血量低于30%
            # 低血量额外伤害
            damage_bonus = 0.5 if target_hp_percentage <= 0.1 else 0.2
            result.add_effect('damage_multiplier', 1.0 + damage_bonus, {
                'source': 'execute',
                'description': f'斩杀效果+{int(damage_bonus*100)}%伤害'
            })
            
            # 斩杀触发事件
            result.add_trigger_event('execute_triggered', {
                'attacker': attacker,
                'target': target,
                'target_hp_percentage': target_hp_percentage
            })
        
        return result

class ComboEffectCalculator(IEffectCalculator):
    """连击效果计算器"""
    
    def get_name(self) -> str:
        return "combo"
    
    def get_priority(self) -> int:
        return 25  # 中等优先级
    
    def calculate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算连击效果"""
        
        result = ConditionalEffectResult()
        
        # 检查连击条件
        if self._can_trigger_combo(attacker, target, skill, conditions):
            result.add_effect('combo_attack', True)
            result.add_effect('combo_damage_multiplier', 0.3, {
                'source': 'combo',
                'description': '连击+30%伤害'
            })
            
            result.add_trigger_event('combo_triggered', {
                'attacker': attacker,
                'target': target
            })
        
        return result
    
    def _can_trigger_combo(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult'
    ) -> bool:
        """检查是否可以触发连击"""
        # 简单的连击条件：攻击者血量高于80%且目标无法行动
        attacker_hp = conditions.get_condition('attacker_hp_percentage', 0.0)
        target_unable = conditions.get_condition('target_unable_to_act', False)
        
        return attacker_hp >= 0.8 and target_unable

class TeamworkEffectCalculator(IEffectCalculator):
    """团队协作效果计算器"""
    
    def get_name(self) -> str:
        return "teamwork"
    
    def get_priority(self) -> int:
        return 30  # 低优先级
    
    def calculate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算团队协作效果"""
        
        result = ConditionalEffectResult()
        
        # 队友数量加成
        ally_count = conditions.get_condition('ally_count', 1)
        if ally_count >= 3:
            result.add_effect('teamwork_damage_bonus', 0.1 * (ally_count - 2), {
                'source': 'teamwork',
                'description': f'团队协作+{int(0.1 * (ally_count - 2) * 100)}%伤害'
            })
        
        # 背水一战效果
        if conditions.get_condition('ally_outnumbered', False):
            result.add_effect('desperate_damage_bonus', 0.25, {
                'source': 'desperate',
                'description': '背水一战+25%伤害'
            })
            
            result.add_trigger_event('desperate_fight_triggered', {
                'attacker': attacker,
                'ally_count': ally_count,
                'enemy_count': conditions.get_condition('enemy_count', 1)
            })
        
        return result

class ConditionalEffectCalculator:
    """条件性效果计算器主类"""
    
    def __init__(self):
        self.calculators: List[IEffectCalculator] = []
        self._custom_calculators: Dict[str, Callable] = {}
        self._setup_default_calculators()
    
    def _setup_default_calculators(self):
        """设置默认计算器"""
        self.calculators = [
            SpiritWisdomEffectCalculator(),
            ExecuteEffectCalculator(),
            CriticalHitEffectCalculator(),
            ComboEffectCalculator(),
            TeamworkEffectCalculator()
        ]
        # 按优先级排序
        self.calculators.sort(key=lambda x: x.get_priority())
    
    def add_calculator(self, calculator: IEffectCalculator):
        """添加自定义计算器"""
        self.calculators.append(calculator)
        self.calculators.sort(key=lambda x: x.get_priority())
    
    def remove_calculator(self, calculator_name: str):
        """移除指定名称的计算器"""
        self.calculators = [c for c in self.calculators if c.get_name() != calculator_name]
    
    def register_custom_effect(self, name: str, calculator_func: Callable):
        """注册自定义效果计算函数"""
        self._custom_calculators[name] = calculator_func
    
    def calculate_conditional_effects(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> ConditionalEffectResult:
        """计算条件性效果"""
        
        logger.debug(f"计算 {getattr(attacker, 'name', 'Unknown')} 的条件性效果")
        
        final_result = ConditionalEffectResult()
        
        # 执行所有计算器
        for calculator in self.calculators:
            try:
                result = calculator.calculate(attacker, target, skill, conditions, battle_state)
                final_result.merge(result)
                
                logger.debug(f"效果计算器 {calculator.get_name()} 完成")
                
            except Exception as e:
                logger.error(f"效果计算器 {calculator.get_name()} 执行失败: {e}")
                continue
        
        # 执行自定义计算器
        for name, calculator_func in self._custom_calculators.items():
            try:
                result = calculator_func(attacker, target, skill, conditions, battle_state)
                if isinstance(result, ConditionalEffectResult):
                    final_result.merge(result)
                    logger.debug(f"自定义效果计算器 {name} 完成")
            except Exception as e:
                logger.error(f"自定义效果计算器 {name} 执行失败: {e}")
                continue
        
        return final_result
    
    def get_available_effects(self) -> List[str]:
        """获取所有可用的效果类型"""
        effects = []
        for calculator in self.calculators:
            effects.append(calculator.get_name())
        effects.extend(self._custom_calculators.keys())
        return effects
    
    def get_calculator_info(self) -> Dict[str, Dict[str, Any]]:
        """获取计算器信息（用于调试）"""
        info = {}
        for calculator in self.calculators:
            info[calculator.get_name()] = {
                'priority': calculator.get_priority(),
                'type': 'built_in'
            }
        
        for name in self._custom_calculators.keys():
            info[name] = {
                'priority': 999,  # 自定义计算器默认最低优先级
                'type': 'custom'
            }
        
        return info
