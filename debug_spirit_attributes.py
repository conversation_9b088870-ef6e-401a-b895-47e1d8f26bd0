#!/usr/bin/env python3
"""
调试精灵属性结构

查看精灵对象的实际属性结构和数值
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_spirit_attributes():
    """调试精灵属性"""
    print("🔧 调试精灵属性...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"可用精灵: {available_spirits}")
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        # 检查第一个精灵的属性结构
        spirit_name = available_spirits[0]
        spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
        
        print(f"\n📊 精灵: {spirit.name}")
        print(f"类型: {type(spirit)}")
        
        # 检查attributes对象
        if hasattr(spirit, 'attributes'):
            attributes = spirit.attributes
            print(f"\n🔍 attributes对象:")
            print(f"  类型: {type(attributes)}")
            print(f"  属性列表:")
            
            for attr_name in dir(attributes):
                if not attr_name.startswith('_'):
                    try:
                        attr_value = getattr(attributes, attr_name)
                        if not callable(attr_value):
                            print(f"    {attr_name}: {type(attr_value)} = {attr_value}")
                    except:
                        print(f"    {attr_name}: <无法访问>")
            
            # 检查常见的属性名称
            common_attrs = [
                'attack', 'actual_attack', 'base_attack', 'final_attack',
                'defense', 'actual_defense', 'base_defense', 'final_defense', 'pdef', 'mdef',
                'speed', 'actual_speed', 'base_speed', 'final_speed',
                'hit_rate', 'actual_hit_rate', 'base_hit_rate', 'final_hit_rate',
                'dodge_rate', 'actual_dodge_rate', 'base_dodge_rate', 'final_dodge_rate',
                'crit_rate', 'actual_crit_rate', 'base_crit_rate', 'final_crit_rate',
                'crit_damage', 'actual_crit_damage', 'base_crit_damage', 'final_crit_damage'
            ]
            
            print(f"\n🎯 常见属性检查:")
            for attr_name in common_attrs:
                if hasattr(attributes, attr_name):
                    value = getattr(attributes, attr_name)
                    print(f"    ✅ {attr_name}: {value}")
                else:
                    print(f"    ❌ {attr_name}: 不存在")
        else:
            print("❌ 精灵没有 attributes 属性")
        
        # 检查精灵的直接属性
        print(f"\n🔍 精灵直接属性:")
        direct_attrs = ['attack', 'defense', 'speed', 'current_hp', 'max_hp']
        for attr_name in direct_attrs:
            if hasattr(spirit, attr_name):
                value = getattr(spirit, attr_name)
                print(f"    ✅ {attr_name}: {value}")
            else:
                print(f"    ❌ {attr_name}: 不存在")
        
        # 检查JSON配置中的属性
        print(f"\n📋 JSON配置属性:")
        if hasattr(spirit, 'metadata'):
            metadata = spirit.metadata
            print(f"  metadata: {metadata}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_current_snapshot_creation():
    """测试当前快照创建"""
    print("\n🔧 测试当前快照创建...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        # 使用当前的快照创建逻辑
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        
        snapshot = recorder._create_spirit_snapshot(spirit)
        
        print(f"📊 当前快照结果:")
        print(f"  名称: {snapshot.name}")
        print(f"  生命值: {snapshot.current_hp}/{snapshot.max_hp}")
        print(f"  气势: {snapshot.energy}/{snapshot.max_energy}")
        print(f"  实际攻击力: {snapshot.actual_attack}")
        print(f"  实际防御力: {snapshot.actual_defense}")
        print(f"  速度: {snapshot.actual_speed}")
        print(f"  命中率: {snapshot.actual_hit_rate}")
        print(f"  闪避率: {snapshot.actual_dodge_rate}")
        print(f"  暴击率: {snapshot.actual_crit_rate}")
        print(f"  暴击伤害: {snapshot.actual_crit_damage}")
        print(f"  效果数量: {len(snapshot.effects)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 快照测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 精灵属性调试")
    print("="*60)
    
    tests = [
        ("精灵属性结构", debug_spirit_attributes),
        ("快照创建测试", test_current_snapshot_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
