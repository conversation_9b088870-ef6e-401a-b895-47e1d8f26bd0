"""
神曜圣谕·女帝 - 主精灵文件

神曜圣谕·女帝 (Divine Oracle - Empress)
属性：空，职业：通灵师、神曜、肉盾

这是女帝精灵的主要定义文件，整合了所有模块。
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.spirit.refactored_spirit import RefactoredSpirit, SpiritMetadata
from core.attribute import Attributes
from core.element import ElementType
from core.profession import ProfessionType
from core.components import SkillComponent
from core.effect.system import IEffect

from .skills import create_nudi_skills, NUDI_SKILLS_DATA
from .passive_effects import (
    create_shuntian_yingren_effect, 
    create_xinggui_nizhuan_effect
)
from .suming_zhihuan_shenyao import create_suming_zhihuan_effect


def create_nudi_spirit() -> RefactoredSpirit:
    """创建神曜圣谕·女帝精灵"""
    
    # 创建属性 - 肉盾型高级精灵
    attributes = Attributes(
        base_hp=2800,      # 极高生命值
        hp_p=0.0,
        hp_flat=0.0,
        base_attack=140,   # 中等攻击力
        attack_p=0.0,
        attack_flat=0.0,
        base_pdef=180,     # 高物理防御
        pdef_p=0.0,
        pdef_flat=0.0,
        base_mdef=170,     # 高魔法防御
        mdef_p=0.0,
        mdef_flat=0.0,
        base_speed=100,         # 中等速度
        base_hit_rate=0.0,      # 命中率加成：0%（标准100%命中）
        base_dodge_rate=0.08,   # 闪避率加成：+8%
        base_break_rate=0.0,
        base_block_rate=0.0,
        base_crit_rate=0.10,    # 较低暴击率
        base_crit_res_rate=0.0
    )
    
    # 创建元数据
    metadata = SpiritMetadata(
        element=ElementType.AIR,  # 空属性
        professions={ProfessionType.NECROMANCER, ProfessionType.SHENYAO, ProfessionType.TANK},  # 通灵师、神曜、肉盾
        tags={"TAUNT", "IMMUNITY", "TONGLING", "TANK"},
        shenge_level=6  # 默认6级神格
    )
    
    # 创建精灵实例
    spirit = RefactoredSpirit(
        id="shen_yao_sheng_yu_nu_di",
        name="神曜圣谕·女帝",
        attributes=attributes,
        position=(1, 2),  # 前排中心位置
        team=0,
        skills=[],
        metadata=metadata
    )
    
    # 创建技能并添加到精灵的技能组件中
    skills = create_nudi_skills(spirit)
    skill_component = spirit.components.get_component(SkillComponent)
    if skill_component:
        for skill in skills:
            skill_component.add_skill(skill)
    
    return spirit


def create_nudi_passive_effects(spirit: RefactoredSpirit) -> List[IEffect]:
    """创建女帝所有被动效果列表，供自动注册"""
    return [
        create_shuntian_yingren_effect(spirit),
        create_suming_zhihuan_effect(spirit, spirit.metadata.shenge_level),
        create_xinggui_nizhuan_effect(spirit)
    ]


# 通用别名（保持向后兼容）
def create_shen_yao_sheng_yu_nu_di_passive_effects(spirit: RefactoredSpirit) -> List[IEffect]:
    """创建女帝所有被动效果列表，供自动注册（向后兼容别名）"""
    return create_nudi_passive_effects(spirit)


def create_passive_effects(spirit: RefactoredSpirit) -> List[IEffect]:
    """通用被动效果创建函数（向后兼容别名）"""
    return create_nudi_passive_effects(spirit)


# 精灵数据配置
SPIRIT_DATA = {
    "id": "shen_yao_sheng_yu_nu_di",
    "name": "神曜圣谕·女帝",
    "attributes": {
        "hp": 2800,
        "attack": 140,
        "pdef": 180,
        "mdef": 170,
        "speed": 100,
        "crit_rate": 0.10,
        "crit_damage": 0.5,
        "hit_rate": 0.0,   # 命中率加成：0%
        "dodge_rate": 0.08  # 闪避率加成：+8%
    },
    "metadata": {
        "element": "AIR",
        "professions": ["NECROMANCER", "SHENYAO", "TANK"],
        "tags": ["TAUNT", "IMMUNITY", "TONGLING", "TANK"],
        "shenge_level": 6
    },
    "skills": NUDI_SKILLS_DATA
}


# 精灵信息
NUDI_INFO = {
    "name": "神曜圣谕·女帝",
    "title": "Divine Oracle - Empress",
    "element": "空",
    "professions": ["通灵师", "神曜", "肉盾"],
    "description": "拥有强大嘲讽和免疫能力的肉盾型精灵，能够保护队友并在战斗中发挥关键作用。",
    "characteristics": [
        "高生命值和防御力",
        "嘲讽机制保护队友",
        "免疫效果减少伤害",
        "通灵技能提供强大续航",
        "神格等级提供渐进式增强"
    ],
    "battle_role": "前排坦克",
    "difficulty": "中等"
}


# 导出函数和数据
__all__ = [
    'create_nudi_spirit',
    'create_nudi_passive_effects',
    'create_shen_yao_sheng_yu_nu_di_passive_effects',  # 向后兼容
    'create_passive_effects',  # 向后兼容
    'SPIRIT_DATA',
    'NUDI_INFO'
]
