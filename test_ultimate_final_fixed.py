#!/usr/bin/env python3
"""
最终的超杀系统测试 - 修复版
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ultimate_system_final():
    """最终测试超杀系统"""
    print("🔧 最终测试超杀系统...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        print(f"📊 测试精灵: {spirit1.name}")
        
        # 设置高气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置气势为: {spirit1.energy}")
        
        # 获取超杀阈值
        ultimate_threshold = spirit1.get_ultimate_threshold()
        print(f"  超杀阈值: {ultimate_threshold}")
        print(f"  应该释放超杀: {spirit1.energy >= ultimate_threshold}")
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 测试AI技能选择
        print(f"\n🎯 测试AI技能选择...")
        from core.ai import get_action_generator
        ai_generator = get_action_generator()
        
        skill_result = ai_generator.skill_selector.select_skill(spirit1, battle_state)
        
        print(f"  AI选择结果:")
        print(f"    技能: {skill_result.skill.metadata.name if skill_result.skill else None}")
        print(f"    技能类型: {getattr(skill_result.skill.metadata, 'cast_type', 'Unknown') if skill_result.skill else 'None'}")
        print(f"    选择原因: {skill_result.reason}")
        
        # 检查是否选择了超杀技能
        if skill_result.skill and getattr(skill_result.skill.metadata, 'cast_type', '') == 'ULTIMATE':
            print(f"  ✅ AI正确选择了超杀技能")
            
            # 测试技能执行
            print(f"\n🎯 测试技能执行...")
            energy_before = spirit1.energy
            print(f"    执行前气势: {energy_before}")
            
            try:
                actions = skill_result.skill.cast(battle_state)
                print(f"    生成动作数量: {len(actions)}")
                
                # 检查动作类型
                energy_consumed = False
                ultimate_damage = False
                log_messages = []
                
                for i, action in enumerate(actions):
                    action_type = type(action).__name__
                    print(f"      动作 {i}: {action_type}")
                    
                    if action_type == 'ConsumeEnergyAction':
                        energy_consumed = True
                        consume_amount = getattr(action, 'amount', 0)
                        print(f"        消耗气势: {consume_amount}")
                    
                    elif action_type == 'DamageAction':
                        is_ultimate = getattr(action, 'is_ultimate', False)
                        overflow_energy = getattr(action, 'overflow_energy', None)
                        ultimate_threshold = getattr(action, 'ultimate_threshold', None)
                        
                        if is_ultimate and overflow_energy is not None and ultimate_threshold is not None:
                            ultimate_damage = True
                            print(f"        超杀伤害: ✅")
                            print(f"        溢出气势: {overflow_energy}")
                            print(f"        超杀阈值: {ultimate_threshold}")
                            
                            # 计算气势系数
                            q_coeff = 1.0 + (overflow_energy / ultimate_threshold)
                            print(f"        气势系数: {q_coeff:.3f}")
                        else:
                            print(f"        普通伤害")
                    
                    elif action_type == 'LogAction':
                        message = getattr(action, 'message', '')
                        log_messages.append(message)
                        print(f"        日志: {message}")
                
                # 检查结果
                if energy_consumed and ultimate_damage:
                    print(f"    ✅ 超杀技能正确生成了气势消耗和增强伤害动作")
                    
                    # 测试战斗中的实际效果
                    print(f"\n🎯 测试战斗中的实际效果...")
                    
                    # 创建战斗引擎
                    from core.battle.engines.factory import create_battle_engine
                    
                    # 重新设置气势（因为可能被消耗了）
                    if hasattr(spirit1, 'components'):
                        from core.components import EnergyComponent
                        energy_component = spirit1.components.get_component(EnergyComponent)
                        if energy_component:
                            energy_component._current_energy = 300
                    
                    engine = create_battle_engine(
                        formation1,
                        formation2,
                        round_limit=1,
                        turn_order_bonus_energy=0  # 不给额外气势
                    )
                    
                    # 执行一回合
                    print(f"    执行第一回合...")
                    result = engine.execute_round()
                    
                    # 检查气势变化
                    current_energy = spirit1.energy
                    print(f"    战斗后 {spirit1.name} 气势: {current_energy}")
                    
                    if current_energy < 100:  # 超杀应该消耗所有气势
                        print(f"    ✅ 检测到超杀技能使用（气势大幅减少）")
                        return True
                    else:
                        print(f"    ❌ 没有检测到超杀技能使用（气势没有大幅减少）")
                        return False
                else:
                    print(f"    ❌ 超杀技能没有正确生成动作")
                    print(f"      气势消耗: {energy_consumed}")
                    print(f"      超杀伤害: {ultimate_damage}")
                    return False
                
            except Exception as e:
                print(f"    ❌ 技能执行失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"  ❌ AI没有选择超杀技能")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 最终超杀系统测试")
    print("="*60)
    
    result = test_ultimate_system_final()
    
    print("\n" + "="*60)
    print("📊 测试结果:")
    print("="*60)
    
    if result:
        print("🎉 测试通过！超杀系统完全正常工作")
        print("\n📋 验证内容:")
        print("  ✅ AI在气势达到阈值时强制选择超杀技能")
        print("  ✅ 超杀技能自动消耗所有气势")
        print("  ✅ 伤害基于溢出气势进行增强")
        print("  ✅ 生成正确的动作序列")
        print("  ✅ 战斗中实际使用超杀技能")
        print("\n🚀 现在可以运行战斗UI验证实际效果：")
        print("  python ui/ux/enhanced_battle_ui.py")
        print("\n🎊 超杀系统的强制释放机制已经完善完成！")
        print("  到达超杀阈值必须释放超杀技能的要求已经实现！")
    else:
        print("❌ 测试失败，超杀系统需要进一步修复")

if __name__ == "__main__":
    main()
