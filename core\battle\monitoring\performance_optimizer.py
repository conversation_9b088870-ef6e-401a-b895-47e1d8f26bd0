"""
战斗系统性能优化器

提供各种性能优化技术，包括缓存、对象池、批处理等
"""
import time
import weakref
from typing import Dict, Any, List, Optional, Callable, TypeVar, Generic
from functools import wraps, lru_cache
from dataclasses import dataclass
from collections import defaultdict, deque


T = TypeVar('T')


@dataclass
class PerformanceMetrics:
    """性能指标"""
    execution_count: int = 0
    total_time: float = 0.0
    avg_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    
    def update(self, execution_time: float):
        """更新性能指标"""
        self.execution_count += 1
        self.total_time += execution_time
        self.avg_time = self.total_time / self.execution_count
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)
    
    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_hits += 1
    
    def record_cache_miss(self):
        """记录缓存未命中"""
        self.cache_misses += 1
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0


class ObjectPool(Generic[T]):
    """对象池，减少对象创建开销"""
    
    def __init__(self, factory: Callable[[], T], max_size: int = 100):
        self.factory = factory
        self.max_size = max_size
        self.pool: deque = deque()
        self.created_count = 0
        self.reused_count = 0
    
    def get(self) -> T:
        """获取对象"""
        if self.pool:
            self.reused_count += 1
            return self.pool.popleft()
        else:
            self.created_count += 1
            return self.factory()
    
    def put(self, obj: T):
        """归还对象"""
        if len(self.pool) < self.max_size:
            # 重置对象状态（如果有reset方法）
            if hasattr(obj, 'reset'):
                obj.reset()
            self.pool.append(obj)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = self.created_count + self.reused_count
        return {
            'created': self.created_count,
            'reused': self.reused_count,
            'total_used': total,
            'reuse_rate': self.reused_count / total if total > 0 else 0.0,
            'pool_size': len(self.pool)
        }


class SmartCache:
    """智能缓存系统"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 300.0):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.cache: Dict[str, Any] = {}
        self.timestamps: Dict[str, float] = {}
        self.access_count: Dict[str, int] = defaultdict(int)
        self.metrics = PerformanceMetrics()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        current_time = time.time()
        
        # 检查是否存在且未过期
        if key in self.cache:
            if current_time - self.timestamps[key] < self.ttl:
                self.access_count[key] += 1
                self.metrics.record_cache_hit()
                return self.cache[key]
            else:
                # 过期，删除
                self._remove_key(key)
        
        self.metrics.record_cache_miss()
        return None
    
    def put(self, key: str, value: Any):
        """设置缓存值"""
        current_time = time.time()
        
        # 如果缓存已满，清理最少使用的项
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_lru()
        
        self.cache[key] = value
        self.timestamps[key] = current_time
        self.access_count[key] = 1
    
    def _remove_key(self, key: str):
        """删除键"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
        self.access_count.pop(key, None)
    
    def _evict_lru(self):
        """清理最少使用的项"""
        if not self.cache:
            return
        
        # 找到访问次数最少的键
        lru_key = min(self.access_count.keys(), key=lambda k: self.access_count[k])
        self._remove_key(lru_key)
    
    def clear_expired(self):
        """清理过期项"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.timestamps.items()
            if current_time - timestamp >= self.ttl
        ]
        for key in expired_keys:
            self._remove_key(key)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hit_rate': self.metrics.cache_hit_rate,
            'hits': self.metrics.cache_hits,
            'misses': self.metrics.cache_misses
        }


class BatchProcessor:
    """批处理器，减少频繁的小操作"""
    
    def __init__(self, batch_size: int = 10, flush_interval: float = 0.1):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.batches: Dict[str, List[Any]] = defaultdict(list)
        self.last_flush: Dict[str, float] = defaultdict(float)
        self.processors: Dict[str, Callable] = {}
    
    def register_processor(self, batch_type: str, processor: Callable[[List[Any]], None]):
        """注册批处理器"""
        self.processors[batch_type] = processor
    
    def add(self, batch_type: str, item: Any):
        """添加项到批次"""
        if batch_type not in self.processors:
            raise ValueError(f"未注册的批处理类型: {batch_type}")
        
        self.batches[batch_type].append(item)
        
        # 检查是否需要刷新
        current_time = time.time()
        batch = self.batches[batch_type]
        
        should_flush = (
            len(batch) >= self.batch_size or
            current_time - self.last_flush[batch_type] >= self.flush_interval
        )
        
        if should_flush:
            self.flush(batch_type)
    
    def flush(self, batch_type: str = None):
        """刷新批次"""
        if batch_type:
            batch_types = [batch_type]
        else:
            batch_types = list(self.batches.keys())
        
        for bt in batch_types:
            batch = self.batches[bt]
            if batch:
                processor = self.processors[bt]
                processor(batch)
                batch.clear()
                self.last_flush[bt] = time.time()
    
    def flush_all(self):
        """刷新所有批次"""
        self.flush()


def performance_monitor(func_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                execution_time = end_time - start_time
                
                # 记录性能数据
                if not hasattr(wrapper, '_metrics'):
                    wrapper._metrics = PerformanceMetrics()
                wrapper._metrics.update(execution_time)
                
                # 如果执行时间过长，记录警告
                if execution_time > 0.1:  # 100ms
                    from ..logging import battle_logger
                    battle_logger.warning(f"函数 {name} 执行时间过长: {execution_time:.3f}s")
        
        def get_metrics():
            return getattr(wrapper, '_metrics', PerformanceMetrics())
        
        wrapper.get_metrics = get_metrics
        return wrapper
    
    return decorator


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self):
        self.caches: Dict[str, SmartCache] = {}
        self.object_pools: Dict[str, ObjectPool] = {}
        self.batch_processor = BatchProcessor()
        self.global_metrics: Dict[str, PerformanceMetrics] = {}
    
    def get_cache(self, name: str, max_size: int = 1000, ttl: float = 300.0) -> SmartCache:
        """获取或创建缓存"""
        if name not in self.caches:
            self.caches[name] = SmartCache(max_size, ttl)
        return self.caches[name]
    
    def get_object_pool(self, name: str, factory: Callable, max_size: int = 100) -> ObjectPool:
        """获取或创建对象池"""
        if name not in self.object_pools:
            self.object_pools[name] = ObjectPool(factory, max_size)
        return self.object_pools[name]
    
    def cached_function(self, cache_name: str, key_func: Callable = None):
        """缓存函数结果的装饰器"""
        def decorator(func):
            cache = self.get_cache(cache_name)
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    key = key_func(*args, **kwargs)
                else:
                    key = f"{func.__name__}_{hash((args, tuple(sorted(kwargs.items()))))}"
                
                # 尝试从缓存获取
                result = cache.get(key)
                if result is not None:
                    return result
                
                # 计算结果并缓存
                result = func(*args, **kwargs)
                cache.put(key, result)
                return result
            
            return wrapper
        return decorator
    
    def cleanup(self):
        """清理过期缓存和统计"""
        for cache in self.caches.values():
            cache.clear_expired()
        
        # 刷新所有批处理
        self.batch_processor.flush_all()
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {
            'caches': {name: cache.get_stats() for name, cache in self.caches.items()},
            'object_pools': {name: pool.get_stats() for name, pool in self.object_pools.items()},
            'global_metrics': {name: {
                'execution_count': metrics.execution_count,
                'avg_time': metrics.avg_time,
                'total_time': metrics.total_time
            } for name, metrics in self.global_metrics.items()}
        }
        return report


# 全局性能优化器实例
_performance_optimizer = PerformanceOptimizer()


def get_performance_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器"""
    return _performance_optimizer


def cached(cache_name: str, key_func: Callable = None):
    """缓存装饰器的便捷函数"""
    return _performance_optimizer.cached_function(cache_name, key_func)


def get_object_pool(name: str, factory: Callable, max_size: int = 100) -> ObjectPool:
    """获取对象池的便捷函数"""
    return _performance_optimizer.get_object_pool(name, factory, max_size)


# 使用示例
if __name__ == "__main__":
    print("=== 性能优化器测试 ===")
    
    optimizer = PerformanceOptimizer()
    
    # 测试缓存
    cache = optimizer.get_cache("test_cache")
    cache.put("key1", "value1")
    print(f"缓存测试: {cache.get('key1')}")
    
    # 测试对象池
    class TestObject:
        def __init__(self):
            self.value = 0
        
        def reset(self):
            self.value = 0
    
    pool = optimizer.get_object_pool("test_pool", TestObject)
    obj1 = pool.get()
    obj1.value = 42
    pool.put(obj1)
    obj2 = pool.get()
    print(f"对象池测试: {obj2.value}")  # 应该是0（已重置）
    
    # 测试性能监控
    @performance_monitor("test_function")
    def slow_function():
        time.sleep(0.01)
        return "result"
    
    slow_function()
    metrics = slow_function.get_metrics()
    print(f"性能监控测试: {metrics.execution_count} 次执行，平均 {metrics.avg_time:.3f}s")
    
    # 生成报告
    report = optimizer.get_performance_report()
    print(f"性能报告: {report}")
