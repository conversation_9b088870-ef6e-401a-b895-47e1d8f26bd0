<template>
  <div class="system-settings h-full flex flex-col">
    <!-- 页面头部 -->
    <div class="page-header bg-slate-800/50 backdrop-blur-sm border-b border-purple-500/20 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-white mb-2">
            <el-icon class="mr-3"><Setting /></el-icon>
            系统设置
          </h1>
          <p class="text-slate-400">个性化配置你的游戏体验</p>
        </div>
        <div class="flex items-center space-x-3">
          <el-button @click="resetToDefaults">
            <el-icon class="mr-2"><RefreshLeft /></el-icon>
            恢复默认
          </el-button>
          <el-button type="primary" @click="saveSettings">
            <el-icon class="mr-2"><Check /></el-icon>
            保存设置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content flex-1 flex overflow-hidden">
      <!-- 左侧导航 -->
      <div class="settings-nav w-56 bg-slate-800/30 border-r border-slate-600/30 p-4 overflow-y-auto">
        <div class="nav-list space-y-2">
          <div
            v-for="category in settingsCategories"
            :key="category.key"
            class="nav-item"
            :class="{ 'active': activeCategory === category.key }"
            @click="activeCategory = category.key"
          >
            <el-icon class="mr-3">
              <component :is="category.icon" />
            </el-icon>
            <span>{{ category.label }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧设置面板 -->
      <div class="settings-panel flex-1 p-4 overflow-y-auto">
        <!-- 通用设置 -->
        <div v-if="activeCategory === 'general'" class="settings-section">
          <h2 class="section-title">通用设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">界面设置</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">主题模式</label>
                <p class="setting-desc">选择界面的主题风格</p>
              </div>
              <el-select v-model="settings.general.theme" style="width: 150px">
                <el-option label="深色主题" value="dark" />
                <el-option label="浅色主题" value="light" />
                <el-option label="自动切换" value="auto" />
              </el-select>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">语言设置</label>
                <p class="setting-desc">选择界面显示语言</p>
              </div>
              <el-select v-model="settings.general.language" style="width: 150px">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="繁体中文" value="zh-TW" />
                <el-option label="English" value="en-US" />
                <el-option label="日本語" value="ja-JP" />
              </el-select>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">侧边栏折叠</label>
                <p class="setting-desc">启动时是否折叠侧边栏</p>
              </div>
              <el-switch v-model="settings.general.sidebarCollapsed" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">自动保存</label>
                <p class="setting-desc">自动保存战术和阵容配置</p>
              </div>
              <el-switch v-model="settings.general.autoSave" />
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">通知设置</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">桌面通知</label>
                <p class="setting-desc">允许显示桌面通知</p>
              </div>
              <el-switch v-model="settings.general.desktopNotifications" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">声音提示</label>
                <p class="setting-desc">操作时播放声音提示</p>
              </div>
              <el-switch v-model="settings.general.soundEffects" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">通知音量</label>
                <p class="setting-desc">调整通知声音的音量</p>
              </div>
              <el-slider
                v-model="settings.general.notificationVolume"
                :min="0"
                :max="100"
                show-input
                style="width: 200px"
              />
            </div>
          </div>
        </div>

        <!-- 战斗设置 -->
        <div v-if="activeCategory === 'battle'" class="settings-section">
          <h2 class="section-title">战斗设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">战斗显示</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">战斗动画</label>
                <p class="setting-desc">显示战斗过程中的动画效果</p>
              </div>
              <el-switch v-model="settings.battle.animations" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">动画速度</label>
                <p class="setting-desc">调整战斗动画的播放速度</p>
              </div>
              <el-slider
                v-model="settings.battle.animationSpeed"
                :min="0.5"
                :max="3"
                :step="0.1"
                show-input
                style="width: 200px"
              />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">伤害数字</label>
                <p class="setting-desc">显示伤害和治疗数字</p>
              </div>
              <el-switch v-model="settings.battle.damageNumbers" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">技能特效</label>
                <p class="setting-desc">显示技能释放的特效</p>
              </div>
              <el-switch v-model="settings.battle.skillEffects" />
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">自动战斗</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">自动播放速度</label>
                <p class="setting-desc">自动战斗的播放速度倍率</p>
              </div>
              <el-select v-model="settings.battle.autoPlaySpeed" style="width: 150px">
                <el-option label="1x" :value="1" />
                <el-option label="1.5x" :value="1.5" />
                <el-option label="2x" :value="2" />
                <el-option label="3x" :value="3" />
                <el-option label="5x" :value="5" />
              </el-select>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">跳过动画</label>
                <p class="setting-desc">自动战斗时跳过动画</p>
              </div>
              <el-switch v-model="settings.battle.skipAnimationsInAuto" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">战斗结果提示</label>
                <p class="setting-desc">战斗结束时显示结果提示</p>
              </div>
              <el-switch v-model="settings.battle.showResultNotification" />
            </div>
          </div>
        </div>

        <!-- 性能设置 -->
        <div v-if="activeCategory === 'performance'" class="settings-section">
          <h2 class="section-title">性能设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">渲染设置</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">渲染质量</label>
                <p class="setting-desc">调整图形渲染质量</p>
              </div>
              <el-select v-model="settings.performance.renderQuality" style="width: 150px">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="超高" value="ultra" />
              </el-select>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">帧率限制</label>
                <p class="setting-desc">限制最大帧率以节省性能</p>
              </div>
              <el-select v-model="settings.performance.fpsLimit" style="width: 150px">
                <el-option label="30 FPS" :value="30" />
                <el-option label="60 FPS" :value="60" />
                <el-option label="120 FPS" :value="120" />
                <el-option label="无限制" :value="0" />
              </el-select>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">垂直同步</label>
                <p class="setting-desc">启用垂直同步以减少画面撕裂</p>
              </div>
              <el-switch v-model="settings.performance.vsync" />
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">内存管理</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">自动清理缓存</label>
                <p class="setting-desc">定期清理不必要的缓存数据</p>
              </div>
              <el-switch v-model="settings.performance.autoCleanCache" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">预加载资源</label>
                <p class="setting-desc">预先加载常用资源以提升响应速度</p>
              </div>
              <el-switch v-model="settings.performance.preloadResources" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">内存使用限制</label>
                <p class="setting-desc">限制应用使用的最大内存 (MB)</p>
              </div>
              <el-input-number
                v-model="settings.performance.memoryLimit"
                :min="512"
                :max="4096"
                :step="256"
                style="width: 150px"
              />
            </div>
          </div>
        </div>

        <!-- 数据设置 -->
        <div v-if="activeCategory === 'data'" class="settings-section">
          <h2 class="section-title">数据设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">数据同步</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">云端同步</label>
                <p class="setting-desc">将数据同步到云端</p>
              </div>
              <el-switch v-model="settings.data.cloudSync" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">自动备份</label>
                <p class="setting-desc">定期自动备份本地数据</p>
              </div>
              <el-switch v-model="settings.data.autoBackup" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">备份频率</label>
                <p class="setting-desc">设置自动备份的频率</p>
              </div>
              <el-select v-model="settings.data.backupFrequency" style="width: 150px">
                <el-option label="每小时" value="hourly" />
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="手动" value="manual" />
              </el-select>
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">数据管理</h3>
            
            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">战斗记录保留</label>
                <p class="setting-desc">保留战斗记录的天数</p>
              </div>
              <el-input-number
                v-model="settings.data.battleLogRetention"
                :min="7"
                :max="365"
                style="width: 150px"
              />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">统计数据收集</label>
                <p class="setting-desc">允许收集匿名统计数据</p>
              </div>
              <el-switch v-model="settings.data.collectAnalytics" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <label class="setting-label">数据压缩</label>
                <p class="setting-desc">压缩存储数据以节省空间</p>
              </div>
              <el-switch v-model="settings.data.compression" />
            </div>
          </div>

          <!-- 数据操作按钮 -->
          <div class="setting-group">
            <h3 class="group-title">数据操作</h3>
            
            <div class="data-actions grid grid-cols-2 gap-4">
              <el-button @click="exportData">
                <el-icon class="mr-2"><Download /></el-icon>
                导出数据
              </el-button>
              
              <el-button @click="importData">
                <el-icon class="mr-2"><Upload /></el-icon>
                导入数据
              </el-button>
              
              <el-button @click="clearCache">
                <el-icon class="mr-2"><Delete /></el-icon>
                清理缓存
              </el-button>
              
              <el-button type="danger" @click="resetAllData">
                <el-icon class="mr-2"><Warning /></el-icon>
                重置所有数据
              </el-button>
            </div>
          </div>
        </div>

        <!-- 关于页面 -->
        <div v-if="activeCategory === 'about'" class="settings-section">
          <h2 class="section-title">关于</h2>
          
          <div class="about-content">
            <div class="app-info bg-slate-800/50 rounded-lg p-6 mb-6 border border-slate-600/30">
              <div class="flex items-center mb-4">
                <div class="app-icon w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white text-2xl font-bold mr-4">
                  ⚔️
                </div>
                <div>
                  <h3 class="text-2xl font-bold text-white">精灵战斗模拟器</h3>
                  <p class="text-slate-400">版本 {{ appVersion }}</p>
                </div>
              </div>
              
              <p class="text-slate-300 mb-4">
                一个功能强大的回合制战斗模拟器，支持复杂的精灵系统、战术规划和数据分析。
              </p>
              
              <div class="app-stats grid grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-2xl font-bold text-purple-400">{{ totalBattles }}</div>
                  <div class="text-slate-400 text-sm">总战斗次数</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-blue-400">{{ totalSpirits }}</div>
                  <div class="text-slate-400 text-sm">精灵数量</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-green-400">{{ totalTactics }}</div>
                  <div class="text-slate-400 text-sm">战术方案</div>
                </div>
              </div>
            </div>

            <div class="system-info bg-slate-800/50 rounded-lg p-6 mb-6 border border-slate-600/30">
              <h3 class="text-lg font-semibold text-white mb-4">系统信息</h3>
              <div class="info-grid grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-slate-400">浏览器:</span>
                  <span class="text-white ml-2">{{ browserInfo }}</span>
                </div>
                <div>
                  <span class="text-slate-400">操作系统:</span>
                  <span class="text-white ml-2">{{ osInfo }}</span>
                </div>
                <div>
                  <span class="text-slate-400">屏幕分辨率:</span>
                  <span class="text-white ml-2">{{ screenResolution }}</span>
                </div>
                <div>
                  <span class="text-slate-400">内存使用:</span>
                  <span class="text-white ml-2">{{ memoryUsage }}</span>
                </div>
              </div>
            </div>

            <div class="links bg-slate-800/50 rounded-lg p-6 border border-slate-600/30">
              <h3 class="text-lg font-semibold text-white mb-4">相关链接</h3>
              <div class="link-grid grid grid-cols-2 gap-3">
                <el-button text @click="openLink('https://github.com')">
                  <el-icon class="mr-2"><Link /></el-icon>
                  GitHub 仓库
                </el-button>
                <el-button text @click="openLink('https://docs.example.com')">
                  <el-icon class="mr-2"><Document /></el-icon>
                  使用文档
                </el-button>
                <el-button text @click="openLink('https://feedback.example.com')">
                  <el-icon class="mr-2"><ChatDotRound /></el-icon>
                  反馈建议
                </el-button>
                <el-button text @click="openLink('https://support.example.com')">
                  <el-icon class="mr-2"><Service /></el-icon>
                  技术支持
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAppStore } from '../stores/app'

const appStore = useAppStore()

// 响应式数据
const activeCategory = ref('general')
const appVersion = ref('2.0.0')
const totalBattles = ref(1247)
const totalSpirits = ref(32)
const totalTactics = ref(15)

// 设置分类
const settingsCategories = [
  { key: 'general', label: '通用设置', icon: 'Setting' },
  { key: 'battle', label: '战斗设置', icon: 'Sword' },
  { key: 'performance', label: '性能设置', icon: 'Monitor' },
  { key: 'data', label: '数据设置', icon: 'FolderOpened' },
  { key: 'about', label: '关于', icon: 'InfoFilled' }
]

// 设置数据
const settings = reactive({
  general: {
    theme: 'dark',
    language: 'zh-CN',
    sidebarCollapsed: false,
    autoSave: true,
    desktopNotifications: true,
    soundEffects: true,
    notificationVolume: 70
  },
  battle: {
    animations: true,
    animationSpeed: 1.0,
    damageNumbers: true,
    skillEffects: true,
    autoPlaySpeed: 2,
    skipAnimationsInAuto: false,
    showResultNotification: true
  },
  performance: {
    renderQuality: 'high',
    fpsLimit: 60,
    vsync: true,
    autoCleanCache: true,
    preloadResources: true,
    memoryLimit: 2048
  },
  data: {
    cloudSync: false,
    autoBackup: true,
    backupFrequency: 'daily',
    battleLogRetention: 30,
    collectAnalytics: true,
    compression: true
  }
})

// 系统信息
const browserInfo = ref('')
const osInfo = ref('')
const screenResolution = ref('')
const memoryUsage = ref('')

// 方法
const saveSettings = async () => {
  try {
    // 保存到本地存储
    localStorage.setItem('gameSettings', JSON.stringify(settings))
    
    // 应用主题设置
    if (settings.general.theme !== appStore.theme) {
      appStore.toggleTheme()
    }
    
    // 应用侧边栏设置
    if (settings.general.sidebarCollapsed !== appStore.sidebarCollapsed) {
      appStore.toggleSidebar()
    }
    
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
    console.error('保存设置失败:', error)
  }
}

const resetToDefaults = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复所有设置到默认值吗？此操作不可撤销。',
      '确认重置',
      {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 重置为默认值
    Object.assign(settings.general, {
      theme: 'dark',
      language: 'zh-CN',
      sidebarCollapsed: false,
      autoSave: true,
      desktopNotifications: true,
      soundEffects: true,
      notificationVolume: 70
    })
    
    Object.assign(settings.battle, {
      animations: true,
      animationSpeed: 1.0,
      damageNumbers: true,
      skillEffects: true,
      autoPlaySpeed: 2,
      skipAnimationsInAuto: false,
      showResultNotification: true
    })
    
    Object.assign(settings.performance, {
      renderQuality: 'high',
      fpsLimit: 60,
      vsync: true,
      autoCleanCache: true,
      preloadResources: true,
      memoryLimit: 2048
    })
    
    Object.assign(settings.data, {
      cloudSync: false,
      autoBackup: true,
      backupFrequency: 'daily',
      battleLogRetention: 30,
      collectAnalytics: true,
      compression: true
    })
    
    ElMessage.success('设置已重置为默认值')
  } catch {
    // 用户取消重置
  }
}

const exportData = () => {
  try {
    const data = {
      settings,
      exportTime: new Date().toISOString(),
      version: appVersion.value
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `game_settings_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
    console.error('导出数据失败:', error)
  }
}

const importData = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        
        if (data.settings) {
          Object.assign(settings, data.settings)
          ElMessage.success('数据导入成功')
        } else {
          ElMessage.error('无效的数据格式')
        }
      } catch (error) {
        ElMessage.error('数据导入失败')
        console.error('导入数据失败:', error)
      }
    }
    reader.readAsText(file)
  }
  
  input.click()
}

const clearCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有缓存数据吗？这可能会影响应用性能。',
      '确认清理',
      {
        confirmButtonText: '清理',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 清理缓存
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
    }
    
    // 清理本地存储中的临时数据
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.startsWith('cache_') || key.startsWith('temp_'))) {
        keysToRemove.push(key)
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    ElMessage.success('缓存清理完成')
  } catch {
    // 用户取消清理
  }
}

const resetAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '警告：此操作将删除所有用户数据，包括精灵、战术、战斗记录等。此操作不可撤销！',
      '确认重置所有数据',
      {
        confirmButtonText: '我确定要重置',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    // 清理所有本地存储
    localStorage.clear()
    sessionStorage.clear()
    
    // 清理IndexedDB（如果使用）
    if ('indexedDB' in window) {
      // 这里可以添加清理IndexedDB的代码
    }
    
    ElMessage.success('所有数据已重置，页面将刷新')
    
    // 延迟刷新页面
    setTimeout(() => {
      window.location.reload()
    }, 2000)
  } catch {
    // 用户取消重置
  }
}

const openLink = (url: string) => {
  window.open(url, '_blank')
}

const getSystemInfo = () => {
  // 获取浏览器信息
  const ua = navigator.userAgent
  if (ua.includes('Chrome')) {
    browserInfo.value = 'Chrome'
  } else if (ua.includes('Firefox')) {
    browserInfo.value = 'Firefox'
  } else if (ua.includes('Safari')) {
    browserInfo.value = 'Safari'
  } else if (ua.includes('Edge')) {
    browserInfo.value = 'Edge'
  } else {
    browserInfo.value = '未知'
  }
  
  // 获取操作系统信息
  if (ua.includes('Windows')) {
    osInfo.value = 'Windows'
  } else if (ua.includes('Mac')) {
    osInfo.value = 'macOS'
  } else if (ua.includes('Linux')) {
    osInfo.value = 'Linux'
  } else if (ua.includes('Android')) {
    osInfo.value = 'Android'
  } else if (ua.includes('iOS')) {
    osInfo.value = 'iOS'
  } else {
    osInfo.value = '未知'
  }
  
  // 获取屏幕分辨率
  screenResolution.value = `${screen.width} x ${screen.height}`
  
  // 获取内存使用情况（如果支持）
  if ('memory' in performance) {
    const memory = (performance as any).memory
    const used = Math.round(memory.usedJSHeapSize / 1024 / 1024)
    const total = Math.round(memory.totalJSHeapSize / 1024 / 1024)
    memoryUsage.value = `${used} MB / ${total} MB`
  } else {
    memoryUsage.value = '不支持'
  }
}

const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem('gameSettings')
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings)
      Object.assign(settings, parsed)
    }
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

// 生命周期
onMounted(() => {
  getSystemInfo()
  loadSettings()
})
</script>

<style scoped lang="scss">
.system-settings {
  background-color: rgba(15, 23, 42, 0.8);
}

.page-header {
  padding: 1.25rem 1.5rem;
}

.settings-content {
  height: calc(100% - 6rem);
}

.settings-nav {
  min-width: 14rem;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  color: #cbd5e1;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: rgba(139, 92, 246, 0.1);
  color: #f8fafc;
}

.nav-item.active {
  background-color: rgba(139, 92, 246, 0.2);
  color: #a78bfa;
}

.settings-section {
  max-width: 900px;
  margin: 0 auto;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f8fafc;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

.setting-group {
  margin-bottom: 2rem;
  padding: 1.25rem;
  background-color: rgba(30, 41, 59, 0.5);
  border-radius: 0.75rem;
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.group-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 1rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(100, 116, 139, 0.1);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  margin-right: 1rem;
}

.setting-label {
  font-weight: 500;
  color: #e2e8f0;
  margin-bottom: 0.25rem;
  display: block;
}

.setting-desc {
  font-size: 0.875rem;
  color: #94a3b8;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}
</style>