// 数据分析页面专用样式
@import './variables.scss';

// 基础布局容器
.analytics-container {
  @apply h-full flex flex-col;
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
  
  // 头部和统计卡片固定
  .analytics-header,
  .stats-grid {
    flex-shrink: 0;
  }
  
  // 图表区域可以滚动
  .charts-section {
    flex: 1 1 auto;
    min-height: 0;
    overflow-y: auto;
  }
}

// 页面头部优化
.analytics-header {
  @apply bg-slate-800/50 backdrop-blur-sm border-b border-purple-500/20;
  padding: 1rem 1.5rem;
  flex-shrink: 0;
  
  .header-content {
    @apply flex items-center justify-between max-w-7xl mx-auto;
    max-width: 1400px;
  }
  
  .header-title {
    @apply flex items-center space-x-3;
    min-width: 0; // 允许文本截断
    
    .title-icon {
      @apply text-purple-400;
      flex-shrink: 0;
    }
    
    .title-text {
      min-width: 0;
      
      h1 {
        @apply text-lg font-bold text-white leading-tight;
        
        @media (min-width: $breakpoint-md) {
          @apply text-xl;
        }
      }
      
      p {
        @apply text-slate-400 text-xs mt-1;
        
        @media (min-width: $breakpoint-md) {
          @apply text-sm;
        }
      }
    }
  }
  
  .header-actions {
    @apply flex items-center space-x-3;
    flex-shrink: 0;
  }
}

// 统计卡片网格
.stats-grid {
  @apply px-6 py-2;
  flex-shrink: 0;
  
  .stats-container {
    @apply flex gap-4 max-w-6xl mx-auto;
    
    // 确保四个卡片始终在一行
    > * {
      flex: 1;
      min-width: 120px; // 设置最小宽度，防止过度收缩
      max-width: none;
    }
    
    @media (max-width: $breakpoint-md) {
      gap: 3;
      
      > * {
        min-width: 100px;
      }
    }
    
    @media (max-width: $breakpoint-sm) {
      gap: 2;
      padding: 0 0.5rem;
      
      > * {
        min-width: 80px;
      }
    }
  }
}

// 统一的卡片样式
.analytics-card {
  @apply rounded-lg border border-slate-600/30 backdrop-blur-sm;
  background: rgba(30, 41, 59, 0.5);
  transition: all $transition-normal;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.4);
  }
  
  // 统计卡片变体
  &.stat-card {
    @apply p-3 text-white;
    height: 80px;
    min-width: 0; // 允许收缩
    
    .stat-content {
      @apply flex items-center justify-between h-full;
    }
    
    .stat-info {
      flex: 1;
      min-width: 0;
      
      .stat-label {
        @apply text-xs opacity-70;
        line-height: 1.1;
        font-size: 11px;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .stat-value {
        @apply font-bold;
        line-height: 1;
        font-size: 18px;
        margin-bottom: 2px;
        
        @media (min-width: $breakpoint-lg) {
          font-size: 20px;
        }
      }
      
      .stat-change {
        line-height: 1;
        font-size: 10px;
        opacity: 0.8;
        white-space: nowrap;
      }
    }
    
    .stat-icon {
      @apply text-xl opacity-40 ml-2;
      flex-shrink: 0;
      
      @media (min-width: $breakpoint-lg) {
        @apply text-2xl;
      }
    }
  }
  
  // 图表卡片变体
  &.chart-card {
    @apply p-4;
    height: 350px;
    display: flex;
    flex-direction: column;
    
    .chart-header {
      @apply flex items-center mb-3;
      flex-shrink: 0;
      
      .chart-title {
        @apply text-base font-semibold text-white flex items-center;
        
        .el-icon {
          @apply mr-2 text-purple-400;
        }
      }
    }
    
    .chart-container {
      flex: 1;
      min-height: 0;
      width: 100%;
      overflow: hidden;
    }
  }
  
  // 表格卡片变体
  &.table-card {
    @apply p-4;
    
    .table-header {
      @apply flex items-center justify-between mb-4;
      flex-shrink: 0;
      
      .table-title {
        @apply text-base font-semibold text-white;
      }
    }
  }
}

// 图表区域布局
.charts-section {
  @apply px-6 py-3;
  flex: 1 1 auto;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
  
  .charts-grid {
    @apply grid gap-4 max-w-6xl mx-auto;
    grid-template-columns: repeat(2, 1fr);
    padding-bottom: 2rem;
    
    @media (max-width: $breakpoint-lg) {
      grid-template-columns: repeat(2, 1fr);
      gap: 3;
    }
    
    @media (max-width: $breakpoint-md) {
      grid-template-columns: 1fr;
      gap: 3;
      padding-bottom: 1rem;
    }
    
    @media (max-width: $breakpoint-sm) {
      @apply px-2;
      gap: 2;
    }
  }
}

// 数据表格区域
.data-section {
  @apply max-w-6xl mx-auto w-full;
  flex-shrink: 0;
  margin-top: 1rem;
  
  .analytics-tabs {
    :deep(.el-tabs__header) {
      @apply bg-slate-800/30 rounded-t-lg p-2 border-b border-slate-600/30;
      margin-bottom: 0;
    }
    
    :deep(.el-tabs__nav-wrap) {
      @apply bg-transparent;
    }
    
    :deep(.el-tabs__item) {
      @apply text-slate-300 px-4 py-2 rounded-md transition-all;
      font-size: 14px;
      
      &:hover {
        @apply text-white bg-slate-700/50;
      }
      
      &.is-active {
        @apply text-white bg-purple-600/80;
      }
    }
    
    :deep(.el-tabs__content) {
      @apply p-0;
      height: 450px;
      overflow-y: auto;
    }
    
    :deep(.el-tab-pane) {
      .analytics-card.table-card {
        height: 430px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        
        .table-header {
          flex-shrink: 0;
          padding: 1rem;
        }
        
        .el-table {
          flex: 1;
          overflow: auto;
          margin: 0 1rem 1rem 1rem;
        }
      }
    }
  }
}

// 响应式优化
@media (max-width: $breakpoint-md) {
  .analytics-container {
    height: 100vh;
    overflow: hidden;
  }
  
  .analytics-header {
    padding: 0.75rem 1rem;
    
    .header-content {
      @apply flex-col space-y-2;
    }
    
    .header-actions {
      @apply w-full justify-between flex-wrap gap-2;
      
      .el-date-picker {
        flex: 1;
        min-width: 180px;
      }
    }
  }
  
  .stats-grid {
    @apply px-4 py-2;
    
    .stats-container {
      // 保持flex布局，确保一行显示
      gap: 2;
    }
  }
  
  .charts-section {
    @apply px-4 py-2;
    
    .charts-grid {
      grid-template-columns: 1fr;
      gap: 3;
    }
  }
  
  .data-section {
    margin: 0 1rem;
    margin-top: 0.5rem;
    
    .analytics-tabs {
      :deep(.el-tabs__content) {
        max-height: 300px;
        overflow-y: auto; // 确保表格区域有滚动条
      }
      
      :deep(.el-tab-pane) {
        .analytics-card.table-card {
          max-height: 280px;
          overflow: hidden;
          
          .table-header {
            padding: 0.5rem;
          }
          
          .el-table {
            margin: 0 0.5rem 0.5rem 0.5rem;
            overflow: auto; // 表格内部滚动
          }
        }
      }
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .analytics-header {
    padding: 0.5rem 0.75rem;
    
    .header-content {
      padding: 0;
    }
    
    .header-actions {
      flex-direction: column;
      gap: 0.5rem;
      
      .el-date-picker {
        width: 100%;
      }
      
      .el-button {
        width: 100%;
      }
    }
  }
  
  .stats-grid {
    @apply px-3 py-2;
    
    .stats-container {
      // 即使在小屏幕也保持一行，让卡片更窄
      gap: 1.5;
      overflow-x: auto; // 如果太窄可以横向滚动
    }
  }
  
  .charts-section {
    @apply px-3 py-2;
    
    .charts-grid {
      gap: 2;
    }
  }
  
  .data-section {
    margin: 0 0.75rem;
    margin-top: 0.25rem;
  }
}

// 对话框样式优化
.analytics-dialog {
  :deep(.el-dialog) {
    @apply bg-slate-900/95 backdrop-blur-sm border border-slate-600/30;
    border-radius: $radius-lg;
  }
  
  :deep(.el-dialog__header) {
    @apply bg-slate-800/50 border-b border-slate-600/30 p-4;
    border-radius: $radius-lg $radius-lg 0 0;
  }
  
  :deep(.el-dialog__title) {
    @apply text-white font-semibold;
  }
  
  :deep(.el-dialog__body) {
    @apply p-0;
  }
}

// 表格样式优化
:deep(.el-table) {
  background: transparent !important;
  
  .el-table__header {
    background: rgba(30, 41, 59, 0.8) !important;
    
    th {
      background: transparent !important;
      color: #f8fafc !important;
      border-bottom: 1px solid rgba(100, 116, 139, 0.3) !important;
      font-weight: 600;
    }
  }
  
  .el-table__body {
    tr {
      background: transparent !important;
      
      &:hover td {
        background: rgba(139, 92, 246, 0.1) !important;
      }
      
      &.el-table__row--striped td {
        background: rgba(30, 41, 59, 0.3) !important;
      }
    }
    
    td {
      background: transparent !important;
      color: #cbd5e1 !important;
      border-bottom: 1px solid rgba(100, 116, 139, 0.2) !important;
    }
  }
}

// 布局修复和响应式优化
.el-date-editor.el-input {
  width: 100% !important;
  
  @media (max-width: 768px) {
    .el-input__inner {
      font-size: 14px;
    }
  }
}

.el-table .el-table__body-wrapper {
  max-height: 400px;
  overflow-y: auto;
  
  @media (max-width: 768px) {
    max-height: 300px;
  }
}

.analytics-dialog {
  @media (max-width: 768px) {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
      max-height: 90vh;
      
      .el-dialog__body {
        max-height: calc(90vh - 120px);
        overflow-y: auto;
      }
    }
  }
}

.chart-container {
  position: relative;
  width: 100%;
  
  > div {
    max-width: 100% !important;
    max-height: 100% !important;
  }
}

.stat-card {
  .stat-value {
    font-size: clamp(0.875rem, 3vw, 1.125rem);
  }
  
  .stat-label {
    font-size: clamp(0.5rem, 1.5vw, 0.625rem);
  }
  
  .stat-change {
    font-size: clamp(0.5rem, 1.2vw, 0.5rem);
  }
}

@media (max-width: 640px) {
  .el-table {
    font-size: 12px;
    
    .el-table__header th {
      padding: 6px 2px;
    }
    
    .el-table__body td {
      padding: 6px 2px;
    }
    
    .el-table__column--hidden-sm {
      display: none !important;
    }
  }
}

// 动画效果
.analytics-card {
  animation: slideInUp 0.3s ease-out;
}

.stats-grid .analytics-card {
  @for $i from 1 through 4 {
    &:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }
}

.charts-section .analytics-card {
  @for $i from 1 through 4 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.2 + ($i - 1) * 0.1}s;
    }
  }
}