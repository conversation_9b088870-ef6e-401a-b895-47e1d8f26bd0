"""
Components系统集成

将AI行动生成系统与现有的components系统集成，
提供更准确的技能获取和状态检查功能。
"""

from __future__ import annotations
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from dataclasses import dataclass

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill

from core.logging import get_logger

logger = get_logger("ai.components")

@dataclass
class SkillAvailabilityResult:
    """技能可用性检查结果"""
    available_skills: List['Skill']
    unavailable_skills: List[tuple]  # (skill, reason)
    total_skills: int
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class ComponentsIntegratedSkillSelector:
    """与Components系统集成的技能选择器"""
    
    def __init__(self):
        self._skill_cache = {}  # 缓存技能信息
    
    def get_available_skills_from_components(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> SkillAvailabilityResult:
        """从components系统获取可用技能"""
        
        available_skills = []
        unavailable_skills = []
        total_skills = 0
        
        try:
            # 1. 尝试从components系统获取技能
            if hasattr(spirit, 'components'):
                skill_component = self._get_skill_component(spirit)
                
                if skill_component:
                    skills = self._get_skills_from_component(skill_component)
                    total_skills = len(skills)
                    
                    for skill in skills:
                        if self._check_skill_availability(spirit, skill, battle_state, skill_component):
                            available_skills.append(skill)
                        else:
                            reason = self._get_unavailability_reason(spirit, skill, battle_state, skill_component)
                            unavailable_skills.append((skill, reason))
                    
                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 通过components获得 {len(available_skills)}/{total_skills} 个可用技能")
            
            # 2. 回退：直接从精灵获取技能
            elif hasattr(spirit, 'skills'):
                skills = spirit.skills
                total_skills = len(skills)
                
                for skill in skills:
                    if self._check_basic_skill_availability(spirit, skill, battle_state):
                        available_skills.append(skill)
                    else:
                        reason = self._get_basic_unavailability_reason(spirit, skill, battle_state)
                        unavailable_skills.append((skill, reason))
                
                logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 直接获得 {len(available_skills)}/{total_skills} 个可用技能")
            
            return SkillAvailabilityResult(
                available_skills=available_skills,
                unavailable_skills=unavailable_skills,
                total_skills=total_skills,
                metadata={
                    'source': 'components' if hasattr(spirit, 'components') else 'direct',
                    'spirit_name': getattr(spirit, 'name', 'Unknown')
                }
            )
            
        except Exception as e:
            logger.error(f"获取可用技能时出错: {e}")
            return SkillAvailabilityResult(
                available_skills=[],
                unavailable_skills=[],
                total_skills=0,
                metadata={'error': str(e)}
            )
    
    def _get_skill_component(self, spirit: 'IBattleEntity'):
        """获取技能组件"""
        try:
            from core.components import SkillComponent
            return spirit.components.get_component(SkillComponent)
        except Exception as e:
            logger.warning(f"获取技能组件失败: {e}")
            return None
    
    def _get_skills_from_component(self, skill_component) -> List['Skill']:
        """从技能组件获取技能列表"""
        try:
            # 尝试使用专门的方法
            if hasattr(skill_component, 'get_all_skills'):
                return skill_component.get_all_skills()
            elif hasattr(skill_component, 'skills'):
                return skill_component.skills
            else:
                return []
        except Exception as e:
            logger.warning(f"从技能组件获取技能失败: {e}")
            return []
    
    def _check_skill_availability(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState',
        skill_component
    ) -> bool:
        """检查技能是否可用（使用components系统）"""
        
        try:
            # 1. 使用SkillComponent的检查方法
            if hasattr(skill_component, 'can_use_skill'):
                return skill_component.can_use_skill(skill, battle_state)
            
            # 2. 使用SkillComponent的具体检查方法
            skill_name = self._get_skill_name(skill)
            
            # 检查冷却
            if hasattr(skill_component, 'is_skill_on_cooldown'):
                if skill_component.is_skill_on_cooldown(skill_name):
                    return False
            
            # 检查禁用状态
            if hasattr(skill_component, 'is_skill_disabled'):
                if skill_component.is_skill_disabled(skill_name):
                    return False
            
            # 检查能量消耗
            if not self._check_energy_cost(spirit, skill):
                return False
            
            # 检查技能特殊条件
            if not self._check_skill_conditions(spirit, skill, battle_state):
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"检查技能可用性时出错: {e}")
            return False
    
    def _check_basic_skill_availability(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> bool:
        """基础技能可用性检查"""
        
        try:
            # 检查能量消耗
            if not self._check_energy_cost(spirit, skill):
                return False
            
            # 检查技能条件
            if not self._check_skill_conditions(spirit, skill, battle_state):
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"基础技能检查时出错: {e}")
            return False
    
    def _check_energy_cost(self, spirit: 'IBattleEntity', skill: 'Skill') -> bool:
        """检查能量消耗"""
        try:
            if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'energy_cost'):
                energy_cost = skill.metadata.energy_cost or 0

                # 🔧 超杀技能适配：超杀技能不检查energy_cost，而是检查energy_threshold
                cast_type = getattr(skill.metadata, 'cast_type', '')
                if cast_type in ['ULTIMATE', 'TONGLING_ULTIMATE']:
                    # 超杀技能使用阈值检查
                    return self._check_ultimate_energy_threshold(spirit, skill)

                # 普通技能检查energy_cost
                if energy_cost > 0:
                    current_energy = getattr(spirit, 'current_energy', 0)
                    return current_energy >= energy_cost
            return True
        except:
            return True

    def _check_ultimate_energy_threshold(self, spirit: 'IBattleEntity', skill: 'Skill') -> bool:
        """检查超杀技能的气势阈值"""
        try:
            # 方法1: 从超杀管理器获取阈值
            if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                skill_name = getattr(skill.metadata, 'name', '') if hasattr(skill, 'metadata') else ''
                if hasattr(spirit.ultimate_manager, 'get_ultimate_skill'):
                    skill_config = spirit.ultimate_manager.get_ultimate_skill(skill_name)
                    if skill_config:
                        current_energy = getattr(spirit, 'current_energy', 0)
                        return current_energy >= skill_config.energy_threshold

                # 获取最低阈值
                if hasattr(spirit.ultimate_manager, 'get_lowest_threshold'):
                    threshold = spirit.ultimate_manager.get_lowest_threshold()
                    current_energy = getattr(spirit, 'current_energy', 0)
                    return current_energy >= threshold

            # 方法2: 使用默认阈值
            current_energy = getattr(spirit, 'current_energy', 0)
            return current_energy >= 300  # 默认超杀阈值
        except:
            return False
    
    def _check_skill_conditions(self, spirit: 'IBattleEntity', skill: 'Skill', battle_state: 'IBattleState') -> bool:
        """检查技能特殊条件"""
        try:
            # 检查技能是否有特殊使用条件
            if hasattr(skill, 'can_use') and callable(getattr(skill, 'can_use', None)):
                return skill.can_use(spirit, battle_state)
            return True
        except:
            return True
    
    def _get_skill_name(self, skill: 'Skill') -> str:
        """获取技能名称"""
        try:
            if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'name'):
                return skill.metadata.name
            return getattr(skill, 'name', '')
        except:
            return ''
    
    def _get_unavailability_reason(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState',
        skill_component
    ) -> str:
        """获取技能不可用的原因"""
        
        try:
            skill_name = self._get_skill_name(skill)
            
            # 检查冷却
            if hasattr(skill_component, 'is_skill_on_cooldown'):
                if skill_component.is_skill_on_cooldown(skill_name):
                    return "技能冷却中"
            
            # 检查禁用
            if hasattr(skill_component, 'is_skill_disabled'):
                if skill_component.is_skill_disabled(skill_name):
                    return "技能被禁用"
            
            # 检查能量
            if not self._check_energy_cost(spirit, skill):
                energy_cost = getattr(skill.metadata, 'energy_cost', 0) if hasattr(skill, 'metadata') else 0
                current_energy = getattr(spirit, 'current_energy', 0)
                return f"能量不足 ({current_energy}/{energy_cost})"
            
            # 检查条件
            if not self._check_skill_conditions(spirit, skill, battle_state):
                return "不满足使用条件"
            
            return "未知原因"
            
        except Exception as e:
            return f"检查失败: {e}"
    
    def _get_basic_unavailability_reason(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> str:
        """获取基础技能不可用原因"""
        
        try:
            # 检查能量
            if not self._check_energy_cost(spirit, skill):
                energy_cost = getattr(skill.metadata, 'energy_cost', 0) if hasattr(skill, 'metadata') else 0
                current_energy = getattr(spirit, 'current_energy', 0)
                return f"能量不足 ({current_energy}/{energy_cost})"
            
            # 检查条件
            if not self._check_skill_conditions(spirit, skill, battle_state):
                return "不满足使用条件"
            
            return "未知原因"
            
        except Exception as e:
            return f"检查失败: {e}"
    
    def calculate_skill_priority(self, spirit: 'IBattleEntity', skill: 'Skill', battle_state: 'IBattleState') -> float:
        """计算技能优先级（考虑components信息）"""
        
        score = 0.0
        
        try:
            # 基础分数
            score += 10.0
            
            # 技能类型加成
            if hasattr(skill, 'metadata'):
                cast_type = getattr(skill.metadata, 'cast_type', 'ACTIVE')
                if cast_type == 'ULTIMATE':
                    score += 50.0  # 超杀技能优先级最高
                elif cast_type == 'HERO':
                    score += 40.0  # 英雄技能次之
                elif cast_type == 'ACTIVE':
                    score += 20.0  # 主动技能
                
                # 能量消耗考虑
                energy_cost = getattr(skill.metadata, 'energy_cost', 0)
                if energy_cost > 0:
                    score += energy_cost * 0.1
            
            # 技能组件加成
            if hasattr(skill, 'components'):
                for component in skill.components:
                    # 检查伤害倍率
                    if hasattr(component, 'power_multiplier'):
                        score += getattr(component, 'power_multiplier', 1.0) * 5.0
                    
                    # 检查其他属性
                    if hasattr(component, 'priority_bonus'):
                        score += getattr(component, 'priority_bonus', 0.0)
            
            # 精灵状态影响
            if hasattr(spirit, 'current_hp') and hasattr(spirit, 'max_hp'):
                hp_percentage = spirit.current_hp / spirit.max_hp
                if hp_percentage <= 0.3:
                    # 低血量时优先使用高伤害技能
                    score += 15.0
            
        except Exception as e:
            logger.warning(f"计算技能优先级时出错: {e}")
        
        return score

# 全局实例
_components_skill_selector = ComponentsIntegratedSkillSelector()

def get_components_skill_selector() -> ComponentsIntegratedSkillSelector:
    """获取全局components技能选择器实例"""
    return _components_skill_selector
