"""
响应式效果管理器

整合属性监控、生命周期管理和更新调度，提供全监控的动态更新系统
"""
from __future__ import annotations
import time
import weakref
from typing import Dict, List, Any, Callable, Optional, Set, Union
from dataclasses import dataclass

from .attribute_watcher import AttributeWatcher, AttributeChange, AttributeCondition
from .lifecycle_manager import EffectLifecycleManager, EffectLifecycleEvent, EffectDependency
from .update_scheduler import DynamicUpdateScheduler, UpdateTask, UpdatePriority, UpdateType
from ..system import EffectManager, EffectResult
from ...logging import get_logger

logger = get_logger("core.effect.reactive.reactive_manager")


class ReactiveEffectManager(EffectManager):
    """响应式效果管理器
    
    扩展原有的效果管理器，添加响应式和动态更新功能
    """
    
    def __init__(self, owner: Any, scheduler: Optional[DynamicUpdateScheduler] = None, unified_event_manager=None):
        # 调用父类构造函数，传递 unified_event_manager
        super().__init__(owner, unified_event_manager)

        # 响应式组件
        self.attribute_watcher = AttributeWatcher(owner)
        self.lifecycle_manager = EffectLifecycleManager(owner)

        # 使用全局调度器或提供的调度器
        if scheduler is not None:
            self.scheduler = scheduler
        else:
            # 使用全局调度器
            from . import get_global_scheduler
            self.scheduler = get_global_scheduler()

        # 配置
        self.reactive_enabled = True
        self.auto_update_enabled = True
        self.batch_updates = True

        # 初始化响应式系统
        self._setup_reactive_system()
    
    def _setup_reactive_system(self):
        """设置响应式系统"""
        # 设置属性监听器
        self._setup_attribute_listeners()
        
        # 设置生命周期监听器
        self._setup_lifecycle_listeners()
        
        # 设置常用的条件触发器
        self._setup_common_triggers()
    
    def _setup_attribute_listeners(self):
        """设置属性监听器"""
        # 监听HP变化
        self.attribute_watcher.watch_attribute("hp", self._on_hp_changed)
        self.attribute_watcher.watch_attribute("max_hp", self._on_max_hp_changed)
        
        # 监听其他重要属性
        for attr in ["attack", "defense", "speed", "energy"]:
            self.attribute_watcher.watch_attribute(attr, self._on_attribute_changed)
    
    def _setup_lifecycle_listeners(self):
        """设置生命周期监听器"""
        # 监听效果应用
        self.lifecycle_manager.add_lifecycle_listener(
            EffectLifecycleEvent.APPLIED, 
            self._on_effect_applied
        )
        
        # 监听效果移除
        self.lifecycle_manager.add_lifecycle_listener(
            EffectLifecycleEvent.REMOVED, 
            self._on_effect_removed
        )
        
        # 监听效果过期
        self.lifecycle_manager.add_lifecycle_listener(
            EffectLifecycleEvent.EXPIRED, 
            self._on_effect_expired
        )
    
    def _setup_common_triggers(self):
        """设置常用的条件触发器"""
        from .attribute_watcher import hp_below_threshold, hp_above_threshold
        
        # HP低于50%时触发
        self.attribute_watcher.add_condition(
            hp_below_threshold(0.5),
            self._on_low_hp
        )
        
        # HP低于25%时触发
        self.attribute_watcher.add_condition(
            hp_below_threshold(0.25),
            self._on_critical_hp
        )
        
        # HP恢复到75%以上时触发
        self.attribute_watcher.add_condition(
            hp_above_threshold(0.75),
            self._on_hp_recovered
        )
    
    def add_effect(self, effect, battle_state=None) -> EffectResult:
        """添加效果（响应式版本）"""
        # 调用原有逻辑
        result = super().add_effect(effect, battle_state)
        
        if result.success and self.reactive_enabled:
            # 通知生命周期管理器
            self.lifecycle_manager.notify_event(
                effect.id, 
                effect.name, 
                EffectLifecycleEvent.APPLIED,
                battle_state=battle_state
            )
            
            # 调度立即更新
            if self.auto_update_enabled:
                self._schedule_effect_update(effect.id, UpdatePriority.HIGH)
        
        return result
    
    def remove_effect(self, effect_id: str, battle_state=None) -> EffectResult:
        """移除效果（响应式版本）"""
        # 获取效果信息
        effect = self.effects.get(effect_id)
        effect_name = effect.name if effect else "unknown"
        
        # 调用原有逻辑
        result = super().remove_effect(effect_id, battle_state)
        
        if result.success and self.reactive_enabled:
            # 通知生命周期管理器
            self.lifecycle_manager.notify_event(
                effect_id, 
                effect_name, 
                EffectLifecycleEvent.REMOVED,
                battle_state=battle_state
            )
        
        return result
    
    def update_effects(self, battle_state=None) -> List[EffectResult]:
        """更新效果（响应式版本）"""
        if not self.reactive_enabled:
            return super().update_effects(battle_state)
        
        # 使用调度器进行批量更新
        if self.batch_updates:
            return self._batch_update_effects(battle_state)
        else:
            return self._immediate_update_effects(battle_state)
    
    def _batch_update_effects(self, battle_state=None) -> List[EffectResult]:
        """批量更新效果"""
        results = []
        
        # 收集所有需要更新的效果
        update_callbacks = []
        for effect in self.effects.values():
            def create_update_callback(eff):
                def callback():
                    return self._update_single_effect(eff, battle_state)
                return callback
            
            update_callbacks.append(create_update_callback(effect))
        
        if update_callbacks:
            # 调度批量任务
            # 🔧 修复：owner不是callable，直接使用
            owner_id = getattr(self.owner, 'id', 'unknown') if self.owner else 'unknown'
            task_id = self.scheduler.schedule_batch(
                update_callbacks, 
                owner_id, 
                UpdatePriority.NORMAL,
                battle_state=battle_state
            )
            
            # 等待批量任务完成（简化版本，实际可能需要异步处理）
            # 这里暂时使用同步方式
            for callback in update_callbacks:
                try:
                    result = callback()
                    if result:
                        results.extend(result if isinstance(result, list) else [result])
                except Exception as e:
                    logger.error(f"批量更新效果失败: {e}")
        
        return results
    
    def _immediate_update_effects(self, battle_state=None) -> List[EffectResult]:
        """立即更新效果"""
        return super().update_effects(battle_state)
    
    def _update_single_effect(self, effect, battle_state=None) -> List[EffectResult]:
        """更新单个效果"""
        results = []
        
        try:
            # 🔧 优化：使用基于效果ID的持续时间管理
            current_round = getattr(battle_state, 'round_num', 0) if battle_state else 0

            # 更新持续时间（只有在不同回合时才递减）
            if effect.duration > 0:
                # 获取或初始化效果的回合信息
                round_info = self._effect_round_info.get(effect.id)
                if round_info is None:
                    # 如果没有记录，说明是旧效果，初始化为当前回合
                    round_info = {
                        "applied_round": current_round,
                        "last_update_round": -1
                    }
                    self._effect_round_info[effect.id] = round_info

                # 只有在不同回合时才递减持续时间
                if current_round > round_info["last_update_round"] and round_info["last_update_round"] != -1:
                    effect.remaining_duration -= 1
                    if effect.remaining_duration <= 0:
                        # 效果过期
                        self.lifecycle_manager.notify_event(
                            effect.id,
                            effect.name,
                            EffectLifecycleEvent.EXPIRED,
                            battle_state=battle_state
                        )
                        remove_result = self.remove_effect(effect.id, battle_state)
                        results.append(remove_result)
                        return results

                # 更新上次更新回合
                round_info["last_update_round"] = current_round
            
            # 触发更新逻辑
            result = effect.on_update(self.owner(), battle_state)
            if result:
                results.append(result)
            
            # 通知生命周期管理器
            self.lifecycle_manager.notify_event(
                effect.id, 
                effect.name, 
                EffectLifecycleEvent.UPDATED,
                battle_state=battle_state
            )
            
        except Exception as e:
            error_result = EffectResult.error(f"更新效果 {effect.name} 失败: {str(e)}", exc=e)
            results.append(error_result)
        
        return results
    
    def _schedule_effect_update(self, effect_id: str, priority: UpdatePriority = UpdatePriority.NORMAL):
        """调度效果更新"""
        def update_callback():
            effect = self.effects.get(effect_id)
            if effect:
                return self._update_single_effect(effect)
            return None
        
        # 🔧 修复：owner不是callable，直接使用
        owner_id = getattr(self.owner, 'id', 'unknown') if self.owner else 'unknown'
        task_id = f"effect_update_{effect_id}_{int(time.time() * 1000000)}"
        
        task = UpdateTask(
            task_id=task_id,
            update_type=UpdateType.EFFECT_UPDATE,
            priority=priority,
            callback=update_callback,
            target_id=owner_id,
            metadata={"effect_id": effect_id}
        )
        
        self.scheduler.schedule_task(task)
    
    def notify_attribute_change(self, attribute_name: str, old_value: Any, new_value: Any, source: Any = None):
        """通知属性变化"""
        if self.reactive_enabled:
            self.attribute_watcher.notify_change(
                attribute_name, old_value, new_value, source=source
            )
    
    def add_effect_dependency(self, effect_id: str, depends_on: List[str], dependency_type: str = "requires"):
        """添加效果依赖关系"""
        dependency = EffectDependency(effect_id, depends_on, dependency_type)
        self.lifecycle_manager.add_dependency(dependency)

    def get_effects(self) -> List[Any]:
        """获取所有效果列表（向后兼容方法）"""
        return list(self.effects.values())
    
    # 事件处理器
    def _on_hp_changed(self, change: AttributeChange):
        """HP变化处理器"""
        logger.debug(f"HP变化: {change.old_value} -> {change.new_value}")
        
        # 触发相关效果的更新
        for effect in self.effects.values():
            if hasattr(effect, 'on_hp_changed'):
                self._schedule_effect_update(effect.id, UpdatePriority.HIGH)
    
    def _on_max_hp_changed(self, change: AttributeChange):
        """最大HP变化处理器"""
        logger.debug(f"最大HP变化: {change.old_value} -> {change.new_value}")
        
        # 可能需要重新计算HP百分比相关的效果
        for effect in self.effects.values():
            if hasattr(effect, 'on_max_hp_changed'):
                self._schedule_effect_update(effect.id, UpdatePriority.NORMAL)
    
    def _on_attribute_changed(self, change: AttributeChange):
        """通用属性变化处理器"""
        logger.debug(f"属性变化 {change.attribute_name}: {change.old_value} -> {change.new_value}")
        
        # 触发相关效果的更新
        for effect in self.effects.values():
            if hasattr(effect, f'on_{change.attribute_name}_changed'):
                self._schedule_effect_update(effect.id, UpdatePriority.NORMAL)
    
    def _on_low_hp(self, change: AttributeChange):
        """低血量触发器"""
        logger.info(f"触发低血量状态: HP = {change.new_value}")
        
        # 可以在这里触发特殊效果，如绝境反击等
        # 🔧 修复：owner不是callable，直接使用
        owner = self.owner
        if owner and hasattr(owner, 'on_low_hp'):
            try:
                owner.on_low_hp(change)
            except Exception as e:
                logger.error(f"处理低血量状态失败: {e}")
    
    def _on_critical_hp(self, change: AttributeChange):
        """危险血量触发器"""
        logger.warning(f"触发危险血量状态: HP = {change.new_value}")
        
        # 可以在这里触发紧急效果
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'on_critical_hp'):
            try:
                owner.on_critical_hp(change)
            except Exception as e:
                logger.error(f"处理危险血量状态失败: {e}")
    
    def _on_hp_recovered(self, change: AttributeChange):
        """血量恢复触发器"""
        logger.info(f"血量恢复: HP = {change.new_value}")
        
        # 可以在这里移除低血量相关的效果
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'on_hp_recovered'):
            try:
                owner.on_hp_recovered(change)
            except Exception as e:
                logger.error(f"处理血量恢复失败: {e}")
    
    def _on_effect_applied(self, record):
        """效果应用处理器"""
        logger.debug(f"效果已应用: {record.effect_name}")
    
    def _on_effect_removed(self, record):
        """效果移除处理器"""
        logger.debug(f"效果已移除: {record.effect_name}")
    
    def _on_effect_expired(self, record):
        """效果过期处理器"""
        logger.debug(f"效果已过期: {record.effect_name}")
    
    def get_reactive_stats(self) -> Dict[str, Any]:
        """获取响应式系统统计信息"""
        return {
            "attribute_watcher": self.attribute_watcher.get_stats(),
            "lifecycle_manager": self.lifecycle_manager.get_stats(),
            "scheduler": self.scheduler.get_stats(),
            "reactive_enabled": self.reactive_enabled,
            "auto_update_enabled": self.auto_update_enabled,
            "batch_updates": self.batch_updates
        }
    
    def cleanup(self):
        """清理资源"""
        if self.scheduler.running:
            self.scheduler.stop()
        
        self.attribute_watcher.clear_history()
        self.lifecycle_manager.clear_history()
        
        logger.info("响应式效果管理器已清理")
