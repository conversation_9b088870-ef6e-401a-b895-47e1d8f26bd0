#!/usr/bin/env python3
"""
测试回合统计修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_round_statistics():
    """测试回合统计功能"""
    print("🔧 测试回合统计功能...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 设置高气势以便测试超杀
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=3,
            turn_order_bonus_energy=0
        )
        
        print(f"✅ 战斗引擎创建成功")
        print(f"  精灵1: {spirit1.name} (气势: {spirit1.energy})")
        print(f"  精灵2: {spirit2.name} (气势: {spirit2.energy})")
        
        # 创建战斗记录器
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        
        # 确保战斗状态能访问到引擎的统计数据
        if not hasattr(engine.battle_state, 'engine'):
            engine.battle_state.engine = engine
        
        # 创建初始快照
        print(f"\n📊 创建初始快照...")
        initial_snapshot = recorder.create_snapshot(engine.battle_state, 0)
        
        print(f"  初始统计:")
        for team in [0, 1]:
            damage = initial_snapshot.total_damage_dealt.get(team, 0)
            healing = initial_snapshot.total_healing_done.get(team, 0)
            print(f"    队伍{team}: 伤害={damage:.0f}, 治疗={healing:.0f}")
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        # 检查统计跟踪器数据
        print(f"\n📈 检查统计跟踪器数据...")
        if hasattr(engine, 'stats_tracker') and engine.stats_tracker:
            stats_data = engine.stats_tracker.data
            print(f"  统计跟踪器中的数据:")
            for spirit_id, data in stats_data.items():
                damage_dealt = data.get('total_damage_dealt', 0)
                damage_taken = data.get('total_damage_taken', 0)
                actions = data.get('actions', 0)
                print(f"    {spirit_id}: 造成伤害={damage_dealt:.0f}, 承受伤害={damage_taken:.0f}, 行动={actions}")
        else:
            print(f"  ❌ 没有找到统计跟踪器")
        
        # 创建回合快照
        print(f"\n📊 创建回合1快照...")
        round1_snapshot = recorder.create_snapshot(engine.battle_state, 1)
        
        print(f"  回合1统计:")
        for team in [0, 1]:
            damage = round1_snapshot.total_damage_dealt.get(team, 0)
            healing = round1_snapshot.total_healing_done.get(team, 0)
            print(f"    队伍{team}: 伤害={damage:.0f}, 治疗={healing:.0f}")
        
        # 检查动作记录
        print(f"\n📋 检查动作记录:")
        print(f"  动作数量: {len(round1_snapshot.actions_performed)}")
        for i, action in enumerate(round1_snapshot.actions_performed):
            print(f"    动作{i}: {action.caster_name} - {action.description}")
            print(f"      伤害: {action.damage_dealt}")
            print(f"      治疗: {action.healing_done}")
        
        # 检查精灵状态变化
        print(f"\n🔍 检查精灵状态变化:")
        for name, spirit_snapshot in round1_snapshot.spirits.items():
            print(f"  {name}:")
            print(f"    HP: {spirit_snapshot.current_hp:.0f}/{spirit_snapshot.max_hp:.0f}")
            print(f"    气势: {spirit_snapshot.energy}/{spirit_snapshot.max_energy}")
        
        # 验证统计是否正确
        total_damage_team0 = round1_snapshot.total_damage_dealt.get(0, 0)
        total_damage_team1 = round1_snapshot.total_damage_dealt.get(1, 0)
        
        if total_damage_team0 > 0 or total_damage_team1 > 0:
            print(f"\n✅ 回合统计修复成功！")
            print(f"  队伍0总伤害: {total_damage_team0:.0f}")
            print(f"  队伍1总伤害: {total_damage_team1:.0f}")
            return True
        else:
            print(f"\n❌ 回合统计仍然为0")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 回合统计修复测试")
    print("="*60)
    
    result = test_round_statistics()
    
    print("\n" + "="*60)
    print("📊 测试结果:")
    print("="*60)
    
    if result:
        print("✅ 回合统计修复成功")
        print("\n📋 修复内容:")
        print("  1. ✅ 从战斗引擎的统计跟踪器获取实际伤害数据")
        print("  2. ✅ 修复了ActionRecord中的damage_dealt字段")
        print("  3. ✅ 确保UI能正确显示回合统计信息")
        print("\n🚀 现在可以运行UI验证修复效果：")
        print("  python ui/ux/enhanced_battle_ui.py")
    else:
        print("❌ 回合统计修复失败，需要进一步调试")

if __name__ == "__main__":
    main()
