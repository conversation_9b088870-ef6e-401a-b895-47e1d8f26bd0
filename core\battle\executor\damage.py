"""Damage and related action handlers registered to the ActionExecutor registry."""
from __future__ import annotations

from typing import List, Optional, Any, cast, TYPE_CHECKING

from .core import handler, ActionExecutor
from .decorators import validate_target, safe_execute, monitor_performance
from ...action import (
    BattleAction,
    DamageAction,
    UpdateDamageAction,
    DieAction,
    LogAction,
    DispatchEventAction,
)
from ...battle import formula_damage_calculator
from ...interfaces import IBattleEntity
from ...spirit.refactored_spirit import RefactoredSpirit

if TYPE_CHECKING:
    from ...event.events import (
        DeclareAttackEvent,
        ModifyAttackEvent,
        BeforeAttackEvent,
        AfterDamageEvent,
        AfterAttackEvent,
        ImmunityEvent,
    )
    from ...effect.effects import BlockedAttackEffectPlaceholder

@handler(DamageAction)
@validate_target(alive_required=True, spirit_type_required=True)
@safe_execute(log_errors=True)  # 🔧 优化: 增强错误处理
@monitor_performance(slow_threshold=0.15)  # 🔧 优化: 降低慢操作阈值
def _handle_damage(
    self: ActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """
    处理伤害动作，计算并应用伤害。

    重构后专注于伤害处理的核心流程：
    1. 攻击声明阶段 (DeclareAttackEvent) - 声明攻击意图，可被取消
    2. 攻击属性修改阶段 (ModifyAttackEvent) - 修改攻击属性
    3. 伤害计算阶段 - 计算最终伤害值
    4. 攻击前阶段 (BeforeAttackEvent) - 免疫检查，可阻止攻击
    5. 伤害应用前阶段 (BeforeDamageAppliedEvent) - 最后的伤害修改机会
    6. 伤害应用阶段 - 实际扣除生命值
    7. 伤害后阶段 (AfterDamageEvent) - 伤害应用后的响应
    8. 攻击后阶段 (AfterAttackEvent) - 攻击完成后的响应
    9. 死亡检查阶段 - 检查目标是否死亡
    10. 返回结果阶段 - 返回所有生成的后续动作

    注意：不再处理行动决策相关的逻辑，专注于伤害计算和应用。
    """
    # 使用cast将action转换为DamageAction
    damage_action = cast(DamageAction, action)
    target = damage_action.target
    caster = damage_action.caster

    # 类型和状态检查
    if not isinstance(target, RefactoredSpirit) or not target.is_alive:
        return None
    if not isinstance(caster, RefactoredSpirit):
        return None

    # 类型转换为RefactoredSpirit（已检查过类型）
    caster_spirit = cast(RefactoredSpirit, caster)
    target_spirit = cast(RefactoredSpirit, target)

    # 以下是原始代码的流程，我们保持不变
    # 1. 攻击声明阶段
    from ...event.events import DeclareAttackEvent
    declare_event = DeclareAttackEvent(
        attacker=caster_spirit,
        target=target_spirit,
        skill_name=getattr(damage_action, "skill_name", None),
        attack_cancelled=False,
        can_be_countered=True
    )

    # 🎯 使用直接dispatch而不是DispatchEventAction的原因：
    # 1. 需要立即检查 declare_event.attack_cancelled 的结果
    # 2. 攻击声明必须同步处理，不能延迟
    # 3. 避免动作包装可能导致的事件修改延迟问题
    try:
        from ...event.unified_manager import unified_event_manager
        # 直接分发事件，立即获取结果
        event_actions = unified_event_manager.dispatch(declare_event, self.battle_state)
        # 执行事件产生的动作
        if event_actions:
            self.execute_actions(event_actions)
    except (ImportError, AttributeError):
        # 降级到DispatchEventAction方式（但可能有延迟问题）
        self.execute_actions([DispatchEventAction(caster=caster_spirit, event=declare_event)])

    # 检查攻击是否被取消
    if declare_event.attack_cancelled:
        return None

    # 2. 攻击属性修改阶段
    from ...event.events import ModifyAttackEvent
    modify_event = ModifyAttackEvent(
        attacker=caster_spirit,
        target=target_spirit,
        skill_name=getattr(damage_action, "skill_name", None),
    )
    self.execute_actions([DispatchEventAction(caster=caster, event=modify_event)])

    # 3. 伤害计算阶段
    # 应用修改事件中的属性到伤害动作
    if getattr(modify_event, 'damage_type_override', None):
        # 如果有伤害类型覆盖，应用它
        from ...action import DamageType
        damage_type_map = {
            "PHYSICAL": DamageType.PHYSICAL,
            "MAGIC": DamageType.MAGIC,
            "DESTRUCTION": DamageType.DESTRUCTION,
            "FIRE": DamageType.FIRE,
            "ICE": DamageType.ICE,
            "LIGHTNING": DamageType.LIGHTNING,
            "POISON": DamageType.POISON,
            "BURN": DamageType.BURN,
            "BLEED": DamageType.BLEED,
            "CURSE": DamageType.CURSE,
            "HOLY": DamageType.HOLY,
            "DARK": DamageType.DARK,
            "NATURE": DamageType.NATURE,
            "PSYCHIC": DamageType.PSYCHIC,
            "SONIC": DamageType.SONIC,
            "INDIRECT": DamageType.INDIRECT,
        }
        if modify_event.damage_type_override in damage_type_map:
            damage_action.damage_type = damage_type_map[modify_event.damage_type_override]
            # 如果是非直接伤害类型，标记为间接伤害
            if DamageType.is_indirect_damage_type(damage_action.damage_type):
                damage_action.is_indirect = True
    if damage_action.damage_value is None:
        # 根据是否为非直接伤害选择不同的计算函数
        if getattr(damage_action, 'is_indirect', False):
            final_damage, breakdown = formula_damage_calculator.calculate_indirect_damage(
                caster=caster_spirit,
                target=target_spirit,
                action=damage_action,
                battle_state=self.battle_state
            )
        else:
            final_damage, breakdown = formula_damage_calculator.calculate_formula_damage(
                caster=caster_spirit,
                target=target_spirit,
                action=damage_action,
                battle_state=self.battle_state
            )
        damage_action.damage_value = final_damage
        damage_action.damage_breakdown = breakdown
        damage_action.is_critical = breakdown.get("is_crit", False) or getattr(modify_event, 'is_guaranteed_crit', False)
    
    # 4. 攻击前阶段（免疫检查）
    from ...event.events import BeforeAttackEvent
    attack_event = BeforeAttackEvent(
        attacker=caster_spirit,  # 修复：使用正确的参数名
        target=target_spirit,
        skill_name=getattr(damage_action, "skill_name", None)
        
    )
    self.execute_actions([DispatchEventAction(caster=caster, event=attack_event)])
    
    # 检查攻击是否被免疫
    if attack_event.attack_blocked:
        # 如果有source_effect（提供免疫的效果）
        if attack_event.source_effect:
            # 使用新的ImmunityEvent
            from ...event.events import ImmunityEvent

            # 创建并派发免疫事件
            immunity_event = ImmunityEvent(
                target=target,
                source_effect_id=getattr(attack_event.source_effect, 'id', 'unknown'),
                immunity_type="attack",
                blocked_action="attack"
            )

            # 返回派发事件的动作
            return [DispatchEventAction(caster=target, event=immunity_event)]
        return None
    
    # 5. 伤害应用前阶段 (BeforeDamageAppliedEvent)
    from ...event.events import BeforeDamageAppliedEvent, DamageModificationContext

    # 创建伤害修改上下文
    damage_context = DamageModificationContext(
        damage=float(damage_action.damage_value or 0),
        is_cancelled=False
    )

    before_damage_event = BeforeDamageAppliedEvent(
        caster=caster_spirit,
        target=target_spirit,
        action=damage_action,
        context=damage_context
    )
    self.execute_actions([DispatchEventAction(caster=caster, event=before_damage_event)])

    # 检查伤害是否被取消
    if damage_context.is_cancelled:
        return None

    # 使用修改后的伤害值
    final_damage_value = damage_context.damage

    # 6. 伤害应用阶段
    original_damage = final_damage_value

    # 检查是否为毁灭伤害
    from ...action import DamageType
    is_destruction = (hasattr(damage_action, 'damage_type') and
                     isinstance(damage_action.damage_type, DamageType) and
                     damage_action.damage_type == DamageType.DESTRUCTION)

    actual_damage = target_spirit.take_damage(original_damage, is_destruction=is_destruction)

    # 更新damage_action中的伤害值为实际造成的伤害
    damage_action.damage_value = int(actual_damage)
    
    # 7. 伤害后阶段 (AfterDamageEvent)
    actions_after_damage: List[BattleAction] = []
    target_died = not target_spirit.is_alive

    from ...event.events import AfterDamageEvent, AfterAttackEvent
    after_damage_event = AfterDamageEvent(
        attacker=caster_spirit,
        target=target_spirit,
        damage_dealt=actual_damage,
        original_damage=original_damage,
        is_critical=damage_action.is_critical,
        skill_name=getattr(damage_action, "skill_name", None),
        target_died=target_died,
        damage_action=damage_action  # 添加伤害动作引用，用于受击加气判断
    )
    actions_after_damage.append(DispatchEventAction(caster=caster, event=after_damage_event))

    # 应用受击加气奖励
    from ..hit_energy_bonus import get_global_hit_energy_handler
    hit_energy_handler = get_global_hit_energy_handler()
    hit_energy_handler.handle_after_damage_event(after_damage_event)

    # 🆕 获取动态死亡系统生成的动作
    from ...death import get_pending_death_actions
    death_actions = get_pending_death_actions()
    if death_actions:
        actions_after_damage.extend(death_actions)

    # 8. 攻击后阶段 (AfterAttackEvent)
    after_attack_event = AfterAttackEvent(
        attacker=caster_spirit,
        target=target_spirit,
        attack_successful=actual_damage > 0,
        damage_dealt=actual_damage,
        effects_applied=[],
        target_died=target_died,
        skill_name=getattr(damage_action, "skill_name", None)
    )
    actions_after_damage.append(DispatchEventAction(caster=caster, event=after_attack_event))

    # 9. 死亡检查阶段 - 现在由动态死亡系统自动处理
    # 不再需要手动检查死亡，动态系统已经在HP变化时自动处理了

    # 10. 返回结果阶段
    # 返回所有生成的后续动作
    
    return actions_after_damage or None


@handler(UpdateDamageAction)
@safe_execute(log_errors=True)
def _handle_update_damage(
    self: ActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """更新原始伤害动作的伤害值。

    注意：此函数通过@handler装饰器自动注册到ActionExecutor，
    因此IDE可能显示"未使用"警告，但实际上是被动态调用的。
    """
    update_action = cast(UpdateDamageAction, action)
    update_action.original_action.damage_value = update_action.new_damage
    return None


# 🔧 优化: 伤害计算缓存系统
_damage_cache = {}
_cache_max_size = 1000

def _generate_damage_cache_key(caster, target, action) -> str:
    """生成伤害计算的缓存键"""
    try:
        # 基于关键属性生成缓存键
        key_parts = [
            f"c_{caster.id}_{caster.attributes.attack}_{caster.attributes.crit_rate}",
            f"t_{target.id}_{target.attributes.defense}_{target.current_hp}",
            f"a_{getattr(action, 'power_multiplier', 1.0)}_{getattr(action, 'damage_type', 'PHYSICAL')}",
            f"f_{getattr(action, 'is_critical', False)}_{getattr(action, 'is_ultimate', False)}"
        ]
        return "_".join(key_parts)
    except Exception:
        # 如果生成失败，返回唯一键（不缓存）
        return f"nocache_{id(action)}"

def _get_cached_damage_result(cache_key: str):
    """获取缓存的伤害计算结果"""
    return _damage_cache.get(cache_key)

def _cache_damage_result(cache_key: str, result):
    """缓存伤害计算结果"""
    global _damage_cache

    # 如果缓存过大，清理一半
    if len(_damage_cache) >= _cache_max_size:
        # 简单的LRU策略：删除前一半
        keys_to_remove = list(_damage_cache.keys())[:_cache_max_size // 2]
        for key in keys_to_remove:
            del _damage_cache[key]

    _damage_cache[cache_key] = result

def clear_damage_cache():
    """清理伤害计算缓存"""
    global _damage_cache
    _damage_cache.clear()

def get_damage_cache_stats():
    """获取伤害计算缓存统计信息"""
    return {
        'cache_size': len(_damage_cache),
        'max_size': _cache_max_size,
        'usage_rate': len(_damage_cache) / _cache_max_size if _cache_max_size > 0 else 0
    }