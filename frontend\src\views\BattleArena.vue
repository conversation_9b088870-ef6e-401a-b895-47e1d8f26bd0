<template>
  <div class="battle-arena h-full flex flex-col bg-gradient-to-b from-slate-800 to-slate-900 overflow-y-auto">
    <!-- Battle controls, etc. -->
    <div class="battle-controls bg-slate-800/50 backdrop-blur-sm border-b border-purple-500/20 p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div v-if="battleStore.battleWinner !== null" class="text-white">
            <span class="text-sm text-slate-400">胜利者:</span>
            <span class="text-lg font-bold ml-2 text-yellow-400">阵营 {{ battleStore.battleWinner === 0 ? 1 : 2 }}</span>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <el-button
            v-if="!battleStore.isInBattle"
            type="primary"
            size="large"
            @click="runBattle"
            :disabled="!canStartBattle || battleStore.isInBattle"
            class="bg-gradient-to-r from-purple-600 to-pink-600 border-none"
          >
            <el-icon class="mr-2"><VideoPlay /></el-icon>
            {{ battleStore.isInBattle ? '战斗中...' : '开始战斗' }}
          </el-button>
          <el-button v-else type="danger" @click="resetBattle">
            <el-icon class="mr-2"><RefreshRight /></el-icon>
            重置
          </el-button>
        </div>
        <div class="flex items-center space-x-3">
          <el-button circle @click="showSettings = true">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <SpiritQuickActions
      :team1="battleStore.team1"
      :team2="battleStore.team2"
      @team-updated="onTeamUpdated"
      @spirit-added="onSpiritAdded"
      class="mx-4"
    />

    <div class="flex-1 grid grid-cols-[320px_1fr_320px] grid-rows-[1fr_288px] gap-4 p-4 min-h-0">
      <BattleGrid
        :team="1"
        :spirits="battleStore.team1"
        :team-name="'阵营 1'"
        :team-alive="battleStore.team1Alive.length"
        :team-total="battleStore.team1.length"
        @spirit-select="onSpiritSelect"
        @position-click="onPositionClick"
        @spirit-move="onSpiritMove"
        @spirit-remove="onSpiritRemove"
        class="bg-blue-900/30 rounded-lg p-4 team-1"
      />

      <div class="bg-slate-800/50 rounded-lg p-4 flex flex-col">
        <div class="text-center mb-4">
          <el-icon class="text-3xl text-purple-400 mb-1"><Trophy /></el-icon>
          <h3 class="text-lg font-bold text-white mb-1">战斗竞技场</h3>
          <p class="text-slate-400 text-xs">
            {{ battleStore.isInBattle ? '战斗进行中...' : '准备开始战斗' }}
          </p>
          <div v-if="battleStore.battleWinner !== null" class="text-yellow-400 text-sm font-bold mt-1">
            🎉 阵营 {{ battleStore.battleWinner === 0 ? 1 : 2 }} 获胜！
          </div>
        </div>
        <div class="flex-1 grid grid-cols-2 gap-2">
            <!-- Simplified stats - can be expanded later -->
        </div>
      </div>

      <BattleGrid
        :team="2"
        :spirits="battleStore.team2"
        :team-name="'阵营 2'"
        :team-alive="battleStore.team2Alive.length"
        :team-total="battleStore.team2.length"
        @spirit-select="onSpiritSelect"
        @position-click="onPositionClick"
        @spirit-move="onSpiritMove"
        @spirit-remove="onSpiritRemove"
        class="bg-red-900/30 rounded-lg p-4 team-2"
      />

      <!-- 底部：战斗日志 (跨3列) -->
      <BattleLog 
        :log-entries="battleStore.battleLog"
        :max-entries="2000"
        @clear-log="onClearLog"
        @export-log="onExportLog"
        class="col-span-3 bg-slate-800/50 rounded-lg p-4 overflow-y-auto" 
      />
    </div>

    <SpiritSelector
      ref="spiritSelector"
      @select="onSpiritSelected"
    />

    <BattleSettings
      v-model="showSettings"
      @settings-changed="onSettingsChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBattleStore } from '../stores/battle'
import { ElMessage } from 'element-plus'
import type { Spirit, LogEntry } from '../types/battle';
import type { SpiritPrototype } from '../types/spirit';

// Components
import BattleGrid from '../components/battle/BattleGrid.vue'
import BattleLog from '../components/battle/BattleLog.vue'
import BattleSettings from '../components/battle/BattleSettings.vue'
import SpiritSelector from '../components/spirit/SpiritSelector.vue'
import SpiritQuickActions from '../components/spirit/SpiritQuickActions.vue'

const battleStore = useBattleStore()

const showSettings = ref(false)
const selectedPosition = ref<[number, number] | null>(null)
const selectedTeam = ref<1 | 2>(1)
const selectedSpiritForAction = ref<Spirit | null>(null)
const spiritSelector = ref<InstanceType<typeof SpiritSelector> | null>(null);

/**
 * Converts a SpiritPrototype to a simplified Spirit object for the battle store.
 * @param prototype The spirit prototype.
 * @returns A Spirit object.
 */
const convertPrototypeToSpirit = (prototype: SpiritPrototype): Spirit => {
  return {
    id: prototype.name_prefix, // Use name as a temp ID
    name: prototype.name_prefix,
    position: [0, 0], // This will be set when added to the grid
    team: selectedTeam.value,
    isAlive: true,
    attributes: {
      hp: prototype.attributes.base_hp,
      maxHp: prototype.attributes.base_hp,
      attack: prototype.attributes.base_attack,
      defense: prototype.attributes.base_pdef, // Or a mix
      speed: prototype.attributes.speed,
      energy: 0,
      maxEnergy: 150, // Default value
    },
    skills: prototype.skills.map(s => s.name), // Just use names for now
    effects: [],
    level: prototype.shenge_level,
    element: prototype.element,
    professions: prototype.professions,
    shengeLevel: prototype.shenge_level,
    contractIds: [],
    tags: [], // Add tags if available in prototype
  };
};


const canStartBattle = computed(() => 
  battleStore.team1.length > 0 && battleStore.team2.length > 0
)

const runBattle = async () => {
  try {
    await battleStore.runBattle()
    ElMessage.success('战斗模拟完成！')
  } catch (error) {
    const message = error instanceof Error ? error.message : '开始战斗失败'
    ElMessage.error(message)
  }
}

const resetBattle = () => {
  battleStore.resetBattle()
  ElMessage.info('战斗已重置，请重新配置队伍。')
}

onMounted(() => {
  // We don't need to fetch all spirits here anymore, selector does it.
  // battleStore.fetchAvailableSpirits() 
})

// Event Handlers
const onSpiritSelect = (payload: { spirit: Spirit; team: 1 | 2 }) => {
  selectedSpiritForAction.value = payload.spirit
};

const onPositionClick = (payload: { team: 1 | 2, position: [number, number] }) => {
  selectedTeam.value = payload.team;
  selectedPosition.value = payload.position;
  spiritSelector.value?.open();
};

const onSpiritSelected = (spiritPrototype: SpiritPrototype) => {
  if (selectedPosition.value) {
    const spirit = convertPrototypeToSpirit(spiritPrototype);
    battleStore.addSpiritToTeam(spirit, selectedTeam.value, selectedPosition.value);
  }
};

const onSpiritMove = (payload: { spirit: Spirit, newPosition: [number, number] }) => {
  try {
    battleStore.moveSpiritToPosition(payload.spirit, payload.newPosition);
  } catch (e: any) {
    ElMessage.error(e.message);
  }
};

const onSpiritRemove = (payload: { spirit: Spirit; team: 1 | 2 }) => {
  battleStore.removeSpiritFromTeam(payload.spirit.id, payload.team);
};

const onTeamUpdated = (payload: { team: 1 | 2, spirits: Spirit[] }) => {
  if (payload.team === 1) battleStore.team1 = payload.spirits;
  else battleStore.team2 = payload.spirits;
};

const onSpiritAdded = (payload: { team: 1 | 2; spiritPrototype: SpiritPrototype; position?: [number, number] }) => {
    const { team, spiritPrototype, position } = payload;
    const spirit = convertPrototypeToSpirit(spiritPrototype);
    
    if (position) {
        battleStore.addSpiritToTeam(spirit, team, position);
    } else {
        const targetTeam = team === 1 ? battleStore.team1 : battleStore.team2;
        const grid: [number, number][] = Array.from({ length: 3 }, (_, r) => 
            Array.from({ length: 3 }, (_, c) => [r, c])
        ).flat() as [number, number][];
        
        const occupiedPositions = new Set(targetTeam.map(s => s.position.join(',')));
        const availablePosition = grid.find(p => !occupiedPositions.has(p.join(',')));
        
        if (availablePosition) {
            battleStore.addSpiritToTeam(spirit, team, availablePosition);
        } else {
            ElMessage.warning('队伍已满，无法添加新的精灵');
        }
    }
}


const onSettingsChanged = (settings: any) => {
    // apply settings
}

const onClearLog = () => {
  battleStore.battleLog = []
  ElMessage.success('战斗日志已清空')
}

const onExportLog = (logEntries: LogEntry[]) => {
  const data = JSON.stringify(logEntries, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `battle-log-${Date.now()}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  ElMessage.success('日志已导出');
}
</script>

<style scoped>
.team-1 { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
.team-2 { box-shadow: 0 0 20px rgba(239, 68, 68, 0.4); }
</style>