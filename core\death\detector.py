"""
动态死亡检测器

负责检测精灵的死亡状态变化并触发相应的处理。
"""
from __future__ import annotations
from typing import TYPE_CHECKING, List, Callable

if TYPE_CHECKING:
    from ..interfaces import IBattleEntity

from .types import DeathTriggerMode, DeathReason
from ..logging import battle_logger


class DynamicDeathDetector:
    """动态死亡检测器"""
    
    def __init__(self, trigger_mode: DeathTriggerMode = DeathTriggerMode.IMMEDIATE):
        """
        初始化动态死亡检测器
        
        Args:
            trigger_mode: 死亡触发模式
        """
        self.trigger_mode = trigger_mode
        self.death_callbacks: List[Callable] = []
        self.pending_deaths: List[tuple] = []  # (entity, reason) 待处理的死亡
        self.death_history: List[str] = []  # 死亡历史记录
        
    def register_death_callback(self, callback: Callable):
        """
        注册死亡回调函数
        
        Args:
            callback: 死亡回调函数，接收 (entity, reason) 参数
        """
        self.death_callbacks.append(callback)
        battle_logger.debug(f"注册死亡回调: {callback.__name__}")
        
    def check_and_trigger_death(
        self, 
        entity: 'IBattleEntity', 
        old_hp: float, 
        new_hp: float,
        reason: DeathReason = DeathReason.DAMAGE
    ) -> bool:
        """
        检查并触发死亡
        
        Args:
            entity: 检查的实体
            old_hp: 变化前的HP
            new_hp: 变化后的HP
            reason: 死亡原因
            
        Returns:
            是否触发了死亡
        """
        # 检查是否从存活变为死亡
        was_alive = old_hp > 0
        is_alive = new_hp > 0
        
        if was_alive and not is_alive:
            battle_logger.info(
                f"🔥 动态死亡检测：{entity.name} 死亡 "
                f"(HP: {old_hp:.1f} -> {new_hp:.1f}, 原因: {reason.value})"
            )
            return self._trigger_death(entity, reason)
        
        return False
    
    def _trigger_death(self, entity: 'IBattleEntity', reason: DeathReason) -> bool:
        """触发死亡处理"""
        if self.trigger_mode == DeathTriggerMode.IMMEDIATE:
            return self._process_death_immediately(entity, reason)
        elif self.trigger_mode == DeathTriggerMode.DEFERRED:
            return self._defer_death(entity, reason)
        elif self.trigger_mode == DeathTriggerMode.BATCH:
            return self._batch_death(entity, reason)
        
        return False
    
    def _process_death_immediately(self, entity: 'IBattleEntity', reason: DeathReason) -> bool:
        """立即处理死亡"""
        try:
            # 调用所有死亡回调
            for callback in self.death_callbacks:
                callback(entity, reason)
            
            # 记录死亡
            self.death_history.append(f"{entity.name} 立即死亡 ({reason.value})")
            battle_logger.debug(f"立即处理死亡: {entity.name}")
            return True
            
        except Exception as e:
            battle_logger.error(f"立即死亡处理失败: {e}")
            return False
    
    def _defer_death(self, entity: 'IBattleEntity', reason: DeathReason) -> bool:
        """延迟死亡处理"""
        # 检查是否已经在待处理队列中
        for pending_entity, _ in self.pending_deaths:
            if pending_entity.id == entity.id:
                battle_logger.debug(f"{entity.name} 已在延迟死亡队列中")
                return True
        
        self.pending_deaths.append((entity, reason))
        battle_logger.debug(f"延迟死亡：{entity.name} 加入待处理队列 ({reason.value})")
        return True
    
    def _batch_death(self, entity: 'IBattleEntity', reason: DeathReason) -> bool:
        """批量死亡处理"""
        return self._defer_death(entity, reason)  # 批量模式也是先延迟
    
    def process_pending_deaths(self) -> List[tuple]:
        """
        处理所有待处理的死亡
        
        Returns:
            处理的死亡列表 [(entity, reason), ...]
        """
        if not self.pending_deaths:
            return []
        
        processed = []
        for entity, reason in self.pending_deaths:
            try:
                # 再次确认死亡状态（可能被复活了）
                if not entity.is_alive:
                    for callback in self.death_callbacks:
                        callback(entity, reason)
                    processed.append((entity, reason))
                    self.death_history.append(f"{entity.name} 延迟死亡 ({reason.value})")
                else:
                    battle_logger.debug(f"{entity.name} 已被复活，取消死亡处理")
                    
            except Exception as e:
                battle_logger.error(f"延迟死亡处理失败: {e}")
        
        # 清空待处理队列
        self.pending_deaths.clear()
        
        if processed:
            battle_logger.info(f"处理了 {len(processed)} 个延迟死亡")
        
        return processed
    
    def get_statistics(self) -> dict:
        """获取死亡统计信息"""
        return {
            'trigger_mode': self.trigger_mode.value,
            'total_deaths': len(self.death_history),
            'pending_deaths': len(self.pending_deaths),
            'death_history': self.death_history[-10:],  # 最近10次死亡
            'registered_callbacks': len(self.death_callbacks)
        }
    
    def clear_history(self):
        """清空死亡历史"""
        self.death_history.clear()
        battle_logger.debug("清空死亡历史记录")
    
    def clear_pending_deaths(self):
        """清空待处理死亡队列"""
        cleared_count = len(self.pending_deaths)
        self.pending_deaths.clear()
        if cleared_count > 0:
            battle_logger.info(f"清空了 {cleared_count} 个待处理死亡")
