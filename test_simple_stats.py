#!/usr/bin/env python3
"""
简化的统计测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_stats():
    """简化的统计测试"""
    print("🔧 测试统计功能...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=2,
            turn_order_bonus_energy=0
        )
        
        print(f"✅ 战斗引擎创建成功")
        
        # 检查统计跟踪器
        if hasattr(engine, 'stats_tracker') and engine.stats_tracker:
            print(f"✅ 统计跟踪器存在")
            print(f"  初始数据条目: {len(engine.stats_tracker.data)}")
        else:
            print(f"❌ 没有统计跟踪器")
            return False
        
        # 检查动作执行器是否有统计跟踪器
        if hasattr(engine.action_executor, 'stats_tracker'):
            print(f"✅ 动作执行器有统计跟踪器")
        else:
            print(f"❌ 动作执行器没有统计跟踪器")
        
        # 执行一回合
        print(f"\n🎯 执行一回合...")
        result = engine.execute_round()
        
        # 检查统计数据
        print(f"\n📊 检查统计数据...")
        stats_data = engine.stats_tracker.data
        print(f"  统计数据条目: {len(stats_data)}")
        
        for spirit_id, data in stats_data.items():
            print(f"  {spirit_id}:")
            print(f"    总伤害: {data.get('total_damage_dealt', 0)}")
            print(f"    承受伤害: {data.get('total_damage_taken', 0)}")
            print(f"    行动次数: {data.get('actions', 0)}")
        
        # 检查精灵状态
        print(f"\n🔍 检查精灵状态:")
        for spirit in [spirit1, spirit2]:
            print(f"  {spirit.name}: HP={spirit.current_hp:.0f}/{spirit.max_hp:.0f}")
        
        # 验证是否有伤害数据
        total_damage = sum(data.get('total_damage_dealt', 0) for data in stats_data.values())
        
        if total_damage > 0:
            print(f"\n✅ 统计功能正常！总伤害: {total_damage:.0f}")
            return True
        else:
            print(f"\n❌ 统计数据仍然为0")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 简化统计测试")
    print("="*50)
    
    result = test_simple_stats()
    
    print("\n" + "="*50)
    if result:
        print("✅ 统计功能修复成功")
    else:
        print("❌ 统计功能仍有问题")

if __name__ == "__main__":
    main()
