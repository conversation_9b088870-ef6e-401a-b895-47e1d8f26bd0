<template>
  <div class="spirit-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="top"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="grid grid-cols-2 gap-4">
          <el-form-item label="精灵名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入精灵名称" />
          </el-form-item>
          
          <el-form-item label="等级" prop="level">
            <el-input-number v-model="formData.level" :min="1" :max="100" />
          </el-form-item>
          
          <el-form-item label="元素类型" prop="element">
            <el-select v-model="formData.element" placeholder="请选择元素类型">
              <el-option
                v-for="element in elementTypes"
                :key="element.value"
                :label="element.label"
                :value="element.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="职业" prop="professions">
            <el-select v-model="formData.professions" multiple placeholder="请选择职业">
              <el-option
                v-for="profession in professionTypes"
                :key="profession.value"
                :label="profession.label"
                :value="profession.value"
              />
            </el-select>
          </el-form-item>
        </div>
        
        <el-form-item label="神格等级" prop="shengeLevel">
          <el-input-number v-model="formData.shengeLevel" :min="0" :max="10" />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-tag
            v-for="tag in formData.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            class="mr-2 mb-2"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            style="width: 100px"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else size="small" @click="showInput">
            + 添加标签
          </el-button>
        </el-form-item>
      </div>

      <!-- 属性配置 -->
      <div class="form-section">
        <h3 class="section-title">属性配置</h3>
        
        <div class="grid grid-cols-2 gap-4">
          <el-form-item label="生命值" prop="attributes.maxHp">
            <el-input-number v-model="formData.attributes.maxHp" :min="1" :max="99999" />
          </el-form-item>
          
          <el-form-item label="当前生命值" prop="attributes.hp">
            <el-input-number 
              v-model="formData.attributes.hp" 
              :min="0" 
              :max="formData.attributes.maxHp" 
            />
          </el-form-item>
          
          <el-form-item label="攻击力" prop="attributes.attack">
            <el-input-number v-model="formData.attributes.attack" :min="1" :max="9999" />
          </el-form-item>
          
          <el-form-item label="防御力" prop="attributes.defense">
            <el-input-number v-model="formData.attributes.defense" :min="0" :max="9999" />
          </el-form-item>
          
          <el-form-item label="速度" prop="attributes.speed">
            <el-input-number v-model="formData.attributes.speed" :min="1" :max="9999" />
          </el-form-item>
          
          <el-form-item label="最大能量" prop="attributes.maxEnergy">
            <el-input-number v-model="formData.attributes.maxEnergy" :min="1" :max="999" />
          </el-form-item>
        </div>
        
        <el-form-item label="当前能量" prop="attributes.energy">
          <el-input-number 
            v-model="formData.attributes.energy" 
            :min="0" 
            :max="formData.attributes.maxEnergy" 
          />
        </el-form-item>
      </div>

      <!-- 位置配置 -->
      <div class="form-section">
        <h3 class="section-title">位置配置</h3>
        
        <div class="grid grid-cols-3 gap-4">
          <el-form-item label="队伍" prop="team">
            <el-select v-model="formData.team" placeholder="请选择队伍">
              <el-option label="队伍 1" :value="1" />
              <el-option label="队伍 2" :value="2" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="行位置" prop="position.0">
            <el-input-number v-model="formData.position[0]" :min="0" :max="2" />
          </el-form-item>
          
          <el-form-item label="列位置" prop="position.1">
            <el-input-number v-model="formData.position[1]" :min="0" :max="2" />
          </el-form-item>
        </div>
      </div>

      <!-- 技能配置 -->
      <div class="form-section">
        <h3 class="section-title">技能配置</h3>
        
        <div class="skills-list">
          <div
            v-for="(skill, index) in formData.skills"
            :key="index"
            class="skill-item bg-slate-700/50 rounded-lg p-4 border border-slate-600/30 mb-3"
          >
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-white font-medium">技能 {{ index + 1 }}</h4>
              <el-button size="small" type="danger" @click="removeSkill(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            
            <div class="grid grid-cols-2 gap-3">
              <el-form-item :prop="`skills.${index}.name`" label="技能名称">
                <el-input v-model="skill.name" placeholder="技能名称" />
              </el-form-item>
              
              <el-form-item :prop="`skills.${index}.category`" label="技能类型">
                <el-select v-model="skill.category" placeholder="技能类型">
                  <el-option label="攻击" value="attack" />
                  <el-option label="防御" value="defense" />
                  <el-option label="支援" value="support" />
                  <el-option label="特殊" value="special" />
                </el-select>
              </el-form-item>
              
              <el-form-item :prop="`skills.${index}.cooldown`" label="冷却时间">
                <el-input-number v-model="skill.cooldown" :min="0" :max="99" />
              </el-form-item>
              
              <el-form-item :prop="`skills.${index}.energyCost`" label="能量消耗">
                <el-input-number v-model="skill.energyCost" :min="0" :max="999" />
              </el-form-item>
            </div>
            
            <el-form-item :prop="`skills.${index}.description`" label="技能描述">
              <el-input 
                v-model="skill.description" 
                type="textarea" 
                :rows="2" 
                placeholder="技能描述"
              />
            </el-form-item>
            
            <el-form-item :prop="`skills.${index}.isPassive`" label="被动技能">
              <el-switch v-model="skill.isPassive" />
            </el-form-item>
          </div>
          
          <el-button @click="addSkill" class="w-full">
            <el-icon class="mr-2"><Plus /></el-icon>
            添加技能
          </el-button>
        </div>
      </div>

      <!-- 契约配置 -->
      <div class="form-section">
        <h3 class="section-title">契约配置</h3>
        
        <el-form-item label="契约ID列表">
          <el-tag
            v-for="contractId in formData.contractIds"
            :key="contractId"
            closable
            @close="removeContract(contractId)"
            class="mr-2 mb-2"
          >
            {{ contractId }}
          </el-tag>
          <el-input
            v-if="contractInputVisible"
            ref="contractInputRef"
            v-model="contractInputValue"
            size="small"
            style="width: 120px"
            @keyup.enter="handleContractInputConfirm"
            @blur="handleContractInputConfirm"
          />
          <el-button v-else size="small" @click="showContractInput">
            + 添加契约
          </el-button>
        </el-form-item>
        
        <el-form-item label="使魔原型名称">
          <el-input v-model="formData.emissaryPrototypeName" placeholder="使魔原型名称" />
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions flex justify-end space-x-3 mt-6 pt-6 border-t border-slate-600/30">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ isEditing ? '更新' : '创建' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { Spirit, ElementType, ProfessionType } from '@/types/battle'

interface Props {
  modelValue: Partial<Spirit>
  isEditing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEditing: false
})

const emit = defineEmits<{
  'update:modelValue': [value: Partial<Spirit>]
  submit: [value: Partial<Spirit>]
  cancel: []
}>()

// 表单引用
const formRef = ref<FormInstance>()
const inputRef = ref()
const contractInputRef = ref()

// 表单数据
const formData = reactive<Partial<Spirit>>({
  name: '',
  level: 1,
  element: undefined,
  professions: [],
  shengeLevel: 0,
  tags: [],
  attributes: {
    hp: 1000,
    maxHp: 1000,
    attack: 100,
    defense: 50,
    speed: 100,
    energy: 100,
    maxEnergy: 100
  },
  team: 1,
  position: [0, 0],
  skills: [],
  contractIds: [],
  emissaryPrototypeName: '',
  isAlive: true,
  effects: [],
  ...props.modelValue
})

// 标签输入
const inputVisible = ref(false)
const inputValue = ref('')

// 契约输入
const contractInputVisible = ref(false)
const contractInputValue = ref('')

// 选项数据
const elementTypes = [
  { label: '火', value: 'fire' },
  { label: '水', value: 'water' },
  { label: '土', value: 'earth' },
  { label: '风', value: 'air' },
  { label: '光', value: 'light' },
  { label: '暗', value: 'dark' },
  { label: '无', value: 'neutral' }
]

const professionTypes = [
  { label: '战士', value: 'warrior' },
  { label: '法师', value: 'mage' },
  { label: '射手', value: 'archer' },
  { label: '治疗', value: 'healer' },
  { label: '刺客', value: 'assassin' },
  { label: '坦克', value: 'tank' }
]

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入精灵名称', trigger: 'blur' },
    { min: 2, max: 20, message: '名称长度应在 2 到 20 个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请输入等级', trigger: 'blur' }
  ],
  'attributes.maxHp': [
    { required: true, message: '请输入最大生命值', trigger: 'blur' }
  ],
  'attributes.attack': [
    { required: true, message: '请输入攻击力', trigger: 'blur' }
  ],
  team: [
    { required: true, message: '请选择队伍', trigger: 'change' }
  ]
}

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 标签相关方法
const removeTag = (tag: string) => {
  const index = formData.tags!.indexOf(tag)
  if (index > -1) {
    formData.tags!.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags!.includes(inputValue.value)) {
    formData.tags!.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 契约相关方法
const removeContract = (contractId: string) => {
  const index = formData.contractIds!.indexOf(contractId)
  if (index > -1) {
    formData.contractIds!.splice(index, 1)
  }
}

const showContractInput = () => {
  contractInputVisible.value = true
  nextTick(() => {
    contractInputRef.value?.focus()
  })
}

const handleContractInputConfirm = () => {
  if (contractInputValue.value && !formData.contractIds!.includes(contractInputValue.value)) {
    formData.contractIds!.push(contractInputValue.value)
  }
  contractInputVisible.value = false
  contractInputValue.value = ''
}

// 技能相关方法
const addSkill = () => {
  formData.skills!.push({
    id: `skill_${Date.now()}`,
    name: '',
    description: '',
    category: 'attack',
    castType: 'active',
    targetType: 'enemy',
    cooldown: 0,
    currentCooldown: 0,
    energyCost: 0,
    isPassive: false
  })
}

const removeSkill = (index: number) => {
  formData.skills!.splice(index, 1)
}

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 确保当前生命值不超过最大生命值
    if (formData.attributes!.hp > formData.attributes!.maxHp) {
      formData.attributes!.hp = formData.attributes!.maxHp
    }
    
    // 确保当前能量不超过最大能量
    if (formData.attributes!.energy > formData.attributes!.maxEnergy) {
      formData.attributes!.energy = formData.attributes!.maxEnergy
    }
    
    emit('submit', formData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped lang="scss">
.spirit-form {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 24px;
  border-radius: 12px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.section-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(139, 92, 246, 0.3);
}

.skill-item {
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(51, 65, 85, 0.7) !important;
    border-color: rgba(139, 92, 246, 0.3) !important;
  }
}

:deep(.el-form-item__label) {
  color: #e2e8f0;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
}

:deep(.el-textarea__inner) {
  background-color: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
  color: #e2e8f0;
}

:deep(.el-input-number .el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
}
</style>