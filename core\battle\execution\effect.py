"""Effect application/removal/update handlers."""
from __future__ import annotations

from typing import Optional, List, cast

from core.battle.executor.executor import handler, UnifiedActionExecutor
from .decorators import validate_target, safe_execute, monitor_performance
from ...action import (
    ApplyEffectAction,
    RemoveEffectAction,
    UpdateEffectAction,
    RemoveEffectsByCategoryAction,
    BattleAction,
    LogAction,
    DispatchEventAction
)
from ...spirit.spirit import Spirit


@handler(ApplyEffectAction)
@validate_target(alive_required=True, spirit_type_required=True)
@safe_execute(log_errors=True)
@monitor_performance(slow_threshold=0.1)
def _handle_apply_effect(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """
    Handles applying an effect, including immunity checks.
    """
    apply_action = cast(ApplyEffectAction, action)
    target = apply_action.target # Assuming 'target' is the correct attribute
    
    if not isinstance(target, Spirit) or not target.is_alive:
        return None

    effect_to_apply = apply_action.effect

    # --- IMMUNITY CHECK ---
    # 🔧 修复：通过effect_manager访问effects
    if hasattr(target, 'effect_manager') and target.effect_manager:
        for existing_effect in target.effect_manager.effects.values():
            if hasattr(existing_effect, 'provides_immunity_to') and existing_effect.provides_immunity_to(effect_to_apply):

                # 使用新的ImmunityEvent
                from ...event.events import ImmunityEvent
                immunity_event = ImmunityEvent(
                    target=target,
                    source_effect_id=getattr(existing_effect, 'id', 'unknown'),
                    immunity_type="effect",
                    blocked_action=getattr(effect_to_apply, 'name', 'unknown_effect')
                )

                log_action = LogAction(
                    caster=target,
                    message=f"{target.name} 的 '{existing_effect.name}' 免疫了 '{effect_to_apply.name}'"
                )
                dispatch_action = DispatchEventAction(caster=target, event=immunity_event)

                return [log_action, dispatch_action]

    # If no immunity, apply the effect normally
    # 🔧 修复：使用effect_manager的add_effect方法
    if hasattr(target, 'effect_manager') and target.effect_manager:
        result = target.effect_manager.add_effect(effect_to_apply, self.battle_state)
    else:
        # 回退到旧方法
        result = target.apply_effect(effect_to_apply, self.battle_state)
    
    if hasattr(result, 'actions') and result.actions:
        return result.actions
        
    return None


@handler(RemoveEffectAction)  # type: ignore
def _handle_remove_effect(
    self: UnifiedActionExecutor, action: RemoveEffectAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    removed_effect, new_actions = target.effect_manager.remove_effect(  # type: ignore[attr-defined]
        action.effect_id, self.battle_state
    )
    if removed_effect:
        return new_actions if new_actions else None
    return None


@handler(UpdateEffectAction)  # type: ignore
def _handle_update_effect(
    self: UnifiedActionExecutor, action: UpdateEffectAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    if not hasattr(target, "effect_manager"):
        return None
    effect = target.effect_manager.get_effect_by_id(action.effect_id)  # type: ignore[attr-defined]
    if effect:
        for attr_name, new_value in action.updates.items():
            if hasattr(effect, attr_name):
                setattr(effect, attr_name, new_value)
    return None


@handler(RemoveEffectsByCategoryAction)  # type: ignore
def _handle_remove_by_category(
    self: UnifiedActionExecutor, action: RemoveEffectsByCategoryAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    target = action.target
    if not hasattr(target, "effect_manager"):
        return None
    to_remove = [
        eff
        for eff in target.effect_manager.effects.values()  # type: ignore[attr-defined]
        if getattr(eff, "category", None) == action.category
    ]
    actions: List[BattleAction] = []
    for eff in to_remove:
        _, remove_actions = target.effect_manager.remove_effect(eff.id, self.battle_state)  # type: ignore[attr-defined]
        if remove_actions:
            actions.extend(remove_actions)
    return actions or None