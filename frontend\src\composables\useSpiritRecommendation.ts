import { computed, ref } from 'vue'
import type { Spirit, ElementType, ProfessionType } from '../types/battle'

/**
 * 精灵推荐系统
 * 基于队伍组成、元素克制、职业平衡等因素智能推荐精灵
 */
export function useSpiritRecommendation() {
  // 元素克制关系
  const elementCounters: Record<ElementType, ElementType[]> = {
    fire: ['water', 'earth'],
    water: ['fire', 'air'],
    earth: ['air', 'light'],
    air: ['earth', 'dark'],
    light: ['dark', 'fire'],
    dark: ['light', 'water'],
    neutral: []
  }

  // 职业组合评分
  const professionSynergy: Record<string, number> = {
    'warrior-healer': 0.9,
    'warrior-tank': 0.8,
    'mage-healer': 0.7,
    'archer-warrior': 0.8,
    'assassin-mage': 0.7,
    'tank-healer': 0.9
  }

  /**
   * 分析当前队伍组成
   */
  const analyzeTeamComposition = (team: Spirit[]) => {
    const elements = team.map(s => s.element).filter(Boolean) as ElementType[]
    const professions = team.flatMap(s => s.professions)
    
    return {
      elementCounts: elements.reduce((acc, el) => {
        acc[el] = (acc[el] || 0) + 1
        return acc
      }, {} as Record<ElementType, number>),
      professionCounts: professions.reduce((acc, prof) => {
        acc[prof] = (acc[prof] || 0) + 1
        return acc
      }, {} as Record<ProfessionType, number>),
      totalPower: team.reduce((sum, s) => sum + s.attributes.attack + s.attributes.defense, 0),
      averageLevel: team.length > 0 ? team.reduce((sum, s) => sum + s.level, 0) / team.length : 0
    }
  }

  /**
   * 计算精灵与队伍的适配度
   */
  const calculateSpiritFitness = (spirit: Spirit, currentTeam: Spirit[], enemyTeam: Spirit[] = []) => {
    let score = 0
    const teamAnalysis = analyzeTeamComposition(currentTeam)
    const enemyAnalysis = analyzeTeamComposition(enemyTeam)

    // 1. 职业平衡评分 (30%)
    const professionBalance = calculateProfessionBalance(spirit, teamAnalysis.professionCounts)
    score += professionBalance * 0.3

    // 2. 元素克制评分 (25%)
    const elementAdvantage = calculateElementAdvantage(spirit, enemyAnalysis.elementCounts)
    score += elementAdvantage * 0.25

    // 3. 属性互补评分 (20%)
    const attributeComplement = calculateAttributeComplement(spirit, currentTeam)
    score += attributeComplement * 0.2

    // 4. 等级匹配评分 (15%)
    const levelMatch = calculateLevelMatch(spirit, teamAnalysis.averageLevel)
    score += levelMatch * 0.15

    // 5. 稀有度加成 (10%)
    const rarityBonus = calculateRarityBonus(spirit)
    score += rarityBonus * 0.1

    return Math.min(score, 1) // 确保分数不超过1
  }

  /**
   * 计算职业平衡度
   */
  const calculateProfessionBalance = (spirit: Spirit, currentProfessions: Record<ProfessionType, number>) => {
    const idealComposition = {
      warrior: 2,
      tank: 1,
      mage: 2,
      archer: 1,
      healer: 1,
      assassin: 1
    }

    let score = 0
    spirit.professions.forEach(profession => {
      const current = currentProfessions[profession] || 0
      const ideal = idealComposition[profession] || 1
      
      if (current < ideal) {
        score += (ideal - current) / ideal
      } else if (current >= ideal) {
        score += 0.3 // 轻微惩罚过多同类职业
      }
    })

    return Math.min(score / spirit.professions.length, 1)
  }

  /**
   * 计算元素克制优势
   */
  const calculateElementAdvantage = (spirit: Spirit, enemyElements: Record<ElementType, number>) => {
    if (!spirit.element) return 0.5

    const counters = elementCounters[spirit.element] || []
    let advantage = 0

    counters.forEach(counterElement => {
      const enemyCount = enemyElements[counterElement] || 0
      advantage += enemyCount * 0.3 // 每个被克制的敌人增加30%优势
    })

    return Math.min(advantage, 1)
  }

  /**
   * 计算属性互补性
   */
  const calculateAttributeComplement = (spirit: Spirit, currentTeam: Spirit[]) => {
    if (currentTeam.length === 0) return 1

    const teamStats = {
      totalAttack: currentTeam.reduce((sum, s) => sum + s.attributes.attack, 0),
      totalDefense: currentTeam.reduce((sum, s) => sum + s.attributes.defense, 0),
      totalHp: currentTeam.reduce((sum, s) => sum + s.attributes.maxHp, 0),
      totalSpeed: currentTeam.reduce((sum, s) => sum + s.attributes.speed, 0)
    }

    const avgStats = {
      attack: teamStats.totalAttack / currentTeam.length,
      defense: teamStats.totalDefense / currentTeam.length,
      hp: teamStats.totalHp / currentTeam.length,
      speed: teamStats.totalSpeed / currentTeam.length
    }

    // 计算精灵在各属性上的相对强度
    const spiritStrengths = {
      attack: spirit.attributes.attack / (avgStats.attack || 1),
      defense: spirit.attributes.defense / (avgStats.defense || 1),
      hp: spirit.attributes.maxHp / (avgStats.hp || 1),
      speed: spirit.attributes.speed / (avgStats.speed || 1)
    }

    // 奖励在团队薄弱属性上表现突出的精灵
    const maxStrength = Math.max(...Object.values(spiritStrengths))
    return Math.min(maxStrength / 2, 1)
  }

  /**
   * 计算等级匹配度
   */
  const calculateLevelMatch = (spirit: Spirit, averageLevel: number) => {
    if (averageLevel === 0) return 1

    const levelDiff = Math.abs(spirit.level - averageLevel)
    const maxAcceptableDiff = 10

    return Math.max(0, 1 - levelDiff / maxAcceptableDiff)
  }

  /**
   * 计算稀有度加成
   */
  const calculateRarityBonus = (spirit: Spirit) => {
    // 基于神格等级和标签判断稀有度
    let rarity = spirit.shengeLevel / 5 // 神格等级贡献

    // 特殊标签加成
    const rareTags = ['神曜', '王者', '圣谕', '希望', '神灵']
    const hasRareTag = spirit.tags.some(tag => rareTags.includes(tag))
    if (hasRareTag) rarity += 0.3

    return Math.min(rarity, 1)
  }

  /**
   * 获取推荐精灵列表
   */
  const getRecommendedSpirits = (
    availableSpirits: Spirit[],
    currentTeam: Spirit[],
    enemyTeam: Spirit[] = [],
    maxRecommendations = 5
  ) => {
    const recommendations = availableSpirits
      .filter(spirit => !currentTeam.some(teamSpirit => teamSpirit.id === spirit.id))
      .map(spirit => ({
        spirit,
        fitness: calculateSpiritFitness(spirit, currentTeam, enemyTeam),
        reasons: generateRecommendationReasons(spirit, currentTeam, enemyTeam)
      }))
      .sort((a, b) => b.fitness - a.fitness)
      .slice(0, maxRecommendations)

    return recommendations
  }

  /**
   * 生成推荐理由
   */
  const generateRecommendationReasons = (spirit: Spirit, currentTeam: Spirit[], enemyTeam: Spirit[]) => {
    const reasons: string[] = []
    const teamAnalysis = analyzeTeamComposition(currentTeam)

    // 职业需求
    const neededProfessions = ['tank', 'healer', 'warrior', 'mage']
    spirit.professions.forEach(profession => {
      const current = teamAnalysis.professionCounts[profession] || 0
      if (current === 0 && neededProfessions.includes(profession)) {
        reasons.push(`补充${getProfessionLabel(profession)}职业`)
      }
    })

    // 元素克制
    if (spirit.element && enemyTeam.length > 0) {
      const counters = elementCounters[spirit.element] || []
      const enemyElements = enemyTeam.map(s => s.element).filter(Boolean)
      const advantageCount = counters.filter(counter => enemyElements.includes(counter)).length
      
      if (advantageCount > 0) {
        reasons.push(`克制敌方${advantageCount}个精灵`)
      }
    }

    // 属性优势
    if (currentTeam.length > 0) {
      const avgAttack = currentTeam.reduce((sum, s) => sum + s.attributes.attack, 0) / currentTeam.length
      if (spirit.attributes.attack > avgAttack * 1.3) {
        reasons.push('高攻击力')
      }
      
      const avgHp = currentTeam.reduce((sum, s) => sum + s.attributes.maxHp, 0) / currentTeam.length
      if (spirit.attributes.maxHp > avgHp * 1.3) {
        reasons.push('高生存能力')
      }
    }

    // 稀有度
    if (spirit.shengeLevel >= 3) {
      reasons.push('高神格等级')
    }

    return reasons.length > 0 ? reasons : ['属性均衡']
  }

  /**
   * 获取职业标签
   */
  const getProfessionLabel = (profession: ProfessionType) => {
    const labels = {
      warrior: '战士',
      mage: '法师',
      archer: '射手',
      healer: '治疗',
      assassin: '刺客',
      tank: '坦克'
    }
    return labels[profession] || profession
  }

  return {
    analyzeTeamComposition,
    calculateSpiritFitness,
    getRecommendedSpirits,
    generateRecommendationReasons
  }
}