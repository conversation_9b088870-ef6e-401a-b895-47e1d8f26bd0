"""
动态死亡系统类型定义
"""
from enum import Enum


class DeathTriggerMode(Enum):
    """死亡触发模式"""
    IMMEDIATE = "immediate"      # 立即触发（HP变化时）
    DEFERRED = "deferred"        # 延迟触发（在动作执行完成后）
    BATCH = "batch"              # 批量触发（回合结束时）


class DeathReason(Enum):
    """死亡原因"""
    DAMAGE = "damage"            # 伤害致死
    EFFECT = "effect"            # 效果致死（如毒、诅咒等）
    SACRIFICE = "sacrifice"      # 献祭
    EXECUTION = "execution"      # 处决
    OTHER = "other"              # 其他原因
