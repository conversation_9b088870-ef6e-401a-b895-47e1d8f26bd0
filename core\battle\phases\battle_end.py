"""BattleEndPhase – handles end-of-battle event dispatch."""
from __future__ import annotations

from typing import List, Any

from .base import IBattlePhase
from ..models import BattleState  # type: ignore
from ...performance import monitor_battle_performance


class BattleEndPhase(IBattlePhase):
    """战斗结束阶段。"""

    @monitor_battle_performance
    def execute(self, battle_state: BattleState) -> List[Any]:  # noqa: D401
        from ...action import DispatchEventAction
        from ...event.events import BattleEndEvent

        actions: List[Any] = []
        winner = battle_state.winner
        if winner is not None:
            battle_end_event = BattleEndEvent(winner=winner)
            actions.append(DispatchEventAction(caster=None, event=battle_end_event))
        return actions 