"""
效果模块 (响应式架构)

默认使用响应式效果管理器，提供全监控的动态更新机制
"""
from __future__ import annotations

# 导入基础类型和接口
from .system import (
    EffectType, EffectCategory, EffectPriority,
    IEffect, EffectResult
)

# 导入响应式管理器作为默认的 EffectManager
from .reactive import (
    ReactiveEffectManager as EffectManager,
    get_global_scheduler,
    shutdown_global_scheduler
)

# 保持向后兼容 - 导入原始管理器
from .system import EffectManager as LegacyEffectManager

__all__ = [
    # 主要的效果管理器（现在是响应式的）
    'EffectManager',

    # 基础类型和接口
    'EffectType',
    'EffectCategory',
    'EffectPriority',
    'IEffect',
    'EffectResult',

    # 响应式系统工具
    'get_global_scheduler',
    'shutdown_global_scheduler',

    # 向后兼容
    'LegacyEffectManager'
]
