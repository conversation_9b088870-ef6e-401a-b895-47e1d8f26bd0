import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/battle'
    },
    {
      path: '/battle',
      name: 'Battle',
      component: () => import('@/views/BattleArena.vue'),
      meta: {
        title: '战斗竞技场',
        icon: 'Trophy'
      }
    },
    {
      path: '/platform',
      name: 'Platform',
      component: () => import('@/views/BattlePlatform.vue'),
      meta: {
        title: '对战平台',
        icon: 'Connection'
      }
    },
    {
      path: '/spirits',
      name: 'Spirits',
      component: () => import('@/views/SpiritManagement.vue'),
      meta: {
        title: '精灵管理',
        icon: 'Avatar'
      }
    },
    {
      path: '/analytics',
      name: 'Analytics',
      component: () => import('@/views/DataAnalytics.vue'),
      meta: {
        title: '数据分析',
        icon: 'DataAnalysis'
      }
    },
    {
      path: '/tactics',
      name: 'Tactics',
      component: () => import('@/views/TacticalPlanner.vue'),
      meta: {
        title: '战术规划',
        icon: 'Guide'
      }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('@/views/SystemSettings.vue'),
      meta: {
        title: '系统设置',
        icon: 'Setting'
      }
    }
  ]
})

router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 精灵战斗模拟器`
  }
  next()
})

export default router