#!/usr/bin/env python3
"""
测试命中率修复效果

验证所有精灵的命中率都不为负值
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_spirits_hit_rate():
    """测试所有精灵的命中率"""
    print("🔧 测试所有精灵的命中率...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"📊 可用精灵数量: {len(available_spirits)}")
        
        all_valid = True
        
        for spirit_name in available_spirits:
            try:
                spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
                
                print(f"\n🎯 {spirit.name}:")
                
                # 检查属性中的命中率
                if hasattr(spirit, 'attributes'):
                    attrs = spirit.attributes
                    
                    # 基础命中率
                    base_hit_rate = getattr(attrs, 'base_hit_rate', 0.0)
                    print(f"  基础命中率: {base_hit_rate}")
                    
                    # 面板命中率
                    hit_rate = getattr(attrs, 'hit_rate', 0.0)
                    print(f"  面板命中率: {hit_rate}")
                    
                    # 检查是否为负值
                    if base_hit_rate < 0:
                        print(f"  ❌ 基础命中率为负值: {base_hit_rate}")
                        all_valid = False
                    else:
                        print(f"  ✅ 基础命中率正常")
                    
                    if hit_rate < 0:
                        print(f"  ❌ 面板命中率为负值: {hit_rate}")
                        all_valid = False
                    else:
                        print(f"  ✅ 面板命中率正常")
                    
                    # 计算实际战斗中的命中率
                    # 根据伤害计算器的逻辑：default_hit_rate(1.0) + hit_rate_bonus
                    actual_hit_rate = 1.0 + hit_rate
                    print(f"  实际战斗命中率: {actual_hit_rate * 100:.1f}%")
                    
                    if actual_hit_rate < 0.05:  # 最低5%
                        print(f"  ⚠️ 实际命中率过低: {actual_hit_rate * 100:.1f}%")
                    elif actual_hit_rate > 0.95:  # 最高95%
                        print(f"  ⚠️ 实际命中率过高: {actual_hit_rate * 100:.1f}%")
                    else:
                        print(f"  ✅ 实际命中率合理")
                
                else:
                    print(f"  ❌ 精灵没有 attributes 属性")
                    all_valid = False
                
            except Exception as e:
                print(f"  ❌ 创建精灵失败: {e}")
                all_valid = False
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hit_rate_calculation():
    """测试命中率计算逻辑"""
    print("\n🔧 测试命中率计算逻辑...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        print(f"📊 命中率计算测试:")
        print(f"  攻击方: {spirit1.name}")
        print(f"  防御方: {spirit2.name}")
        
        # 使用伤害计算器的命中率计算逻辑
        from core.battle.utilities.formula_damage_calculator import calculate_hit_and_dodge
        from core.battle.utilities.formula_damage_calculator import DamageModifiers
        
        # 创建空的修正器
        modifiers_caster = DamageModifiers()
        modifiers_target = DamageModifiers()
        
        # 计算命中和闪避
        is_hit, is_dodge = calculate_hit_and_dodge(
            spirit1, spirit2, modifiers_caster, modifiers_target
        )
        
        print(f"\n📈 计算结果:")
        print(f"  是否命中: {is_hit}")
        print(f"  是否闪避: {is_dodge}")
        
        # 显示详细计算过程
        default_hit_rate = 1.0
        hit_rate_bonus = getattr(spirit1.attributes, 'hit_rate', 0.0)
        default_dodge_rate = 0.0
        dodge_rate_bonus = getattr(spirit2.attributes, 'dodge_rate', 0.0)
        
        print(f"\n🔍 计算详情:")
        print(f"  基础命中率: {default_hit_rate * 100:.1f}%")
        print(f"  攻击方命中加成: {hit_rate_bonus * 100:.1f}%")
        print(f"  基础闪避率: {default_dodge_rate * 100:.1f}%")
        print(f"  防御方闪避加成: {dodge_rate_bonus * 100:.1f}%")
        
        # 计算最终命中率
        final_hit_rate = max(0.05, min(0.95,
            default_hit_rate + hit_rate_bonus + modifiers_caster.hit_rate_bonus
            - default_dodge_rate - dodge_rate_bonus - modifiers_target.dodge_rate_bonus))
        
        print(f"  最终命中率: {final_hit_rate * 100:.1f}%")
        
        # 验证命中率范围
        if 0.05 <= final_hit_rate <= 0.95:
            print(f"  ✅ 命中率在合理范围内")
            return True
        else:
            print(f"  ❌ 命中率超出合理范围")
            return False
        
    except Exception as e:
        print(f"❌ 命中率计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_display():
    """测试UI显示的命中率"""
    print("\n🔧 测试UI显示的命中率...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"📊 UI显示测试:")
        
        for spirit_name in available_spirits:
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"\n🎯 {spirit.name}:")
            
            # 模拟UI显示的属性
            if hasattr(spirit, 'attributes'):
                attrs = spirit.attributes
                
                # 这些是UI可能显示的属性
                display_attrs = {
                    '攻击力': getattr(attrs, 'attack', 0),
                    '物理防御': getattr(attrs, 'pdef', 0),
                    '魔法防御': getattr(attrs, 'mdef', 0),
                    '速度': getattr(attrs, 'speed', 0),
                    '命中率': getattr(attrs, 'hit_rate', 0),
                    '闪避率': getattr(attrs, 'dodge_rate', 0),
                    '暴击率': getattr(attrs, 'crit_rate', 0),
                    '暴击伤害': getattr(attrs, 'crit_damage', 1.5),
                }
                
                for attr_name, attr_value in display_attrs.items():
                    if attr_name in ['命中率', '闪避率', '暴击率']:
                        # 百分比属性
                        display_value = f"{attr_value * 100:.1f}%"
                        if attr_value < 0:
                            print(f"    ❌ {attr_name}: {display_value} (负值！)")
                        else:
                            print(f"    ✅ {attr_name}: {display_value}")
                    elif attr_name == '暴击伤害':
                        # 倍数属性
                        display_value = f"{attr_value:.1f}x"
                        print(f"    ✅ {attr_name}: {display_value}")
                    else:
                        # 数值属性
                        print(f"    ✅ {attr_name}: {attr_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 命中率修复效果测试")
    print("="*60)
    
    tests = [
        ("所有精灵命中率检查", test_all_spirits_hit_rate),
        ("命中率计算逻辑", test_hit_rate_calculation),
        ("UI显示测试", test_ui_display),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！命中率修复成功")
        print("\n📋 修复内容:")
        print("  ✅ 神曜虚无·伏妖: base_hit_rate从-0.10改为0.0")
        print("  ✅ 天恩圣祭·空灵圣龙: base_hit_rate从-0.05改为0.0")
        print("  ✅ 所有精灵的命中率都不再为负值")
        print("  ✅ UI显示的命中率都为正常值")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
