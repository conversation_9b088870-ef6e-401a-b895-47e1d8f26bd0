"""CloneSpiritAction handler."""
from __future__ import annotations

from typing import Optional, List

from core.battle.executor.executor import handler, UnifiedActionExecutor
from ...action import CloneSpiritAction, ApplyEffectAction, BattleAction


@handler(CloneSpiritAction)
def _handle_clone(
    self: UnifiedActionExecutor, action: CloneSpiritAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    spirit_to_clone = action.spirit_to_clone
    team = action.team
    position = action.position

    formation = self.battle_state.get_formation_by_team_id(team)  # type: ignore[attr-defined]
    if not formation.is_position_empty(position):  # type: ignore[attr-defined]
        return None

    try:
        from ...spirit.spirit import Spirit, SpiritMetadata
        from ...attribute import Attributes
        import copy

        original_attrs = spirit_to_clone.attributes
        ratio = action.attribute_ratio
        cloned_attrs = Attributes(
            base_hp=int(original_attrs.hp * ratio),
            hp_p=0.0,
            hp_flat=0.0,
            base_attack=int(original_attrs.attack * ratio),
            attack_p=0.0,
            attack_flat=0.0,
            base_pdef=int(original_attrs.pdef * ratio),
            pdef_p=0.0,
            pdef_flat=0.0,
            base_mdef=int(original_attrs.mdef * ratio),
            mdef_p=0.0,
            mdef_flat=0.0,
            base_speed=int(original_attrs.speed * ratio),
            base_hit_rate=original_attrs.hit_rate,
            base_dodge_rate=original_attrs.dodge_rate,
            base_break_rate=original_attrs.break_rate,
            base_block_rate=original_attrs.block_rate,
            base_crit_rate=original_attrs.crit_rate,
            base_crit_res_rate=original_attrs.crit_res_rate,
        )

        clone_id = f"{spirit_to_clone.id}_clone_{len(self.battle_state.get_all_spirits())}"  # type: ignore[attr-defined]
        clone_name = f"{spirit_to_clone.name}(克隆)"  # type: ignore[attr-defined]

        cloned_spirit = Spirit(
            id=clone_id,
            name=clone_name,
            attributes=cloned_attrs,
            position=position,
            team=team,
            metadata=SpiritMetadata(
                element=spirit_to_clone.metadata.element,  # type: ignore[attr-defined]
                professions=copy.copy(spirit_to_clone.metadata.professions),  # type: ignore[attr-defined]
                tags=copy.copy(spirit_to_clone.metadata.tags),  # type: ignore[attr-defined]
            ),
        )

        self.battle_state.add_spirit(cloned_spirit, team, position, is_summoned=True)  # type: ignore[attr-defined]

        actions: List[BattleAction] = []
        for eff in action.initial_effects:
            eff.caster = action.caster
            actions.append(ApplyEffectAction(caster=action.caster, target=cloned_spirit, effect=eff))
        return actions or None
    except Exception as exc:  # pragma: no cover
        self.battle_log.append({"level": "ERROR", "message": f"CloneSpirit failed: {exc}"})
        return None 