#!/usr/bin/env python3
"""
基于完整公式的伤害计算器

实现标准的伤害计算公式：
- 普攻伤害 = [实攻-实防] * 技能倍率 * PZC * PZJ * K * B * F
- 超杀伤害 = [实攻-实防] * 技能倍率 * CZC * CZJ * K * B * Q * F

其中：
- PZC/CZC(增伤乘) = ∏(1+技能增伤) * ∏(1+伤害加深) * ∏(1-技能减伤) * ...
- PZJ/CZJ(增伤加) = (1 + 各种加法增伤 - 各种加法减伤)
- K = 元素克制系数
- B = 暴击系数  
- Q = 气势系数(仅超杀)
- F = 其他修正(格挡、溅射等)
"""

import math
import random
from typing import Dict, Tuple, Any, List, Optional, Union, cast
from dataclasses import dataclass

# 新增：导入缓存和性能监控装饰器
try:
    from ...cache import cache_manager
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False
    cache_manager = None

try:
    from ...performance import monitor_battle_performance
    PERFORMANCE_MONITORING = True
except ImportError:
    PERFORMANCE_MONITORING = False
    def monitor_battle_performance(func):
        return func

@dataclass
class DamageModifiers:
    """伤害修正系数集合"""
    # PZC/CZC 乘法因子
    skill_damage_multipliers: List[float]  # 技能增伤 ∏(1+技能增伤)
    damage_amplify_multipliers: List[float]  # 伤害加深 ∏(1+伤害加深)
    skill_damage_reductions: List[float]  # 技能减伤 ∏(1-技能减伤)

    # PZJ/CZJ 加法因子
    flat_damage_bonus: float  # 各种加法增伤
    flat_damage_reduction: float  # 各种加法减伤

    # 防御相关
    ignore_defense_percent: float  # 技能无视防御百分比
    armor_break_percent: float  # 破刃无视防御百分比

    # 其他修正
    crit_rate_bonus: float  # 暴击率加成
    hit_rate_bonus: float  # 命中率加成
    dodge_rate_bonus: float  # 闪避率加成
    # 新增：修复linter aerrors
    synergy_damage_bonus: Optional[float] = None
    equipment_damage_reduction: Optional[float] = None
    bond_damage_reduction: Optional[float] = None
    equipment_damage_bonus: Optional[float] = None
    divine_damage_bonus: Optional[float] = None
    special_skill_bonus: Optional[float] = None
    equipment_damage_penalty: Optional[float] = None
    ultimate_exclusive_bonus: Optional[float] = None
    ultimate_equipment_bonus: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'skill_damage_multipliers': self.skill_damage_multipliers,
            'damage_amplify_multipliers': self.damage_amplify_multipliers,
            'skill_damage_reductions': self.skill_damage_reductions,
            'flat_damage_bonus': self.flat_damage_bonus,
            'flat_damage_reduction': self.flat_damage_reduction,
            'ignore_defense_percent': self.ignore_defense_percent,
            'armor_break_percent': self.armor_break_percent,
            'crit_rate_bonus': self.crit_rate_bonus,
            'hit_rate_bonus': self.hit_rate_bonus,
            'dodge_rate_bonus': self.dodge_rate_bonus,
            'synergy_damage_bonus': self.synergy_damage_bonus,
            'equipment_damage_reduction': self.equipment_damage_reduction,
            'bond_damage_reduction': self.bond_damage_reduction,
            'equipment_damage_bonus': self.equipment_damage_bonus,
            'divine_damage_bonus': self.divine_damage_bonus,
            'special_skill_bonus': self.special_skill_bonus,
            'equipment_damage_penalty': self.equipment_damage_penalty,
            'ultimate_exclusive_bonus': self.ultimate_exclusive_bonus,
            'ultimate_equipment_bonus': self.ultimate_equipment_bonus,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DamageModifiers':
        """从字典反序列化"""
        return cls(
            skill_damage_multipliers=data.get('skill_damage_multipliers', []),
            damage_amplify_multipliers=data.get('damage_amplify_multipliers', []),
            skill_damage_reductions=data.get('skill_damage_reductions', []),
            flat_damage_bonus=data.get('flat_damage_bonus', 0.0),
            flat_damage_reduction=data.get('flat_damage_reduction', 0.0),
            ignore_defense_percent=data.get('ignore_defense_percent', 0.0),
            armor_break_percent=data.get('armor_break_percent', 0.0),
            crit_rate_bonus=data.get('crit_rate_bonus', 0.0),
            hit_rate_bonus=data.get('hit_rate_bonus', 0.0),
            dodge_rate_bonus=data.get('dodge_rate_bonus', 0.0),
            synergy_damage_bonus=data.get('synergy_damage_bonus'),
            equipment_damage_reduction=data.get('equipment_damage_reduction'),
            bond_damage_reduction=data.get('bond_damage_reduction'),
            equipment_damage_bonus=data.get('equipment_damage_bonus'),
            divine_damage_bonus=data.get('divine_damage_bonus'),
            special_skill_bonus=data.get('special_skill_bonus'),
            equipment_damage_penalty=data.get('equipment_damage_penalty'),
            ultimate_exclusive_bonus=data.get('ultimate_exclusive_bonus'),
            ultimate_equipment_bonus=data.get('ultimate_equipment_bonus'),
        )

def extract_damage_modifiers(spirit) -> DamageModifiers:
    """从精灵的效果系统中提取伤害修正系数"""
    
    # 初始化修正系数
    modifiers = DamageModifiers(
        skill_damage_multipliers=[],
        damage_amplify_multipliers=[],
        skill_damage_reductions=[],
        flat_damage_bonus=0.0,
        flat_damage_reduction=0.0,
        ignore_defense_percent=0.0,
        armor_break_percent=0.0,
        crit_rate_bonus=0.0,
        hit_rate_bonus=0.0,
        dodge_rate_bonus=0.0
    )
    
    # 方法1：从效果管理器直接获取
    if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
        for effect_id, effect in spirit.effect_manager.effects.items():
            if hasattr(effect, 'data') and effect.data:
                _extract_from_effect_data(effect.data, modifiers)
    
    # 方法2：从战斗上下文获取（兼容性）
    elif hasattr(spirit, 'get_battle_context'):
        context = spirit.get_battle_context()
        effect_properties = context.get("effect_properties", {})
        
        for effect_id, effect_data in effect_properties.items():
            effect_data_dict = effect_data.get("data", {})
            _extract_from_effect_data(effect_data_dict, modifiers)
    
    # 方法3：从聚合数据获取（性能优化）
    elif hasattr(spirit, '_unified_effect_manager'):
        aggregated_data = spirit._unified_effect_manager.get_aggregated_data()
        base_modifiers = aggregated_data.get("modifiers", {})
        
        # 将聚合的修正值转换为我们的格式
        if "damage_bonus" in base_modifiers and base_modifiers["damage_bonus"] != 1.0:
            # damage_bonus是乘法形式，转换为增伤
            bonus = base_modifiers["damage_bonus"] - 1.0
            modifiers.skill_damage_multipliers.append(bonus)
        
        modifiers.crit_rate_bonus = base_modifiers.get("crit_bonus", 0.0)
        modifiers.hit_rate_bonus = base_modifiers.get("hit_bonus", 0.0)
        modifiers.dodge_rate_bonus = base_modifiers.get("dodge_bonus", 0.0)
    
    return modifiers

def _extract_from_effect_data(effect_data: dict, modifiers: DamageModifiers) -> None:
    """从效果数据中提取修正值"""
    
    # ZC (增乘) 相关
    if "skill_damage_multiplier" in effect_data:
        modifiers.skill_damage_multipliers.append(effect_data["skill_damage_multiplier"])
    
    if "damage_amplify" in effect_data:
        modifiers.damage_amplify_multipliers.append(effect_data["damage_amplify"])
    
    if "skill_damage_reduction" in effect_data:
        modifiers.skill_damage_reductions.append(effect_data["skill_damage_reduction"])
    
    # 兼容旧的命名
    if "damage_multiplier" in effect_data:
        modifiers.skill_damage_multipliers.append(effect_data["damage_multiplier"])
    
    if "damage_reduction_multiplier" in effect_data:
        modifiers.skill_damage_reductions.append(effect_data["damage_reduction_multiplier"])
    
    # ZJ (增加) 相关
    if "flat_damage_bonus" in effect_data:
        modifiers.flat_damage_bonus += effect_data["flat_damage_bonus"]
    
    if "flat_damage_reduction" in effect_data:
        modifiers.flat_damage_reduction += effect_data["flat_damage_reduction"]
    
    # 防御相关
    if "ignore_defense" in effect_data:
        modifiers.ignore_defense_percent += effect_data["ignore_defense"]
    
    if "armor_break" in effect_data:
        modifiers.armor_break_percent += effect_data["armor_break"]
    
    # 其他修正
    if "crit_bonus" in effect_data:
        modifiers.crit_rate_bonus += effect_data["crit_bonus"]
    
    if "hit_bonus" in effect_data:
        modifiers.hit_rate_bonus += effect_data["hit_bonus"]
    
    if "dodge_bonus" in effect_data:
        modifiers.dodge_rate_bonus += effect_data["dodge_bonus"]
    
    # 神曜技能特殊处理
    _extract_divine_skill_modifiers(effect_data, modifiers)

def _extract_divine_skill_modifiers(effect_data: dict, modifiers: DamageModifiers) -> None:
    """提取神曜技能的增伤修正"""
    
    # 神曜技能通常没有直接的增伤数据，但可能有间接影响
    # 这里处理一些特殊的神曜技能增伤逻辑
    
    # 示例：模块共鸣的属性提升可能影响伤害
    if "mokuai_gongming_active" in effect_data:
        # 模块共鸣激活时，可能有隐含的增伤效果
        if effect_data["mokuai_gongming_active"]:
            # 假设模块共鸣提供5%的技能增伤
            modifiers.skill_damage_multipliers.append(0.05)
    
    # 示例：构装机甲的特殊状态
    if "gouzhuang_jijia_enhanced" in effect_data:
        if effect_data["gouzhuang_jijia_enhanced"]:
            # 构装机甲增强状态下提供伤害加深
            modifiers.damage_amplify_multipliers.append(0.08)
    
    # 示例：通灵状态的增伤
    if "tongling_enhanced_damage" in effect_data:
        bonus = effect_data["tongling_enhanced_damage"]
        if bonus > 0:
            modifiers.skill_damage_multipliers.append(bonus)
    
    # 示例：神格等级影响（如果有的话）
    if "shenge_damage_bonus" in effect_data:
        bonus = effect_data["shenge_damage_bonus"]
        if bonus > 0:
            modifiers.flat_damage_bonus += bonus

def calculate_pzc(modifiers: DamageModifiers) -> float:
    """
    计算普攻增伤乘法系数 (PZC)
    PZC = ∏(1+技能增伤) * ∏(1+伤害加深) * ∏(1-技能减伤) * ...
    """
    pzc = 1.0
    
    # ∏(1+技能增伤)
    for multiplier in modifiers.skill_damage_multipliers:
        pzc *= (1.0 + multiplier)
    
    # ∏(1+伤害加深)  
    for amplify in modifiers.damage_amplify_multipliers:
        pzc *= (1.0 + amplify)
    
    # ∏(1-技能减伤)
    for reduction in modifiers.skill_damage_reductions:
        pzc *= (1.0 - reduction)
    
    # 其他乘法因子（可扩展）
    # 制衡协战增伤
    if modifiers.synergy_damage_bonus is not None:
        pzc *= (1.0 + modifiers.synergy_damage_bonus)
    
    # 套装减伤效果
    if modifiers.equipment_damage_reduction is not None:
        pzc *= (1.0 - modifiers.equipment_damage_reduction)
    
    # 羁绊减伤
    if modifiers.bond_damage_reduction is not None:
        pzc *= (1.0 - modifiers.bond_damage_reduction)
    
    return max(0.1, pzc)  # 最低保持10%伤害

def calculate_czc(modifiers: DamageModifiers) -> float:
    """
    计算超杀增伤乘法系数 (CZC)
    CZC = ∏(1+技能增伤) * ∏(1+伤害加深) * ∏(1-技能减伤) * ...
    
    注意：CZC和PZC计算方式完全相同，只是适用的技能类型不同
    """
    return calculate_pzc(modifiers)

def calculate_pzj(modifiers: DamageModifiers) -> float:
    """
    计算普攻增伤加法系数 (PZJ)
    PZJ = (1 + 各种加法增伤 - 各种加法减伤)
    """
    pzj = 1.0 + modifiers.flat_damage_bonus - modifiers.flat_damage_reduction
    
    # 其他加法因子（可扩展）
    # 装备增伤
    if modifiers.equipment_damage_bonus is not None:
        pzj += modifiers.equipment_damage_bonus
    
    # 神格加伤
    if modifiers.divine_damage_bonus is not None:
        pzj += modifiers.divine_damage_bonus
    
    # 技能特殊增伤
    if modifiers.special_skill_bonus is not None:
        pzj += modifiers.special_skill_bonus
    
    # 减伤效果
    if modifiers.equipment_damage_penalty is not None:
        pzj -= modifiers.equipment_damage_penalty
    
    return max(0.1, pzj)  # 最低保持10%伤害

def calculate_czj(modifiers: DamageModifiers) -> float:
    """
    计算超杀增伤加法系数 (CZJ)  
    CZJ = (1 + 各种加法增伤 - 各种加法减伤)
    """
    czj = 1.0 + modifiers.flat_damage_bonus - modifiers.flat_damage_reduction
    
    # 超杀特有的加法因子（可扩展）
    # 超杀专属增伤
    if modifiers.ultimate_exclusive_bonus is not None:
        czj += modifiers.ultimate_exclusive_bonus
    
    # 超杀套装增伤
    if modifiers.ultimate_equipment_bonus is not None:
        czj += modifiers.ultimate_equipment_bonus
    # + 灵纹超增 + 弑电套装增伤 + 亘古套装增伤 + 宠物超增
    # + 装备增伤 + 神格加伤 + 英雄技增伤 + 契约技能超增
    # - 灵纹超减 - 宠物超减 - 装备减伤
    
    return max(0.1, czj)  # 最低保持10%伤害

def calculate_actual_defense(target, modifiers: DamageModifiers, damage_type: str) -> float:
    """
    计算实际防御值
    实防 = 防 * (1 + ∑加防 + 神格加防 + 协战加防 - ∑减防 - 降全属性) 
         * (1 - 技能无视防御) * (1 - 破刃无视防御)
    """
    # 获取基础防御
    if damage_type == "DESTRUCTION":
        base_defense = 0.0  # 毁灭伤害无视防御
    elif damage_type == "MAGIC":
        base_defense = getattr(target.attributes, 'mdef', 0.0)
    else:
        base_defense = getattr(target.attributes, 'pdef', 0.0)
    
    if base_defense <= 0:
        return 0.0
    
    # 计算防御加成
    defense_bonus = 1.0
    
    # 防御增益
    if hasattr(target, 'get_defense_bonus'):
        defense_bonus += target.get_defense_bonus()
    
    # 神格加防
    if hasattr(target.attributes, 'divine_defense_bonus'):
        defense_bonus += target.attributes.divine_defense_bonus
    
    # 协战加防
    if hasattr(target, 'get_synergy_defense_bonus'):
        defense_bonus += target.get_synergy_defense_bonus()
    
    # 防御减益
    if hasattr(target, 'get_defense_penalty'):
        defense_bonus -= target.get_defense_penalty()
    
    # 应用防御加成
    actual_defense = base_defense * defense_bonus
    
    # 应用无视防御效果
    ignore_defense_factor = 1.0 - min(0.9, modifiers.ignore_defense_percent)  # 最多无视90%
    armor_break_factor = 1.0 - min(0.9, modifiers.armor_break_percent)  # 最多无视90%
    
    actual_defense *= ignore_defense_factor * armor_break_factor
    
    return max(0.0, actual_defense)


def calculate_indirect_damage_defense(target, modifiers: Optional[DamageModifiers], damage_type) -> float:
    """
    计算非直接伤害的防御值
    非直接伤害不受常规防御、增伤、减伤影响，只受到专门的非直接伤害减少影响
    """
    from ...action import DamageType

    # 如果不是DamageType枚举，转换为字符串处理
    if isinstance(damage_type, DamageType):
        damage_type_str = damage_type.value
    else:
        damage_type_str = str(damage_type)

    # 非直接伤害减少值（专门针对非直接伤害的减免）
    indirect_damage_reduction = 0.0

    # 获取通用非直接伤害减少
    if hasattr(target.attributes, 'indirect_damage_reduction'):
        indirect_damage_reduction += getattr(target.attributes, 'indirect_damage_reduction', 0.0)

    # 根据不同的非直接伤害类型获取特定抗性
    if damage_type_str in ["火焰", "灼烧"]:
        # 火焰伤害只受到火焰抗性影响
        fire_resistance = getattr(target.attributes, 'fire_resistance', 0.0)
        indirect_damage_reduction += fire_resistance
    elif damage_type_str in ["冰霜"]:
        # 冰霜伤害只受到冰霜抗性影响
        ice_resistance = getattr(target.attributes, 'ice_resistance', 0.0)
        indirect_damage_reduction += ice_resistance
    elif damage_type_str in ["雷电"]:
        # 雷电伤害只受到雷电抗性影响
        lightning_resistance = getattr(target.attributes, 'lightning_resistance', 0.0)
        indirect_damage_reduction += lightning_resistance
    elif damage_type_str in ["毒素"]:
        # 毒素伤害只受到毒素抗性影响
        poison_resistance = getattr(target.attributes, 'poison_resistance', 0.0)
        indirect_damage_reduction += poison_resistance
    elif damage_type_str in ["流血"]:
        # 流血伤害只受到流血抗性影响
        bleed_resistance = getattr(target.attributes, 'bleed_resistance', 0.0)
        indirect_damage_reduction += bleed_resistance
    elif damage_type_str in ["诅咒"]:
        # 诅咒伤害只受到诅咒抗性影响
        curse_resistance = getattr(target.attributes, 'curse_resistance', 0.0)
        indirect_damage_reduction += curse_resistance
    elif damage_type_str in ["暗影"]:
        # 暗影伤害只受到暗影抗性影响
        dark_resistance = getattr(target.attributes, 'dark_resistance', 0.0)
        indirect_damage_reduction += dark_resistance
    elif damage_type_str in ["神圣"]:
        # 神圣伤害只受到神圣抗性影响
        holy_resistance = getattr(target.attributes, 'holy_resistance', 0.0)
        indirect_damage_reduction += holy_resistance
    elif damage_type_str in ["精神"]:
        # 精神伤害只受到精神抗性影响
        psychic_resistance = getattr(target.attributes, 'psychic_resistance', 0.0)
        indirect_damage_reduction += psychic_resistance
    elif damage_type_str in ["自然"]:
        # 自然伤害只受到自然抗性影响
        nature_resistance = getattr(target.attributes, 'nature_resistance', 0.0)
        indirect_damage_reduction += nature_resistance
    elif damage_type_str in ["音波"]:
        # 音波伤害只受到音波抗性影响
        sonic_resistance = getattr(target.attributes, 'sonic_resistance', 0.0)
        indirect_damage_reduction += sonic_resistance

    # 获取来自效果的非直接伤害减少
    if hasattr(target, 'get_indirect_damage_reduction'):
        effect_reduction = target.get_indirect_damage_reduction(damage_type_str)
        indirect_damage_reduction += effect_reduction

    # 限制最大减免为90%
    indirect_damage_reduction = min(0.9, indirect_damage_reduction)

    # 返回减免比例（用于后续计算）
    return indirect_damage_reduction


def calculate_element_coefficient(caster_element: str, target_element: str, caster) -> float:
    """计算元素克制系数 K"""
    # 元素克制关系
    ELEMENTAL_ADVANTAGE = {
        "FIRE": ["GRASS"],
        "WATER": ["FIRE"], 
        "GRASS": ["WATER"],
        "LIGHT": ["DARK"],
        "DARK": ["LIGHT"],
        "AIR": ["LIGHT", "DARK"],
        "CREATION": ["WATER", "FIRE", "GRASS"],
    }
    
    if not caster_element or not target_element or caster_element == "NONE" or target_element == "NONE":
        return 1.0
    
    if target_element in ELEMENTAL_ADVANTAGE.get(caster_element, []):
        # 克制：1.3 * (1 + 克制加成)
        suppress_bonus = getattr(caster.attributes, 'suppress_bonus', 0.0)
        return 1.3 * (1.0 + suppress_bonus)
    elif caster_element in ELEMENTAL_ADVANTAGE.get(target_element, []):
        # 被克制：0.7
        return 0.7
    else:
        # 无克制关系：1.0
        return 1.0

def calculate_crit_coefficient(caster, target, modifiers: DamageModifiers) -> Tuple[bool, float]:
    """计算暴击系数 B"""
    # 计算暴击率
    base_crit_rate = getattr(caster.attributes, 'crit_rate', 0.0)
    crit_res_rate = getattr(target.attributes, 'crit_res_rate', 0.0)
    
    final_crit_rate = max(0.0, min(1.0, base_crit_rate + modifiers.crit_rate_bonus - crit_res_rate))
    
    # 暴击判定
    crit_roll = random.random()
    is_crit = crit_roll < final_crit_rate
    
    if is_crit:
        # 暴击伤害：1.5 * (1 + 暴击伤害加成)
        crit_damage_extra = getattr(caster.attributes, 'crit_damage_extra', 0.0)
        crit_coeff = 1.5 * (1.0 + crit_damage_extra)
    else:
        crit_coeff = 1.0
    
    return is_crit, crit_coeff

def calculate_momentum_coefficient(caster, is_ultimate: bool, overflow_energy: Optional[int] = None, ultimate_threshold: Optional[int] = None) -> float:
    """计算气势系数 Q (仅超杀技能)"""
    if not is_ultimate:
        return 1.0

    # 获取超杀阈值
    if ultimate_threshold is not None:
        threshold = ultimate_threshold
    elif hasattr(caster, 'ultimate_manager') and hasattr(caster.ultimate_manager, 'get_lowest_threshold'):
        threshold = caster.ultimate_manager.get_lowest_threshold()
    else:
        threshold = 300  # 默认阈值

    if threshold <= 0:
        return 1.0

    if overflow_energy is not None:
        actual_overflow = overflow_energy
    else:
        current_energy = getattr(caster, 'energy', 0)
        # 修正：溢出气势 = 当前气势 - 超杀阈值
        actual_overflow = max(0, current_energy - threshold)

    # Q = 1 + (溢出气势 / 超杀阈值)
    q_coeff = 1.0 + (actual_overflow / threshold)

    return q_coeff

def calculate_hit_and_dodge(caster, target, modifiers_caster: DamageModifiers, modifiers_target: DamageModifiers) -> Tuple[bool, bool]:
    """计算命中和闪避"""
    # 默认命中率为100%，attributes中的hit_rate作为加成
    default_hit_rate = 1.0  # 100%基础命中率
    hit_rate_bonus = getattr(caster.attributes, 'hit_rate', 0.0)  # 命中率加成

    # 默认闪避率，attributes中的dodge_rate作为加成
    default_dodge_rate = 0.0  # 0%基础闪避率
    dodge_rate_bonus = getattr(target.attributes, 'dodge_rate', 0.0)  # 闪避率加成

    # 计算最终命中率 = 默认命中率 + 命中加成 + 修正器加成 - 目标闪避率 - 闪避加成 - 修正器闪避加成
    final_hit_rate = max(0.05, min(0.95,
        default_hit_rate + hit_rate_bonus + modifiers_caster.hit_rate_bonus
        - default_dodge_rate - dodge_rate_bonus - modifiers_target.dodge_rate_bonus))

    # 进行命中判定
    hit_roll = random.random()
    is_hit = hit_roll <= final_hit_rate

    # 如果命中，则没有被闪避；如果未命中，则被闪避
    is_dodged = not is_hit

    return is_hit, is_dodged

def calculate_formula_damage(
    caster,
    target, 
    action,
    battle_state,
) -> Tuple[int, Dict[str, Any]]:
    """
    基于完整公式的伤害计算
    
    普攻伤害 = [实攻-实防] * 技能倍率 * PZC * PZJ * K * B * F
    超杀伤害 = [实攻-实防] * 技能倍率 * CZC * CZJ * K * B * Q * F
    """
    
    # 提取伤害修正系数
    caster_modifiers = extract_damage_modifiers(caster)
    target_modifiers = extract_damage_modifiers(target)
    
    # 构建计算详情
    breakdown = {
        "calculation_steps": [],
        "caster_modifiers": caster_modifiers.to_dict(),  # 序列化修正系数
        "target_modifiers": target_modifiers.to_dict(),  # 序列化修正系数
        "calculation_metadata": {
            "formula_version": "1.0",
            "calculation_time": None,  # 可以添加时间戳
            "damage_type": None,  # 将在后面设置
            "is_ultimate": None,  # 将在后面设置
        }
    }
    
    # 1. 命中和闪避判定
    is_hit, is_dodged = calculate_hit_and_dodge(caster, target, caster_modifiers, target_modifiers)
    breakdown["is_hit"] = is_hit
    breakdown["is_dodged"] = is_dodged
    breakdown["calculation_steps"].append(f"命中判定: {'命中' if is_hit else '未命中'}")
    
    if not is_hit or is_dodged:
        breakdown["final_damage"] = 0
        breakdown["calculation_steps"].append("伤害为0（未命中或闪避）")
        return 0, breakdown
    
    # 2. 获取基础数据
    # 伤害计算完全基于实攻，不再依赖技能组件的预计算
    actual_attack = caster.attributes.get_actual_attack(caster)
    damage_type = getattr(action, 'damage_type', 'PHYSICAL')
    skill_multiplier = getattr(action, 'power_multiplier', 1.0)
    is_ultimate = getattr(action, 'is_ultimate', False)
    is_indirect = getattr(action, 'is_indirect', False)

    # 更新计算元数据
    breakdown["calculation_metadata"]["damage_type"] = str(damage_type)
    breakdown["calculation_metadata"]["is_ultimate"] = is_ultimate
    breakdown["calculation_metadata"]["is_indirect"] = is_indirect

    # 检查是否为非直接伤害类型
    from ...action import DamageType
    if isinstance(damage_type, DamageType) and DamageType.is_indirect_damage_type(damage_type):
        is_indirect = True

    # 3. 如果是非直接伤害，使用专门的计算函数
    if is_indirect:
        return calculate_indirect_damage(caster, target, action, battle_state)

    # 4. 检查是否为毁灭伤害
    is_destruction = (isinstance(damage_type, DamageType) and damage_type == DamageType.DESTRUCTION) or \
                    (isinstance(damage_type, str) and damage_type.upper() == "DESTRUCTION")

    # 5. 计算实际防御（毁灭伤害无视防御）
    if is_destruction:
        actual_defense = 0.0  # 毁灭伤害无视防御
        breakdown["calculation_steps"].append("毁灭伤害: 无视防御")
    else:
        # 转换damage_type为字符串格式
        damage_type_str = damage_type.value if isinstance(damage_type, DamageType) else str(damage_type)
        actual_defense = calculate_actual_defense(target, caster_modifiers, damage_type_str)

    # 序列化基础数据
    breakdown["actual_attack"] = float(actual_attack)
    breakdown["actual_defense"] = float(actual_defense)
    breakdown["is_indirect"] = bool(is_indirect)
    breakdown["is_destruction"] = bool(is_destruction)
    breakdown["skill_multiplier"] = float(skill_multiplier)

    # 添加详细的计算阶段数据
    breakdown["calculation_phases"] = {
        "phase_1_hit_dodge": {
            "is_hit": is_hit,
            "is_dodged": is_dodged,
            "description": "命中和闪避判定"
        }
    }

    # 6. 基础伤害：[实攻-实防]
    if is_destruction:
        # 毁灭伤害直接使用实攻作为基础伤害
        base_damage = max(1.0, actual_attack)
        breakdown["calculation_steps"].append(f"毁灭伤害基础: {actual_attack:.1f} (无视防御)")
    elif actual_attack <= 0:
        base_damage = 1.0
    elif actual_defense / actual_attack > 0.9:
        # 防御过高替代公式
        base_damage = 0.2 * actual_attack * (1 - actual_defense / (actual_attack + actual_defense))
    else:
        base_damage = max(1.0, actual_attack - actual_defense)
    
    breakdown["base_damage"] = float(base_damage)
    breakdown["calculation_steps"].append(f"基础伤害 [实攻-实防]: {actual_attack:.1f} - {actual_defense:.1f} = {base_damage:.1f}")

    # 添加基础伤害阶段数据
    breakdown["calculation_phases"]["phase_2_base_damage"] = {
        "actual_attack": float(actual_attack),
        "actual_defense": float(actual_defense),
        "base_damage": float(base_damage),
        "description": "基础伤害计算 [实攻-实防]"
    }

    # 5. 技能倍率
    damage = base_damage * skill_multiplier
    breakdown["calculation_steps"].append(f"技能倍率: {base_damage:.1f} × {skill_multiplier} = {damage:.1f}")

    # 添加技能倍率阶段数据
    breakdown["calculation_phases"]["phase_3_skill_multiplier"] = {
        "skill_multiplier": float(skill_multiplier),
        "damage_after_multiplier": float(damage),
        "description": "应用技能倍率"
    }
    
    # 6. 计算PZC/CZC和PZJ/CZJ - 优先使用Attributes类的统一方法
    try:
        if is_ultimate:
            zc_coeff = caster.attributes.get_czc(caster)  # 使用Attributes类的方法
            zj_coeff = caster.attributes.get_czj(caster)
            breakdown["calculation_steps"].append(f"超杀增伤: CZC={zc_coeff:.3f}, CZJ={zj_coeff:.3f}")
        else:
            zc_coeff = caster.attributes.get_pzc(caster)  # 使用Attributes类的方法
            zj_coeff = caster.attributes.get_pzj(caster)
            breakdown["calculation_steps"].append(f"普攻增伤: PZC={zc_coeff:.3f}, PZJ={zj_coeff:.3f}")
    except (AttributeError, ImportError):
        # 降级到原有的计算方法
        if is_ultimate:
            zc_coeff = calculate_czc(caster_modifiers)  # CZC
            zj_coeff = calculate_czj(caster_modifiers)  # CZJ
            breakdown["calculation_steps"].append(f"超杀增伤(降级): CZC={zc_coeff:.3f}, CZJ={zj_coeff:.3f}")
        else:
            zc_coeff = calculate_pzc(caster_modifiers)  # PZC
            zj_coeff = calculate_pzj(caster_modifiers)  # PZJ
            breakdown["calculation_steps"].append(f"普攻增伤(降级): PZC={zc_coeff:.3f}, PZJ={zj_coeff:.3f}")
    
    damage *= zc_coeff * zj_coeff
    breakdown["zc_coeff"] = float(zc_coeff)
    breakdown["zj_coeff"] = float(zj_coeff)
    breakdown["calculation_steps"].append(f"增伤系数: × {zc_coeff:.3f} × {zj_coeff:.3f} = {damage:.1f}")

    # 添加增伤系数阶段数据
    breakdown["calculation_phases"]["phase_4_damage_modifiers"] = {
        "zc_coeff": float(zc_coeff),
        "zj_coeff": float(zj_coeff),
        "damage_after_modifiers": float(damage),
        "modifier_type": "超杀增伤" if is_ultimate else "普攻增伤",
        "description": "应用增伤系数 (PZC/CZC × PZJ/CZJ)"
    }
    
    # 7. 元素克制系数 K
    caster_element = getattr(caster.metadata, 'element', None)
    target_element = getattr(target.metadata, 'element', None)
    caster_element_str = caster_element.value if caster_element else None
    target_element_str = target_element.value if target_element else None
    
    k_coeff = calculate_element_coefficient(
        caster_element=cast(str, caster_element_str) or "NONE", 
        target_element=cast(str, target_element_str) or "NONE", 
        caster=caster
    )
    damage *= k_coeff
    breakdown["k_coeff"] = float(k_coeff)
    breakdown["calculation_steps"].append(f"元素克制: × {k_coeff:.3f} = {damage:.1f}")

    # 添加元素克制阶段数据
    breakdown["calculation_phases"]["phase_5_element_coefficient"] = {
        "caster_element": caster_element_str or "NONE",
        "target_element": target_element_str or "NONE",
        "k_coeff": float(k_coeff),
        "damage_after_element": float(damage),
        "description": "应用元素克制系数"
    }

    # 8. 暴击系数 B
    is_crit, b_coeff = calculate_crit_coefficient(caster, target, caster_modifiers)
    damage *= b_coeff
    breakdown["is_crit"] = bool(is_crit)
    breakdown["b_coeff"] = float(b_coeff)
    breakdown["calculation_steps"].append(f"暴击系数: × {b_coeff:.3f} = {damage:.1f}")

    # 添加暴击阶段数据
    breakdown["calculation_phases"]["phase_6_crit_coefficient"] = {
        "is_crit": bool(is_crit),
        "b_coeff": float(b_coeff),
        "damage_after_crit": float(damage),
        "description": "应用暴击系数"
    }
    
    # 9. 气势系数 Q (仅超杀)
    if is_ultimate:
        overflow_energy = getattr(action, 'overflow_energy', None)
        ultimate_threshold = getattr(action, 'ultimate_threshold', None)
        q_coeff = calculate_momentum_coefficient(caster, is_ultimate, overflow_energy, ultimate_threshold)
        damage *= q_coeff
        breakdown["q_coeff"] = float(q_coeff)
        breakdown["calculation_steps"].append(f"气势系数: × {q_coeff:.3f} = {damage:.1f}")

        # 添加气势系数阶段数据
        breakdown["calculation_phases"]["phase_7_momentum_coefficient"] = {
            "q_coeff": float(q_coeff),
            "damage_after_momentum": float(damage),
            "overflow_energy": overflow_energy,
            "ultimate_threshold": ultimate_threshold,
            "description": "应用气势系数 (仅超杀技能)"
        }

    # 10. 其他修正 F (格挡、溅射等)
    f_coeff = 1.0

    # 格挡判定（毁灭伤害无视格挡）
    if not is_destruction:
        base_block_rate = getattr(target.attributes, 'block_rate', 0.0)
        base_break_rate = getattr(caster.attributes, 'break_rate', 0.0)
        block_chance = max(0.0, min(0.8, base_block_rate - base_break_rate))

        block_roll = random.random()
        is_blocked = block_roll < block_chance

        if is_blocked:
            f_coeff *= 0.5
            breakdown["calculation_steps"].append("格挡减伤: × 0.5")
    else:
        breakdown["calculation_steps"].append("毁灭伤害: 无视格挡")
    
    # 溅射减伤
    if getattr(action, 'is_splash', False):
        f_coeff *= 0.5
        breakdown["calculation_steps"].append("溅射减伤: × 0.5")
    
    damage *= f_coeff
    breakdown["f_coeff"] = float(f_coeff)

    # 添加其他修正阶段数据
    breakdown["calculation_phases"]["phase_8_other_modifiers"] = {
        "f_coeff": float(f_coeff),
        "damage_after_modifiers": float(damage),
        "block_applied": f_coeff < 1.0,
        "splash_applied": getattr(action, 'is_splash', False),
        "description": "应用其他修正 (格挡、溅射等)"
    }

    # 11. 最终伤害
    final_damage = max(1, int(math.floor(damage)))
    breakdown["final_damage"] = int(final_damage)
    breakdown["calculation_steps"].append(f"最终伤害: {final_damage}")

    # 添加最终结果阶段数据
    breakdown["calculation_phases"]["phase_9_final_result"] = {
        "damage_before_floor": float(damage),
        "final_damage": int(final_damage),
        "minimum_damage_applied": damage < 1.0,
        "description": "最终伤害计算 (取整，最小值1)"
    }
    
    return final_damage, breakdown

# 兼容性函数 - 替换原有的计算函数
def calculate_damage_with_dodge(caster, target, action, battle_state) -> Tuple[int, Dict[str, Any]]:
    """兼容性函数 - 使用新的公式计算器"""
    return calculate_formula_damage(caster, target, action, battle_state)

def calculate_damage(caster, target, action, battle_state) -> Tuple[int, Dict[str, Any]]:
    """兼容性函数 - 使用新的公式计算器"""
    return calculate_formula_damage(caster, target, action, battle_state)


def calculate_indirect_damage(
    caster,
    target,
    action,
    battle_state,
) -> Tuple[int, Dict[str, Any]]:
    """
    计算非直接伤害

    非直接伤害不受常规增伤、减伤、防御影响，只受到专门的非直接伤害减少影响
    计算公式：非直接伤害 = 实攻 * 技能倍率 * (1 - 非直接伤害减免)
    """

    # 构建计算详情
    breakdown = {
        "calculation_steps": [],
        "is_indirect": True,
        "damage_type": str(getattr(action, 'damage_type', 'INDIRECT')),
        "calculation_metadata": {
            "formula_version": "1.0",
            "calculation_type": "indirect_damage",
            "damage_type": str(getattr(action, 'damage_type', 'INDIRECT')),
        },
        "calculation_phases": {}
    }

    # 1. 获取基础数据
    actual_attack = caster.attributes.get_actual_attack(caster)
    skill_multiplier = getattr(action, 'power_multiplier', 1.0)
    damage_type = getattr(action, 'damage_type', 'INDIRECT')

    breakdown["actual_attack"] = float(actual_attack)
    breakdown["skill_multiplier"] = float(skill_multiplier)
    breakdown["calculation_steps"].append(f"实攻: {actual_attack}")
    breakdown["calculation_steps"].append(f"技能倍率: {skill_multiplier}")

    # 添加基础数据阶段
    breakdown["calculation_phases"]["phase_1_base_data"] = {
        "actual_attack": float(actual_attack),
        "skill_multiplier": float(skill_multiplier),
        "damage_type": str(damage_type),
        "description": "获取基础数据"
    }

    # 2. 基础伤害计算：实攻 * 技能倍率
    base_damage = actual_attack * skill_multiplier
    breakdown["base_damage"] = float(base_damage)
    breakdown["calculation_steps"].append(f"基础伤害: {actual_attack} × {skill_multiplier} = {base_damage}")

    # 添加基础伤害阶段
    breakdown["calculation_phases"]["phase_2_base_damage"] = {
        "base_damage": float(base_damage),
        "calculation": f"{actual_attack} × {skill_multiplier}",
        "description": "计算基础伤害 (实攻 × 技能倍率)"
    }

    # 3. 计算非直接伤害减免
    # 非直接伤害不受常规修正影响，直接传入None
    indirect_damage_reduction = calculate_indirect_damage_defense(target, None, damage_type)
    breakdown["indirect_damage_reduction"] = float(indirect_damage_reduction)
    breakdown["calculation_steps"].append(f"非直接伤害减免: {indirect_damage_reduction*100:.1f}%")

    # 添加减免计算阶段
    breakdown["calculation_phases"]["phase_3_damage_reduction"] = {
        "indirect_damage_reduction": float(indirect_damage_reduction),
        "reduction_percentage": float(indirect_damage_reduction * 100),
        "damage_type": str(damage_type),
        "description": "计算非直接伤害减免"
    }

    # 4. 应用减免
    final_damage = base_damage * (1 - indirect_damage_reduction)
    breakdown["final_damage_before_floor"] = float(final_damage)
    breakdown["calculation_steps"].append(f"应用减免: {base_damage} × (1 - {indirect_damage_reduction:.3f}) = {final_damage}")

    # 添加减免应用阶段
    breakdown["calculation_phases"]["phase_4_apply_reduction"] = {
        "damage_before_reduction": float(base_damage),
        "reduction_factor": float(1 - indirect_damage_reduction),
        "damage_after_reduction": float(final_damage),
        "description": "应用非直接伤害减免"
    }

    # 5. 确保最小伤害为1
    final_damage = max(1, int(math.floor(final_damage)))
    breakdown["final_damage"] = int(final_damage)
    breakdown["calculation_steps"].append(f"最终伤害（取整）: {final_damage}")

    # 添加最终结果阶段
    breakdown["calculation_phases"]["phase_5_final_result"] = {
        "damage_before_floor": float(breakdown["final_damage_before_floor"]),
        "final_damage": int(final_damage),
        "minimum_damage_applied": breakdown["final_damage_before_floor"] < 1.0,
        "description": "最终伤害计算 (取整，最小值1)"
    }

    return final_damage, breakdown


@monitor_battle_performance
def calculate_healing(
    caster,
    is_ultimate: bool,
    power_multiplier: float = 1.0,
    can_crit: bool = False,
) -> int:
    """
    根据公式计算治疗量。

    治疗量 = 实攻 × 技能倍率 × (1 + 技能增伤) × Q × 暴击系数

    Args:
        caster: 施放治疗的精灵。
        is_ultimate: 技能是否为超杀技能。
        power_multiplier: 技能的治疗倍率。
        can_crit: 本次治疗是否可以暴击。

    Returns:
        计算出的最终治疗量。
    """
    base_heal = caster.attributes.attack * power_multiplier

    # 技能增伤
    base_heal *= (1 + caster.attributes.skill_bonus)

    # 气势系数 Q
    q_coeff = calculate_momentum_coefficient(caster, is_ultimate)
    base_heal *= q_coeff

    # 暴击（可选）
    if can_crit and random.random() < caster.attributes.crit_rate:
        base_heal *= 1.5 * (1 + caster.attributes.crit_damage_extra)

    return max(1, int(base_heal))