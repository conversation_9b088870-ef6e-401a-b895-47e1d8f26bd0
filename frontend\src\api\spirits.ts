import type { SpiritPrototype } from '../types/spirit';

const API_BASE_URL = 'http://localhost:8000'; // 或者您的后端API地址

/**
 * 从后端获取所有精灵原型数据。
 * @returns 返回一个包含所有精灵原型数据的Promise。
 */
export async function getSpirits(): Promise<SpiritPrototype[]> {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  try {
    console.log(`[API请求] GET /api/spirits (ID: ${requestId})`)
    
    const response = await fetch(`${API_BASE_URL}/api/spirits`, {
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId
      }
    });
    
    if (!response.ok) {
      const errorData = await response.text()
      console.error(`[API错误] GET /api/spirits (ID: ${requestId})`, {
        status: response.status,
        statusText: response.statusText,
        data: errorData
      })
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
    }
    
    const data: SpiritPrototype[] = await response.json();
    console.log(`[API成功] GET /api/spirits (ID: ${requestId}), 获取到 ${data.length} 个精灵`)
    return data;
  } catch (error: any) {
    console.error(`[精灵API错误] (ID: ${requestId})`, {
      message: error.message,
      error: error
    });
    return []; // 在出错时返回空数组
  }
} 