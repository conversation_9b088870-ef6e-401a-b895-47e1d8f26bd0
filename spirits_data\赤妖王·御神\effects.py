"""
赤妖王·御神 - 基础效果模块

包含御神使用的特殊效果类：
- SpiritWisdomBattleBoostEffect: 灵目慧心战斗加成效果
- ConditionalSurvivalEffect: 条件生存效果
- DynamicDamageReductionEffect: 动态减伤效果
- ReviveAllyEffect: 复活队友效果
"""
from __future__ import annotations
from typing import List, Optional, TYPE_CHECKING, Dict, Any
import uuid

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition, BeforeAttackCondition
from core.status import battle_status_checker


class SpiritWisdomBattleBoostEffect(IEffect):
    """灵目慧心战斗加成效果 - 攻击无法行动精灵时触发"""

    def __init__(self, duration: int = 3, caster=None):
        super().__init__(
            effect_id=f"spirit_wisdom_boost_{uuid.uuid4().hex}",
            name="灵目慧心战斗加成",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.BUFF,
            priority=EffectPriority.HIGH,
            duration=duration
        )
        
        self.caster = caster
        self.owner = None

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        return [
            BeforeAttackCondition()  # 攻击前触发
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: IBattleState) -> EffectResult:
        """处理攻击无法行动精灵时的加成"""
        if event_data.get("event_type") != "BEFORE_ATTACK":
            return EffectResult.success()

        # 检查攻击者是否为效果拥有者
        attacker = event_data.get("attacker") or event_data.get("source")  # 兼容两种字段名
        if attacker != self.owner:
            return EffectResult.success("不是效果拥有者的攻击")

        # 检查目标是否无法行动
        target = event_data.get("target")
        if not target or not battle_status_checker.status_checker.is_unable_to_act(target):
            return EffectResult.success("目标可以行动")

        from core.action import LogAction

        actions = []

        # 获得30点气势
        if hasattr(self.owner, 'energy'):
            max_energy = getattr(self.owner, 'max_energy', 300)
            current_energy = getattr(self.owner, 'energy', 0)
            setattr(self.owner, 'energy', min(current_energy + 30, max_energy))

        target_name = getattr(target, 'name', '目标')
        owner_name = getattr(self.owner, 'name', '精灵')

        log_action = LogAction(
            caster=self.caster,
            message=f"👁️ {owner_name} 攻击无法行动的 {target_name}，获得战斗加成！暴击率+40%、暴击伤害+40%、破击率+40%，获得30点气势！"
        )
        actions.append(log_action)

        return EffectResult.success_with_actions(actions, "灵目慧心战斗加成触发")

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        self.owner = target
        return EffectResult.success_with_data(
            {"applied": True}, 
            f"{target.name} 获得灵目慧心战斗加成效果"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{target.name} 失去灵目慧心战斗加成效果"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data({}, "灵目慧心战斗加成效果更新")

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "攻击无法行动精灵时获得战斗加成",
            "duration": self.remaining_duration if self.duration > 0 else -1
        }


class ConditionalSurvivalEffect(IEffect):
    """条件生存效果 - 狐念之力的生存机制（简化版）"""

    def __init__(self, owner_spirit, duration: int = -1):
        super().__init__(
            effect_id=f"conditional_survival_{owner_spirit.id}",
            name="狐念之力生存",
            effect_type=EffectType.PASSIVE,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=duration
        )

        self.owner_spirit = owner_spirit

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        return EffectResult.success_with_data(
            {"applied": True},
            f"{getattr(target, 'name', '精灵')} 获得狐念之力条件生存效果"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True},
            f"{getattr(target, 'name', '精灵')} 失去狐念之力条件生存效果"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data({}, "狐念之力条件生存效果更新")

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "敌方有无法行动精灵时，致命伤害改为最大生命值60%",
            "duration": self.remaining_duration if self.duration > 0 else -1
        }


# 导出函数
def create_spirit_wisdom_battle_boost_effect(duration: int = 3, caster=None) -> SpiritWisdomBattleBoostEffect:
    """创建灵目慧心战斗加成效果"""
    return SpiritWisdomBattleBoostEffect(duration, caster)


def create_conditional_survival_effect(owner_spirit, duration: int = -1) -> ConditionalSurvivalEffect:
    """创建条件生存效果"""
    return ConditionalSurvivalEffect(owner_spirit, duration)


# 导出所有效果类
__all__ = [
    'SpiritWisdomBattleBoostEffect',
    'ConditionalSurvivalEffect',
    'create_spirit_wisdom_battle_boost_effect',
    'create_conditional_survival_effect'
]
