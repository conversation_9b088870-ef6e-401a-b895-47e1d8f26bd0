#!/usr/bin/env python3
"""
调试战斗结束问题

分析为什么战斗会立即结束以及胜负判定错误的问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_battle_creation():
    """调试战斗创建过程"""
    print("🔧 调试战斗创建过程...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"✅ 可用精灵: {available_spirits}")
        
        # 创建两个精灵
        spirit1_name = available_spirits[0]
        spirit2_name = available_spirits[1]
        
        spirit1 = spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
        
        print(f"✅ 精灵1创建: {spirit1.name}")
        print(f"  - ID: {spirit1.id}")
        print(f"  - 队伍: {spirit1.team}")
        print(f"  - 生命值: {spirit1.current_hp}/{spirit1.max_hp}")
        print(f"  - 存活状态: {spirit1.is_alive}")
        
        print(f"✅ 精灵2创建: {spirit2.name}")
        print(f"  - ID: {spirit2.id}")
        print(f"  - 队伍: {spirit2.team}")
        print(f"  - 生命值: {spirit2.current_hp}/{spirit2.max_hp}")
        print(f"  - 存活状态: {spirit2.is_alive}")
        
        # 创建阵型
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"✅ 阵型1精灵数: {len(formation1.get_all_spirits())}")
        print(f"✅ 阵型1存活精灵数: {len(formation1.get_living_spirits())}")
        print(f"✅ 阵型2精灵数: {len(formation2.get_all_spirits())}")
        print(f"✅ 阵型2存活精灵数: {len(formation2.get_living_spirits())}")
        
        # 创建战斗状态
        from core.battle.models import BattleState
        battle_state = BattleState(formation1, formation2)
        
        print(f"✅ 战斗状态创建完成")
        print(f"  - 队伍0存活精灵: {len(battle_state.get_living_spirits(0))}")
        print(f"  - 队伍1存活精灵: {len(battle_state.get_living_spirits(1))}")
        print(f"  - 总存活精灵: {len(battle_state.get_all_living_spirits())}")
        
        # 测试胜负判定
        from core.battle.conditions import KnockoutCondition
        condition_checker = KnockoutCondition()
        
        winner = condition_checker.check_battle_end(battle_state)
        print(f"✅ 胜负判定结果: {winner}")
        
        if winner is not None:
            print("❌ 战斗在创建时就结束了！")
            print("🔍 详细分析:")
            
            # 检查每个精灵的详细状态
            all_spirits = battle_state.get_all_spirits()
            for i, spirit in enumerate(all_spirits):
                print(f"  精灵{i+1}: {spirit.name}")
                print(f"    - 队伍: {spirit.team}")
                print(f"    - 生命值: {spirit.current_hp}/{spirit.max_hp}")
                print(f"    - is_alive: {spirit.is_alive}")
                print(f"    - 位置: {getattr(spirit, 'position', 'Unknown')}")
                
                # 检查健康组件
                if hasattr(spirit, 'components'):
                    from core.components.health_component import HealthComponent
                    health_comp = spirit.components.get_component(HealthComponent)
                    if health_comp:
                        print(f"    - 健康组件HP: {health_comp.current_hp}/{health_comp.max_hp}")
                    else:
                        print(f"    - 无健康组件")
                
                print()
        else:
            print("✅ 战斗状态正常，可以开始")
        
        return spirit1, spirit2, battle_state, condition_checker
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_battle_engine():
    """调试战斗引擎"""
    print("\n🔧 调试战斗引擎...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.battle.engines.factory import create_battle_engine
        from core.formation import Formation
        from core.spirit.spirit_service import get_spirit_service
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        # 创建阵型
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=10,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗引擎创建完成")
        print(f"  - 引擎类型: {engine.__class__.__name__}")
        print(f"  - 胜负判定器: {engine.condition_checker.__class__.__name__}")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        print(f"  - 获胜方: {engine.battle_state.winner}")
        
        # 检查初始状态
        print(f"\n📊 初始战斗状态:")
        print(f"  - 队伍0存活: {len(engine.battle_state.get_living_spirits(0))}")
        print(f"  - 队伍1存活: {len(engine.battle_state.get_living_spirits(1))}")
        
        # 检查胜负判定
        winner = engine.condition_checker.check_battle_end(engine.battle_state)
        print(f"  - 胜负判定: {winner}")
        
        if winner is not None:
            print("❌ 引擎创建时战斗就结束了！")
            
            # 详细检查精灵状态
            print("\n🔍 精灵详细状态:")
            for team in [0, 1]:
                spirits = engine.battle_state.get_living_spirits(team)
                print(f"  队伍{team}存活精灵: {len(spirits)}")
                
                all_team_spirits = [s for s in engine.battle_state.get_all_spirits() if s.team == team]
                print(f"  队伍{team}总精灵: {len(all_team_spirits)}")
                
                for spirit in all_team_spirits:
                    print(f"    - {spirit.name}: HP={spirit.current_hp}, alive={spirit.is_alive}")
        
        return engine
        
    except Exception as e:
        print(f"❌ 引擎调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_spirit_creation():
    """调试精灵创建过程"""
    print("\n🔧 调试精灵创建过程...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"✅ 可用精灵: {available_spirits}")
        
        # 逐个创建精灵并检查
        for i, spirit_name in enumerate(available_spirits[:2]):
            print(f"\n📊 创建精灵: {spirit_name}")
            
            spirit = spirit_service.create_spirit(spirit_name, team=i, position=(1, 1))
            
            if spirit:
                print(f"  ✅ 创建成功")
                print(f"    - 名称: {spirit.name}")
                print(f"    - ID: {spirit.id}")
                print(f"    - 队伍: {spirit.team}")
                print(f"    - 最大生命值: {spirit.max_hp}")
                print(f"    - 当前生命值: {spirit.current_hp}")
                print(f"    - 存活状态: {spirit.is_alive}")
                
                # 检查组件
                if hasattr(spirit, 'components'):
                    print(f"    - 组件数量: {len(spirit.components._components)}")
                    
                    from core.components.health_component import HealthComponent
                    health_comp = spirit.components.get_component(HealthComponent)
                    if health_comp:
                        print(f"    - 健康组件: HP={health_comp.current_hp}/{health_comp.max_hp}")
                    else:
                        print(f"    - ❌ 无健康组件")
                
                # 检查属性
                if hasattr(spirit, 'attributes'):
                    print(f"    - 实攻: {getattr(spirit.attributes, 'actual_attack', 'N/A')}")
                    print(f"    - 实防: {getattr(spirit.attributes, 'actual_defense', 'N/A')}")
                
            else:
                print(f"  ❌ 创建失败")
        
    except Exception as e:
        print(f"❌ 精灵创建调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("="*60)
    print("🔧 战斗结束问题调试")
    print("="*60)
    
    # 1. 调试精灵创建
    debug_spirit_creation()
    
    # 2. 调试战斗创建
    result = debug_battle_creation()
    
    # 3. 调试战斗引擎
    engine = debug_battle_engine()
    
    print("\n" + "="*60)
    print("📊 调试总结")
    print("="*60)
    
    if result and len(result) >= 3:
        spirit1, spirit2, battle_state, condition_checker = result[:4]
        
        print(f"精灵1状态: {spirit1.name} - HP:{spirit1.current_hp}, alive:{spirit1.is_alive}")
        print(f"精灵2状态: {spirit2.name} - HP:{spirit2.current_hp}, alive:{spirit2.is_alive}")
        print(f"战斗状态: 队伍0={len(battle_state.get_living_spirits(0))}, 队伍1={len(battle_state.get_living_spirits(1))}")
        
        winner = condition_checker.check_battle_end(battle_state)
        print(f"胜负判定: {winner}")
        
        if winner is not None:
            print("\n🔍 问题分析:")
            print("战斗在创建时就结束了，可能的原因:")
            print("1. 精灵创建时生命值为0")
            print("2. 精灵的is_alive属性错误")
            print("3. 阵型添加精灵失败")
            print("4. 胜负判定逻辑错误")
    
    if engine:
        print(f"\n引擎状态: 回合{engine.battle_state.round_num}, 获胜方:{engine.battle_state.winner}")

if __name__ == "__main__":
    main()
