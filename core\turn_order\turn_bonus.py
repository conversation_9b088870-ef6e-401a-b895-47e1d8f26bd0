"""
回合顺序奖励系统

为按顺位出手的精灵提供气势奖励等机制。
"""
from __future__ import annotations
from typing import TYPE_CHECKING, List, Optional

if TYPE_CHECKING:
    from ..interfaces import IBattleEntity, IBattleState

from ..logging import battle_logger


class TurnOrderBonus:
    """回合顺序奖励管理器"""

    def __init__(self, energy_bonus: int = 50):
        """
        初始化回合顺序奖励

        Args:
            energy_bonus: 顺位出手获得的气势奖励（默认50点）
        """
        self.energy_bonus = energy_bonus
        self.turn_counter = 0  # 回合计数器
        self.original_turn_order = []  # 原始回合顺序
        self.current_turn_index = 0  # 当前顺位索引
        self.spirits_received_bonus = set()  # 本回合已获得顺位奖励的精灵
    
    def set_original_turn_order(self, turn_order: List['IBattleEntity']):
        """
        设置原始回合顺序

        Args:
            turn_order: 原始的回合顺序列表
        """
        self.original_turn_order = [(spirit.id, spirit.name) for spirit in turn_order]
        self.current_turn_index = 0
        self.spirits_received_bonus.clear()
        battle_logger.debug(f"设置原始回合顺序: {[name for _, name in self.original_turn_order]}")

    def reset_turn(self):
        """重置回合状态"""
        self.turn_counter += 1
        self.current_turn_index = 0
        self.spirits_received_bonus.clear()
        battle_logger.debug(f"回合顺序奖励系统：开始第 {self.turn_counter} 回合")

    def check_and_apply_turn_order_bonus(self, spirit: 'IBattleEntity', is_scheduled_turn: bool = True) -> int:
        """
        检查并应用顺位出手奖励

        Args:
            spirit: 行动的精灵
            is_scheduled_turn: 是否为预定的顺位行动（True=顺位，False=立即行动等）

        Returns:
            实际获得的气势奖励
        """
        # 如果不是顺位行动，直接返回0
        if not is_scheduled_turn:
            battle_logger.debug(f"{spirit.name} 非顺位行动（立即行动/额外回合），不获得气势奖励")
            return 0

        # 检查是否为当前顺位的精灵
        if not self._is_current_scheduled_spirit(spirit):
            battle_logger.debug(f"{spirit.name} 不是当前顺位精灵，不获得气势奖励")
            return 0

        # 检查是否已经获得过奖励
        if spirit.id in self.spirits_received_bonus:
            battle_logger.debug(f"{spirit.name} 本回合已获得顺位奖励，不重复给予")
            return 0

        # 检查精灵是否存活
        if not spirit.is_alive:
            return 0

        # 应用气势奖励
        if hasattr(spirit, 'gain_energy'):
            actual_gain = spirit.gain_energy(self.energy_bonus)

            # 记录已获得奖励
            self.spirits_received_bonus.add(spirit.id)

            # 推进顺位索引
            self._advance_turn_index()

            battle_logger.info(
                f"🎯 顺位出手奖励：{spirit.name} 获得 {actual_gain} 点气势 "
                f"(顺位: {self.current_turn_index}, 当前气势: {spirit.energy}/{spirit.max_energy})"
            )

            return actual_gain
        else:
            battle_logger.warning(f"精灵 {spirit.name} 不支持气势系统，无法获得顺位出手奖励")
            return 0

    def _is_current_scheduled_spirit(self, spirit: 'IBattleEntity') -> bool:
        """检查是否为当前顺位的精灵"""
        if self.current_turn_index >= len(self.original_turn_order):
            return False

        expected_spirit_id, expected_name = self.original_turn_order[self.current_turn_index]
        return spirit.id == expected_spirit_id

    def _advance_turn_index(self):
        """推进顺位索引"""
        self.current_turn_index += 1
    
    def apply_bonus_to_action_queue(self, action_queue: List['IBattleEntity']) -> List[int]:
        """
        为整个行动队列应用顺位出手奖励

        Args:
            action_queue: 按顺序排列的行动队列

        Returns:
            每个精灵获得的气势奖励列表
        """
        # 设置原始回合顺序
        self.set_original_turn_order(action_queue)

        bonuses = []
        for spirit in action_queue:
            bonus = self.check_and_apply_turn_order_bonus(spirit, is_scheduled_turn=True)
            bonuses.append(bonus)

        total_bonus = sum(bonuses)
        if total_bonus > 0:
            battle_logger.info(f"🎯 本回合顺位出手奖励总计：{total_bonus} 点气势")

        return bonuses

    def get_turn_statistics(self) -> dict:
        """获取回合统计信息"""
        return {
            'current_turn': self.turn_counter,
            'energy_bonus_per_spirit': self.energy_bonus,
            'spirits_received_bonus': len(self.spirits_received_bonus),
            'total_energy_given_this_turn': len(self.spirits_received_bonus) * self.energy_bonus,
            'current_turn_index': self.current_turn_index,
            'total_scheduled_spirits': len(self.original_turn_order)
        }


class EnhancedTurnOrderStrategy:
    """增强的回合顺序策略，包含奖励机制"""
    
    def __init__(self, base_strategy, bonus_manager: Optional[TurnOrderBonus] = None):
        """
        初始化增强回合顺序策略
        
        Args:
            base_strategy: 基础的回合顺序策略（如 FixedGridTurnOrderStrategy）
            bonus_manager: 奖励管理器
        """
        self.base_strategy = base_strategy
        self.bonus_manager = bonus_manager or TurnOrderBonus()
    
    def create_action_queue(self, state: 'IBattleState') -> List['IBattleEntity']:
        """
        创建行动队列并应用顺位出手奖励
        
        Args:
            state: 战斗状态
            
        Returns:
            行动队列
        """
        # 重置回合状态
        self.bonus_manager.reset_turn()
        
        # 使用基础策略创建行动队列
        action_queue = self.base_strategy.create_action_queue(state)
        
        # 应用顺位出手奖励
        self.bonus_manager.apply_bonus_to_action_queue(action_queue)
        
        return action_queue
    
    def get_bonus_statistics(self) -> dict:
        """获取奖励统计信息"""
        return self.bonus_manager.get_turn_statistics()


# 便捷函数
def create_enhanced_turn_order_strategy(base_strategy=None, energy_bonus: int = 50):
    """
    创建带有顺位出手奖励的回合顺序策略
    
    Args:
        base_strategy: 基础策略，默认使用 FixedGridTurnOrderStrategy
        energy_bonus: 气势奖励，默认50点
        
    Returns:
        增强的回合顺序策略
    """
    if base_strategy is None:
        from . import FixedGridTurnOrderStrategy
        base_strategy = FixedGridTurnOrderStrategy()
    
    bonus_manager = TurnOrderBonus(energy_bonus)
    return EnhancedTurnOrderStrategy(base_strategy, bonus_manager)
