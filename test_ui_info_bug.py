#!/usr/bin/env python3
"""
测试UI中的info变量问题

直接测试可能出现问题的组件
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_spirit_detail_panel():
    """测试精灵详情面板"""
    print("🔧 测试精灵详情面板...")
    
    try:
        # 创建一个模拟的精灵快照
        from ui.ux.models.battle_record import SpiritSnapshot
        
        # 测试没有效果的精灵
        spirit_no_effects = SpiritSnapshot(
            name="测试精灵",
            team=0,
            position=(1, 1),
            current_hp=1000,
            max_hp=1000,
            energy=50,
            max_energy=100,
            is_alive=True,
            effects=[]  # 空效果列表
        )
        
        # 创建精灵详情面板
        import tkinter as tk
        from ui.ux.components.spirit_detail_panel import SpiritDetailPanel
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        panel = SpiritDetailPanel(root)
        
        print("测试无效果精灵...")
        panel.update_spirit(spirit_no_effects)
        print("✅ 无效果精灵测试通过")
        
        # 测试有效果的精灵
        spirit_with_effects = SpiritSnapshot(
            name="测试精灵2",
            team=1,
            position=(3, 1),
            current_hp=800,
            max_hp=1000,
            energy=75,
            max_energy=100,
            is_alive=True,
            effects=[
                {
                    'id': 'effect1',
                    'name': '攻击强化',
                    'type': 'BUFF',
                    'duration': 3,
                    'stacks': 1,
                    'description': '攻击力提升20%'
                }
            ]
        )
        
        print("测试有效果精灵...")
        panel.update_spirit(spirit_with_effects)
        print("✅ 有效果精灵测试通过")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 精灵详情面板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_round_history_panel():
    """测试回合历史面板"""
    print("\n🔧 测试回合历史面板...")
    
    try:
        from ui.ux.models.battle_record import RoundSnapshot, SpiritSnapshot, ActionRecord
        from datetime import datetime
        
        # 创建一个模拟的回合快照（无行动）
        snapshot_no_actions = RoundSnapshot(
            round_num=1,
            timestamp=datetime.now(),
            actions_performed=[]  # 空行动列表
        )
        
        # 创建回合历史面板
        import tkinter as tk
        from ui.ux.components.round_history_panel import RoundHistoryPanel
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        panel = RoundHistoryPanel(root)
        
        print("测试无行动回合...")
        panel.update_actions(snapshot_no_actions)
        print("✅ 无行动回合测试通过")
        
        # 创建一个有行动的回合快照
        action = ActionRecord(
            action_id="action1",
            action_type="attack",
            caster_name="精灵1",
            target_names=["精灵2"],
            description="精灵1攻击精灵2",
            timestamp=datetime.now()
        )
        
        snapshot_with_actions = RoundSnapshot(
            round_num=2,
            timestamp=datetime.now(),
            actions_performed=[action]
        )
        
        print("测试有行动回合...")
        panel.update_actions(snapshot_with_actions)
        print("✅ 有行动回合测试通过")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 回合历史面板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_battle_recorder():
    """测试战斗记录器"""
    print("\n🔧 测试战斗记录器...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建简单的战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        from core.spirit.spirit_service import get_spirit_service
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建精灵和阵型
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 创建战斗记录器
        from ui.ux.models.battle_record import BattleRecorder
        
        recorder = BattleRecorder()
        
        print("测试创建快照...")
        snapshot = recorder.create_snapshot(battle_state, 1)
        print("✅ 快照创建成功")
        
        print(f"快照包含 {len(snapshot.spirits)} 个精灵")
        for name, spirit in snapshot.spirits.items():
            print(f"  - {name}: {len(spirit.effects)} 个效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 战斗记录器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 UI Info变量问题测试")
    print("="*60)
    
    tests = [
        ("精灵详情面板", test_spirit_detail_panel),
        ("回合历史面板", test_round_history_panel),
        ("战斗记录器", test_battle_recorder),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！info变量问题已修复")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
