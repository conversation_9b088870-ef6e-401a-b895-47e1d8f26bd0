"""
女帝宿命之环神曜技 - 完整实现

描述：
- 进入战斗时，获得嘲讽（持续2次攻击）及免疫（持续1次攻击）
- 3级神格：该次免疫时令自身获得50点气势
- 6级神格：通灵时额外触发1次
- 10级神格：每次触发后，嘲讽及免疫的持续时间增加1次攻击
"""
from __future__ import annotations
from typing import List, Dict, Any, Optional, TYPE_CHECKING
import uuid

if TYPE_CHECKING:
    from src.core.interfaces import IBattleEntity, IBattleState

from src.core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from src.core.effect.triggers import TriggerCondition, BattleStartCondition, ActionCompleteCondition, ImmunityCondition
from src.core.spirit.refactored_spirit import RefactoredSpirit


class SuMingZhiHuanShenYaoEffect(IEffect):
    """
    宿命之环神曜技效果
    
    完整实现版本：
    - 进入战斗时，获得嘲讽（持续2次攻击）及免疫（持续1次攻击）
    - 3级神格：该次免疫时令自身获得50点气势
    - 6级神格：通灵时额外触发1次
    - 10级神格：每次触发后，嘲讽及免疫的持续时间增加1次攻击
    """
    
    def __init__(self, owner_spirit: RefactoredSpirit, shenge_level: int = 1):
        super().__init__(
            effect_id=f"sumingzhihuan_{owner_spirit.id}",
            name="宿命之环",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=-1  # 永久效果
        )
        
        self.owner_spirit = owner_spirit
        self.shenge_level = shenge_level
        self.battle_start_triggered = False
        self.tongling_trigger_count = 0
        
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True
    
    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        return [
            BattleStartCondition(),  # 战斗开始时触发
            ActionCompleteCondition(actor="self"),  # 自身行动完成后触发（检测通灵技能）
            ImmunityCondition(target="self")  # 自身免疫时触发（3级神格效果）
        ]
    
    def on_triggered(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理事件触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "BATTLE_START":
                return self._handle_battle_start(event_data, battle_state)
            elif event_type == "ACTION_COMPLETE":
                return self._handle_action_complete(event_data, battle_state)
            elif event_type == "IMMUNITY":
                return self._handle_immunity_triggered(event_data, battle_state)

            return EffectResult.success()

        except Exception as e:
            from src.core.logging import spirit_logger
            spirit_logger.error(f"宿命之环效果触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)
    
    def _handle_battle_start(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理战斗开始事件"""
        if self.battle_start_triggered:
            return EffectResult.success("战斗开始已处理")
        
        self.battle_start_triggered = True
        return self._trigger_suming_zhihuan_effect(battle_state, "战斗开始")
    
    def _handle_action_complete(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理行动完成事件，检测通灵技能"""
        actor = event_data.get("actor")
        if actor != self.owner_spirit:
            return EffectResult.success("不是自身行动")
        
        # 检查是否释放了通灵技能
        skill_name = event_data.get("skill_name", "")
        if "通灵" in skill_name or "星轨逆转" in skill_name:
            # 6级神格：通灵时额外触发1次
            if self.shenge_level >= 6:
                self.tongling_trigger_count += 1
                return self._trigger_suming_zhihuan_effect(battle_state, f"通灵触发第{self.tongling_trigger_count}次")
        
        return EffectResult.success()
    
    def _handle_immunity_triggered(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理免疫触发事件"""
        target = event_data.get("target")
        if target != self.owner_spirit:
            return EffectResult.success("不是自身免疫")
        
        # 3级神格：该次免疫时令自身获得50点气势
        if self.shenge_level >= 3:
            from src.core.action import LogAction
            
            # 增加气势
            current_energy = getattr(self.owner_spirit, 'energy', 0)
            max_energy = getattr(self.owner_spirit, 'max_energy', 300)
            new_energy = min(current_energy + 50, max_energy)
            self.owner_spirit.energy = new_energy
            
            actions = [
                LogAction(
                    caster=self.owner_spirit,
                    message=f"🌟 {self.owner_spirit.name} 免疫攻击时获得50点气势！（当前气势：{new_energy}）"
                )
            ]
            
            return EffectResult.success_with_actions(actions, "3级神格免疫气势加成触发")
        
        return EffectResult.success()
    
    def _trigger_suming_zhihuan_effect(self, battle_state, trigger_reason: str) -> EffectResult:
        """触发宿命之环效果"""
        from src.core.action import LogAction, ApplyEffectAction
        from src.core.effect.effects import AttackImmunityEffect
        
        
        actions = []
        
        # 计算嘲讽和免疫的持续次数
        taunt_charges = 2
        immunity_charges = 1
        
        # 10级神格：每次触发后，嘲讽及免疫的持续时间增加1次攻击
        if self.shenge_level >= 10:
            # 根据触发次数增加持续时间
            bonus_charges = self.tongling_trigger_count if hasattr(self, 'tongling_trigger_count') else 0
            taunt_charges += bonus_charges
            immunity_charges += bonus_charges
        
        # 创建嘲讽效果
        taunt_effect = TauntEffect(charges=taunt_charges)
        
        # 创建免疫效果
        immunity_effect = AttackImmunityEffect(charges=immunity_charges)
        
        actions.extend([
            LogAction(
                caster=self.owner_spirit,
                message=f"🔮 [{trigger_reason}] {self.owner_spirit.name} 宿命之环触发！获得嘲讽({taunt_charges}次)和免疫({immunity_charges}次)！"
            ),
            ApplyEffectAction(
                caster=self.owner_spirit,
                target=self.owner_spirit,
                effect=taunt_effect
            ),
            ApplyEffectAction(
                caster=self.owner_spirit,
                target=self.owner_spirit,
                effect=immunity_effect
            )
        ])
        
        return EffectResult.success_with_actions(actions, f"宿命之环效果触发：{trigger_reason}")
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        # 设置神曜增伤数据
        self.set_data("suming_zhihuan_active", True)
        self.set_data("damage_reduction", 0.15)  # 15%减伤
        self.set_data("flat_damage_bonus", 0.10)  # 10%加法增伤
        
        # 神格等级加成
        if self.shenge_level >= 3:
            self.set_data("skill_damage_multiplier", 0.08)  # 3级神格：8%技能增伤
        
        if self.shenge_level >= 6:
            self.set_data("damage_amplify", 0.10)  # 6级神格：10%伤害加深
            self.set_data("crit_bonus", 0.12)  # 6级神格：12%暴击率加成
        
        if self.shenge_level >= 10:
            self.set_data("flat_damage_bonus", 0.18)  # 10级神格：提升到18%加法增伤
            self.set_data("armor_break", 0.15)  # 10级神格：15%破甲
        
        return EffectResult.success_with_data(
            {"applied": True}, 
            f"{target.name} 获得宿命之环效果（{self.shenge_level}级神格）"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{target.name} 失去宿命之环效果"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data({}, "宿命之环效果更新")
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"宿命之环神曜技效果（{self.shenge_level}级神格）",
            "shenge_level": self.shenge_level,
            "tongling_triggers": getattr(self, 'tongling_trigger_count', 0),
            "battle_started": self.battle_start_triggered
        }


# 导出函数
def create_suming_zhihuan_effect(owner_spirit: RefactoredSpirit, shenge_level: int = 1) -> SuMingZhiHuanShenYaoEffect:
    """创建宿命之环效果"""
    return SuMingZhiHuanShenYaoEffect(owner_spirit, shenge_level)
