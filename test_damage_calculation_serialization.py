#!/usr/bin/env python3
"""
测试伤害计算序列化功能

验证伤害计算器的各个阶段是否正确序列化
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_damage_modifiers_serialization():
    """测试DamageModifiers序列化"""
    print("🔧 测试DamageModifiers序列化...")
    
    try:
        from core.battle.utilities.formula_damage_calculator import DamageModifiers
        
        # 创建测试修正系数
        modifiers = DamageModifiers(
            skill_damage_multipliers=[0.1, 0.2],
            damage_amplify_multipliers=[0.05],
            skill_damage_reductions=[0.1],
            flat_damage_bonus=50.0,
            flat_damage_reduction=10.0,
            ignore_defense_percent=0.2,
            armor_break_percent=0.1,
            crit_rate_bonus=0.05,
            hit_rate_bonus=0.1,
            dodge_rate_bonus=0.02,
            synergy_damage_bonus=0.08,
            equipment_damage_bonus=25.0
        )
        
        # 序列化
        serialized = modifiers.to_dict()
        print(f"✅ 序列化成功，包含 {len(serialized)} 个字段")
        
        # 反序列化
        deserialized = DamageModifiers.from_dict(serialized)
        print(f"✅ 反序列化成功")
        
        # 验证数据一致性
        assert deserialized.skill_damage_multipliers == modifiers.skill_damage_multipliers
        assert deserialized.flat_damage_bonus == modifiers.flat_damage_bonus
        assert deserialized.synergy_damage_bonus == modifiers.synergy_damage_bonus
        print(f"✅ 数据一致性验证通过")
        
        # 测试JSON序列化
        json_str = json.dumps(serialized, indent=2)
        print(f"✅ JSON序列化成功，长度: {len(json_str)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ DamageModifiers序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_damage_calculation_serialization():
    """测试伤害计算过程序列化"""
    print("\n🔧 测试伤害计算过程序列化...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建测试精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        caster = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        target = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        if not caster or not target:
            print("❌ 精灵创建失败")
            return False
        
        # 创建模拟动作
        class MockAction:
            def __init__(self):
                self.damage_type = "PHYSICAL"
                self.power_multiplier = 1.5
                self.is_ultimate = False
                self.is_indirect = False
                self.is_splash = False
                self.skill_name = "测试技能"
        
        action = MockAction()
        
        # 创建模拟战斗状态
        from core.battle.models import BattleState
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        battle_state = BattleState(formation1, formation2)
        
        # 执行伤害计算
        from core.battle.utilities.formula_damage_calculator import calculate_formula_damage
        
        final_damage, breakdown = calculate_formula_damage(caster, target, action, battle_state)
        
        print(f"✅ 伤害计算完成，最终伤害: {final_damage}")
        print(f"📊 计算详情包含 {len(breakdown)} 个字段")
        
        # 验证序列化字段
        required_fields = [
            "calculation_steps",
            "caster_modifiers", 
            "target_modifiers",
            "calculation_metadata",
            "calculation_phases",
            "final_damage"
        ]
        
        for field in required_fields:
            if field not in breakdown:
                print(f"❌ 缺少必需字段: {field}")
                return False
            print(f"  ✅ {field}: {type(breakdown[field])}")
        
        # 验证计算阶段
        phases = breakdown["calculation_phases"]
        print(f"📋 计算阶段数量: {len(phases)}")
        for phase_name, phase_data in phases.items():
            print(f"  📊 {phase_name}: {phase_data.get('description', 'No description')}")
        
        # 测试JSON序列化
        try:
            json_str = json.dumps(breakdown, indent=2, ensure_ascii=False)
            print(f"✅ JSON序列化成功，长度: {len(json_str)} 字符")
            
            # 验证可以反序列化
            parsed = json.loads(json_str)
            print(f"✅ JSON反序列化成功")
            
        except Exception as e:
            print(f"❌ JSON序列化失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 伤害计算序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indirect_damage_serialization():
    """测试非直接伤害序列化"""
    print("\n🔧 测试非直接伤害序列化...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建测试精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        caster = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        target = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 创建非直接伤害动作
        class MockIndirectAction:
            def __init__(self):
                self.damage_type = "火焰"
                self.power_multiplier = 1.0
                self.is_ultimate = False
                self.is_indirect = True
                self.skill_name = "火焰伤害"
        
        action = MockIndirectAction()
        
        # 创建模拟战斗状态
        from core.battle.models import BattleState
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        battle_state = BattleState(formation1, formation2)
        
        # 执行非直接伤害计算
        from core.battle.utilities.formula_damage_calculator import calculate_indirect_damage
        
        final_damage, breakdown = calculate_indirect_damage(caster, target, action, battle_state)
        
        print(f"✅ 非直接伤害计算完成，最终伤害: {final_damage}")
        print(f"📊 计算详情包含 {len(breakdown)} 个字段")
        
        # 验证非直接伤害特有字段
        assert breakdown["is_indirect"] == True
        assert "calculation_phases" in breakdown
        assert "calculation_metadata" in breakdown
        
        phases = breakdown["calculation_phases"]
        print(f"📋 非直接伤害计算阶段: {len(phases)}")
        for phase_name, phase_data in phases.items():
            print(f"  📊 {phase_name}: {phase_data.get('description', 'No description')}")
        
        # 测试JSON序列化
        json_str = json.dumps(breakdown, indent=2, ensure_ascii=False)
        print(f"✅ 非直接伤害JSON序列化成功，长度: {len(json_str)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 非直接伤害序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 伤害计算序列化功能测试")
    print("="*60)
    
    tests = [
        ("DamageModifiers序列化", test_damage_modifiers_serialization),
        ("伤害计算过程序列化", test_damage_calculation_serialization),
        ("非直接伤害序列化", test_indirect_damage_serialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有序列化测试通过！")
        print("\n📋 序列化功能验证:")
        print("  ✅ DamageModifiers类支持序列化/反序列化")
        print("  ✅ 伤害计算过程完整序列化")
        print("  ✅ 计算阶段详细记录")
        print("  ✅ JSON格式兼容")
        print("  ✅ 非直接伤害序列化")
        print("\n🚀 伤害计算器现在支持完整的序列化功能！")
    else:
        print("❌ 部分序列化测试失败，需要进一步检查")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
