from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, List, Any

# 第三方库导入
from collections import defaultdict

# 本地导入
from ..battle.utilities.formula_damage_calculator import calculate_czc, extract_damage_modifiers
from ..battle.utilities.formula_damage_calculator import calculate_czj, extract_damage_modifiers
from ..battle.utilities.formula_damage_calculator import calculate_pzc, extract_damage_modifiers
from ..battle.utilities.formula_damage_calculator import calculate_pzj, extract_damage_modifiers
from ..cache import cache_attribute_calculation
from ..performance import monitor_battle_performance
from .system import AttributeManager, AttributeCalculator

"""精灵属性模块。

定义了 `Attributes` 类，作为精灵所有战斗相关属性的统一管理接口。
此类负责存储基础属性，并通过 `AttributeManager` 应用临时的战斗修改，
最终通过一系列的属性（property）动态计算出精灵在战斗中的实际数值。
"""


# 新增：导入缓存装饰器和性能监控

# 优化的缓存装饰器导入
def _get_cache_decorator():
    """延迟导入缓存装饰器，带有更好的错误处理"""
    try:
        # 验证缓存装饰器是否可用
        if callable(cache_attribute_calculation):
            return cache_attribute_calculation
        else:
            return lambda func: func
    except (ImportError, AttributeError):
        # 如果导入失败，返回一个空装饰器
        return lambda func: func

# 获取缓存装饰器实例
_cache_attr = _get_cache_decorator()

# 验证缓存是否真正可用（用于调试和监控）
def is_cache_available() -> bool:
    """检查缓存装饰器是否真正可用"""
    return _cache_attr != (lambda func: func)


@dataclass
class AttributeDefaults:
    """属性默认值配置"""
    base_hp: int = 1000
    hp_p: float = 0.0
    hp_flat: float = 0.0
    base_attack: int = 100
    attack_p: float = 0.0
    attack_flat: float = 0.0
    base_pdef: int = 50
    pdef_p: float = 0.0
    pdef_flat: float = 0.0
    base_mdef: int = 50
    mdef_p: float = 0.0
    mdef_flat: float = 0.0
    base_speed: int = 100
    base_hit_rate: float = 0.0
    base_dodge_rate: float = 0.0
    base_break_rate: float = 0.0
    base_block_rate: float = 0.0
    base_crit_rate: float = 0.0
    base_crit_res_rate: float = 0.0
    base_crit_damage: float = 1.5
    base_damage_reduction: float = 0.0
    base_penetration: float = 0.0
    base_max_energy: int = 300


@dataclass
class AttributeConfig:
    """
    精灵属性配置。

    定义了精灵的基础属性值。
    """
    id: str
    base_attack: float
    base_hp: float
    hp_p: float
    hp_flat: float
    attack_p: float
    attack_flat: float
    base_pdef: float
    pdef_p: float
    pdef_flat: float
    base_mdef: float
    mdef_p: float
    mdef_flat: float
    base_speed: float
    base_hit_rate: float
    base_dodge_rate: float
    base_break_rate: float
    base_crit_rate: float
    base_crit_res_rate: float
    base_block_rate: float
    base_max_energy: int = 300  # 基础最大气势（系统常量）


class Attributes:
    """
    管理和计算精灵所有属性的容器。

    该类存储精灵的基础属性值，并使用 `AttributeManager` 来应用
    来自技能、效果等的动态修改。最终的属性值通过 property 动态计算得出。
    """

    def __init__(
        self,
        base_hp: float,
        hp_p: float,
        hp_flat: float,
        base_attack: float,
        attack_p: float,
        attack_flat: float,
        base_pdef: float,
        pdef_p: float,
        pdef_flat: float,
        base_mdef: float,
        mdef_p: float,
        mdef_flat: float,
        base_speed: float,
        base_hit_rate: float,
        base_dodge_rate: float,
        base_break_rate: float,
        base_block_rate: float,
        base_crit_rate: float,
        base_crit_res_rate: float,
        # 新增战斗属性
        base_crit_damage: float = 1.5,  # 暴击伤害倍数
        base_damage_reduction: float = 0.0,  # 减伤率
        base_penetration: float = 0.0,  # 穿透率（无视部分防御）
    ):
        """
        初始化精灵的全部基础属性。

        Args:
            base_hp: 基础生命值。
            hp_p: 初始生命值百分比加成。
            hp_flat: 初始生命值固定加成。
            base_attack: 基础攻击力。
            attack_p: 初始攻击力百分比加成。
            attack_flat: 初始攻击力固定加成。
            base_pdef: 基础物理防御。
            pdef_p: 初始物理防御百分比加成。
            pdef_flat: 初始物理防御固定加成。
            base_mdef: 基础魔法防御。
            mdef_p: 初始魔法防御百分比加成。
            mdef_flat: 初始魔法防御固定加成。
            base_speed: 基础速度。
            base_hit_rate: 此处作为加成值命中率。
            base_dodge_rate: 基础闪避率。
            base_break_rate: 基础破击率。
            base_block_rate: 基础格挡率。
            base_crit_rate: 基础暴击率。
            base_crit_res_rate: 基础暴击抵抗率。
            base_crit_damage: 基础暴击伤害倍数。
            base_damage_reduction: 基础减伤率。
            base_penetration: 基础穿透率。
        """
        # 主要属性
        self.base_hp = base_hp
        self.hp_p = hp_p
        self.hp_flat = hp_flat

        self.base_attack = base_attack
        self.attack_p = attack_p
        self.attack_flat = attack_flat

        self.base_pdef = base_pdef
        self.pdef_p = pdef_p
        self.pdef_flat = pdef_flat

        self.base_mdef = base_mdef
        self.mdef_p = mdef_p
        self.mdef_flat = mdef_flat

        self.base_speed = base_speed

        # 二级属性
        self.base_hit_rate = base_hit_rate
        self.base_dodge_rate = base_dodge_rate
        self.base_break_rate = base_break_rate
        self.base_block_rate = base_block_rate
        self.base_crit_rate = base_crit_rate
        self.base_crit_res_rate = base_crit_res_rate
        self.base_max_energy = 300  # 基础最大气势（系统常量）

        # 新增战斗属性
        self.base_crit_damage = base_crit_damage
        self.base_damage_reduction = base_damage_reduction
        self.base_penetration = base_penetration

        self.attribute_manager = AttributeManager()
        
        # 为了向后兼容，保留旧的 modifiers 字段
        self.modifiers: Dict[str, List[Any]] = defaultdict(list)

    @classmethod
    def from_json_config(cls, config: Dict[str, Any]) -> 'Attributes':
        """从 JSON 配置创建属性对象

        Args:
            config: JSON 配置中的 attributes 字段

        Returns:
            创建的属性对象
        """
        defaults = AttributeDefaults()

        return cls(
            # 主要属性
            base_hp=config.get('base_hp', defaults.base_hp),
            hp_p=config.get('hp_p', defaults.hp_p),
            hp_flat=config.get('hp_flat', defaults.hp_flat),

            base_attack=config.get('base_attack', defaults.base_attack),
            attack_p=config.get('attack_p', defaults.attack_p),
            attack_flat=config.get('attack_flat', defaults.attack_flat),

            base_pdef=config.get('base_pdef', defaults.base_pdef),
            pdef_p=config.get('pdef_p', defaults.pdef_p),
            pdef_flat=config.get('pdef_flat', defaults.pdef_flat),

            base_mdef=config.get('base_mdef', defaults.base_mdef),
            mdef_p=config.get('mdef_p', defaults.mdef_p),
            mdef_flat=config.get('mdef_flat', defaults.mdef_flat),

            base_speed=config.get('base_speed', defaults.base_speed),

            # 二级属性
            base_hit_rate=config.get('base_hit_rate', defaults.base_hit_rate),
            base_dodge_rate=config.get('base_dodge_rate', defaults.base_dodge_rate),
            base_break_rate=config.get('base_break_rate', defaults.base_break_rate),
            base_block_rate=config.get('base_block_rate', defaults.base_block_rate),
            base_crit_rate=config.get('base_crit_rate', defaults.base_crit_rate),
            base_crit_res_rate=config.get('base_crit_res_rate', defaults.base_crit_res_rate),

            # 战斗属性
            base_crit_damage=config.get('base_crit_damage', defaults.base_crit_damage),
            base_damage_reduction=config.get('base_damage_reduction', defaults.base_damage_reduction),
            base_penetration=config.get('base_penetration', defaults.base_penetration)
        )

    @classmethod
    def create_with_validation(cls, config: Dict[str, Any]) -> 'Attributes':
        """创建属性对象并进行验证

        Args:
            config: JSON 配置中的 attributes 字段

        Returns:
            创建的属性对象

        Raises:
            ValueError: 当属性值不合法时
        """
        # 验证必需的属性
        required_attrs = ['base_hp', 'base_attack']
        for attr in required_attrs:
            if attr not in config:
                raise ValueError(f"缺少必需的属性: {attr}")

        # 验证属性值范围
        if config.get('base_hp', 0) <= 0:
            raise ValueError(f"base_hp 必须大于 0，当前值: {config.get('base_hp')}")

        if config.get('base_attack', 0) <= 0:
            raise ValueError(f"base_attack 必须大于 0，当前值: {config.get('base_attack')}")

        # 验证百分比属性范围
        percentage_attrs = ['hp_p', 'attack_p', 'pdef_p', 'mdef_p']
        for attr in percentage_attrs:
            value = config.get(attr, 0.0)
            if not isinstance(value, (int, float)):
                raise ValueError(f"{attr} 必须是数字，当前值: {value}")
            if value < -1.0 or value > 10.0:  # 允许 -100% 到 1000% 的范围
                raise ValueError(f"{attr} 超出合理范围 [-1.0, 10.0]，当前值: {value}")

        # 验证速度
        speed = config.get('base_speed', 100)
        if speed <= 0:
            raise ValueError(f"base_speed 必须大于 0，当前值: {speed}")

        return cls.from_json_config(config)

    def to_dict(self) -> Dict[str, Any]:
        """将属性对象序列化为字典。"""
        return {
            "hp": self.hp,
            "attack": self.attack,
            "pdef": self.pdef,
            "mdef": self.mdef,
            "speed": self.speed,
            "hit_rate": self.hit_rate,
            "dodge_rate": self.dodge_rate,
            "break_rate": self.break_rate,
            "block_rate": self.block_rate,
            "crit_rate": self.crit_rate,
            "crit_res_rate": self.crit_res_rate,
        }

    def add_modifier(self, source_id: str, attr: str, value: float) -> None:
        """
        添加一个属性修改器。

        Args:
            source_id: 修改来源的唯一ID。
            attr: 要修改的属性名称。
            value: 修改的数值。
        """
        self.attribute_manager.add_modifier(source_id, attr, value)
        # 向后兼容
        self.modifiers[attr].append((source_id, value))

    def remove_modifier(self, source_id: str) -> None:
        """
        移除指定来源的所有属性修改器。

        Args:
            source_id: 要移除的修改器的来源ID。
        """
        self.attribute_manager.remove_modifier(source_id)
        # 向后兼容
        for attr in self.modifiers:
            self.modifiers[attr] = [
                (s_id, v) for s_id, v in self.modifiers[attr] if s_id != source_id
            ]

    def set_modifier(self, source_id: str, attr: str, value: float) -> None:
        """
        设置一个属性修改器（会覆盖来自同一来源的对同一属性的旧修改）。

        Args:
            source_id: 修改来源的唯一ID。
            attr: 要修改的属性名称。
            value: 新的修改数值。
        """
        self.attribute_manager.set_modifier(source_id, attr, value)
        # 向后兼容
        self.modifiers[attr] = [
            (s_id, v) for s_id, v in self.modifiers[attr] if s_id != source_id
        ]
        self.modifiers[attr].append((source_id, value))

    def _get_modified_value(self, base_value: float, p_mods_key: str, flat_mods_key: str) -> float:
        """
        计算包含百分比和固定值修改的最终属性值。

        Args:
            base_value: 属性的基础值。
            p_mods_key: 百分比修改器的键。
            flat_mods_key: 固定值修改器的键。

        Returns:
            计算后的最终属性值。
        """
        p_modifiers = self.attribute_manager.get_modifiers(p_mods_key)
        flat_modifiers = self.attribute_manager.get_modifiers(flat_mods_key)
        return AttributeCalculator.calculate_modified_value(base_value, p_modifiers, flat_modifiers)

    def _get_simple_modified_value(self, base_value: float, mods_key: str) -> float:
        """
        计算只包含固定值修改的最终属性值。

        Args:
            base_value: 属性的基础值。
            mods_key: 修改器的键。

        Returns:
            计算后的最终属性值。
        """
        modifiers = self.attribute_manager.get_modifiers(mods_key)
        return AttributeCalculator.calculate_simple_modified_value(base_value, modifiers)

    @property
    def hp(self) -> float:
        """动态计算的最大生命值。"""
        return self._get_modified_value(self.base_hp, "hp_p", "hp_flat")

    @property
    @_cache_attr
    def attack(self) -> float:
        """面板攻击力 = 基础攻击 * (1 + 百分比加成) + 固定加成"""
        return self._get_modified_value(self.base_attack, "attack_p", "attack_flat")
    
    @_cache_attr
    def get_actual_attack(self, spirit=None) -> float:
        """
        实攻 = 面板攻击 * (1 + 动态加成 - 减成)
        
        动态加成包括：
        - 上阵羁绊加攻
        - 英雄技加攻  
        - 神格加攻
        - 神曜技能加攻
        - 降全属性减成
        """
        panel_attack = self.attack
        
        if spirit is None:
            return panel_attack
        
        # 从精灵的效果系统获取动态攻击修正
        dynamic_modifiers = self._get_dynamic_attack_modifiers(spirit)
        
        total_modifier = (
            dynamic_modifiers.get("formation_bonus", 0.0) +      # 上阵羁绊加攻
            dynamic_modifiers.get("hero_skill_bonus", 0.0) +     # 英雄技加攻
            dynamic_modifiers.get("shenge_bonus", 0.0) +         # 神格加攻
            dynamic_modifiers.get("divine_skill_bonus", 0.0) -   # 神曜技能加攻
            dynamic_modifiers.get("attribute_reduction", 0.0)    # 降全属性
        )
        
        return panel_attack * (1.0 + total_modifier)
    
    def _get_dynamic_attack_modifiers(self, spirit) -> dict:
        """从精灵的效果系统获取动态攻击修正"""
        modifiers = {
            "formation_bonus": 0.0,
            "hero_skill_bonus": 0.0, 
            "shenge_bonus": 0.0,
            "divine_skill_bonus": 0.0,
            "attribute_reduction": 0.0
        }
        
        # 从战斗上下文获取修正
        if hasattr(spirit, 'get_battle_context'):
            context = spirit.get_battle_context()
            effect_properties = context.get("effect_properties", {})
            
            # 遍历所有效果，收集攻击力修正
            for _, effect_data in effect_properties.items():
                effect_data_dict = effect_data.get("data", {})
                
                # 神曜技能攻击加成
                if "attack_bonus" in effect_data_dict:
                    modifiers["divine_skill_bonus"] += effect_data_dict["attack_bonus"]
                
                # 英雄技攻击加成
                if "hero_attack_bonus" in effect_data_dict:
                    modifiers["hero_skill_bonus"] += effect_data_dict["hero_attack_bonus"]
                
                # 其他类型的攻击修正...
        
        return modifiers

    @property
    @_cache_attr
    def pdef(self) -> float:
        """动态计算的物理防御。"""
        return self._get_modified_value(self.base_pdef, "pdef_p", "pdef_flat")

    @property
    @_cache_attr
    def mdef(self) -> float:
        """动态计算的魔法防御。"""
        return self._get_modified_value(self.base_mdef, "mdef_p", "mdef_flat")

    @property
    @_cache_attr
    def speed(self) -> float:
        """动态计算的速度。"""
        return self._get_simple_modified_value(self.base_speed, "speed")

    @property
    def hit_rate(self) -> float:
        """动态计算的命中率。"""
        return self._get_simple_modified_value(self.base_hit_rate, "hit_rate")

    @property
    def dodge_rate(self) -> float:
        """动态计算的闪避率。"""
        return self._get_simple_modified_value(self.base_dodge_rate, "dodge_rate")

    @property
    def break_rate(self) -> float:
        """动态计算的破击率。"""
        return self._get_simple_modified_value(self.base_break_rate, "break_rate")

    @property
    def block_rate(self) -> float:
        """动态计算的格挡率。"""
        return self._get_simple_modified_value(self.base_block_rate, "block_rate")

    @property
    def crit_rate(self) -> float:
        """动态计算的暴击率。"""
        return self._get_simple_modified_value(self.base_crit_rate, "crit_rate")

    @property
    def crit_res_rate(self) -> float:
        """动态计算的暴击抵抗率。"""
        return self._get_simple_modified_value(self.base_crit_res_rate, "crit_res_rate")

    @property
    def max_energy(self) -> int:
        """计算最大气势值"""
        return int(self._get_simple_modified_value(float(self.base_max_energy), "max_energy"))



    # 新增战斗属性的property方法
    @property
    def crit_damage(self) -> float:
        """动态计算的暴击伤害倍数。"""
        return self._get_simple_modified_value(self.base_crit_damage, "crit_damage")

    @property
    def damage_reduction(self) -> float:
        """动态计算的减伤率。"""
        return self._get_simple_modified_value(self.base_damage_reduction, "damage_reduction")

    @property
    def penetration(self) -> float:
        """动态计算的穿透率。"""
        return self._get_simple_modified_value(self.base_penetration, "penetration")

    def _get_product_modifier(self, mods_key: str) -> float:
        """
        获取累积乘积修改值。

        Args:
            mods_key: 修改器的键。

        Returns:
            累积乘积结果。
        """
        modifiers = self.attribute_manager.get_modifiers(mods_key)
        return AttributeCalculator.calculate_product_modifier(modifiers)

    def _get_sum_modifier(self, mods_key: str) -> float:
        """
        获取累积总和修改值。

        Args:
            mods_key: 修改器的键。

        Returns:
            累积总和结果。
        """
        modifiers = self.attribute_manager.get_modifiers(mods_key)
        return sum(mod.value for mod in modifiers)

    # ====================== 伤害/治疗相关系数 ======================
    def get_pzc(self, spirit=None) -> float:
        """
        普通攻击乘法增伤系数 (PZC)

        统一调用formula_damage_calculator中的权威实现
        """
        if spirit is None:
            # 如果没有spirit参数，使用简化版本
            return self._get_product_modifier("pzc")

        try:
            modifiers = extract_damage_modifiers(spirit)
            return calculate_pzc(modifiers)
        except ImportError:
            # 降级到简化版本
            return self._get_product_modifier("pzc")

    def get_pzj(self, spirit=None) -> float:
        """
        普通攻击加法增伤系数 (PZJ)

        统一调用formula_damage_calculator中的权威实现
        """
        if spirit is None:
            return self._get_sum_modifier("pzj")

        try:
            modifiers = extract_damage_modifiers(spirit)
            return calculate_pzj(modifiers)
        except ImportError:
            return self._get_sum_modifier("pzj")

    def get_czc(self, spirit=None) -> float:
        """
        超杀乘法增伤系数 (CZC)

        统一调用formula_damage_calculator中的权威实现
        """
        if spirit is None:
            return self._get_product_modifier("czc")

        try:
            modifiers = extract_damage_modifiers(spirit)
            return calculate_czc(modifiers)
        except ImportError:
            return self._get_product_modifier("czc")

    def get_czj(self, spirit=None) -> float:
        """
        超杀加法增伤系数 (CZJ)

        统一调用formula_damage_calculator中的权威实现
        """
        if spirit is None:
            return self._get_sum_modifier("czj")

        try:
            modifiers = extract_damage_modifiers(spirit)
            return calculate_czj(modifiers)
        except ImportError:
            return self._get_sum_modifier("czj")

    # 保持向后兼容的property版本（简化版）
    @property
    def pzc(self) -> float:
        """普通攻击乘法增伤系数 (PZC) - 简化版，建议使用get_pzc(spirit)"""
        return self.get_pzc()

    @property
    def pzj(self) -> float:
        """普通攻击加法增伤系数 (PZJ) - 简化版，建议使用get_pzj(spirit)"""
        return self.get_pzj()

    @property
    def czc(self) -> float:
        """超杀乘法增伤系数 (CZC) - 简化版，建议使用get_czc(spirit)"""
        return self.get_czc()

    @property
    def czj(self) -> float:
        """超杀加法增伤系数 (CZJ) - 简化版，建议使用get_czj(spirit)"""
        return self.get_czj()

    @property
    def crit_damage_extra(self) -> float:
        """暴击伤害额外加成系数，返回总和。"""
        return self._get_sum_modifier("crit_damage_extra")

    @property
    def suppress_bonus(self) -> float:
        """克制增伤系数，返回总和。"""
        return self._get_sum_modifier("suppress_bonus")

    @property
    def attached_damage_bonus(self) -> float:
        """附伤系数，返回总和。"""
        return self._get_sum_modifier("attached_damage_bonus")

    # ====================== 面板攻防 (简化版) ======================
    @property
    def panel_attack(self) -> float:
        """
        面板攻击力 - 等同于attack属性

        注意：为了避免概念混乱，panel_attack现在直接返回attack属性。
        如果需要特殊的面板计算逻辑，请使用专门的方法。
        """
        return self.attack

    @property
    def panel_pdef(self) -> float:
        """
        面板物理防御 - 等同于pdef属性

        注意：为了避免概念混乱，panel_pdef现在直接返回pdef属性。
        """
        return self.pdef

    @property
    def panel_mdef(self) -> float:
        """
        面板魔法防御 - 等同于mdef属性

        注意：为了避免概念混乱，panel_mdef现在直接返回mdef属性。
        """
        return self.mdef

    def _get_panel_modified_value(self, base_value: float, p_mods_key: str, flat_mods_key: str) -> float:
        """
        计算面板层面的加成（如图鉴、羁绊等长期加成）。

        注意：此方法已被简化，如果需要特殊的面板计算，请直接使用_get_modified_value。
        """
        return self._get_modified_value(base_value, p_mods_key, flat_mods_key)

    @property
    def skill_bonus(self) -> float:
        """技能增伤系数，返回总和。"""
        return self._get_sum_modifier("skill_bonus")

    @property
    def dmg_reduction_pct(self) -> float:
        """伤害减免百分比，返回总和，最大为1.0。"""
        return min(1.0, self._get_sum_modifier("dmg_reduction_pct"))

    @property
    def non_direct_reduction_pct(self) -> float:
        """非直接伤害减免百分比（如持续伤害），返回总和，最大为1.0。"""
        return min(1.0, self._get_sum_modifier("non_direct_reduction_pct"))

    # ====================== 实用方法 ======================
    def get_damage_coefficients(self, spirit=None, is_ultimate: bool = False) -> Dict[str, float]:
        """
        获取完整的伤害系数集合

        Args:
            spirit: 精灵实例，用于获取动态修正
            is_ultimate: 是否为超杀技能

        Returns:
            包含所有伤害系数的字典
        """
        if is_ultimate:
            return {
                'zc': self.get_czc(spirit),  # 超杀乘法增伤
                'zj': self.get_czj(spirit),  # 超杀加法增伤
                'crit_damage_extra': self.crit_damage_extra,
                'suppress_bonus': self.suppress_bonus,
                'skill_bonus': self.skill_bonus
            }
        else:
            return {
                'zc': self.get_pzc(spirit),  # 普攻乘法增伤
                'zj': self.get_pzj(spirit),  # 普攻加法增伤
                'crit_damage_extra': self.crit_damage_extra,
                'suppress_bonus': self.suppress_bonus,
                'skill_bonus': self.skill_bonus
            }

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存系统信息

        Returns:
            缓存系统的状态信息
        """
        return {
            'cache_available': is_cache_available(),
            'cache_decorator': str(type(_cache_attr)),
            'performance_monitoring': hasattr(self, '_performance_stats')
        }

    def validate_attributes(self) -> Dict[str, Any]:
        """
        验证属性值的合理性

        Returns:
            验证结果
        """
        issues = []
        warnings = []

        # 检查基础属性
        if self.base_hp <= 0:
            issues.append("基础生命值必须大于0")
        if self.base_attack <= 0:
            issues.append("基础攻击力必须大于0")

        # 检查百分比属性
        if self.base_crit_rate < 0 or self.base_crit_rate > 1:
            warnings.append(f"暴击率 {self.base_crit_rate} 超出正常范围 [0, 1]")
        if self.base_dodge_rate < 0 or self.base_dodge_rate > 1:
            warnings.append(f"闪避率 {self.base_dodge_rate} 超出正常范围 [0, 1]")

        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }


__all__ = ['Attributes', 'AttributeConfig', 'is_cache_available']
