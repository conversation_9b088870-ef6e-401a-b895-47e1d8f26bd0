#!/usr/bin/env python3
"""
系统性调试超杀技能bug
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def step1_check_skill_loading():
    """步骤1：检查技能加载"""
    print("🔍 步骤1：检查技能加载...")
    
    try:
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        print(f"  精灵: {spirit.name}")
        print(f"  技能数量: {len(spirit.skills) if hasattr(spirit, 'skills') else 0}")
        
        ultimate_skill = None
        for i, skill in enumerate(spirit.skills):
            skill_name = getattr(skill.metadata, 'name', f'技能{i}')
            cast_type = getattr(skill.metadata, 'cast_type', 'Unknown')
            print(f"    技能{i}: {skill_name} ({cast_type})")
            
            if cast_type == 'ULTIMATE':
                ultimate_skill = skill
                print(f"      ✅ 找到超杀技能")
                print(f"      组件数量: {len(skill.components)}")
                for j, comp in enumerate(skill.components):
                    print(f"        组件{j}: {type(comp).__name__}")
        
        return ultimate_skill
        
    except Exception as e:
        print(f"  ❌ 技能加载检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def step2_check_target_selection(skill, spirit):
    """步骤2：检查目标选择"""
    print("\n🔍 步骤2：检查目标选择...")
    
    try:
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.models import BattleState
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        target_spirit = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit, 1, 1)
        formation2.add_spirit(target_spirit, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        if hasattr(skill, 'target_selector'):
            print(f"  目标选择器: {type(skill.target_selector).__name__}")
            
            # 创建一个简单的context
            class SimpleContext:
                def __init__(self):
                    self.skill_level = 1
                    self.is_critical = False
                    self.is_ultimate = True
                    self.additional_data = {'skill_name': skill.metadata.name}
            
            context = SimpleContext()
            
            try:
                targets = skill.target_selector.select_targets(spirit, battle_state, context)
                print(f"  选择的目标数量: {len(targets)}")
                
                for i, target in enumerate(targets):
                    print(f"    目标{i}: {getattr(target, 'name', 'Unknown')}")
                
                return targets, battle_state
                
            except Exception as e:
                print(f"  ❌ 目标选择失败: {e}")
                import traceback
                traceback.print_exc()
                return [], battle_state
        else:
            print(f"  ❌ 没有目标选择器")
            return [], battle_state
            
    except Exception as e:
        print(f"  ❌ 目标选择检查失败: {e}")
        import traceback
        traceback.print_exc()
        return [], None

def step3_check_component_execution(skill, spirit, targets, battle_state):
    """步骤3：检查组件执行"""
    print("\n🔍 步骤3：检查组件执行...")
    
    try:
        # 设置高气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置气势为: {spirit.energy}")
        
        # 创建SkillContext
        from core.skill.skills import SkillContext
        context = SkillContext(
            skill_level=1,
            is_critical=False,
            is_ultimate=True,
            additional_data={'skill_name': skill.metadata.name}
        )
        
        print(f"  SkillContext创建成功")
        print(f"    is_ultimate: {context.is_ultimate}")
        print(f"    skill_name: {context.additional_data.get('skill_name')}")
        
        all_actions = []
        
        for i, component in enumerate(skill.components):
            comp_type = type(component).__name__
            print(f"  执行组件{i}: {comp_type}")
            
            try:
                actions = component.execute(spirit, targets, battle_state, context)
                print(f"    返回动作数量: {len(actions)}")
                
                for j, action in enumerate(actions):
                    action_type = type(action).__name__
                    print(f"      动作{j}: {action_type}")
                    
                    if action_type == 'ConsumeEnergyAction':
                        amount = getattr(action, 'amount', 0)
                        print(f"        消耗气势: {amount}")
                    elif action_type == 'DamageAction':
                        is_ultimate = getattr(action, 'is_ultimate', False)
                        overflow = getattr(action, 'overflow_energy', None)
                        threshold = getattr(action, 'ultimate_threshold', None)
                        print(f"        is_ultimate: {is_ultimate}")
                        print(f"        overflow_energy: {overflow}")
                        print(f"        ultimate_threshold: {threshold}")
                
                all_actions.extend(actions)
                
            except Exception as e:
                print(f"    ❌ 组件执行失败: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"  总动作数量: {len(all_actions)}")
        return all_actions
        
    except Exception as e:
        print(f"  ❌ 组件执行检查失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def step4_check_skill_cast(skill, battle_state):
    """步骤4：检查技能cast方法"""
    print("\n🔍 步骤4：检查技能cast方法...")

    try:
        # 确保精灵有足够的气势
        if hasattr(skill, 'owner') and skill.owner and hasattr(skill.owner, 'components'):
            from core.components import EnergyComponent
            energy_component = skill.owner.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  重新设置气势为: {skill.owner.energy}")

        print(f"  技能有cast方法: {hasattr(skill, 'cast')}")

        if hasattr(skill, 'cast'):
            print(f"  调用skill.cast(battle_state)...")

            try:
                actions = skill.cast(battle_state)
                print(f"  cast返回动作数量: {len(actions)}")

                for i, action in enumerate(actions):
                    action_type = type(action).__name__
                    print(f"    动作{i}: {action_type}")

                return actions
                
            except Exception as e:
                print(f"  ❌ cast方法执行失败: {e}")
                import traceback
                traceback.print_exc()
                return []
        else:
            print(f"  ❌ 技能没有cast方法")
            return []
            
    except Exception as e:
        print(f"  ❌ cast方法检查失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def step5_compare_results(component_actions, cast_actions):
    """步骤5：对比结果"""
    print("\n🔍 步骤5：对比结果...")
    
    print(f"  组件直接执行: {len(component_actions)} 个动作")
    print(f"  cast方法执行: {len(cast_actions)} 个动作")
    
    if len(component_actions) > 0 and len(cast_actions) == 0:
        print(f"  ❌ 问题确认：组件能生成动作，但cast方法不能")
        print(f"  🔍 问题可能在于：")
        print(f"    1. cast方法的实现有问题")
        print(f"    2. cast方法中的目标选择失败")
        print(f"    3. cast方法中的context创建有问题")
        print(f"    4. cast方法中的异常被忽略")
        return False
    elif len(component_actions) == 0 and len(cast_actions) == 0:
        print(f"  ❌ 问题确认：组件本身有问题")
        return False
    else:
        print(f"  ✅ 结果一致，没有问题")
        return True

def main():
    """主函数"""
    print("="*60)
    print("🔧 系统性调试超杀技能bug")
    print("="*60)
    
    # 步骤1：检查技能加载
    ultimate_skill = step1_check_skill_loading()
    if not ultimate_skill:
        print("\n❌ 步骤1失败，无法继续")
        return
    
    # 步骤2：检查目标选择
    spirit = None
    try:
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
    except:
        pass
    
    if not spirit:
        print("\n❌ 无法创建精灵，无法继续")
        return
    
    targets, battle_state = step2_check_target_selection(ultimate_skill, spirit)
    if not battle_state:
        print("\n❌ 步骤2失败，无法继续")
        return
    
    # 步骤3：检查组件执行
    component_actions = step3_check_component_execution(ultimate_skill, spirit, targets, battle_state)
    
    # 步骤4：检查技能cast方法
    cast_actions = step4_check_skill_cast(ultimate_skill, battle_state)
    
    # 步骤5：对比结果
    success = step5_compare_results(component_actions, cast_actions)
    
    print("\n" + "="*60)
    print("📊 系统性调试结果:")
    print("="*60)
    
    if success:
        print("✅ 没有发现问题")
    else:
        print("❌ 发现问题，需要进一步修复")

if __name__ == "__main__":
    main()
