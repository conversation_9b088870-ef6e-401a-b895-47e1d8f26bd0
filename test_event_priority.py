#!/usr/bin/env python3
"""
测试EventPriority导入
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_event_priority():
    """测试EventPriority导入"""
    print("🔧 测试EventPriority导入...")
    
    try:
        # 测试导入EventPriority
        from core.event.unified_manager import EventPriority
        print(f"✅ EventPriority导入成功: {EventPriority}")
        
        # 测试使用EventPriority
        priority = EventPriority.NORMAL
        print(f"✅ EventPriority.NORMAL: {priority}")
        
        # 测试从effect.system导入
        from core.effect.system import EffectManager
        print(f"✅ EffectManager导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*40)
    print("🔧 EventPriority导入测试")
    print("="*40)
    
    result = test_event_priority()
    
    print("\n" + "="*40)
    if result:
        print("✅ EventPriority导入测试成功")
    else:
        print("❌ EventPriority导入测试失败")

if __name__ == "__main__":
    main()
