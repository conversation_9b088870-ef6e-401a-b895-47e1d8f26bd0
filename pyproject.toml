[tool.flake8]
max-line-length = 120
exclude = ["__pycache__", ".git", ".venv", "venv", "build", "dist"]
ignore = ["E203", "W503"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
mypy_path = "src"

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra"
testpaths = ["tests"]
pythonpath = ["."]

[tool.sphinx]
project = "aoqiai"
author = "Your Team"
release = "0.1"

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"
