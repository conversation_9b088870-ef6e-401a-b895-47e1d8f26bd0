"""
战斗系统性能监控器

提供详细的性能监控、分析和优化建议
"""

import time
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics

@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    total_time: float = 0.0
    call_count: int = 0
    min_time: float = float('inf')
    max_time: float = 0.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    @property
    def avg_time(self) -> float:
        """平均执行时间"""
        return self.total_time / self.call_count if self.call_count > 0 else 0.0
    
    @property
    def recent_avg_time(self) -> float:
        """最近的平均执行时间"""
        return statistics.mean(self.recent_times) if self.recent_times else 0.0
    
    def add_measurement(self, execution_time: float):
        """添加一次测量"""
        self.total_time += execution_time
        self.call_count += 1
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)
        self.recent_times.append(execution_time)

class BattlePerformanceMonitor:
    """战斗性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.lock = threading.RLock()
        self.enabled = True
        self.slow_operation_threshold = 0.1  # 100ms
        self.warning_threshold = 0.05  # 50ms
        
        # 性能统计
        self.total_battles = 0
        self.total_battle_time = 0.0
        self.slow_operations = []
        
    def measure_operation(self, operation_name: str):
        """性能测量装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if not self.enabled:
                    return func(*args, **kwargs)
                
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time
                    self.record_measurement(operation_name, execution_time)
                    
                    # 检查慢操作
                    if execution_time > self.slow_operation_threshold:
                        self.record_slow_operation(operation_name, execution_time, args, kwargs)
            
            return wrapper
        return decorator
    
    def record_measurement(self, operation_name: str, execution_time: float):
        """记录性能测量"""
        with self.lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = PerformanceMetric(operation_name)
            
            self.metrics[operation_name].add_measurement(execution_time)
    
    def record_slow_operation(self, operation_name: str, execution_time: float, args, kwargs):
        """记录慢操作"""
        slow_op = {
            'operation': operation_name,
            'time': execution_time,
            'timestamp': time.time(),
            'args_count': len(args) if args else 0,
            'kwargs_count': len(kwargs) if kwargs else 0
        }
        
        with self.lock:
            self.slow_operations.append(slow_op)
            # 只保留最近的100个慢操作
            if len(self.slow_operations) > 100:
                self.slow_operations.pop(0)
    
    def start_battle_measurement(self):
        """开始战斗测量"""
        self.battle_start_time = time.perf_counter()
    
    def end_battle_measurement(self):
        """结束战斗测量"""
        if hasattr(self, 'battle_start_time'):
            battle_time = time.perf_counter() - self.battle_start_time
            with self.lock:
                self.total_battles += 1
                self.total_battle_time += battle_time
            delattr(self, 'battle_start_time')
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self.lock:
            report = {
                'summary': {
                    'total_battles': self.total_battles,
                    'avg_battle_time': self.total_battle_time / self.total_battles if self.total_battles > 0 else 0,
                    'total_operations': sum(m.call_count for m in self.metrics.values()),
                    'slow_operations_count': len(self.slow_operations)
                },
                'operations': {},
                'slow_operations': self.slow_operations[-10:],  # 最近10个慢操作
                'recommendations': self._generate_recommendations()
            }
            
            # 操作详情
            for name, metric in self.metrics.items():
                report['operations'][name] = {
                    'avg_time': metric.avg_time,
                    'recent_avg_time': metric.recent_avg_time,
                    'call_count': metric.call_count,
                    'min_time': metric.min_time,
                    'max_time': metric.max_time,
                    'total_time': metric.total_time
                }
            
            return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        # 检查慢操作
        if len(self.slow_operations) > 10:
            recommendations.append("检测到多个慢操作，建议优化性能瓶颈")
        
        # 检查高频操作
        for name, metric in self.metrics.items():
            if metric.call_count > 1000 and metric.avg_time > self.warning_threshold:
                recommendations.append(f"高频操作 '{name}' 平均耗时较长，建议优化")
        
        # 检查内存使用
        if len(self.metrics) > 100:
            recommendations.append("监控的操作类型过多，可能存在内存泄漏")
        
        return recommendations
    
    def get_top_slow_operations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最慢的操作"""
        with self.lock:
            sorted_metrics = sorted(
                self.metrics.items(),
                key=lambda x: x[1].avg_time,
                reverse=True
            )
            
            return [
                {
                    'name': name,
                    'avg_time': metric.avg_time,
                    'call_count': metric.call_count,
                    'total_time': metric.total_time
                }
                for name, metric in sorted_metrics[:limit]
            ]
    
    def get_most_frequent_operations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最频繁的操作"""
        with self.lock:
            sorted_metrics = sorted(
                self.metrics.items(),
                key=lambda x: x[1].call_count,
                reverse=True
            )
            
            return [
                {
                    'name': name,
                    'call_count': metric.call_count,
                    'avg_time': metric.avg_time,
                    'total_time': metric.total_time
                }
                for name, metric in sorted_metrics[:limit]
            ]
    
    def reset_metrics(self):
        """重置所有指标"""
        with self.lock:
            self.metrics.clear()
            self.slow_operations.clear()
            self.total_battles = 0
            self.total_battle_time = 0.0
    
    def enable_monitoring(self):
        """启用监控"""
        self.enabled = True
    
    def disable_monitoring(self):
        """禁用监控"""
        self.enabled = False
    
    def set_thresholds(self, slow_threshold: float = None, warning_threshold: float = None):
        """设置性能阈值"""
        if slow_threshold is not None:
            self.slow_operation_threshold = slow_threshold
        if warning_threshold is not None:
            self.warning_threshold = warning_threshold

# 全局性能监控器实例
battle_performance_monitor = BattlePerformanceMonitor()

# 便捷的装饰器
def monitor_performance(operation_name: str = None, slow_threshold: float = None):
    """性能监控装饰器"""
    def decorator(func):
        name = operation_name or f"{func.__module__}.{func.__name__}"
        
        def wrapper(*args, **kwargs):
            if not battle_performance_monitor.enabled:
                return func(*args, **kwargs)
            
            start_time = time.perf_counter()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.perf_counter()
                execution_time = end_time - start_time
                battle_performance_monitor.record_measurement(name, execution_time)
                
                # 检查自定义慢操作阈值
                threshold = slow_threshold or battle_performance_monitor.slow_operation_threshold
                if execution_time > threshold:
                    battle_performance_monitor.record_slow_operation(name, execution_time, args, kwargs)
        
        return wrapper
    return decorator

def get_performance_summary() -> str:
    """获取性能摘要字符串"""
    report = battle_performance_monitor.get_performance_report()
    
    summary = f"""
🔍 战斗系统性能报告
{'='*50}
📊 总体统计:
   - 总战斗数: {report['summary']['total_battles']}
   - 平均战斗时间: {report['summary']['avg_battle_time']:.3f}s
   - 总操作数: {report['summary']['total_operations']}
   - 慢操作数: {report['summary']['slow_operations_count']}

⚡ 最慢的操作:
"""
    
    slow_ops = battle_performance_monitor.get_top_slow_operations(5)
    for i, op in enumerate(slow_ops, 1):
        summary += f"   {i}. {op['name']}: {op['avg_time']:.3f}s (调用{op['call_count']}次)\n"
    
    summary += "\n🔥 最频繁的操作:\n"
    frequent_ops = battle_performance_monitor.get_most_frequent_operations(5)
    for i, op in enumerate(frequent_ops, 1):
        summary += f"   {i}. {op['name']}: {op['call_count']}次 (平均{op['avg_time']:.3f}s)\n"
    
    if report['recommendations']:
        summary += "\n💡 优化建议:\n"
        for rec in report['recommendations']:
            summary += f"   - {rec}\n"
    
    return summary
