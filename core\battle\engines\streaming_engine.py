"""
统一流式战斗引擎
提供高效、实时、可配置的战斗事件流
"""
from __future__ import annotations
from typing import Generator, Dict, Any, List, Optional, Set
from enum import Enum
import time
import json
from dataclasses import dataclass

from .battle_engine import RefactoredBattleEngine
from ..execution import UnifiedActionExecutor
from ...action import LogAction, DamageAction, ApplyEffectAction, BattleAction
# 移除对 src.api.events 的依赖，使用内部事件系统
# from src.api.events import (
#     BattleStartEvent, RoundStartEvent, ActionResultEvent,
#     SpiritUpdateEvent, BattleEndEvent, ErrorEvent,
#     TargetResult, SpiritInitialState
# )
from ...exceptions import EffectImmuneException, AttackImmuneException
from ...interfaces import IBattleEntity


class StreamLevel(Enum):
    """流式传输的详细级别"""
    MINIMAL = "minimal"      # 只包含关键事件（开始、结束、回合）
    STANDARD = "standard"    # 包含动作结果和状态更新
    DETAILED = "detailed"    # 包含所有事件和详细信息
    DEBUG = "debug"          # 包含调试信息


@dataclass
class StreamConfig:
    """流式传输配置"""
    level: StreamLevel = StreamLevel.STANDARD
    buffer_events: bool = True          # 是否缓冲事件以减少网络请求
    include_timestamps: bool = True     # 是否包含时间戳
    include_metadata: bool = False      # 是否包含元数据
    max_buffer_size: int = 10          # 最大缓冲事件数量
    aggregate_updates: bool = True      # 是否聚合同一精灵的多次更新


class EventCollector:
    """事件收集器 - 负责从战斗引擎收集和转换事件"""
    
    def __init__(self, config: StreamConfig):
        self.config = config
        self.start_time = time.time()
        self.collected_events: List[Dict[str, Any]] = []
        self.spirit_states: Dict[str, Dict[str, Any]] = {}
        
    def collect_battle_start(self, battle_state) -> Dict[str, Any]:
        """收集战斗开始事件"""
        team0_spirits = self._get_team_initial_states(battle_state, 0)
        team1_spirits = self._get_team_initial_states(battle_state, 1)
        
        # 初始化精灵状态跟踪
        for spirits in [team0_spirits, team1_spirits]:
            for spirit in spirits:
                self.spirit_states[spirit.id] = {
                    "current_hp": spirit.current_hp,
                    "energy": spirit.energy,
                    "is_alive": True
                }
        
        # 使用简单字典代替事件类
        event_data = {
            "battle_id": f"battle_{int(self.start_time)}",
            "team0": team0_spirits,
            "team1": team1_spirits
        }
        
        return self._create_stream_event("battle_start", event_data)

    def collect_damage_immunity(self, target: IBattleEntity, source: IBattleEntity) -> Dict[str, Any]:
        """收集攻击免疫事件（已升级）"""
        data = {
            "target": {
                "id": target.id,
                "name": target.name
            },
            "source": {
                "id": source.id,
                "name": source.name
            }
        }
        return self._create_stream_event("DAMAGE_IMMUNE", data)

    def collect_passive_initialization(self, spirit: IBattleEntity, effects_applied: List[str]) -> Optional[Dict[str, Any]]:
        """收集被动技能初始化事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return None
            
        return self._create_stream_event("passive_init", {
            "spirit_id": getattr(spirit, 'id', str(spirit)),
            "spirit_name": getattr(spirit, 'name', 'Unknown'),
            "effects_applied": effects_applied,
            "total_effects": len(effects_applied),
            "effect_details": [
                {
                    "name": effect_name,
                    "type": "passive",
                    "permanent": True
                } for effect_name in effects_applied
            ]
        })
    
    def collect_skill_activation(self, caster: IBattleEntity, skill_name: str, skill_type: str, targets: Optional[List[IBattleEntity]] = None) -> Optional[Dict[str, Any]]:
        """收集技能激活事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return None
        
        # 确定技能分类
        skill_category = "未知技能"
        energy_cost = 0
        
        if skill_type == "PASSIVE":
            if "神曜" in skill_name or "量子裂变" in skill_name:
                skill_category = "神曜技"
            elif "英雄" in skill_name or "多重协奏" in skill_name or "模块共鸣" in skill_name:
                skill_category = "英雄技"
            else:
                skill_category = "被动技"
        elif skill_type == "ULTIMATE":
            skill_category = "超杀技"
            energy_cost = 150
        elif skill_type == "ACTIVE":
            skill_category = "主动技"
            energy_cost = 0
            
        return self._create_stream_event("skill_activation", {
            "caster_id": getattr(caster, 'id', str(caster)),
            "caster_name": getattr(caster, 'name', 'Unknown'),
            "skill_name": skill_name,
            "skill_type": skill_type,
            "skill_category": skill_category,
            "target_count": len(targets) if targets else 0,
            "target_ids": [getattr(t, 'id', str(t)) for t in targets] if targets else [],
            "energy_cost": energy_cost,
            "is_shenyao_skill": skill_category == "神曜技",
            "is_hero_skill": skill_category == "英雄技"
        })
    
    def collect_effect_application(self, target: IBattleEntity, effect_name: str, effect_type: str, duration: int = -1) -> Optional[Dict[str, Any]]:
        """收集效果应用事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return None
            
        return self._create_stream_event("effect_applied", {
            "target_id": getattr(target, 'id', str(target)),
            "target_name": getattr(target, 'name', 'Unknown'),
            "effect_name": effect_name,
            "effect_type": effect_type,
            "duration": duration,
            "is_permanent": duration == -1 or duration >= 999,
            "stack_count": 1  # 可以扩展为实际的叠加数
        })
    
    def collect_energy_change(self, spirit: IBattleEntity, old_energy: int, new_energy: int, reason: str) -> Optional[Dict[str, Any]]:
        """收集能量变化事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return None
            
        change = new_energy - old_energy
        if change == 0:
            return None
            
        return self._create_stream_event("energy_change", {
            "spirit_id": getattr(spirit, 'id', str(spirit)),
            "spirit_name": getattr(spirit, 'name', 'Unknown'),
            "energy_before": old_energy,
            "energy_after": new_energy,
            "energy_change": change,
            "reason": reason,
            "is_full": new_energy >= 150,
            "can_use_ultimate": new_energy >= 150
        })
    
    def collect_round_start(self, round_num: int) -> Dict[str, Any]:
        """收集回合开始事件"""
        # 使用简单字典代替事件类
        event_data = {"round_num": round_num}
        return self._create_stream_event("round_start", event_data)
    
    def collect_round_end(self, round_num: int) -> Dict[str, Any]:
        """收集回合结束事件"""
        # from src.api.events import RoundEndEvent
        # 使用简单字典代替事件类
        event_data = {"round_num": round_num}
        return self._create_stream_event("round_end", event_data)
    
    def collect_action_result(self, action: BattleAction, targets: List[IBattleEntity], results: List[Dict]) -> Optional[Dict[str, Any]]:
        """收集动作结果事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return None
            
        # 构建目标结果
        target_results = []
        for i, target in enumerate(targets):
            if i < len(results):
                result = results[i]
                # 使用简单字典代替事件类
                target_result = {
                    "target_id": getattr(target, 'id', str(target)),
                    "hp_change": result.get('hp_change', 0),
                    "hp_after": result.get('hp_after', getattr(target, 'current_hp', 0)),
                    "energy_change": result.get('energy_change', 0),
                    "energy_after": result.get('energy_after', getattr(target, 'energy', 0)),
                    "is_critical": result.get('is_critical', False),
                    "is_dodged": result.get('is_dodged', False),
                    "is_blocked": result.get('is_blocked', False),
                    "is_killed": result.get('is_killed', False),
                    "applied_effects": result.get('applied_effects', []),
                    "removed_effects": result.get('removed_effects', [])
                }
                target_results.append(target_result)
        
        # 确定动作类型
        action_type = "UNKNOWN"
        skill_name = None
        
        if isinstance(action, DamageAction):
            action_type = "DAMAGE"
            if hasattr(action, 'skill_name'):
                skill_name = action.skill_name
            elif getattr(action, 'is_ultimate', False):
                action_type = "ULTIMATE"
        elif isinstance(action, ApplyEffectAction):
            action_type = "EFFECT"
            skill_name = getattr(action.effect, 'name', 'Unknown Effect')
        
        # 使用简单字典代替事件类
        event_data = {
            "caster_id": getattr(action.caster, 'id', 'system') if action.caster else 'system',
            "action_type": action_type,
            "skill_name": skill_name,
            "targets": target_results
        }
        
        return self._create_stream_event("action_result", event_data)
    
    def collect_tongling_progress(self, spirit: IBattleEntity, progress: int, max_progress: int = 100, 
                                 tongling_count: int = 0, max_tongling: int = 2) -> Optional[Dict[str, Any]]:
        """收集通灵进度事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return None
            
        return self._create_stream_event("tongling_progress", {
            "spirit_id": getattr(spirit, 'id', str(spirit)),
            "spirit_name": getattr(spirit, 'name', 'Unknown'),
            "progress": progress,
            "max_progress": max_progress,
            "progress_percent": round((progress / max_progress) * 100, 1),
            "tongling_count": tongling_count,
            "max_tongling": max_tongling,
            "can_tongling": progress >= max_progress and tongling_count < max_tongling,
            "is_ready": progress >= max_progress
        })
    
    def collect_special_states(self, spirit: IBattleEntity) -> Dict[str, Any]:
        """收集精灵的特殊状态"""
        special_states = {}
        
        # 检查通灵进度和其他特殊状态
        if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
            for effect_id, effect in spirit.effect_manager.effects.items():
                # 通灵进度
                if hasattr(effect, 'tongling_progress'):
                    special_states.update({
                        "tongling_progress": effect.tongling_progress,
                        "tongling_count": getattr(effect, 'tongling_count', 0),
                        "max_tongling": getattr(effect, 'max_tongling', 2),
                        "is_transformed": getattr(effect, 'is_transformed', False),
                        "tongling_ready": effect.tongling_progress >= 100
                    })
                
                # 效果剩余次数
                if hasattr(effect, 'charges') and effect.charges > 0:
                    if "effect_charges" not in special_states:
                        special_states["effect_charges"] = {}
                    special_states["effect_charges"][effect.name] = effect.charges
                
                # 收集其他特殊状态
                self._collect_effect_internal_states(effect, special_states)
        
        # 收集属性计算详情
        special_states["attribute_details"] = self._collect_attribute_details(spirit)
        
        return special_states
    
    def _collect_effect_internal_states(self, effect, special_states: Dict[str, Any]) -> None:
        """收集效果的内部状态"""
        effect_name = getattr(effect, 'name', 'Unknown')
        
        # 构装机甲状态
        if hasattr(effect, 'first_immunity_used'):
            special_states[f"{effect_name}_immunity_used"] = effect.first_immunity_used
        
        # 模块共鸣攻击力层数
        if hasattr(effect, 'attack_boost_stacks'):
            special_states[f"{effect_name}_attack_stacks"] = dict(effect.attack_boost_stacks)
        
        # 天籁之音复活次数
        if hasattr(effect, 'revive_charges'):
            special_states[f"{effect_name}_revive_charges"] = effect.revive_charges
        
        # 藏隙匿形隐身状态
        if hasattr(effect, 'invisibility_granted'):
            special_states[f"{effect_name}_invisibility"] = effect.invisibility_granted
        
        # 量子裂变克隆状态
        if hasattr(effect, 'cloned_spirit'):
            special_states[f"{effect_name}_clone_active"] = effect.cloned_spirit is not None
            special_states[f"{effect_name}_clone_revived"] = getattr(effect, 'clone_revived', False)
    
    def _collect_attribute_details(self, spirit) -> Dict[str, Any]:
        """收集属性计算详情"""
        details = {}
        
        try:
            # 基础属性
            if hasattr(spirit, 'attributes'):
                attrs = spirit.attributes
                details["base_attributes"] = {
                    "hp": getattr(attrs, 'base_hp', 0),
                    "attack": getattr(attrs, 'base_attack', 0),
                    "pdef": getattr(attrs, 'base_pdef', 0),
                    "mdef": getattr(attrs, 'base_mdef', 0),
                    "speed": getattr(attrs, 'base_speed', 0),
                    "crit_rate": getattr(attrs, 'base_crit_rate', 0),
                    "dodge_rate": getattr(attrs, 'base_dodge_rate', 0)
                }
                
                # 计算后的最终属性
                details["final_attributes"] = {
                    "hp": getattr(attrs, 'hp', 0),
                    "attack": getattr(attrs, 'attack', 0),
                    "pdef": getattr(attrs, 'pdef', 0),
                    "mdef": getattr(attrs, 'mdef', 0),
                    "speed": getattr(attrs, 'speed', 0),
                    "crit_rate": getattr(attrs, 'crit_rate', 0),
                    "dodge_rate": getattr(attrs, 'dodge_rate', 0)
                }
                
                # 属性修正器（如果有）
                if hasattr(attrs, 'modifiers'):
                    details["attribute_modifiers"] = self._format_attribute_modifiers(attrs.modifiers)
        
        except Exception as e:
            details["error"] = f"收集属性详情失败: {str(e)}"
        
        return details
    
    def _format_attribute_modifiers(self, modifiers) -> Dict[str, List[Dict[str, Any]]]:
        """格式化属性修正器信息"""
        formatted = {}
        
        try:
            for attr_name, modifier_list in modifiers.items():
                formatted[attr_name] = []
                for modifier in modifier_list:
                    formatted[attr_name].append({
                        "source": getattr(modifier, 'source_id', 'Unknown'),
                        "value": getattr(modifier, 'value', 0),
                        "type": getattr(modifier, 'modifier_type', 'Unknown')
                    })
        except Exception:
            pass
        
        return formatted

    def collect_spirit_updates(self, spirit_updates: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """收集精灵状态更新事件"""
        if self.config.level == StreamLevel.MINIMAL:
            return []
            
        events = []
        
        for spirit_id, updates in spirit_updates.items():
            # 过滤出真正变化的属性
            filtered_updates = {}
            old_state = self.spirit_states.get(spirit_id, {})
            
            for key, new_value in updates.items():
                old_value = old_state.get(key)
                if old_value != new_value:
                    filtered_updates[key] = new_value
                    old_state[key] = new_value  # 更新跟踪状态
            
            # 收集特殊状态（通灵进度等）
            special_states = updates.get('special_states', {})
            
            if filtered_updates or special_states:
                # 使用简单字典代替事件类
                event_data = {
                    "spirit_id": spirit_id,
                    "updates": filtered_updates,
                    "special_states": special_states
                }
                events.append(self._create_stream_event("spirit_update", event_data))
        
        return events
    
    def collect_internal_data_events(self) -> List[Dict[str, Any]]:
        """收集内部数据事件"""
        try:
            from ...event.system import internal_data_collector
            events = internal_data_collector.get_and_clear_events()
            from ...logging import battle_logger
            battle_logger.debug(f"收集到 {len(events)} 个内部数据事件")
            for event in events:
                battle_logger.debug(f"内部事件: {event.get('type', 'unknown')}")
            return events
        except ImportError as e:
            from ...logging import battle_logger
            battle_logger.debug(f"无法导入内部数据收集器: {e}")
            return []
    
    def collect_battle_end(self, winner: Optional[int], reason: str = "COMPLETED") -> Dict[str, Any]:
        """收集战斗结束事件"""
        # 使用简单字典代替事件类
        event_data = {
            "winner": winner if winner is not None else -1,
            "reason": reason
        }
        return self._create_stream_event("battle_end", event_data)
    
    def collect_error(self, error_message: str, error_code: Optional[str] = None) -> Dict[str, Any]:
        """收集错误事件"""
        # 使用简单字典代替事件类
        event_data = {
            "message": error_message,
            "code": error_code
        }
        return self._create_stream_event("error", event_data)
    
    def _get_team_initial_states(self, battle_state, team_id: int) -> List[Dict]:
        """获取队伍初始状态"""
        spirits = battle_state.get_living_spirits(team_id)
        initial_states = []
        
        for spirit in spirits:
            # 使用简单字典代替事件类
            state = {
                "id": getattr(spirit, 'id', str(spirit)),
                "name": getattr(spirit, 'name', 'Unknown'),
                "team": team_id,
                "position": getattr(spirit, 'position', (0, 0)),
                "max_hp": getattr(spirit, 'max_hp', 0),
                "current_hp": getattr(spirit, 'current_hp', 0),
                "energy": getattr(spirit, 'energy', 0)
            }
            initial_states.append(state)
        
        return initial_states
    
    def _create_stream_event(self, event_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建流式事件"""
        event = {
            "event": event_type,
            "data": data,
            "id": f"{event_type}_{int(time.time() * 1000)}"
        }
        
        if self.config.include_timestamps:
            event["timestamp"] = time.time() - self.start_time
            
        if self.config.include_metadata:
            event["metadata"] = {
                "level": self.config.level.value,
                "sequence": len(self.collected_events)
            }
        
        return event


class StreamingActionExecutor(UnifiedActionExecutor):
    """流式动作执行器 - 扩展基础执行器以支持事件收集"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.event_collector: Optional[EventCollector] = None
        # 可选统计跟踪器 (由引擎注入)
        self.stats_tracker = None  # type: ignore[assignment]
        self.pending_spirit_updates: Dict[str, Dict[str, Any]] = {}
        self.battle_initialized = False
        self.event_manager: Optional[Any] = None
        self.battle_log: Optional[List[Dict[str, Any]]] = None
        self.recorder: Optional[Any] = None
        
    def set_event_collector(self, collector: EventCollector):
        """设置事件收集器"""
        self.event_collector = collector
    
    def initialize_battle_events(self):
        """初始化战斗事件 - 收集被动技能等初始化信息"""
        if not self.event_collector or self.battle_initialized:
            return
            
        self.battle_initialized = True
        
        # 收集所有精灵的被动技能初始化
        for spirit in self.battle_state.get_all_spirits():
            if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
                effects_applied = []
                
                # 收集已应用的被动效果
                for effect_id, effect in spirit.effect_manager.effects.items():
                    effect_name = getattr(effect, 'name', 'Unknown Effect')
                    effects_applied.append(effect_name)
                    
                    # 发送效果应用事件
                    effect_event = self.event_collector.collect_effect_application(
                        spirit, effect_name, "passive", getattr(effect, 'duration', -1)
                    )
                    if effect_event:
                        self._emit_event(effect_event)
                
                # 发送被动初始化事件
                if effects_applied:
                    passive_event = self.event_collector.collect_passive_initialization(
                        spirit, effects_applied
                    )
                    if passive_event:
                        self._emit_event(passive_event)
            
            # 收集技能信息
            if hasattr(spirit, 'skills'):
                for skill in spirit.skills:
                    if hasattr(skill, 'metadata'):
                        skill_event = self.event_collector.collect_skill_activation(
                            spirit, 
                            skill.metadata.name,
                            skill.metadata.cast_type,
                            []
                        )
                        if skill_event:
                            # 修改事件类型为技能注册
                            skill_event['event'] = 'skill_registered'
                            skill_event['data']['registered'] = True
                            
                            # 添加技能描述和特殊标记
                            if hasattr(skill.metadata, 'description'):
                                skill_event['data']['description'] = skill.metadata.description
                            
                            # 标记特殊技能类型 - 更精确的识别
                            skill_category = "未知技能"
                            
                            if skill.metadata.cast_type == "PASSIVE":
                                # 通过技能名称识别神曜技
                                if any(keyword in skill.metadata.name for keyword in ["神曜", "量子裂变", "SHENYAO"]):
                                    skill_category = "神曜技"
                                # 通过技能名称识别英雄技
                                elif any(keyword in skill.metadata.name for keyword in ["英雄", "多重协奏", "模块共鸣", "HERO"]):
                                    skill_category = "英雄技"
                                else:
                                    skill_category = "被动技"
                            elif skill.metadata.cast_type == "ULTIMATE":
                                skill_category = "超杀技"
                            elif skill.metadata.cast_type == "ACTIVE":
                                skill_category = "主动技"
                            
                            skill_event['data']['skill_category'] = skill_category
                            skill_event['data']['is_shenyao_skill'] = skill_category == "神曜技"
                            skill_event['data']['is_hero_skill'] = skill_category == "英雄技"
                            skill_event['data']['is_passive_skill'] = skill.metadata.cast_type == "PASSIVE"
                            
                            self._emit_event(skill_event)
        
    def _execute_single_action(self, action: BattleAction) -> Optional[List[BattleAction]]:
        """重写单个动作执行，添加事件收集"""
        # 在动作执行前收集状态
        if self.event_collector and isinstance(action, DamageAction):
            target = action.target
            if target.is_alive:
                hp_before = target.current_hp
                energy_before = getattr(target, 'energy', 0)
                
                # 执行原始逻辑
                try:
                    result = super()._execute_single_action(action)
                except EffectImmuneException:
                    # 如果目标免疫，则不执行伤害，只记录免疫事件
                    if self.event_collector:
                        effect_app_event = self.event_collector.collect_effect_application(
                            target, "免疫", "immune", -1
                        )
                        if effect_app_event:
                            self._emit_event(effect_app_event)

                        action_result = {
                            'hp_change': 0,
                            'hp_after': target.current_hp,
                            'energy_change': 0,
                            'energy_after': getattr(target, 'energy', 0),
                            'is_critical': False,
                            'is_dodged': False,
                            'is_blocked': False,
                            'is_killed': not target.is_alive,
                            'applied_effects': [],
                            'removed_effects': [],
                            'is_immune': True,  # 添加免疫标记
                            'immune_reason': 'effect_immune'
                        }
                        action_event = self.event_collector.collect_action_result(action, [target], [action_result])
                        if action_event:
                            self._emit_event(action_event)
                    
                    # 确保统计跟踪器能接收到免疫信息
                    if self.stats_tracker:
                        self.stats_tracker.on_action(action, action_result)
                        
                    return None
                except AttackImmuneException:
                    # 如果目标对攻击免疫，则不执行伤害，只记录攻击免疫事件
                    if self.event_collector and action.caster:
                        damage_immunity_event = self.event_collector.collect_damage_immunity(target, action.caster)
                        self._emit_event(damage_immunity_event)

                        action_result = {
                            'hp_change': 0,
                            'hp_after': target.current_hp,
                            'energy_change': 0,
                            'energy_after': getattr(target, 'energy', 0),
                            'is_critical': False,
                            'is_dodged': False,
                            'is_blocked': False,
                            'is_damage_immune': True,  # 标记为攻击免疫
                            'is_killed': not target.is_alive,
                            'applied_effects': [],
                            'removed_effects': [],
                            'is_immune': True,  # 添加通用免疫标记
                            'immune_reason': 'damage_immune',
                            'original_damage': getattr(action, 'damage_value', 0)
                        }
                        action_event = self.event_collector.collect_action_result(action, [target], [action_result])
                        if action_event:
                            self._emit_event(action_event)
                    
                    # 确保统计跟踪器能接收到免疫信息
                    if self.stats_tracker:
                        self.stats_tracker.on_action(action, action_result)
                        
                    return None

                # 在动作执行后收集事件数据
                hp_after = target.current_hp
                energy_after = getattr(target, 'energy', 0)
                actual_damage = hp_before - hp_after
                
                # 收集能量变化事件
                if energy_after != energy_before:
                    if self.event_collector:
                        energy_event = self.event_collector.collect_energy_change(
                            target, energy_before, energy_after, "受到攻击"
                        )
                        if energy_event:
                            self._emit_event(energy_event)
                
                # 构建结果数据
                action_result = {
                    'hp_change': -actual_damage,
                    'hp_after': hp_after,
                    'energy_change': energy_after - energy_before,
                    'energy_after': energy_after,
                    'is_critical': getattr(action, 'is_critical', False),
                    'is_dodged': actual_damage == 0 and hp_before > 0,
                    'is_blocked': False,
                    'is_damage_immune': False,
                    'is_killed': not target.is_alive,
                    'applied_effects': [],
                    'removed_effects': []
                }
                
                # 检查是否触发了免疫（通过事件数据）
                if hasattr(action, 'immunity_triggered') and getattr(action, 'immunity_triggered', False):
                    action_result['is_immune'] = True
                    action_result['is_damage_immune'] = True
                    action_result['original_damage'] = getattr(action, 'damage_value', 0)
                    action_result['immune_reason'] = getattr(action, 'immunity_source', 'unknown')
                
                # 收集动作结果事件
                if self.event_collector:
                    action_event = self.event_collector.collect_action_result(
                        action, [target], [action_result]
                    )
                    if action_event:
                        self._emit_event(action_event)
                
                # 收集精灵状态更新
                if self.event_collector:
                    spirit_id = getattr(target, 'id', str(target))
                    special_states = self.event_collector.collect_special_states(target)
                    
                    self.pending_spirit_updates[spirit_id] = {
                        'current_hp': hp_after,
                        'energy': energy_after,
                        'is_alive': target.is_alive,
                        'special_states': special_states
                    }
                
                # 统计
                if self.stats_tracker:
                    self.stats_tracker.on_action(action, action_result)
                
                return result
            else:
                return super()._execute_single_action(action)
        
        elif self.event_collector and isinstance(action, ApplyEffectAction):
            # 处理效果应用事件
            result = super()._execute_single_action(action)
            
            action_result = {
                'hp_change': 0,
                'hp_after': action.target.current_hp,
                'energy_change': 0,
                'energy_after': getattr(action.target, 'energy', 0),
                'is_critical': False,
                'is_dodged': False,
                'is_blocked': False,
                'is_killed': not action.target.is_alive,
                'applied_effects': [getattr(action.effect, 'name', 'Unknown Effect')],
                'removed_effects': []
            }
            
            if self.event_collector:
                action_event = self.event_collector.collect_action_result(
                    action, [action.target], [action_result]
                )
                if action_event:
                    self._emit_event(action_event)
            
            # 统计
            if self.stats_tracker:
                self.stats_tracker.on_action(action, action_result)
                
            return result
        
        else:
            # 对于其他动作类型，直接执行
            result = super()._execute_single_action(action)
            if self.stats_tracker:
                stats_result = {}
                if isinstance(action, ApplyEffectAction):
                    # 检查效果应用结果是否包含免疫标记
                    # 注意：此处的result是来自父类执行器的返回，不是顶层捕获的
                    # 需要一种机制将EffectResult的data传递上来
                    pass
                self.stats_tracker.on_action(action, stats_result)
            return result
    
    
    def flush_spirit_updates(self):
        """刷新待发送的精灵状态更新"""
        if self.event_collector and self.pending_spirit_updates:
            update_events = self.event_collector.collect_spirit_updates(
                self.pending_spirit_updates
            )
            for event in update_events:
                self._emit_event(event)
            self.pending_spirit_updates.clear()
    
    def _emit_event(self, event: Dict[str, Any]):
        """发送事件（由流式引擎处理）"""
        # 这个方法会被StreamingBattleEngine重写
        pass


class StreamingBattleEngine:
    """统一流式战斗引擎"""
    
    def __init__(self, formation1, formation2, config: Optional[StreamConfig] = None, executor_type: str = "phased", **kwargs):
        self.config = config or StreamConfig()
        
        # 传递执行器类型参数给基础引擎
        self.base_engine = RefactoredBattleEngine(
            formation1, formation2, 
            executor_type=executor_type,  # 传递执行器类型
            **kwargs
        )
        self.event_collector = EventCollector(self.config)
        
        # 替换动作执行器
        self.streaming_executor = StreamingActionExecutor(
            self.base_engine.battle_state,
            self.base_engine.condition_checker
        )
        # 设置额外的属性
        if hasattr(self.base_engine, 'event_manager'):
            self.streaming_executor.event_manager = self.base_engine.event_manager
        if hasattr(self.base_engine, 'battle_log'):
            self.streaming_executor.battle_log = self.base_engine.battle_log
        if hasattr(self.base_engine.action_executor, 'recorder'):
            self.streaming_executor.recorder = self.base_engine.action_executor.recorder
        # 注入统计跟踪器
        self.streaming_executor.stats_tracker = self.base_engine.stats_tracker  # type: ignore[attr-defined]
        
        # 设置事件发送回调
        self.streaming_executor._emit_event = self._emit_event
        self.base_engine.action_executor = self.streaming_executor
        
        # 事件缓冲
        self.event_buffer: List[Dict[str, Any]] = []
        
    def run_battle_stream(self) -> Generator[Dict[str, Any], None, None]:
        """运行流式战斗"""
        try:
            # 1. 发送战斗开始事件
            battle_start_event = self.event_collector.collect_battle_start(
                self.base_engine.battle_state
            )
            yield from self._yield_event(battle_start_event)
            
            # 1.5. 初始化战斗事件（被动技能等）
            self.streaming_executor.initialize_battle_events()
            yield from self._flush_buffer()
            
            # 2. 战斗主循环
            for battle_data in self.base_engine.run_battle():
                if battle_data.get('type') == 'round_end':
                    round_num = battle_data.get('round_num', 0)
                    
                    # 发送回合开始事件
                    round_start_event = self.event_collector.collect_round_start(round_num)
                    yield from self._yield_event(round_start_event)
                    
                    # 刷新待发送的精灵状态更新
                    self.streaming_executor.flush_spirit_updates()
                    
                    # 收集内部数据事件（伤害计算详情等）
                    internal_events = self.event_collector.collect_internal_data_events()
                    for event in internal_events:
                        self._emit_event(event)
                        from ...logging import battle_logger
                        battle_logger.debug(f"发送内部数据事件: {event.get('event', 'unknown')}")
                    
                    # 发送回合结束事件
                    round_end_event = self.event_collector.collect_round_end(round_num)
                    yield from self._yield_event(round_end_event)
                    
                    # 发送缓冲的事件
                    yield from self._flush_buffer()
            
            # 3. 发送战斗结束事件
            battle_end_event = self.event_collector.collect_battle_end(
                self.base_engine.battle_state.winner,
                "COMPLETED"
            )
            yield from self._yield_event(battle_end_event)
            
        except Exception as e:
            # 发送错误事件
            error_event = self.event_collector.collect_error(
                f"战斗执行错误: {str(e)}", "BATTLE_ERROR"
            )
            yield from self._yield_event(error_event)
    
    def _emit_event(self, event: Dict[str, Any]):
        """接收来自执行器的事件"""
        if self.config.buffer_events:
            self.event_buffer.append(event)
            if len(self.event_buffer) >= self.config.max_buffer_size:
                # 缓冲区满了，需要外部调用flush
                pass
        else:
            # 立即发送（在生成器中处理）
            self.event_buffer.append(event)
    
    def _yield_event(self, event: Dict[str, Any]) -> Generator[Dict[str, Any], None, None]:
        """发送单个事件"""
        yield self._format_sse_event(event)
    
    def _flush_buffer(self) -> Generator[Dict[str, Any], None, None]:
        """刷新事件缓冲区"""
        for event in self.event_buffer:
            yield self._format_sse_event(event)
        self.event_buffer.clear()
    
    def _format_sse_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """格式化SSE事件"""
        return {
            "event": event.get("event", "message"),
            "data": event.get("data", {}),
            "id": event.get("id", f"event_{int(time.time() * 1000)}"),
            "timestamp": event.get("timestamp", time.time() - self.event_collector.start_time)
        }
    
    def get_battle_result(self) -> Dict[str, Any]:
        """获取战斗结果（兼容性接口）"""
        return self.base_engine.get_battle_result()


# 工厂函数
def create_streaming_engine(formation1, formation2, level: StreamLevel = StreamLevel.STANDARD, executor_type: str = "phased", **kwargs):
    """创建流式战斗引擎"""
    config = StreamConfig(level=level)
    return StreamingBattleEngine(formation1, formation2, config, executor_type=executor_type, **kwargs)


def create_minimal_stream(formation1, formation2, executor_type: str = "phased", **kwargs):
    """创建最小化流式引擎（只包含关键事件）"""
    return create_streaming_engine(formation1, formation2, StreamLevel.MINIMAL, executor_type=executor_type, **kwargs)


def create_detailed_stream(formation1, formation2, executor_type: str = "phased", **kwargs):
    """创建详细流式引擎（包含所有事件）"""
    return create_streaming_engine(formation1, formation2, StreamLevel.DETAILED, executor_type=executor_type, **kwargs)


__all__ = [
    'StreamingBattleEngine',
    'StreamConfig', 
    'StreamLevel',
    'create_streaming_engine',
    'create_minimal_stream',
    'create_detailed_stream'
]