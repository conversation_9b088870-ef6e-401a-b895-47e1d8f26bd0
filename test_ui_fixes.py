#!/usr/bin/env python3
"""
测试UI修复效果

验证效果显示和回合统计是否正确
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_battle_recording():
    """测试战斗记录功能"""
    print("🔧 测试战斗记录功能...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗创建成功")
        print(f"精灵1: {spirit1.name}")
        print(f"精灵2: {spirit2.name}")
        
        # 创建战斗记录器
        from ui.ux.models.battle_record import BattleRecorder
        recorder = BattleRecorder()
        
        # 创建初始快照
        print("\n📊 创建初始快照...")
        initial_snapshot = recorder.create_snapshot(engine.battle_state, 0)
        recorder.add_snapshot(initial_snapshot)
        
        print(f"初始快照:")
        print(f"  精灵数量: {len(initial_snapshot.spirits)}")
        for name, spirit in initial_snapshot.spirits.items():
            print(f"    {name}: {len(spirit.effects)} 个效果")
            for effect in spirit.effects:
                print(f"      - {effect['name']} ({effect['type']})")
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        # 创建回合快照
        print(f"创建回合快照...")
        round_snapshot = recorder.create_snapshot(engine.battle_state, 1)
        recorder.add_snapshot(round_snapshot)
        
        print(f"回合快照:")
        print(f"  回合数: {round_snapshot.round_num}")
        print(f"  行动数: {len(round_snapshot.actions_performed)}")
        print(f"  精灵数量: {len(round_snapshot.spirits)}")
        
        # 显示行动记录
        print(f"\n📝 行动记录:")
        for i, action in enumerate(round_snapshot.actions_performed):
            print(f"  行动 {i+1}: {action.description}")
            print(f"    施放者: {action.caster_name}")
            print(f"    伤害: {action.damage_dealt}")
            print(f"    治疗: {action.healing_done}")
        
        # 显示统计信息
        print(f"\n📈 统计信息:")
        for team in [0, 1]:
            damage = round_snapshot.total_damage_dealt.get(team, 0)
            healing = round_snapshot.total_healing_done.get(team, 0)
            print(f"  队伍{team}: 伤害 {damage}, 治疗 {healing}")
        
        # 显示精灵状态变化
        print(f"\n🔄 精灵状态变化:")
        for name, spirit in round_snapshot.spirits.items():
            print(f"  {name}:")
            print(f"    HP变化: {spirit.hp_change}")
            print(f"    气势变化: {spirit.energy_change}")
            print(f"    效果数量: {len(spirit.effects)}")
            if spirit.effects_added:
                print(f"    新增效果: {spirit.effects_added}")
            if spirit.effects_removed:
                print(f"    移除效果: {spirit.effects_removed}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n🔧 测试UI组件...")
    
    try:
        # 创建模拟数据
        from ui.ux.models.battle_record import SpiritSnapshot, RoundSnapshot, ActionRecord
        from datetime import datetime
        
        # 创建有效果的精灵快照
        spirit_with_effects = SpiritSnapshot(
            name="测试精灵",
            team=0,
            position=(1, 1),
            current_hp=800,
            max_hp=1000,
            energy=75,
            max_energy=100,
            is_alive=True,
            effects=[
                {
                    'id': 'passive_skill',
                    'name': '被动: 战斗专精',
                    'type': 'PASSIVE',
                    'duration': -1,
                    'stacks': 1,
                    'description': '提升战斗能力'
                },
                {
                    'id': 'buff_attack',
                    'name': '攻击强化',
                    'type': 'BUFF',
                    'duration': 3,
                    'stacks': 2,
                    'description': '攻击力提升40%'
                }
            ],
            hp_change=-200,
            energy_change=25,
            effects_added=['攻击强化'],
            effects_removed=[]
        )
        
        # 创建行动记录
        action = ActionRecord(
            action_id="test_action",
            action_type="attack",
            caster_name="测试精灵",
            target_names=["敌方精灵"],
            description="测试精灵使用技能攻击敌方精灵",
            timestamp=datetime.now(),
            damage_dealt={"敌方精灵": 200},
            healing_done={},
            effects_applied={"敌方精灵": ["攻击降低"]}
        )
        
        # 创建回合快照
        round_snapshot = RoundSnapshot(
            round_num=1,
            timestamp=datetime.now(),
            spirits={"测试精灵": spirit_with_effects},
            actions_performed=[action],
            total_damage_dealt={0: 200, 1: 0},
            total_healing_done={0: 0, 1: 0},
            changes_summary="测试精灵失去了200HP，获得了25气势，新增效果：攻击强化"
        )
        
        # 测试精灵详情面板
        print("测试精灵详情面板...")
        import tkinter as tk
        from ui.ux.components.spirit_detail_panel import SpiritDetailPanel
        
        root = tk.Tk()
        root.withdraw()
        
        detail_panel = SpiritDetailPanel(root)
        detail_panel.update_spirit(spirit_with_effects)
        
        print("✅ 精灵详情面板测试成功")
        print(f"  精灵: {spirit_with_effects.name}")
        print(f"  效果数量: {len(spirit_with_effects.effects)}")
        print(f"  HP变化: {spirit_with_effects.hp_change}")
        print(f"  气势变化: {spirit_with_effects.energy_change}")
        
        # 测试回合历史面板
        print("\n测试回合历史面板...")
        from ui.ux.components.round_history_panel import RoundHistoryPanel
        
        history_panel = RoundHistoryPanel(root)
        history_panel.update_overview(round_snapshot)
        history_panel.update_actions(round_snapshot)
        history_panel.update_changes(round_snapshot)
        
        print("✅ 回合历史面板测试成功")
        print(f"  回合: {round_snapshot.round_num}")
        print(f"  行动数: {len(round_snapshot.actions_performed)}")
        print(f"  统计: 队伍0伤害{round_snapshot.total_damage_dealt[0]}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 UI修复效果测试")
    print("="*60)
    
    tests = [
        ("战斗记录功能", test_battle_recording),
        ("UI组件", test_ui_components),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！UI修复成功")
        print("\n📋 修复内容:")
        print("  ✅ 效果信息正确显示（包括被动技能）")
        print("  ✅ 回合统计数据正确计算")
        print("  ✅ 行动记录正确生成")
        print("  ✅ 状态变化正确追踪")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
