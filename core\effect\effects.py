#!/usr/bin/env python3
"""
基础效果实现

提供常用的基础效果类，可以直接使用或作为自定义效果的基类。
"""

from typing import Dict, Any, List, Optional
from .system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from .triggers import (
    TriggerCondition, RoundEndCondition, BeforeAttackCondition,
    EventType
)
from ..event.events import ImmunityEvent


class DamageOverTimeEffect(IEffect):
    """持续伤害效果（已迁移至新触发系统）"""
    
    def __init__(self, damage_per_turn: float, duration: int, caster=None):
        super().__init__(
            effect_id=f"dot_{id(self)}",
            name="持续伤害",
            effect_type=EffectType.TEMPORARY,
            category=EffectCategory.DEBUFF,
            priority=EffectPriority.NORMAL,
            duration=duration,
            caster=caster
        )
        self.damage_per_turn = damage_per_turn
        self.stackable = True
        self.max_stacks = 5
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """应用持续伤害效果"""
        self.set_data("damage_per_turn", self.damage_per_turn)
        return EffectResult.success_with_data(
            {"applied": True, "damage_per_turn": self.damage_per_turn},
            f"对 {target.name} 施加持续伤害效果"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """移除持续伤害效果"""
        return EffectResult.success_with_data(
            {"removed": True},
            f"移除 {target.name} 的持续伤害效果"
        )
    
    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """声明此效果在回合结束时触发。"""
        return [RoundEndCondition()]

    def on_triggered(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """回合结束时造成伤害。"""
        from src.core.action import DamageAction
        
        damage = self.get_data("damage_per_turn", 0) * self.current_stacks
        if damage > 0 and self.owner and self.owner.is_alive:
            damage_action = DamageAction(
                caster=self.caster, # The original caster of the DoT
                target=self.owner,  # The spirit who has the DoT effect
                damage_value=int(damage),
                skill_name=self.name
            )
            return EffectResult.success_with_actions(
                [damage_action],
                message=f"对 {self.owner.name} 造成 {damage} 点持续伤害"
            )
        return EffectResult.success()

    def on_update(self, target, battle_state) -> EffectResult:
        """不再处理伤害逻辑，仅用于兼容性。"""
        return EffectResult.success()
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """层数变化时更新伤害"""
        return EffectResult.success_with_data(
            {"old_stacks": old_stacks, "new_stacks": new_stacks},
            f"持续伤害层数从 {old_stacks} 变为 {new_stacks}"
        )
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用"""
        return hasattr(target, 'hp') and target.hp > 0
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "type": "持续伤害",
            "damage_per_turn": self.damage_per_turn,
            "stacks": self.current_stacks,
            "remaining_turns": self.remaining_duration,
            "total_damage_per_turn": self.damage_per_turn * self.current_stacks
        }


class HealOverTimeEffect(IEffect):
    """持续治疗效果"""
    
    def __init__(self, heal_per_turn: float, duration: int, caster=None):
        super().__init__(
            effect_id=f"hot_{id(self)}",
            name="持续治疗",
            effect_type=EffectType.TEMPORARY,
            category=EffectCategory.BUFF,
            priority=EffectPriority.NORMAL,
            duration=duration,
            caster=caster
        )
        self.heal_per_turn = heal_per_turn
        self.stackable = True
        self.max_stacks = 3
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """应用持续治疗效果"""
        self.set_data("heal_per_turn", self.heal_per_turn)
        return EffectResult.success_with_data(
            {"applied": True, "heal_per_turn": self.heal_per_turn},
            f"对 {target.name} 施加持续治疗效果"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """移除持续治疗效果"""
        return EffectResult.success_with_data(
            {"removed": True},
            f"移除 {target.name} 的持续治疗效果"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """每回合进行治疗"""
        heal = self.heal_per_turn * self.current_stacks
        return EffectResult.success_with_data(
            {"heal": heal, "target": target.id},
            f"为 {target.name} 恢复 {heal} 点生命值"
        )
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """层数变化时更新治疗量"""
        return EffectResult.success_with_data(
            {"old_stacks": old_stacks, "new_stacks": new_stacks},
            f"持续治疗层数从 {old_stacks} 变为 {new_stacks}"
        )
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用"""
        return hasattr(target, 'hp') and target.hp > 0
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "type": "持续治疗",
            "heal_per_turn": self.heal_per_turn,
            "stacks": self.current_stacks,
            "remaining_turns": self.remaining_duration,
            "total_heal_per_turn": self.heal_per_turn * self.current_stacks
        }


class AttributeModifierEffect(IEffect):
    """属性修改效果"""
    
    def __init__(self, attribute_name: str, modifier: float, duration: int = -1, caster=None):
        super().__init__(
            effect_id=f"attr_mod_{attribute_name}_{id(self)}",
            name=f"{attribute_name}修改",
            effect_type=EffectType.TEMPORARY if duration > 0 else EffectType.PERMANENT,
            category=EffectCategory.BUFF if modifier > 0 else EffectCategory.DEBUFF,
            priority=EffectPriority.HIGH,
            duration=duration,
            caster=caster
        )
        self.attribute_name = attribute_name
        self.modifier = modifier
        self.stackable = True
        self.max_stacks = 10
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """应用属性修改"""
        self.set_data("attribute_name", self.attribute_name)
        self.set_data("modifier", self.modifier)
        return EffectResult.success_with_data(
            {"applied": True, "attribute": self.attribute_name, "modifier": self.modifier},
            f"修改 {target.name} 的 {self.attribute_name} 属性"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """移除属性修改"""
        return EffectResult.success_with_data(
            {"removed": True, "attribute": self.attribute_name},
            f"恢复 {target.name} 的 {self.attribute_name} 属性"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """属性修改效果通常不需要每回合更新"""
        return EffectResult.success_with_data(
            {"maintained": True},
            f"维持 {target.name} 的属性修改效果"
        )
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """层数变化时更新修改量"""
        return EffectResult.success_with_data(
            {"old_stacks": old_stacks, "new_stacks": new_stacks, "total_modifier": self.modifier * new_stacks},
            f"属性修改层数从 {old_stacks} 变为 {new_stacks}"
        )
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用"""
        return hasattr(target, 'get_attribute') or hasattr(target, self.attribute_name)
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "type": "属性修改",
            "attribute": self.attribute_name,
            "modifier": self.modifier,
            "stacks": self.current_stacks,
            "total_modifier": self.modifier * self.current_stacks,
            "remaining_turns": self.remaining_duration if self.duration > 0 else "永久"
        }


class ShieldEffect(IEffect):
    """护盾效果"""
    
    def __init__(self, shield_amount: float, duration: int = -1, caster=None):
        super().__init__(
            effect_id=f"shield_{id(self)}",
            name="护盾",
            effect_type=EffectType.TEMPORARY if duration > 0 else EffectType.PERMANENT,
            category=EffectCategory.SHIELD,
            priority=EffectPriority.HIGH,
            duration=duration,
            caster=caster
        )
        self.shield_amount = shield_amount
        self.current_shield = shield_amount
        self.stackable = True
        self.max_stacks = 1  # 护盾通常不叠加层数，而是叠加数值
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """应用护盾效果"""
        self.set_data("shield_amount", self.shield_amount)
        self.set_data("current_shield", self.current_shield)
        return EffectResult.success_with_data(
            {"applied": True, "shield_amount": self.shield_amount},
            f"为 {target.name} 提供 {self.shield_amount} 点护盾"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """移除护盾效果"""
        return EffectResult.success_with_data(
            {"removed": True, "remaining_shield": self.current_shield},
            f"移除 {target.name} 的护盾效果"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """护盾效果通常不需要每回合更新"""
        return EffectResult.success_with_data(
            {"maintained": True, "current_shield": self.current_shield},
            f"维持 {target.name} 的护盾效果"
        )
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """护盾通常通过增加数值而不是层数来叠加"""
        return EffectResult.success_with_data(
            {"old_stacks": old_stacks, "new_stacks": new_stacks},
            f"护盾层数变化"
        )
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用"""
        return hasattr(target, 'hp')
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "type": "护盾",
            "shield_amount": self.shield_amount,
            "current_shield": self.current_shield,
            "remaining_turns": self.remaining_duration if self.duration > 0 else "永久"
        }
    
    def absorb_damage(self, damage: float) -> float:
        """吸收伤害，返回剩余伤害"""
        if self.current_shield <= 0:
            return damage
        
        if damage <= self.current_shield:
            self.current_shield -= damage
            self.set_data("current_shield", self.current_shield)
            return 0
        else:
            remaining_damage = damage - self.current_shield
            self.current_shield = 0
            self.set_data("current_shield", 0)
            return remaining_damage


class StunEffect(IEffect):
    """眩晕效果"""
    
    def __init__(self, duration: int, caster=None):
        super().__init__(
            effect_id=f"stun_{id(self)}",
            name="眩晕",
            effect_type=EffectType.TEMPORARY,
            category=EffectCategory.CONTROL,
            priority=EffectPriority.HIGHEST,
            duration=duration,
            caster=caster
        )
        self.stackable = False  # 眩晕不叠加
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """应用眩晕效果"""
        return EffectResult.success_with_data(
            {"applied": True, "stunned": True},
            f"{target.name} 被眩晕"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """移除眩晕效果"""
        return EffectResult.success_with_data(
            {"removed": True, "stunned": False},
            f"{target.name} 从眩晕中恢复"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """眩晕效果维持"""
        return EffectResult.success_with_data(
            {"maintained": True, "stunned": True},
            f"{target.name} 仍处于眩晕状态"
        )
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """眩晕不叠加层数"""
        return EffectResult.success_with_data(
            {"no_stack": True},
            "眩晕效果不叠加"
        )
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用"""
        return hasattr(target, 'hp') and target.hp > 0
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "type": "控制",
            "effect": "无法行动",
            "remaining_turns": self.remaining_duration
        }


class AttackImmunityEffect(IEffect):
    """攻击免疫效果
    
    提供完全的攻击免疫实现，通过 BEFORE_ATTACK 事件完全阻止攻击及其所有效果。
    支持次数限制和自动移除。
    """
    
    def __init__(self, charges: int = 1, duration: int = -1, caster=None):
        super().__init__(
            effect_id=f"attack_immunity_{id(self)}",
            name="攻击免疫",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SHIELD,
            priority=EffectPriority.HIGH,
            duration=duration,
            caster=caster
        )
        self.stackable = False  # 免疫效果不叠加层数，而是刷新次数
        self.set_data("charges", charges)
        self.set_data("original_charges", charges)
        self.set_data("immunity_type", "attack")
        # 🔧 修复：确保持续时间正确设置
        if duration != -1:
            self.set_data("duration", duration)
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """应用攻击免疫效果"""
        charges = self.get_data("charges", 0)
        return EffectResult.success_with_data(
            {"applied": True, "charges": charges, "immunity_type": "attack"},
            f"{target.name} 获得攻击免疫效果（{charges}次）"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """移除攻击免疫效果"""
        return EffectResult.success_with_data(
            {"removed": True, "immunity_type": "attack"},
            f"{target.name} 失去攻击免疫效果"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """检查免疫次数，如果用完则标记为待移除"""
        from src.core.action import RemoveEffectAction
        charges = self.get_data("charges", 0)
        if charges <= 0:
            # 次数用完，标记为待移除
            return EffectResult.success_with_actions([
                RemoveEffectAction(caster=target, target=target, effect_id=self.id)
            ], "伤害免疫次数已用完")
        
        return EffectResult.success_with_data(
            {"maintained": True, "charges": charges},
            f"维持伤害免疫效果（剩余{charges}次）"
        )
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """免疫效果不叠加层数"""
        return EffectResult.success_with_data(
            {"no_stack": True},
            "伤害免疫效果不叠加层数"
        )

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """声明此效果只在攻击前被调用。"""
        return [
            BeforeAttackCondition()
            # 移除 ImmunityCondition 以避免重复消耗
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理攻击前免疫。"""
        # 只处理 BEFORE_ATTACK 事件
        if event_data.get("event_type") == "BEFORE_ATTACK":
            return self._handle_before_attack(event_data, battle_state)

        return EffectResult.success()

    def _handle_before_attack(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """
        响应 BEFORE_ATTACK 事件实现完全攻击免疫。
        """
        from src.core.action import LogAction, DispatchEventAction, RemoveEffectAction
        from src.core.event.events import ImmunityEvent

        target = event_data.get("target")
        if not target or target != self.owner:
            return EffectResult.success()

        charges = self.get_data("charges", 0)
        if charges > 0:
            # 修改事件数据字典
            event_data["attack_blocked"] = True
            event_data["source_effect"] = self

            # 如果可能，也修改原始事件对象的属性
            original_event = event_data.get("_original_event")
            if original_event and hasattr(original_event, 'attack_blocked'):
                original_event.attack_blocked = True
                original_event.source_effect = self

            # 消耗一次免疫次数
            new_charges = charges - 1
            self.set_data("charges", new_charges)

            # 🔧 修复：不在这里生成ImmunityEvent，让伤害处理器统一处理
            # 这样避免了双重事件生成，确保统计系统正确工作

            # 更新统计信息
            self.update_statistics(battle_state, source=event_data.get("source"), damage_amount=event_data.get("damage", 0))

            actions = [
                LogAction(caster=self.owner, message=f"🛡️ {target.name} 的 '{self.name}' 效果免疫了本次攻击（剩余{new_charges}次）")
            ]

            # 如果次数用完，立即移除效果
            if new_charges <= 0:
                from ..action import RemoveEffectAction
                actions.append(RemoveEffectAction(caster=target, target=target, effect_id=self.id))
                return EffectResult.success_with_actions(actions, "攻击被免疫，免疫效果已用完")
            else:
                return EffectResult.success_with_actions(actions, f"攻击被免疫，剩余{new_charges}次")

        return EffectResult.success()



    def provides_immunity_to(self, effect_to_apply: IEffect) -> bool:
        """
        检查此效果是否能免疫指定效果。
        此效果设计为只通过 `BeforeAttack` 事件来免疫攻击，不提供对其他任何效果类型的被动免疫。
        """
        return False


    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return hasattr(target, 'current_hp') and target.current_hp > 0
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        charges = self.get_data("charges", 0)
        original_charges = self.get_data("original_charges", 1)
        
        return {
            "name": self.name,
            "type": "攻击免疫",
            "description": f"完全免疫下{charges}次攻击及其所有效果",
            "charges": charges,
            "max_charges": original_charges,
            "duration": self.remaining_duration if self.duration > 0 else -1,
            "is_active": charges > 0,
            "immunity_type": "attack"
        }
    
    def consume_charge(self) -> bool:
        """消耗一次免疫次数，返回是否成功"""
        charges = self.get_data("charges", 0)
        if charges > 0:
            self.set_data("charges", charges - 1)
            return True
        return False
    
    def get_remaining_charges(self) -> int:
        """获取剩余免疫次数"""
        return self.get_data("charges", 0)
    
    def refresh_charges(self, new_charges: Optional[int] = None) -> None:
        """刷新免疫次数"""
        if new_charges is None:
            new_charges = self.get_data("original_charges", 1)
        self.set_data("charges", new_charges)

    def update_statistics(self, battle_state, source=None, damage_amount=0):
        """更新免疫统计信息"""
        if hasattr(battle_state, 'statistics'):
            # 获取目标实体
            target = self.owner if hasattr(self, 'owner') else None
            
            # 更新通用免疫计数
            battle_state.statistics.update_immunity_triggered()
            
            # 更新目标特定免疫计数
            if target:
                battle_state.statistics.update_entity_stat(
                    target.id, 'immunities_triggered', 1
                )
                
            # 更新来源特定反制计数
            if source:
                battle_state.statistics.update_entity_stat(
                    source.id, 'counter_immunited', 1
                )
                
            # 记录详细免疫事件
            battle_state.statistics.add_damage_immunity_event({
                'timestamp': battle_state.turn_number,
                'target': target.id if target else None,
                'source': source.id if source else None,
                'damage_amount': damage_amount,
                'effect_id': self.id,
                'effect_name': self.name
            })


class BlockedAttackEffectPlaceholder(IEffect):
    """A placeholder effect to represent a blocked attack in events."""
    def __init__(self, name: str, caster=None):
        super().__init__(
            effect_id=f"placeholder_{name}",
            name=name,
            effect_type=EffectType.TEMPORARY, # Use TEMPORARY
            category=EffectCategory.SYSTEM, # Use SYSTEM
            duration=0,
            caster=caster
        )
    def on_apply(self, target, battle_state) -> EffectResult:
        return EffectResult.success()
    def on_remove(self, target, battle_state) -> EffectResult:
        return EffectResult.success()


__all__ = [
    'DamageOverTimeEffect',
    'HealOverTimeEffect', 
    'AttributeModifierEffect',
    'ShieldEffect',
    'StunEffect',
    'AttackImmunityEffect'
]