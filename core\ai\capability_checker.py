"""
行动能力检查器

负责检查精灵是否能够行动，包括：
- 基础存活检查
- 控制效果检查
- 特殊状态检查
- 能量和资源检查
"""

from __future__ import annotations
from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING, Dict, Any
from abc import ABC, abstractmethod

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.status import battle_status_checker
from core.logging import get_logger

logger = get_logger("ai.capability")

@dataclass
class ActionCapabilityResult:
    """行动能力检查结果"""
    can_act: bool
    reason: str
    blocked_by: List[str] = field(default_factory=list)
    additional_info: Dict[str, Any] = field(default_factory=dict)
    
    def is_blocked_by(self, block_type: str) -> bool:
        """检查是否被特定类型阻止"""
        return block_type in self.blocked_by
    
    def add_block_reason(self, block_type: str, reason: str = None):
        """添加阻止原因"""
        if block_type not in self.blocked_by:
            self.blocked_by.append(block_type)
        if reason:
            self.additional_info[block_type] = reason

class ICapabilityChecker(ABC):
    """行动能力检查器接口"""
    
    @abstractmethod
    def check_capability(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> ActionCapabilityResult:
        """检查精灵的行动能力"""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取检查器优先级（数字越小优先级越高）"""
        pass

class BasicCapabilityChecker(ICapabilityChecker):
    """基础行动能力检查器"""
    
    def get_priority(self) -> int:
        return 10  # 高优先级
    
    def check_capability(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> ActionCapabilityResult:
        """检查基础行动能力"""
        
        # 1. 存活检查
        if not spirit.is_alive:
            return ActionCapabilityResult(
                can_act=False,
                reason="精灵已阵亡",
                blocked_by=["death"]
            )
        
        # 2. 基础状态检查
        if hasattr(spirit, 'current_hp') and spirit.current_hp <= 0:
            return ActionCapabilityResult(
                can_act=False,
                reason="生命值为0",
                blocked_by=["no_hp"]
            )
        
        return ActionCapabilityResult(can_act=True, reason="基础检查通过")

class ControlEffectChecker(ICapabilityChecker):
    """控制效果检查器"""
    
    def get_priority(self) -> int:
        return 20  # 中等优先级
    
    def check_capability(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> ActionCapabilityResult:
        """检查控制效果"""
        
        # 使用现有的状态检查器
        if battle_status_checker.status_checker.is_unable_to_act(spirit):
            action_state = battle_status_checker.status_checker.get_action_state(spirit)
            
            result = ActionCapabilityResult(
                can_act=False,
                reason=f"受到控制效果影响: {action_state.name}",
                blocked_by=["control_effect"]
            )
            result.additional_info["action_state"] = action_state
            return result
        
        return ActionCapabilityResult(can_act=True, reason="控制效果检查通过")


class XuWuStateCapabilityChecker(ICapabilityChecker):
    """虚无状态行动能力检查器"""

    def get_priority(self) -> int:
        return 15  # 最高优先级，在基础检查之后立即执行

    def check_capability(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> ActionCapabilityResult:
        """检查虚无状态是否阻止行动"""

        # 检查精灵是否有虚无状态效果
        if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
            for effect_id, effect in spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', '')
                if effect_name == "虚无状态":
                    return ActionCapabilityResult(
                        can_act=False,
                        reason="处于虚无状态，无法行动",
                        blocked_by=["xuwu_state"]
                    )

        return ActionCapabilityResult(can_act=True, reason="虚无状态检查通过")


class ResourceChecker(ICapabilityChecker):
    """资源检查器（能量、技能冷却等）"""
    
    def get_priority(self) -> int:
        return 30  # 低优先级
    
    def check_capability(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> ActionCapabilityResult:
        """检查资源状态"""
        
        result = ActionCapabilityResult(can_act=True, reason="资源检查通过")
        
        # 检查是否有可用技能
        if hasattr(spirit, 'components'):
            from core.components import SkillComponent
            skill_component = spirit.components.get_component(SkillComponent)
            
            if not skill_component or not skill_component.skills:
                result.can_act = False
                result.reason = "没有可用技能"
                result.blocked_by.append("no_skills")
                return result
            
            # 检查是否有可用的技能（不在冷却中）
            available_skills = []
            for skill in skill_component.skills:
                if self._can_use_skill(spirit, skill, battle_state):
                    available_skills.append(skill)
            
            if not available_skills:
                result.can_act = False
                result.reason = "所有技能都在冷却中或无法使用"
                result.blocked_by.append("skills_unavailable")
                return result
            
            result.additional_info["available_skills"] = available_skills
        
        return result
    
    def _can_use_skill(self, spirit: 'IBattleEntity', skill, battle_state: 'IBattleState') -> bool:
        """检查是否可以使用技能"""
        try:
            # 检查能量消耗
            if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'energy_cost'):
                energy_cost = skill.metadata.energy_cost

                # 🔧 超杀技能适配：超杀技能不检查energy_cost，而是检查energy_threshold
                cast_type = getattr(skill.metadata, 'cast_type', '')
                if cast_type in ['ULTIMATE', 'TONGLING_ULTIMATE']:
                    # 超杀技能使用阈值检查
                    return self._check_ultimate_energy_threshold(spirit, skill)

                # 普通技能检查energy_cost
                if energy_cost > 0:
                    current_energy = getattr(spirit, 'current_energy', 0)
                    if current_energy < energy_cost:
                        return False
            
            # 检查技能冷却
            if hasattr(skill, 'is_on_cooldown') and skill.is_on_cooldown():
                return False
            
            # 检查技能特殊条件
            if hasattr(skill, 'can_use') and not skill.can_use(spirit, battle_state):
                return False
            
            return True
        except Exception as e:
            logger.warning(f"检查技能可用性时出错: {e}")
            return False

    def _check_ultimate_energy_threshold(self, spirit: 'IBattleEntity', skill) -> bool:
        """检查超杀技能的气势阈值"""
        try:
            # 方法1: 从超杀管理器获取阈值
            if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                skill_name = getattr(skill.metadata, 'name', '') if hasattr(skill, 'metadata') else ''
                if hasattr(spirit.ultimate_manager, 'get_ultimate_skill'):
                    skill_config = spirit.ultimate_manager.get_ultimate_skill(skill_name)
                    if skill_config:
                        current_energy = getattr(spirit, 'current_energy', 0)
                        return current_energy >= skill_config.energy_threshold

                # 获取最低阈值
                if hasattr(spirit.ultimate_manager, 'get_lowest_threshold'):
                    threshold = spirit.ultimate_manager.get_lowest_threshold()
                    current_energy = getattr(spirit, 'current_energy', 0)
                    return current_energy >= threshold

            # 方法2: 使用默认阈值
            current_energy = getattr(spirit, 'current_energy', 0)
            return current_energy >= 300  # 默认超杀阈值
        except:
            return False

class ActionCapabilityChecker:
    """行动能力检查器主类"""
    
    def __init__(self):
        self.checkers: List[ICapabilityChecker] = []
        self._setup_default_checkers()
    
    def _setup_default_checkers(self):
        """设置默认检查器"""
        self.checkers = [
            BasicCapabilityChecker(),
            XuWuStateCapabilityChecker(),  # 虚无状态检查器
            ControlEffectChecker(),
            ResourceChecker()
        ]
        # 按优先级排序
        self.checkers.sort(key=lambda x: x.get_priority())
    
    def add_checker(self, checker: ICapabilityChecker):
        """添加自定义检查器"""
        self.checkers.append(checker)
        self.checkers.sort(key=lambda x: x.get_priority())
    
    def remove_checker(self, checker_type: type):
        """移除指定类型的检查器"""
        self.checkers = [c for c in self.checkers if not isinstance(c, checker_type)]
    
    def can_act(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> ActionCapabilityResult:
        """检查精灵是否能够行动"""
        
        logger.debug(f"检查 {getattr(spirit, 'name', 'Unknown')} 的行动能力")
        
        # 依次执行所有检查器
        for checker in self.checkers:
            try:
                result = checker.check_capability(spirit, battle_state)
                
                # 如果任何检查器返回无法行动，立即返回
                if not result.can_act:
                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 无法行动: {result.reason}")
                    return result
                    
            except Exception as e:
                logger.error(f"行动能力检查器 {checker.__class__.__name__} 执行失败: {e}")
                # 继续执行其他检查器
                continue
        
        # 所有检查都通过
        final_result = ActionCapabilityResult(can_act=True, reason="所有检查都通过")
        logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 可以行动")
        return final_result
    
    def get_detailed_status(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> Dict[str, ActionCapabilityResult]:
        """获取详细的检查状态（用于调试）"""
        
        results = {}
        for checker in self.checkers:
            try:
                result = checker.check_capability(spirit, battle_state)
                results[checker.__class__.__name__] = result
            except Exception as e:
                results[checker.__class__.__name__] = ActionCapabilityResult(
                    can_act=False,
                    reason=f"检查器执行失败: {e}",
                    blocked_by=["checker_error"]
                )
        
        return results
