"""
赤妖王·御神 - 精灵模块

这个模块包含了赤妖王·御神精灵的完整实现，包括：
- 基础效果（灵目慧心战斗加成、条件生存等）
- 被动技能效果（狐念之力）
- 技能组件（各种技能的特殊效果）
- 技能定义（所有技能的完整定义）
- 精灵主体（精灵的创建和配置）

模块结构：
- effects.py: 基础效果类
- passive_effects.py: 被动技能效果
- skill_components.py: 技能组件
- skills.py: 技能定义
- spirit.py: 主精灵文件
"""

# 导入主要的创建函数
from .spirit import (
    create_yushen_spirit,
    create_yushen_passive_effects,
    create_chiyaowang_yushen_spirit,  # 向后兼容
    create_passive_effects,  # 向后兼容
    get_spirit_data_from_json,  # 替代 SPIRIT_DATA
    YUSHEN_INFO
)

# 导入基础效果
from .effects import (
    SpiritWisdomBattleBoostEffect,
    ConditionalSurvivalEffect,
    create_spirit_wisdom_battle_boost_effect,
    create_conditional_survival_effect
)

# 导入被动效果
from .passive_effects import (
    FoxSpiritPowerEffect,
    create_fox_spirit_power_effect
)

# 导入技能组件
from .skill_components import (
    AzureFlameDebuffComponent,
    ThousandWebSwitchComponent,
    SpiritWisdomTeamBuffComponent,
    SpiritWisdomBattleBoostComponent,
    EnergyManipulationComponent
)

# 导入技能定义
from .skills import (
    create_yushen_skills,
    YUSHEN_SKILLS_DATA
)


# 主要导出接口
__all__ = [
    # 主要创建函数
    'create_yushen_spirit',
    'create_yushen_passive_effects',
    
    # 向后兼容函数
    'create_chiyaowang_yushen_spirit',
    'create_passive_effects',
    
    # 基础效果
    'SpiritWisdomBattleBoostEffect',
    'ConditionalSurvivalEffect',
    'create_spirit_wisdom_battle_boost_effect',
    'create_conditional_survival_effect',
    
    # 被动效果
    'FoxSpiritPowerEffect',
    'create_fox_spirit_power_effect',
    
    # 技能组件
    'AzureFlameDebuffComponent',
    'ThousandWebSwitchComponent',
    'SpiritWisdomTeamBuffComponent',
    'SpiritWisdomBattleBoostComponent',
    'EnergyManipulationComponent',
    
    # 技能定义
    'create_yushen_skills',
    'YUSHEN_SKILLS_DATA',
    
    # 数据配置
    'get_spirit_data_from_json',  # 替代 SPIRIT_DATA
    'YUSHEN_INFO'
]


# 模块信息
__version__ = "1.0.0"
__author__ = "Augment Agent"
__description__ = "赤妖王·御神精灵模块 - 完整的模块化实现"


def get_module_info():
    """获取模块信息"""
    return {
        "name": "赤妖王·御神",
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "spirit_id": "chiyaowang_yushen",
        "spirit_name": "赤妖王·御神",
        "files": [
            "effects.py",
            "passive_effects.py", 
            "skill_components.py",
            "skills.py",
            "spirit.py",
            "__init__.py"
        ],
        "main_features": [
            "模块化架构",
            "复杂的生存机制",
            "智能目标选择",
            "团队增益系统",
            "条件性战斗加成"
        ]
    }


def validate_module():
    """验证模块完整性"""
    try:
        # 测试主要功能
        spirit = create_yushen_spirit()
        effects = create_yushen_passive_effects(spirit)
        skills = create_yushen_skills(spirit)
        
        return {
            "valid": True,
            "spirit_created": spirit is not None,
            "effects_count": len(effects),
            "skills_count": len(skills),
            "spirit_name": spirit.name if spirit else None
        }
    except Exception as e:
        return {
            "valid": False,
            "error": str(e)
        }


# 快速测试函数
def quick_test():
    """快速测试模块功能"""
    print("🔍 测试赤妖王·御神模块...")
    
    # 获取模块信息
    info = get_module_info()
    print(f"📋 模块: {info['name']} v{info['version']}")
    
    # 验证模块
    validation = validate_module()
    if validation["valid"]:
        print("✅ 模块验证通过")
        print(f"   精灵: {validation['spirit_name']}")
        print(f"   被动效果: {validation['effects_count']}个")
        print(f"   技能: {validation['skills_count']}个")
    else:
        print(f"❌ 模块验证失败: {validation['error']}")
    
    return validation["valid"]


if __name__ == "__main__":
    # 如果直接运行此模块，执行快速测试
    quick_test()
