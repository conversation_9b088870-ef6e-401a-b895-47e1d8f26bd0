#!/usr/bin/env python3
"""
最终战斗测试

验证所有修复是否成功，并运行一个完整的战斗示例
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_final_battle_test():
    """运行最终战斗测试"""
    print("🎮 最终战斗测试")
    print("="*60)
    
    try:
        # 1. 初始化系统
        print("1. 🚀 初始化核心系统...")
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        print("   ✅ 核心系统初始化成功")
        
        # 2. 获取精灵服务
        print("2. 🧙 获取精灵服务...")
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        print(f"   ✅ 发现 {len(available_spirits)} 个可用精灵")
        
        if len(available_spirits) < 2:
            print("   ❌ 可用精灵不足2个")
            return False
        
        # 显示前几个精灵
        print("   📋 可用精灵列表:")
        for i, spirit_id in enumerate(available_spirits[:4], 1):
            print(f"      {i}. {spirit_id}")
        
        # 3. 创建战斗阵型
        print("3. 🏗️ 创建战斗阵型...")
        from core.formation import Formation
        
        # 队伍1
        formation1 = Formation()
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        if spirit1:
            formation1.add_spirit(spirit1, 1, 1)
            print(f"   ✅ 队伍1: {spirit1.name} (HP: {spirit1.current_hp}/{spirit1.max_hp})")
        else:
            print("   ❌ 创建队伍1精灵失败")
            return False
        
        # 队伍2
        formation2 = Formation()
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        if spirit2:
            formation2.add_spirit(spirit2, 3, 1)
            print(f"   ✅ 队伍2: {spirit2.name} (HP: {spirit2.current_hp}/{spirit2.max_hp})")
        else:
            print("   ❌ 创建队伍2精灵失败")
            return False
        
        # 4. 创建战斗引擎
        print("4. ⚔️ 创建战斗引擎...")
        from core.battle.engines import create_battle_engine
        
        battle_engine = create_battle_engine(
            formation1=formation1,
            formation2=formation2,
            victory="ko",
            round_limit=10,  # 限制10回合
            executor_type="phased"
        )
        print(f"   ✅ 战斗引擎创建成功 (回合限制: {battle_engine.round_limit})")
        
        # 5. 运行战斗
        print("5. 🎯 开始战斗...")
        print("   " + "-"*50)
        
        round_count = 0
        battle_ended = False
        
        for round_result in battle_engine.run_battle():
            round_count += 1
            print(f"   📊 第{round_count}回合执行完成")
            
            # 显示精灵状态
            if round_count % 2 == 0:  # 每2回合显示一次状态
                print(f"      {spirit1.name}: HP {spirit1.current_hp}/{spirit1.max_hp}")
                print(f"      {spirit2.name}: HP {spirit2.current_hp}/{spirit2.max_hp}")
            
            # 检查战斗结束
            if round_result.get("type") == "battle_end":
                winner = round_result.get("winner")
                if winner is not None:
                    winner_name = spirit1.name if winner == 0 else spirit2.name
                    print(f"   🏆 战斗结束！获胜方: 队伍{winner+1} ({winner_name})")
                else:
                    print("   🤝 战斗平局")
                battle_ended = True
                break
            
            # 防止无限循环
            if round_count >= 15:
                print("   ⏰ 达到最大测试回合数，停止战斗")
                break
        
        print("   " + "-"*50)
        print(f"6. 📈 战斗统计:")
        print(f"   - 总回合数: {round_count}")
        print(f"   - 回合限制: {battle_engine.round_limit}")
        print(f"   - 战斗结束: {'是' if battle_ended else '否'}")
        print(f"   - 最终状态:")
        print(f"     {spirit1.name}: HP {spirit1.current_hp}/{spirit1.max_hp} ({'存活' if spirit1.is_alive else '阵亡'})")
        print(f"     {spirit2.name}: HP {spirit2.current_hp}/{spirit2.max_hp} ({'存活' if spirit2.is_alive else '阵亡'})")
        
        return True
        
    except Exception as e:
        print(f"❌ 战斗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎊 战斗系统修复验证")
    print("="*60)
    print("本测试将验证以下修复:")
    print("  ✅ ActionStartEvent参数修复")
    print("  ✅ LogAction导入修复")
    print("  ✅ 战斗引擎回合限制修复")
    print("  ✅ 精灵数量限制修复")
    print("  ✅ 系统初始化修复")
    print("="*60)
    
    success = run_final_battle_test()
    
    print("\n" + "="*60)
    if success:
        print("🎉 最终测试成功！")
        print("📋 修复总结:")
        print("  ✅ 所有ActionStartEvent错误已修复")
        print("  ✅ 所有LogAction导入错误已修复")
        print("  ✅ 战斗回合限制正确设置为10")
        print("  ✅ 精灵数量限制已移除")
        print("  ✅ AI行动生成系统正常工作")
        print("  ✅ 战斗引擎稳定运行")
        print("\n🚀 您现在可以运行 python battle_program.py 享受完整的AI战斗体验！")
        print("   - 选择快速战斗模式（选项2）")
        print("   - 观看AI精灵智能战斗")
        print("   - 体验条件性效果（如御神英雄技）")
        print("   - 战斗将在10回合内结束")
    else:
        print("❌ 最终测试失败，仍有问题需要修复")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
