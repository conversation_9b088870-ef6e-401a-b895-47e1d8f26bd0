"""
组件管理器

负责管理精灵的所有组件，提供统一的组件访问接口。
"""
from __future__ import annotations
from typing import Dict, Type, TypeVar, Optional, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from ..spirit.spirit import Spirit

T = TypeVar('T')


class ComponentManager:
    """组件管理器 - 管理精灵的所有组件"""
    
    def __init__(self, owner: 'Spirit'):
        """
        初始化组件管理器
        
        Args:
            owner: 拥有此组件管理器的精灵
        """
        self.owner = owner
        self._components: Dict[Type, Any] = {}
    
    def add_component(self, component: Any) -> None:
        """
        添加组件
        
        Args:
            component: 要添加的组件实例
        """
        component_type = type(component)
        self._components[component_type] = component
    
    def get_component(self, component_type: Type[T]) -> Optional[T]:
        """
        获取指定类型的组件
        
        Args:
            component_type: 组件类型
            
        Returns:
            组件实例，如果不存在则返回None
        """
        return self._components.get(component_type)
    
    def has_component(self, component_type: Type) -> bool:
        """
        检查是否有指定类型的组件
        
        Args:
            component_type: 组件类型
            
        Returns:
            是否存在该类型的组件
        """
        return component_type in self._components
    
    def remove_component(self, component_type: Type) -> bool:
        """
        移除指定类型的组件
        
        Args:
            component_type: 组件类型
            
        Returns:
            是否成功移除
        """
        if component_type in self._components:
            del self._components[component_type]
            return True
        return False
    
    def get_all_components(self) -> Dict[Type, Any]:
        """
        获取所有组件
        
        Returns:
            所有组件的字典
        """
        return self._components.copy()
    
    def clear(self) -> None:
        """清空所有组件"""
        self._components.clear()
    
    def __len__(self) -> int:
        """返回组件数量"""
        return len(self._components)
    
    def __contains__(self, component_type: Type) -> bool:
        """检查是否包含指定类型的组件"""
        return component_type in self._components
    
    def __repr__(self) -> str:
        """字符串表示"""
        component_names = [cls.__name__ for cls in self._components.keys()]
        return f"ComponentManager({', '.join(component_names)})"
