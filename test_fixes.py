#!/usr/bin/env python3
"""
测试修复结果

验证ActionStartEvent和LogAction的修复是否成功
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_action_start_event():
    """测试ActionStartEvent修复"""
    print("🔧 测试ActionStartEvent修复...")
    
    try:
        from core.event.events import ActionStartEvent
        
        # 创建模拟精灵
        class MockSpirit:
            def __init__(self, name):
                self.name = name
                self.id = name.lower()
        
        spirit = MockSpirit("测试精灵")
        
        # 测试正确的参数
        event = ActionStartEvent(actor=spirit, turn_number=1)
        print(f"✅ ActionStartEvent创建成功: {event.actor.name}, 回合{event.turn_number}")
        
        # 测试错误的参数（应该失败）
        try:
            wrong_event = ActionStartEvent(actor=spirit, battle_state="错误参数")
            print("❌ 错误：应该拒绝battle_state参数")
        except TypeError as e:
            print(f"✅ 正确拒绝了错误参数: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ActionStartEvent测试失败: {e}")
        return False

def test_log_action():
    """测试LogAction修复"""
    print("\n🔧 测试LogAction修复...")
    
    try:
        from core.action import LogAction
        
        # 创建模拟精灵
        class MockSpirit:
            def __init__(self, name):
                self.name = name
                self.id = name.lower()
        
        spirit = MockSpirit("测试精灵")
        
        # 创建LogAction
        log_action = LogAction(
            caster=spirit,
            message="测试日志消息",
            level="INFO"
        )
        
        print(f"✅ LogAction创建成功: {log_action.message}")
        print(f"  - 施法者: {log_action.caster.name}")
        print(f"  - 级别: {log_action.level}")
        
        return True
        
    except Exception as e:
        print(f"❌ LogAction测试失败: {e}")
        return False

def test_battle_engine_round_limit():
    """测试战斗引擎回合限制"""
    print("\n🔧 测试战斗引擎回合限制...")
    
    try:
        from core.battle.engines import create_battle_engine
        from core.formation import Formation
        
        # 创建空阵型用于测试
        formation1 = Formation()
        formation2 = Formation()
        
        # 创建战斗引擎
        battle_engine = create_battle_engine(
            formation1=formation1,
            formation2=formation2,
            victory="ko",
            round_limit=5,  # 设置为5回合
            executor_type="phased"
        )
        
        print("✅ 战斗引擎创建成功")
        print(f"  - 回合限制: {getattr(battle_engine, 'round_limit', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 战斗引擎测试失败: {e}")
        return False

def test_import_fixes():
    """测试导入修复"""
    print("\n🔧 测试导入修复...")
    
    try:
        # 测试contract模块
        from core.contract import ContractManager
        print("✅ contract模块导入成功")
        
        # 测试data模块
        from core.data import DataAccess
        print("✅ data模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 修复验证测试")
    print("="*60)
    
    tests = [
        ("ActionStartEvent修复", test_action_start_event),
        ("LogAction修复", test_log_action),
        ("战斗引擎回合限制", test_battle_engine_round_limit),
        ("导入修复", test_import_fixes),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！")
        print("\n📋 修复总结:")
        print("  ✅ ActionStartEvent参数修复")
        print("  ✅ LogAction导入修复")
        print("  ✅ 战斗引擎回合限制修复")
        print("  ✅ 模块导入问题修复")
        print("\n🚀 系统现在应该可以正常运行战斗了！")
    else:
        print("❌ 部分修复仍有问题，需要进一步检查")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
