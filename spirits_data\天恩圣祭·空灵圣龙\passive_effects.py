"""
天恩圣祭·空灵圣龙 - 被动效果

藏隙匿形被动效果：
1. 获得隐身，直到同横排其他精灵全部死亡
2. 隐身状态或神使状态下，该精灵获得40%增伤
"""

from __future__ import annotations
from typing import List, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition
from core.spirit.spirit import Spirit
from .effects import InvisibilityEffect
from .utils import is_spirit_invisible, is_spirit_divine_messenger


class CangXiNiXingPassiveEffect(IEffect):
    """
    藏隙匿形被动效果
    1. 获得隐身，直到同横排其他精灵全部死亡
    2. 隐身状态或神使状态下，该精灵获得40%增伤
    """
    def __init__(self, owner_spirit: Spirit):
        super().__init__(
            effect_id=f"cangxi_nixing_{owner_spirit.id}",
            name="藏隙匿形",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=-1  # 永久被动
        )
        self.owner_spirit = owner_spirit
        self.invisibility_granted = False

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果应用时触发"""
        return EffectResult.success_with_data(
            {"applied": True},
            f"{getattr(target, 'name', '精灵')} 获得藏隙匿形被动效果"
        )

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        from core.effect.triggers import (
            BattleStartCondition, SpiritFaintCondition, BeforeDamageAppliedCondition
        )
        return [
            BattleStartCondition(),           # 战斗开始时检查隐身
            SpiritFaintCondition(),           # 精灵死亡时检查隐身条件
            BeforeDamageAppliedCondition(target="self"), # 造成伤害前检查增伤
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理事件触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "BATTLE_START":
                return self._handle_battle_start(event_data, battle_state)
            elif event_type == "SPIRIT_FAINT":
                return self._handle_spirit_faint(event_data, battle_state)
            elif event_type == "BEFORE_DAMAGE":
                return self._handle_before_damage(event_data, battle_state)

            return EffectResult.success()

        except Exception as e:
            from core.logging import spirit_logger
            spirit_logger.error(f"藏隙匿形被动效果触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)

    def on_event(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理事件（兼容性方法）"""
        return self.on_triggered(event_data, battle_state)

    def _handle_battle_start(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理战斗开始事件"""
        from core.action import LogAction, ApplyEffectAction
        
        actions = []
        
        # 战斗开始时检查是否需要隐身
        if self._should_have_invisibility(battle_state) and not self.invisibility_granted:
            invisibility_effect = InvisibilityEffect(duration=999)  # 持续到条件改变
            actions.extend([
                LogAction(
                    caster=self.owner_spirit,
                    message=f"[藏隙匿形] {self.owner_spirit.name} 获得隐身！"
                ),
                ApplyEffectAction(
                    caster=self.owner_spirit,
                    target=self.owner_spirit,
                    effect=invisibility_effect
                )
            ])
            self.invisibility_granted = True
        
        return EffectResult.success_with_actions(actions, "藏隙匿形战斗开始处理完成")

    def _handle_spirit_faint(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理精灵死亡事件"""
        from core.action import LogAction, RemoveEffectAction
        
        actions = []
        fainted_spirit = event_data.get("spirit")
        
        # 如果死亡的精灵是同排的，检查是否需要移除隐身
        if (fainted_spirit and 
            fainted_spirit.team == self.owner_spirit.team and
            fainted_spirit.position[0] == self.owner_spirit.position[0] and  # 同横排
            fainted_spirit != self.owner_spirit):
            
            # 检查同排是否还有其他存活精灵
            if not self._should_have_invisibility(battle_state):
                # 移除隐身效果
                invisibility_effects = [
                    effect for effect in self.owner_spirit.effect_manager.effects.values()
                    if effect.name == "隐身"
                ]
                
                for effect in invisibility_effects:
                    actions.append(RemoveEffectAction(
                        caster=self.owner_spirit,
                        target=self.owner_spirit,
                        effect_id=effect.id
                    ))
                
                if invisibility_effects:
                    actions.append(LogAction(
                        caster=self.owner_spirit,
                        message=f"[藏隙匿形] {self.owner_spirit.name} 失去隐身！"
                    ))
                    self.invisibility_granted = False
        
        return EffectResult.success_with_actions(actions, "藏隙匿形精灵死亡处理完成")

    def _handle_before_damage(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理造成伤害前事件 - 检查增伤"""
        attacker = event_data.get("attacker")
        if attacker != self.owner_spirit:
            return EffectResult.success("不是效果拥有者攻击")
        
        # 检查是否处于隐身或神使状态
        has_invisibility = is_spirit_invisible(self.owner_spirit)
        has_divine_messenger = is_spirit_divine_messenger(self.owner_spirit)
        
        if has_invisibility or has_divine_messenger:
            # 应用40%增伤
            current_damage = event_data.get("damage", 0)
            bonus_damage = int(current_damage * 0.4)
            event_data["damage"] = current_damage + bonus_damage
            
            state_desc = "隐身" if has_invisibility else "神使"
            return EffectResult.success_with_data(
                {"damage_bonus": bonus_damage},
                f"[藏隙匿形] {state_desc}状态下增伤40%，额外伤害: {bonus_damage}"
            )
        
        return EffectResult.success()

    def _should_have_invisibility(self, battle_state: "IBattleState") -> bool:
        """检查是否应该拥有隐身"""
        # 获取同横排的其他存活精灵
        same_row_spirits = [
            spirit for spirit in battle_state.get_all_spirits()
            if (spirit.team == self.owner_spirit.team and
                spirit.position[0] == self.owner_spirit.position[0] and  # 同横排
                spirit != self.owner_spirit and
                spirit.is_alive)
        ]
        
        # 如果同横排没有其他存活精灵，则不应该有隐身
        return len(same_row_spirits) > 0

    # 这些方法已经被utils.py中的工具函数替代

    def on_remove(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{getattr(target, 'name', '精灵')} 失去藏隙匿形被动效果"
        )

    def on_update(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data(
            {"updated": True}, 
            "藏隙匿形被动效果更新"
        )

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "获得隐身直到同排精灵死亡；隐身或神使状态下增伤40%",
            "invisibility_granted": self.invisibility_granted,
            "duration": -1
        }


def create_kongling_passive_effects(owner_spirit: Spirit) -> List[IEffect]:
    """创建空灵圣龙的被动效果"""
    return [
        CangXiNiXingPassiveEffect(owner_spirit)
    ]


# 导出所有被动效果类和创建函数
__all__ = [
    'CangXiNiXingPassiveEffect',
    'create_kongling_passive_effects'
]
