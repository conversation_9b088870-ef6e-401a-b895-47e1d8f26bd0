# AI行动生成系统

这是一个完整的、可扩展的精灵行动生成系统，专门设计用于处理复杂的战斗逻辑，包括控制效果检查、动态条件评估、条件性效果计算等。

## 🎯 核心特性

### 1. 分层架构设计
- **行动能力检查器**: 检查精灵是否能够行动（控制效果、资源等）
- **动态条件评估器**: 评估攻击时的各种动态条件
- **条件性效果计算器**: 根据条件计算额外效果（如御神英雄技）
- **智能行动生成器**: 整合所有检查和计算逻辑
- **扩展接口**: 支持插件化扩展

### 2. 复杂条件处理
- ✅ 控制效果检查（眩晕、沉默、冰冻等）
- ✅ 动态目标状态判断（无法行动、低血量等）
- ✅ 条件性效果应用（御神英雄技的40%暴击率加成等）
- ✅ 事件时机管理（在正确时机发出事件）

### 3. 高性能优化
- 🚀 缓存机制（条件评估结果缓存）
- 🚀 批量处理（减少重复计算）
- 🚀 错误隔离（单个组件失败不影响整体）
- 🚀 统计监控（性能和使用统计）

## 📋 系统架构

```
精灵.generate_actions()
    ↓
IntelligentActionGenerator
    ├── ActionCapabilityChecker (行动能力检查)
    │   ├── BasicCapabilityChecker (基础检查)
    │   ├── ControlEffectChecker (控制效果检查)
    │   └── ResourceChecker (资源检查)
    │
    ├── SkillSelector (技能选择)
    │   └── 评估技能优先级
    │
    ├── TargetSelector (目标选择)
    │   └── 评估目标优先级
    │
    ├── DynamicConditionEvaluator (动态条件评估)
    │   ├── TargetStatusEvaluator (目标状态)
    │   ├── AttackerStatusEvaluator (攻击者状态)
    │   ├── BattlefieldEvaluator (战场环境)
    │   └── SkillEvaluator (技能特定)
    │
    └── ConditionalEffectCalculator (条件性效果计算)
        ├── SpiritWisdomEffectCalculator (灵目慧心)
        ├── CriticalHitEffectCalculator (暴击效果)
        ├── ExecuteEffectCalculator (斩杀效果)
        ├── ComboEffectCalculator (连击效果)
        └── TeamworkEffectCalculator (团队协作)
```

## 🚀 快速开始

### 基础使用

```python
# 在精灵类中，系统已经自动集成
def generate_actions(self, battle_state):
    """精灵自动调用此方法生成行动"""
    # 系统会自动：
    # 1. 检查是否能行动
    # 2. 选择技能和目标
    # 3. 评估动态条件
    # 4. 计算条件性效果
    # 5. 生成增强行动
    pass

# 检查精灵是否能行动
can_act = spirit.can_act(battle_state)

# 获取详细的行动能力信息
details = spirit.get_action_capability_details(battle_state)

# 预览行动（不实际执行）
preview = spirit.preview_action(battle_state)
```

### 扩展系统使用

```python
from core.ai.extensions import register_condition_checker, register_effect_calculator

# 注册自定义条件检查器
@register_condition_checker(
    name="my_condition_checker",
    version="1.0.0",
    author="Your Name",
    description="自定义条件检查器"
)
class MyConditionChecker:
    def check_condition(self, attacker, target, skill, battle_state):
        return {
            'my_custom_condition': True,
            'special_bonus': attacker.level > 10
        }

# 注册自定义效果计算器
@register_effect_calculator(
    name="my_effect_calculator",
    version="1.0.0", 
    author="Your Name",
    description="自定义效果计算器"
)
class MyEffectCalculator:
    def calculate_effects(self, attacker, target, skill, conditions, battle_state):
        from core.ai.effect_calculator import ConditionalEffectResult
        
        result = ConditionalEffectResult()
        
        if conditions.get_condition('my_custom_condition'):
            result.add_effect('custom_damage_bonus', 0.2)
            result.add_trigger_event('custom_effect_triggered')
        
        return result
```

## 🎯 实际应用示例

### 御神英雄技的复杂处理

系统能够完美处理御神英雄技的复杂逻辑：

```python
# 当队友攻击无法行动的精灵时：
# 1. DynamicConditionEvaluator 检测到目标无法行动
# 2. SpiritWisdomEffectCalculator 检测到攻击者有灵目慧心效果
# 3. 自动应用：暴击率+40%、暴击伤害+40%、破击率+40%、获得30气势
# 4. 生成 EnhancedAttackAction 包含所有加成
# 5. 执行器处理增强攻击，应用所有效果
```

### 控制效果处理

```python
# 当精灵被眩晕时：
# 1. ActionCapabilityChecker 检测到控制效果
# 2. 返回 UnableToActEvent 而不是攻击动作
# 3. 发出相应事件供其他系统监听
# 4. 记录无法行动的原因
```

## 🔧 配置和自定义

### 全局配置

```python
from core.ai import get_action_generator

# 获取全局行动生成器
generator = get_action_generator()

# 添加自定义检查器
generator.capability_checker.add_checker(MyCustomChecker())

# 添加自定义评估器
generator.condition_evaluator.add_evaluator(MyCustomEvaluator())

# 添加自定义计算器
generator.effect_calculator.add_calculator(MyCustomCalculator())
```

### 扩展管理

```python
from core.ai.extensions import ExtensionManager

# 列出所有扩展
extensions = ExtensionManager.list_all_extensions()

# 获取扩展统计
stats = ExtensionManager.get_extension_stats()

# 重新加载扩展
ExtensionManager.reload_extensions()
```

## 📊 性能监控

系统内置了完整的性能监控：

```python
# 缓存统计
evaluator = get_action_generator().condition_evaluator
print(f"缓存命中率: {evaluator._cache_hits / (evaluator._cache_hits + evaluator._cache_misses)}")

# 执行时间统计
# 系统会自动记录各个组件的执行时间

# 错误统计
# 系统会记录和报告各种错误情况
```

## 🎮 增强动作类型

系统引入了新的动作类型来支持复杂逻辑：

- **EnhancedAttackAction**: 包含条件性效果的攻击动作
- **UnableToActEvent**: 无法行动事件
- **ActionDecisionAction**: 行动决策动作
- **ConditionalEffectTriggerAction**: 条件性效果触发动作

## 🔍 调试和分析

```python
# 获取详细的行动能力分析
details = spirit.get_action_capability_details(battle_state)
print(f"能否行动: {details['can_act']}")
print(f"原因: {details['reason']}")
print(f"详细状态: {details['detailed_status']}")

# 预览行动效果
preview = spirit.preview_action(battle_state)
print(f"选择的技能: {preview['skill_name']}")
print(f"目标数量: {preview['target_count']}")
print(f"预期效果: {preview['action_previews']}")
```

## 🚀 未来扩展

系统设计为高度可扩展，可以轻松添加：

- 新的条件检查器（天气、地形、时间等）
- 新的效果计算器（元素相克、阵型加成等）
- 新的行动策略（防御策略、逃跑策略等）
- 新的AI算法（机器学习、神经网络等）

## 📝 注意事项

1. **性能考虑**: 系统使用缓存机制，但复杂的条件检查仍可能影响性能
2. **扩展兼容性**: 自定义扩展应该处理异常情况，避免影响核心系统
3. **调试支持**: 系统提供了丰富的调试信息，建议在开发时启用详细日志
4. **版本兼容**: 扩展系统支持版本管理，升级时注意兼容性

## 🎉 总结

这个AI行动生成系统是一个完整的、生产级的解决方案，能够处理从简单的技能选择到复杂的条件性效果计算的所有情况。通过分层架构和扩展接口，它既保证了核心功能的稳定性，又提供了无限的扩展可能性。

无论是实现御神英雄技这样的复杂逻辑，还是添加新的战斗机制，这个系统都能够优雅地处理，让您的战斗系统真正"活"起来！
