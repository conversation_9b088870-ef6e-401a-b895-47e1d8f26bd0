"""
神曜圣谕·女帝 - 基础效果模块

包含女帝使用的通用效果类：
- TauntEffect: 嘲讽效果
- DamageReductionEffect: 减伤效果
"""
from __future__ import annotations
from typing import List, Optional, TYPE_CHECKING, Dict, Any
import uuid

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition


class TauntEffect(IEffect):
    """
    嘲讽效果 - 强制敌人攻击自己

    🔥 完整实现版本：包含目标重定向机制
    """

    def __init__(self, charges: int = 2, duration: int = -1):
        super().__init__(
            effect_id=f"taunt_{uuid.uuid4().hex}",
            name="Taunt",
            effect_type=EffectType.BUFF,
            category=EffectCategory.CONTROL,
            priority=EffectPriority.HIGH,
            duration=duration
        )
        
        self.charges = charges
        self.max_charges = charges
        self.remaining_charges = charges

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        self.set_data("taunt_active", True)
        self.set_data("charges", self.charges)
        self.set_data("max_charges", self.max_charges)
        
        return EffectResult.success_with_data(
            {"applied": True, "charges": self.charges}, 
            f"{target.name} 获得嘲讽效果（{self.charges}次）"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{target.name} 失去嘲讽效果"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        # 检查是否还有剩余次数
        if self.remaining_charges <= 0:
            return EffectResult.success_with_data(
                {"should_remove": True}, 
                "嘲讽次数用完，效果移除"
            )
        
        return EffectResult.success_with_data({}, f"嘲讽剩余次数：{self.remaining_charges}")

    def consume_charge(self) -> bool:
        """消耗一次嘲讽次数"""
        if self.remaining_charges > 0:
            self.remaining_charges -= 1
            self.set_data("charges", self.remaining_charges)
            return True
        return False

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"嘲讽效果（剩余{self.remaining_charges}次）",
            "charges": self.remaining_charges,
            "max_charges": self.max_charges,
            "duration": self.remaining_duration if self.duration > 0 else -1
        }


class DamageReductionEffect(IEffect):
    """
    减伤效果
    
    改写自旧的 DamageReductionEffect，使用统一效果系统
    """
    
    def __init__(self, reduction_rate: float = 0.2, duration: int = -1, stacks: int = 1):
        super().__init__(
            effect_id=f"damage_reduction_{uuid.uuid4().hex}",
            name="DamageReduction",
            effect_type=EffectType.BUFF,
            category=EffectCategory.DEFENSIVE,
            priority=EffectPriority.MEDIUM,
            duration=duration
        )
        
        self.reduction_rate = reduction_rate
        self.stacks = stacks
        self.max_stacks = 10  # 最大叠加层数

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        self.set_data("reduction_rate", self.reduction_rate)
        self.set_data("stacks", self.stacks)
        
        return EffectResult.success_with_data(
            {"applied": True, "reduction_rate": self.reduction_rate}, 
            f"{target.name} 获得{self.reduction_rate*100:.0f}%减伤效果"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{target.name} 失去减伤效果"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data({}, f"减伤效果持续中：{self.reduction_rate*100:.0f}%")

    def add_stack(self, additional_rate: float = 0.1) -> bool:
        """增加减伤层数"""
        if self.stacks < self.max_stacks:
            self.stacks += 1
            self.reduction_rate += additional_rate
            self.set_data("reduction_rate", self.reduction_rate)
            self.set_data("stacks", self.stacks)
            return True
        return False

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "减伤效果",
            "reduction_rate": f"{self.reduction_rate*100:.0f}%",
            "stacks": self.stacks,
            "duration": self.remaining_duration if self.duration > 0 else -1
        }


# 导出函数
def create_taunt_effect(charges: int = 2, duration: int = -1) -> TauntEffect:
    """创建嘲讽效果"""
    return TauntEffect(charges, duration)


def create_damage_reduction_effect(reduction_rate: float = 0.2, duration: int = -1, stacks: int = 1) -> DamageReductionEffect:
    """创建减伤效果"""
    return DamageReductionEffect(reduction_rate, duration, stacks)


# 导出所有效果类
__all__ = [
    'TauntEffect',
    'DamageReductionEffect', 
    'create_taunt_effect',
    'create_damage_reduction_effect'
]
