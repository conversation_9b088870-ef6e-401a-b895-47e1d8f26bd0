#!/usr/bin/env python3
"""
调试虚无效果的事件处理
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_xuwu_event_debug():
    """调试虚无效果的事件处理"""
    print("🔧 调试虚无效果的事件处理...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=10, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 等待虚无效果被应用
        print(f"\n📋 等待虚无效果被应用...")
        
        # 执行第一回合，让伏妖攻击并应用虚无效果
        result = engine.execute_next_spirit_turn()
        print(f"第一回合结果: {result.get('type', 'Unknown')}")
        
        # 检查虚无效果是否被应用
        target_effects = len(other_spirit.effect_manager.effects)
        print(f"目标效果数量: {target_effects}")
        
        xuwu_effect = None
        if target_effects > 0:
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"  - {effect_name}")
                if effect_name == "虚无状态":
                    xuwu_effect = effect
                    print(f"    ✅ 找到虚无效果: {effect_id}")
        
        if not xuwu_effect:
            print(f"❌ 虚无效果没有被应用，无法继续测试")
            return False
        
        # 检查虚无效果的触发条件
        print(f"\n📋 检查虚无效果的触发条件:")
        if hasattr(xuwu_effect, 'get_trigger_conditions'):
            conditions = xuwu_effect.get_trigger_conditions()
            print(f"  触发条件数量: {len(conditions)}")
            for i, condition in enumerate(conditions):
                condition_type = type(condition).__name__
                event_type = getattr(condition, 'event_type', 'Unknown')
                print(f"    条件{i+1}: {condition_type} -> {event_type}")
        
        # 检查事件管理器的订阅情况
        print(f"\n📋 检查事件管理器的订阅情况:")
        if hasattr(battle_state, 'unified_event_manager'):
            event_manager = battle_state.unified_event_manager
            subscriptions = getattr(event_manager, '_subscriptions', {})
            print(f"  总订阅数量: {sum(len(listeners) for listeners in subscriptions.values())}")
            
            for event_type, listeners in subscriptions.items():
                if listeners:
                    print(f"    {event_type}: {len(listeners)} 个监听器")
                    for j, listener in enumerate(listeners):
                        listener_name = getattr(listener, '__name__', str(listener))
                        print(f"      监听器{j+1}: {listener_name}")
        
        # 手动测试虚无效果的事件处理
        print(f"\n📋 手动测试虚无效果的事件处理:")
        
        # 测试1：虚无状态精灵发起攻击
        print(f"  测试1: 虚无状态精灵发起攻击")
        test_event_data_1 = {
            "event_type": "BEFORE_ATTACK",
            "attacker": other_spirit,  # 虚无状态精灵
            "target": fuyao_spirit,
            "skill_name": "测试攻击"
        }
        
        try:
            result_1 = xuwu_effect.on_triggered(test_event_data_1, battle_state)
            print(f"    结果: {result_1.success if hasattr(result_1, 'success') else 'Unknown'}")
            print(f"    消息: {result_1.message if hasattr(result_1, 'message') else 'No message'}")
            
            if hasattr(result_1, 'actions') and result_1.actions:
                print(f"    生成动作数量: {len(result_1.actions)}")
                for k, action in enumerate(result_1.actions):
                    action_type = type(action).__name__
                    print(f"      动作{k+1}: {action_type}")
            
            # 检查事件数据是否被修改
            if test_event_data_1.get("attack_blocked"):
                print(f"    ✅ 攻击被阻止: attack_blocked = True")
            else:
                print(f"    ❌ 攻击没有被阻止: attack_blocked = {test_event_data_1.get('attack_blocked', 'Not set')}")
                
        except Exception as e:
            print(f"    ❌ 测试失败: {e}")
        
        # 测试2：攻击虚无状态精灵
        print(f"  测试2: 攻击虚无状态精灵")
        test_event_data_2 = {
            "event_type": "BEFORE_ATTACK",
            "attacker": fuyao_spirit,
            "target": other_spirit,  # 虚无状态精灵
            "skill_name": "测试攻击"
        }
        
        try:
            result_2 = xuwu_effect.on_triggered(test_event_data_2, battle_state)
            print(f"    结果: {result_2.success if hasattr(result_2, 'success') else 'Unknown'}")
            print(f"    消息: {result_2.message if hasattr(result_2, 'message') else 'No message'}")
            
            if hasattr(result_2, 'actions') and result_2.actions:
                print(f"    生成动作数量: {len(result_2.actions)}")
                for k, action in enumerate(result_2.actions):
                    action_type = type(action).__name__
                    print(f"      动作{k+1}: {action_type}")
            
            # 检查事件数据是否被修改
            if test_event_data_2.get("attack_blocked"):
                print(f"    ✅ 攻击被阻止: attack_blocked = True")
            else:
                print(f"    ❌ 攻击没有被阻止: attack_blocked = {test_event_data_2.get('attack_blocked', 'Not set')}")
                
        except Exception as e:
            print(f"    ❌ 测试失败: {e}")
        
        # 判断测试结果
        attack_blocked_1 = test_event_data_1.get("attack_blocked", False)
        attack_blocked_2 = test_event_data_2.get("attack_blocked", False)
        
        print(f"\n📋 调试结果总结:")
        print(f"  虚无效果存在: ✅")
        print(f"  触发条件正确: ✅")
        print(f"  虚无状态精灵发起攻击被阻止: {'✅' if attack_blocked_1 else '❌'}")
        print(f"  攻击虚无状态精灵被阻止: {'✅' if attack_blocked_2 else '❌'}")
        
        if attack_blocked_1 and attack_blocked_2:
            print(f"\n✅ 虚无效果的事件处理完全正常！")
            print(f"  问题可能在于实际战斗中的事件分发机制")
            return True
        else:
            print(f"\n❌ 虚无效果的事件处理有问题")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 虚无效果事件处理调试")
    print("="*60)
    
    result = test_xuwu_event_debug()
    
    print("\n" + "="*60)
    if result:
        print("✅ 虚无效果事件处理调试成功")
        print("虚无效果的事件处理逻辑正常")
    else:
        print("❌ 虚无效果事件处理调试失败")

if __name__ == "__main__":
    main()
