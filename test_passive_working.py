#!/usr/bin/env python3
"""
简化测试：验证被动技能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_passive_working():
    """测试被动技能是否正常工作"""
    print("🔧 测试被动技能是否正常工作...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        # 创建伏妖和其他精灵
        fuyao_spirit = None
        other_spirit = None
        
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            fuyao_spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        other_spirit = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1 = Formation()
        formation2 = Formation()
        
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        # 检查被动技能
        print(f"\n🔍 检查被动技能:")
        passive_skills = []
        
        for skill in fuyao_spirit.skills:
            if hasattr(skill, 'metadata') and getattr(skill.metadata, 'cast_type', '') == 'PASSIVE':
                passive_skills.append(skill.metadata.name)
        
        print(f"  {fuyao_spirit.name} 被动技能: {passive_skills}")
        
        if not passive_skills:
            print(f"  ❌ 没有找到被动技能")
            return False
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=2,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗引擎创建成功")
        
        # 执行第一回合
        print(f"\n🎯 执行第一回合...")
        
        try:
            result = engine.execute_round()
            result_type = result.get('type', 'unknown')
            print(f"  第一回合执行结果: {result_type}")
            
            # 检查日志中是否有被动技能应用的记录
            battle_log = result.get('state', {}).get('log', [])
            passive_applied = False
            
            for log_entry in battle_log:
                message = log_entry.get('message', '')
                if '被动技能' in message and '已应用' in message:
                    print(f"  ✅ 发现被动技能应用: {message}")
                    passive_applied = True
            
            if passive_applied:
                print(f"\n✅ 被动技能修复成功！")
                print(f"  📋 验证结果:")
                print(f"    1. 被动技能应用已重新启用")
                print(f"    2. 第一回合成功应用了被动技能")
                print(f"    3. 战斗日志显示被动技能正常工作")
                print(f"  🎯 伏妖被动和其他技能效果现在应该正常生效")
                return True
            else:
                print(f"  ❌ 未在日志中找到被动技能应用记录")
                return False
                
        except Exception as e:
            print(f"  ❌ 第一回合执行失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 被动技能工作验证")
    print("="*50)
    
    result = test_passive_working()
    
    print("\n" + "="*50)
    if result:
        print("✅ 被动技能修复验证成功")
        print("\n🎉 极其严重的问题已解决！")
        print("伏妖被动和其他技能效果现在正常生效")
    else:
        print("❌ 被动技能修复验证失败")

if __name__ == "__main__":
    main()
