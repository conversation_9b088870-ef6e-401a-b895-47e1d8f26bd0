<template>
  <div class="analytics-card stat-card" :style="{ background: gradient }">
    <div class="stat-content">
      <div class="stat-info">
        <p class="stat-label">{{ title }}</p>
        <p class="stat-value">{{ formattedValue }}</p>
        <p class="stat-change" v-if="change !== undefined">
          <span :class="changeClass">
            {{ changePrefix }}{{ Math.abs(change) }}{{ changeSuffix }}
          </span>
          {{ changeLabel }}
        </p>
      </div>
      <el-icon class="stat-icon">
        <component :is="icon" />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title: string
  value: number | string
  change?: number
  changeLabel?: string
  icon: string
  gradient: string
  suffix?: string
}

const props = withDefaults(defineProps<Props>(), {
  changeLabel: 'vs 上周',
  suffix: ''
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString() + props.suffix
  }
  return props.value
})

const changeClass = computed(() => {
  if (props.change === undefined) return ''
  
  // 对于时长类指标，减少是好的
  if (props.title.includes('时长') || props.title.includes('时间')) {
    return props.change <= 0 ? 'text-green-300' : 'text-red-300'
  }
  
  // 对于其他指标，增加是好的
  return props.change >= 0 ? 'text-green-300' : 'text-red-300'
})

const changePrefix = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0 ? '+' : ''
})

const changeSuffix = computed(() => {
  if (props.change === undefined) return ''
  if (props.title.includes('时长') || props.title.includes('时间')) {
    return 's'
  }
  return '%'
})
</script>