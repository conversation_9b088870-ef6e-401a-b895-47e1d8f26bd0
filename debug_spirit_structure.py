#!/usr/bin/env python3
"""
调试精灵结构

查看精灵对象的实际属性和效果管理器结构
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_spirit_structure():
    """调试精灵结构"""
    print("🔧 调试精灵结构...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"可用精灵: {available_spirits}")
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        # 创建一个精灵
        spirit_name = available_spirits[0]
        spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
        
        print(f"\n📊 精灵: {spirit.name}")
        print(f"类型: {type(spirit)}")
        print(f"ID: {spirit.id}")
        print(f"队伍: {spirit.team}")
        print(f"生命值: {spirit.current_hp}/{spirit.max_hp}")
        
        # 检查属性
        print(f"\n🔍 精灵属性:")
        for attr_name in dir(spirit):
            if not attr_name.startswith('_'):
                try:
                    attr_value = getattr(spirit, attr_name)
                    if not callable(attr_value):
                        print(f"  {attr_name}: {type(attr_value)} = {attr_value}")
                except:
                    print(f"  {attr_name}: <无法访问>")
        
        # 检查效果管理器
        print(f"\n🎯 效果管理器检查:")
        if hasattr(spirit, 'effect_manager'):
            effect_manager = spirit.effect_manager
            print(f"  effect_manager 存在: {effect_manager}")
            print(f"  effect_manager 类型: {type(effect_manager)}")
            
            if effect_manager:
                print(f"  effect_manager 属性:")
                for attr_name in dir(effect_manager):
                    if not attr_name.startswith('_'):
                        try:
                            attr_value = getattr(effect_manager, attr_name)
                            if not callable(attr_value):
                                print(f"    {attr_name}: {type(attr_value)} = {attr_value}")
                        except:
                            print(f"    {attr_name}: <无法访问>")
                
                # 检查effects属性
                if hasattr(effect_manager, 'effects'):
                    effects = effect_manager.effects
                    print(f"  effects: {type(effects)} = {effects}")
                    
                    if hasattr(effects, 'items'):
                        print(f"  effects.items():")
                        for effect_id, effect in effects.items():
                            print(f"    {effect_id}: {type(effect)} = {effect}")
                            
                            # 检查效果的属性
                            print(f"      效果属性:")
                            for effect_attr in dir(effect):
                                if not effect_attr.startswith('_'):
                                    try:
                                        effect_value = getattr(effect, effect_attr)
                                        if not callable(effect_value):
                                            print(f"        {effect_attr}: {effect_value}")
                                    except:
                                        print(f"        {effect_attr}: <无法访问>")
                else:
                    print("  ❌ effect_manager 没有 effects 属性")
            else:
                print("  ❌ effect_manager 为 None")
        else:
            print("  ❌ 精灵没有 effect_manager 属性")
        
        # 检查其他可能的效果存储方式
        print(f"\n🔍 其他效果存储检查:")
        possible_effect_attrs = ['effects', 'active_effects', 'buffs', 'debuffs', 'status_effects']
        
        for attr_name in possible_effect_attrs:
            if hasattr(spirit, attr_name):
                attr_value = getattr(spirit, attr_name)
                print(f"  {attr_name}: {type(attr_value)} = {attr_value}")
                
                if hasattr(attr_value, '__len__'):
                    print(f"    长度: {len(attr_value)}")
                
                if hasattr(attr_value, 'items'):
                    print(f"    items:")
                    for k, v in attr_value.items():
                        print(f"      {k}: {v}")
                elif hasattr(attr_value, '__iter__') and not isinstance(attr_value, str):
                    print(f"    内容:")
                    for i, item in enumerate(attr_value):
                        print(f"      [{i}]: {item}")
                        if i >= 5:  # 限制显示数量
                            print(f"      ... (还有 {len(attr_value) - 6} 个)")
                            break
        
        # 检查技能
        print(f"\n🎮 技能检查:")
        if hasattr(spirit, 'skills'):
            skills = spirit.skills
            print(f"  skills: {type(skills)} = {skills}")
            print(f"  技能数量: {len(skills) if hasattr(skills, '__len__') else '未知'}")
            
            if hasattr(skills, '__iter__'):
                for i, skill in enumerate(skills):
                    print(f"    技能 {i}: {skill}")
                    if hasattr(skill, 'metadata'):
                        metadata = skill.metadata
                        print(f"      名称: {getattr(metadata, 'name', '未知')}")
                        print(f"      类型: {getattr(metadata, 'cast_type', '未知')}")
                        print(f"      描述: {getattr(metadata, 'description', '未知')}")
                    if i >= 3:  # 限制显示数量
                        print(f"      ... (还有 {len(skills) - 4} 个技能)")
                        break
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 精灵结构调试")
    print("="*60)
    
    success = debug_spirit_structure()
    
    print("\n" + "="*60)
    print("📊 调试结果")
    print("="*60)
    
    if success:
        print("✅ 调试完成")
    else:
        print("❌ 调试失败")

if __name__ == "__main__":
    main()
