#!/usr/bin/env python3
"""
调试can_cast方法
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_can_cast():
    """调试can_cast方法"""
    print("🔍 调试can_cast方法...")
    
    try:
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.models import BattleState
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 设置高气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
        
        # 创建战斗状态
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 获取超杀技能
        ultimate_skill = None
        for skill in spirit1.skills:
            if hasattr(skill, 'metadata') and skill.metadata.cast_type == 'ULTIMATE':
                ultimate_skill = skill
                break
        
        if ultimate_skill:
            print(f"  找到超杀技能: {ultimate_skill.metadata.name}")
            
            # 检查owner
            print(f"  技能owner: {ultimate_skill.owner}")
            print(f"  技能owner是否为None: {ultimate_skill.owner is None}")
            
            if ultimate_skill.owner is None:
                print(f"  ❌ 问题找到：技能的owner为None")
                print(f"  🔧 尝试设置owner...")
                ultimate_skill.owner = spirit1
                print(f"  设置后owner: {ultimate_skill.owner}")
            
            # 检查条件
            print(f"  技能条件数量: {len(ultimate_skill.conditions)}")
            for i, condition in enumerate(ultimate_skill.conditions):
                print(f"    条件{i}: {type(condition).__name__}")
            
            # 调用can_cast
            print(f"  调用can_cast...")
            can_cast_result = ultimate_skill.can_cast(battle_state)
            print(f"  can_cast结果: {can_cast_result}")
            
            if can_cast_result:
                print(f"  ✅ can_cast返回True")
                
                # 再次测试cast方法
                print(f"  再次测试cast方法...")
                actions = ultimate_skill.cast(battle_state)
                print(f"  cast返回动作数量: {len(actions)}")
                
                if len(actions) > 0:
                    print(f"  ✅ 修复成功！cast方法现在能正常工作")
                    return True
                else:
                    print(f"  ❌ cast方法仍然返回0个动作")
                    return False
            else:
                print(f"  ❌ can_cast返回False")
                
                # 详细检查每个条件
                print(f"  详细检查条件...")
                
                from core.skill.skills import SkillContext
                context = SkillContext(
                    skill_level=ultimate_skill.current_level,
                    additional_data={
                        'skill_id': ultimate_skill.id,
                        'cooldown': ultimate_skill.metadata.cooldown
                    }
                )
                
                for i, condition in enumerate(ultimate_skill.conditions):
                    try:
                        result = condition.check(ultimate_skill.owner, battle_state, context)
                        print(f"    条件{i} ({type(condition).__name__}): {result}")
                        if not result:
                            print(f"      ❌ 这个条件导致can_cast失败")
                    except Exception as e:
                        print(f"    条件{i} ({type(condition).__name__}): 异常 - {e}")
                
                return False
        else:
            print(f"  ❌ 没有找到超杀技能")
            return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 调试can_cast方法")
    print("="*60)
    
    result = debug_can_cast()
    
    print("\n" + "="*60)
    print("📊 调试结果:")
    print("="*60)
    
    if result:
        print("✅ 问题已修复")
    else:
        print("❌ 问题仍然存在")

if __name__ == "__main__":
    main()
