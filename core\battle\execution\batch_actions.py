"""
批量Action支持

提供批量处理多个相同类型Action的功能，提高性能和减少重复代码。
"""
from __future__ import annotations

from typing import List, Optional, Dict, Any, cast
from dataclasses import dataclass, field

from core.battle.executor.executor import handler, UnifiedActionExecutor
from .decorators import safe_execute, monitor_performance
from ...action import BattleAction, ApplyEffectAction, DamageAction, ModifyAttributeAction
from ...interfaces import IBattleEntity
from ...effect.system import IEffect


@dataclass(slots=True)
class BatchApplyEffectAction(BattleAction):
    """批量施加效果Action"""
    targets: List[IBattleEntity]
    effect: IEffect
    from_attack: bool = False
    
    def get_targets(self) -> List[IBattleEntity]:
        return self.targets
    
    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "target_ids": [t.id for t in self.targets],
            "effect_name": self.effect.name,
            "from_attack": self.from_attack,
            "target_count": len(self.targets)
        })
        return data


@dataclass(slots=True)
class BatchDamageAction(BattleAction):
    """批量伤害Action"""
    damage_actions: List[DamageAction]
    
    def get_targets(self) -> List[IBattleEntity]:
        return [action.target for action in self.damage_actions]
    
    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "damage_count": len(self.damage_actions),
            "total_damage": sum(action.damage_value or 0 for action in self.damage_actions),
            "target_ids": [action.target.id for action in self.damage_actions]
        })
        return data


@dataclass(slots=True)
class BatchModifyAttributeAction(BattleAction):
    """批量属性修改Action"""
    modifications: List[ModifyAttributeAction]
    
    def get_targets(self) -> List[IBattleEntity]:
        return [mod.target for mod in self.modifications]
    
    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "modification_count": len(self.modifications),
            "target_ids": [mod.target.id for mod in self.modifications],
            "attributes_modified": list(set(mod.attribute_name for mod in self.modifications))
        })
        return data


@dataclass(slots=True)
class ConditionalAction(BattleAction):
    """条件执行Action"""
    condition: str  # 条件描述（实际应该是Callable，这里简化）
    true_action: BattleAction
    false_action: Optional[BattleAction] = None
    
    def get_targets(self) -> List[IBattleEntity]:
        targets = self.true_action.get_targets()
        if self.false_action:
            targets.extend(self.false_action.get_targets())
        return targets
    
    def to_dict(self) -> Dict[str, Any]:
        data = BattleAction.to_dict(self)
        data.update({
            "condition": self.condition,
            "true_action_type": type(self.true_action).__name__,
            "false_action_type": type(self.false_action).__name__ if self.false_action else None
        })
        return data


@dataclass
class BatchExecutionResult:
    """批量执行结果"""
    total_actions: int
    successful_actions: int
    failed_actions: int
    results: List[Optional[List[BattleAction]]] = field(default_factory=list)
    errors: List[Exception] = field(default_factory=list)
    execution_time: float = 0.0
    
    @property
    def success_rate(self) -> float:
        return (self.successful_actions / max(1, self.total_actions)) * 100
    
    def get_all_result_actions(self) -> List[BattleAction]:
        """获取所有成功执行的结果Action"""
        all_actions = []
        for result in self.results:
            if result:
                all_actions.extend(result)
        return all_actions


class BatchActionProcessor:
    """批量Action处理器"""
    
    def __init__(self, executor: UnifiedActionExecutor):
        self.executor = executor
        self.stats = {
            'batch_executions': 0,
            'total_actions_processed': 0,
            'total_time_saved': 0.0  # 相比单独执行节省的时间
        }
    
    def process_batch_apply_effect(self, batch_action: BatchApplyEffectAction) -> BatchExecutionResult:
        """处理批量效果应用"""
        import time
        start_time = time.time()
        
        result = BatchExecutionResult(
            total_actions=len(batch_action.targets),
            successful_actions=0,
            failed_actions=0
        )
        
        # 预检查：验证效果和目标
        valid_targets = []
        for target in batch_action.targets:
            if self._validate_effect_target(target, batch_action.effect):
                valid_targets.append(target)
            else:
                result.failed_actions += 1
        
        # 批量应用效果
        for target in valid_targets:
            try:
                single_action = ApplyEffectAction(
                    caster=batch_action.caster,
                    target=target,
                    effect=batch_action.effect,
                    from_attack=batch_action.from_attack
                )
                
                # 使用原有的处理器
                action_result = self.executor.execute_actions([single_action])
                result.results.append(action_result)
                result.successful_actions += 1
                
            except Exception as e:
                result.errors.append(e)
                result.failed_actions += 1
        
        result.execution_time = time.time() - start_time
        self._update_stats(result)
        
        return result
    
    def process_batch_damage(self, batch_action: BatchDamageAction) -> BatchExecutionResult:
        """处理批量伤害"""
        import time
        start_time = time.time()
        
        result = BatchExecutionResult(
            total_actions=len(batch_action.damage_actions),
            successful_actions=0,
            failed_actions=0
        )
        
        # 按目标分组，避免重复计算
        target_groups = self._group_damage_by_target(batch_action.damage_actions)
        
        for target, damage_actions in target_groups.items():
            try:
                # 合并相同目标的伤害
                total_damage = sum(action.damage_value or 0 for action in damage_actions)
                
                # 创建合并的伤害Action
                merged_action = DamageAction(
                    caster=batch_action.caster,
                    target=target,
                    damage_value=total_damage,
                    # 使用第一个Action的其他属性
                    damage_type=damage_actions[0].damage_type,
                    is_critical=any(action.is_critical for action in damage_actions),
                    is_ultimate=any(action.is_ultimate for action in damage_actions),
                    skill_name=f"批量伤害({len(damage_actions)}个)"
                )
                
                # 执行合并的伤害
                action_result = self.executor.execute_actions([merged_action])
                result.results.append(action_result)
                result.successful_actions += len(damage_actions)
                
            except Exception as e:
                result.errors.append(e)
                result.failed_actions += len(damage_actions)
        
        result.execution_time = time.time() - start_time
        self._update_stats(result)
        
        return result
    
    def process_batch_modify_attribute(self, batch_action: BatchModifyAttributeAction) -> BatchExecutionResult:
        """处理批量属性修改"""
        import time
        start_time = time.time()
        
        result = BatchExecutionResult(
            total_actions=len(batch_action.modifications),
            successful_actions=0,
            failed_actions=0
        )
        
        # 按目标和属性分组，优化执行
        grouped_mods = self._group_modifications(batch_action.modifications)
        
        for (target, attr_name), modifications in grouped_mods.items():
            try:
                # 合并相同目标和属性的修改
                total_value = sum(mod.value for mod in modifications if not mod.is_remove)
                remove_sources = [mod.source_id for mod in modifications if mod.is_remove and mod.source_id]
                
                # 先处理移除
                for source_id in remove_sources:
                    remove_action = ModifyAttributeAction(
                        caster=batch_action.caster,
                        target=target,
                        attribute_name=attr_name,
                        value=0,
                        source_id=source_id,
                        is_remove=True
                    )
                    self.executor.execute_actions([remove_action])
                
                # 再处理添加（如果有）
                if total_value != 0:
                    add_action = ModifyAttributeAction(
                        caster=batch_action.caster,
                        target=target,
                        attribute_name=attr_name,
                        value=total_value,
                        source_id=f"batch_{id(batch_action)}",
                        is_remove=False
                    )
                    action_result = self.executor.execute_actions([add_action])
                    result.results.append(action_result)
                
                result.successful_actions += len(modifications)
                
            except Exception as e:
                result.errors.append(e)
                result.failed_actions += len(modifications)
        
        result.execution_time = time.time() - start_time
        self._update_stats(result)
        
        return result
    
    def _validate_effect_target(self, target: IBattleEntity, effect: IEffect) -> bool:
        """验证效果目标的有效性"""
        # 基础验证
        if not hasattr(target, 'is_alive') or not target.is_alive:
            return False
        
        # 检查是否可以应用效果
        if hasattr(effect, 'can_apply_to'):
            return effect.can_apply_to(target)
        
        return True
    
    def _group_damage_by_target(self, damage_actions: List[DamageAction]) -> Dict[IBattleEntity, List[DamageAction]]:
        """按目标分组伤害Action"""
        groups = {}
        for action in damage_actions:
            if action.target not in groups:
                groups[action.target] = []
            groups[action.target].append(action)
        return groups
    
    def _group_modifications(self, modifications: List[ModifyAttributeAction]) -> Dict[tuple, List[ModifyAttributeAction]]:
        """按目标和属性分组修改Action"""
        groups = {}
        for mod in modifications:
            key = (mod.target, mod.attribute_name)
            if key not in groups:
                groups[key] = []
            groups[key].append(mod)
        return groups
    
    def _update_stats(self, result: BatchExecutionResult):
        """更新统计信息"""
        self.stats['batch_executions'] += 1
        self.stats['total_actions_processed'] += result.total_actions
        
        # 估算节省的时间（假设单独执行每个Action需要额外的开销）
        estimated_individual_time = result.total_actions * 0.001  # 假设每个Action单独执行需要1ms开销
        time_saved = max(0, estimated_individual_time - result.execution_time)
        self.stats['total_time_saved'] += time_saved
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'avg_actions_per_batch': (
                self.stats['total_actions_processed'] / 
                max(1, self.stats['batch_executions'])
            ),
            'avg_time_saved_per_batch': (
                self.stats['total_time_saved'] / 
                max(1, self.stats['batch_executions'])
            )
        }


# 注册批量Action处理器
@handler(BatchApplyEffectAction)
@safe_execute(log_errors=True)
@monitor_performance(slow_threshold=0.5)  # 批量操作可能较慢
def _handle_batch_apply_effect(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理批量效果应用"""
    batch_action = cast(BatchApplyEffectAction, action)
    
    # 创建批量处理器
    if not hasattr(self, '_batch_processor'):
        self._batch_processor = BatchActionProcessor(self)  # type: ignore[attr-defined]
    
    # 执行批量处理
    result = self._batch_processor.process_batch_apply_effect(batch_action)  # type: ignore[attr-defined]
    
    # 记录执行结果
    self.battle_log.append({
        "level": "INFO",
        "message": f"批量效果应用完成: {result.successful_actions}/{result.total_actions} 成功, "
                  f"耗时 {result.execution_time:.4f}s, 成功率 {result.success_rate:.1f}%"
    })
    
    # 如果有错误，记录它们
    for error in result.errors:
        self.battle_log.append({
            "level": "ERROR",
            "message": f"批量效果应用错误: {str(error)}"
        })
    
    return result.get_all_result_actions()


@handler(BatchDamageAction)
@safe_execute(log_errors=True)
@monitor_performance(slow_threshold=1.0)  # 批量伤害可能很慢
def _handle_batch_damage(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理批量伤害"""
    batch_action = cast(BatchDamageAction, action)
    
    # 创建批量处理器
    if not hasattr(self, '_batch_processor'):
        self._batch_processor = BatchActionProcessor(self)  # type: ignore[attr-defined]
    
    # 执行批量处理
    result = self._batch_processor.process_batch_damage(batch_action)  # type: ignore[attr-defined]
    
    # 记录执行结果
    total_damage = sum(action.damage_value or 0 for action in batch_action.damage_actions)
    self.battle_log.append({
        "level": "INFO",
        "message": f"批量伤害完成: {result.successful_actions}/{result.total_actions} 成功, "
                  f"总伤害 {total_damage}, 耗时 {result.execution_time:.4f}s"
    })
    
    return result.get_all_result_actions()


@handler(ConditionalAction)
@safe_execute(log_errors=True)
def _handle_conditional_action(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """处理条件Action"""
    conditional_action = cast(ConditionalAction, action)
    
    # 简化的条件评估（实际应该更复杂）
    condition_met = _evaluate_condition(self, conditional_action.condition)
    
    if condition_met:
        self.battle_log.append({
            "level": "DEBUG",
            "message": f"条件 '{conditional_action.condition}' 满足，执行true_action"
        })
        return self.execute_actions([conditional_action.true_action])
    elif conditional_action.false_action:
        self.battle_log.append({
            "level": "DEBUG",
            "message": f"条件 '{conditional_action.condition}' 不满足，执行false_action"
        })
        return self.execute_actions([conditional_action.false_action])
    else:
        self.battle_log.append({
            "level": "DEBUG",
            "message": f"条件 '{conditional_action.condition}' 不满足，无false_action"
        })
        return None


def _evaluate_condition(self, condition: str) -> bool:
    """评估条件（简化版本）"""
    # 这里应该实现真正的条件评估逻辑
    # 例如解析条件字符串，检查battle_state等
    
    # 简化示例
    if condition == "target_hp_low":
        return True  # 假设条件满足
    elif condition == "caster_has_energy":
        return True  # 假设条件满足
    else:
        return False
