"""Basic stat/turn manipulation handlers (HP, Energy, Extra Turn)."""
from __future__ import annotations

from typing import Optional, List

from core.battle.executor.executor import handler, UnifiedActionExecutor
from .decorators import validate_target, safe_execute
from ...action import SetHPAction, ConsumeEnergyAction, ExtraTurnAction, BattleAction


@handler(SetHPAction)
@validate_target(alive_required=False)  # HP设置不要求目标存活
@safe_execute()
def _handle_set_hp(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    """Directly set target HP."""
    from typing import cast
    set_hp_action = cast(SetHPAction, action)
    set_hp_action.target.set_hp(set_hp_action.hp)  # type: ignore[attr-defined]
    return None


@handler(ConsumeEnergyAction)
@validate_target(alive_required=True)
@safe_execute()
def _handle_consume_energy(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    from typing import cast
    consume_action = cast(ConsumeEnergyAction, action)
    new_energy = max(0, consume_action.target.energy - consume_action.amount)  # type: ignore[attr-defined]
    consume_action.target.set_energy(new_energy)  # type: ignore[attr-defined]
    return None


@handler(ExtraTurnAction)
@safe_execute()
def _handle_extra_turn(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:  # noqa: D401
    from typing import cast
    extra_turn_action = cast(ExtraTurnAction, action)
    caster = extra_turn_action.caster
    if caster is None:
        return None
    if not hasattr(caster, "extra_turns"):
        caster.extra_turns = 0  # type: ignore[attr-defined]
    caster.extra_turns += 1  # type: ignore[attr-defined]
    return None