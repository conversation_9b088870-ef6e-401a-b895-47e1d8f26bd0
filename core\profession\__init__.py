from __future__ import annotations
from enum import Enum

"""职业类型模块。

定义了游戏中所有可用的职业类型。
"""


class ProfessionType(Enum):
    """
    表示游戏中的职业类型。

    每种职业类型都对应游戏中的一个特定角色定位。

    Attributes:
        MAGIC: 法术输出
        TANK: 承伤前排
        SPEED: 高速行动
        CONTROL: 控场干扰
        HEALER: 治疗支援
        BALANCE: 均衡全能
        CLAW: 近战爆发
        HERO: 英雄系
        SUMMONER: 召唤单位
        DIVINE_REVELATION: 神启系
        NECROMANCER: 通灵师
        ELEMENTALIST: 元素师
        GOD_EYE: 神眼系
        AWAKENER: 天觉者
        SHENYAO: 神曜系
    """
    MAGIC = "魔法"
    TANK = "肉盾"
    SPEED = "疾速"
    CONTROL = "控制"
    HEALER = "治疗"
    BALANCE = "平衡"
    CLAW = "利爪"
    HERO = "英雄"
    SUMMONER = "召唤师"
    DIVINE_REVELATION = "神启"
    NECROMANCER = "通灵师"
    ELEMENTALIST = "元素师"
    GOD_EYE = "神眼"
    AWAKENER = "天觉者"
    SHENYAO = "神曜"


__all__ = ['ProfessionType']
