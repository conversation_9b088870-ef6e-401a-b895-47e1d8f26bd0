<template>
  <div class="tactic-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="top"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <div class="grid grid-cols-2 gap-4">
          <el-form-item label="战术名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入战术名称" />
          </el-form-item>
          
          <el-form-item label="战术类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择战术类型">
              <el-option label="攻击型" value="offensive" />
              <el-option label="防守型" value="defensive" />
              <el-option label="平衡型" value="balanced" />
              <el-option label="特殊型" value="special" />
            </el-select>
          </el-form-item>
        </div>
        
        <el-form-item label="战术描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请描述战术的核心思路和适用场景"
          />
        </el-form-item>
        
        <div class="grid grid-cols-3 gap-4">
          <el-form-item label="难度等级" prop="difficulty">
            <el-rate
              v-model="formData.difficulty"
              :max="5"
              show-text
              :texts="['很简单', '简单', '一般', '困难', '很困难']"
            />
          </el-form-item>
          
          <el-form-item label="推荐等级" prop="recommendedLevel">
            <el-input-number v-model="formData.recommendedLevel" :min="1" :max="100" />
          </el-form-item>
          
          <el-form-item label="适用模式" prop="gameMode">
            <el-select v-model="formData.gameMode" placeholder="适用模式">
              <el-option label="竞技场" value="arena" />
              <el-option label="副本" value="dungeon" />
              <el-option label="公会战" value="guild_war" />
              <el-option label="通用" value="general" />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 标签和分类 -->
      <div class="form-section">
        <h3 class="section-title">标签和分类</h3>
        
        <el-form-item label="战术标签">
          <el-tag
            v-for="tag in formData.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            class="mr-2 mb-2"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            style="width: 100px"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else size="small" @click="showInput">
            + 添加标签
          </el-button>
        </el-form-item>
        
        <div class="grid grid-cols-2 gap-4">
          <el-form-item label="主要元素">
            <el-select v-model="formData.primaryElement" placeholder="主要元素类型">
              <el-option label="火" value="fire" />
              <el-option label="水" value="water" />
              <el-option label="土" value="earth" />
              <el-option label="风" value="air" />
              <el-option label="光" value="light" />
              <el-option label="暗" value="dark" />
              <el-option label="混合" value="mixed" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="主要职业">
            <el-select v-model="formData.primaryProfession" placeholder="主要职业">
              <el-option label="战士" value="warrior" />
              <el-option label="法师" value="mage" />
              <el-option label="射手" value="archer" />
              <el-option label="治疗" value="healer" />
              <el-option label="刺客" value="assassin" />
              <el-option label="坦克" value="tank" />
              <el-option label="混合" value="mixed" />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 战术特点 -->
      <div class="form-section">
        <h3 class="section-title">战术特点</h3>
        
        <div class="grid grid-cols-2 gap-4">
          <div class="feature-group">
            <h4 class="text-slate-300 font-medium mb-3">攻击特点</h4>
            <el-checkbox-group v-model="formData.attackFeatures">
              <el-checkbox label="burst" class="block mb-2">爆发伤害</el-checkbox>
              <el-checkbox label="sustained" class="block mb-2">持续输出</el-checkbox>
              <el-checkbox label="aoe" class="block mb-2">范围攻击</el-checkbox>
              <el-checkbox label="single_target" class="block mb-2">单体集火</el-checkbox>
              <el-checkbox label="critical" class="block mb-2">暴击流</el-checkbox>
            </el-checkbox-group>
          </div>
          
          <div class="feature-group">
            <h4 class="text-slate-300 font-medium mb-3">防御特点</h4>
            <el-checkbox-group v-model="formData.defenseFeatures">
              <el-checkbox label="tank" class="block mb-2">坦克保护</el-checkbox>
              <el-checkbox label="healing" class="block mb-2">治疗恢复</el-checkbox>
              <el-checkbox label="shield" class="block mb-2">护盾防护</el-checkbox>
              <el-checkbox label="dodge" class="block mb-2">闪避流</el-checkbox>
              <el-checkbox label="counter" class="block mb-2">反击流</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-4 mt-4">
          <div class="feature-group">
            <h4 class="text-slate-300 font-medium mb-3">控制特点</h4>
            <el-checkbox-group v-model="formData.controlFeatures">
              <el-checkbox label="stun" class="block mb-2">眩晕控制</el-checkbox>
              <el-checkbox label="slow" class="block mb-2">减速控制</el-checkbox>
              <el-checkbox label="silence" class="block mb-2">沉默控制</el-checkbox>
              <el-checkbox label="debuff" class="block mb-2">减益效果</el-checkbox>
              <el-checkbox label="displacement" class="block mb-2">位移控制</el-checkbox>
            </el-checkbox-group>
          </div>
          
          <div class="feature-group">
            <h4 class="text-slate-300 font-medium mb-3">辅助特点</h4>
            <el-checkbox-group v-model="formData.supportFeatures">
              <el-checkbox label="buff" class="block mb-2">增益效果</el-checkbox>
              <el-checkbox label="energy" class="block mb-2">能量管理</el-checkbox>
              <el-checkbox label="revive" class="block mb-2">复活机制</el-checkbox>
              <el-checkbox label="summon" class="block mb-2">召唤协助</el-checkbox>
              <el-checkbox label="formation" class="block mb-2">阵型变换</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <!-- 适用条件 -->
      <div class="form-section">
        <h3 class="section-title">适用条件</h3>
        
        <div class="grid grid-cols-2 gap-4">
          <el-form-item label="推荐对手类型">
            <el-select v-model="formData.recommendedOpponent" multiple placeholder="选择对手类型">
              <el-option label="高攻击力队伍" value="high_attack" />
              <el-option label="高防御力队伍" value="high_defense" />
              <el-option label="治疗流队伍" value="healing_team" />
              <el-option label="控制流队伍" value="control_team" />
              <el-option label="爆发流队伍" value="burst_team" />
              <el-option label="平衡型队伍" value="balanced_team" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="不推荐对手">
            <el-select v-model="formData.notRecommendedOpponent" multiple placeholder="选择不推荐对手">
              <el-option label="免疫流队伍" value="immune_team" />
              <el-option label="反击流队伍" value="counter_team" />
              <el-option label="速度流队伍" value="speed_team" />
              <el-option label="复活流队伍" value="revive_team" />
              <el-option label="召唤流队伍" value="summon_team" />
            </el-select>
          </el-form-item>
        </div>
        
        <el-form-item label="战术说明">
          <el-input
            v-model="formData.instructions"
            type="textarea"
            :rows="4"
            placeholder="详细说明战术的使用方法、注意事项和技巧"
          />
        </el-form-item>
      </div>

      <!-- 预期效果 -->
      <div class="form-section">
        <h3 class="section-title">预期效果</h3>
        
        <div class="grid grid-cols-3 gap-4">
          <el-form-item label="预期胜率 (%)">
            <el-slider
              v-model="formData.expectedWinRate"
              :min="0"
              :max="100"
              show-input
              :format-tooltip="(val) => `${val}%`"
            />
          </el-form-item>
          
          <el-form-item label="预期战斗时长 (秒)">
            <el-input-number v-model="formData.expectedDuration" :min="10" :max="300" />
          </el-form-item>
          
          <el-form-item label="资源消耗等级">
            <el-rate
              v-model="formData.resourceCost"
              :max="5"
              show-text
              :texts="['很低', '低', '中等', '高', '很高']"
            />
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions flex justify-end space-x-3 mt-6 pt-6 border-t border-slate-600/30">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button @click="resetForm">重置</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ isEditing ? '更新战术' : '创建战术' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

interface TacticFormData {
  name: string
  type: string
  description: string
  difficulty: number
  recommendedLevel: number
  gameMode: string
  tags: string[]
  primaryElement: string
  primaryProfession: string
  attackFeatures: string[]
  defenseFeatures: string[]
  controlFeatures: string[]
  supportFeatures: string[]
  recommendedOpponent: string[]
  notRecommendedOpponent: string[]
  instructions: string
  expectedWinRate: number
  expectedDuration: number
  resourceCost: number
}

interface Props {
  modelValue: Partial<TacticFormData>
  isEditing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEditing: false
})

const emit = defineEmits<{
  'update:modelValue': [value: Partial<TacticFormData>]
  submit: [value: Partial<TacticFormData>]
  cancel: []
}>()

// 表单引用
const formRef = ref<FormInstance>()
const inputRef = ref()

// 表单数据
const formData = reactive<Partial<TacticFormData>>({
  name: '',
  type: 'balanced',
  description: '',
  difficulty: 3,
  recommendedLevel: 1,
  gameMode: 'general',
  tags: [],
  primaryElement: '',
  primaryProfession: '',
  attackFeatures: [],
  defenseFeatures: [],
  controlFeatures: [],
  supportFeatures: [],
  recommendedOpponent: [],
  notRecommendedOpponent: [],
  instructions: '',
  expectedWinRate: 50,
  expectedDuration: 60,
  resourceCost: 3,
  ...props.modelValue
})

// 标签输入
const inputVisible = ref(false)
const inputValue = ref('')

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入战术名称', trigger: 'blur' },
    { min: 2, max: 30, message: '名称长度应在 2 到 30 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择战术类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入战术描述', trigger: 'blur' },
    { min: 10, max: 200, message: '描述长度应在 10 到 200 个字符', trigger: 'blur' }
  ],
  difficulty: [
    { required: true, message: '请设置难度等级', trigger: 'change' }
  ]
}

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 标签相关方法
const removeTag = (tag: string) => {
  const index = formData.tags!.indexOf(tag)
  if (index > -1) {
    formData.tags!.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags!.includes(inputValue.value)) {
    formData.tags!.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 表单操作
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('submit', formData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置为默认值
  Object.assign(formData, {
    name: '',
    type: 'balanced',
    description: '',
    difficulty: 3,
    recommendedLevel: 1,
    gameMode: 'general',
    tags: [],
    primaryElement: '',
    primaryProfession: '',
    attackFeatures: [],
    defenseFeatures: [],
    controlFeatures: [],
    supportFeatures: [],
    recommendedOpponent: [],
    notRecommendedOpponent: [],
    instructions: '',
    expectedWinRate: 50,
    expectedDuration: 60,
    resourceCost: 3
  })
}
</script>

<style scoped lang="scss">
.tactic-form {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 24px;
  border-radius: 12px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.section-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(139, 92, 246, 0.3);
}

.feature-group {
  padding: 16px;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 6px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

:deep(.el-form-item__label) {
  color: #e2e8f0;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
}

:deep(.el-textarea__inner) {
  background-color: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
  color: #e2e8f0;
}

:deep(.el-checkbox__label) {
  color: #e2e8f0;
}

:deep(.el-rate__text) {
  color: #e2e8f0;
}

:deep(.el-slider__runway) {
  background-color: rgba(148, 163, 184, 0.3);
}

:deep(.el-slider__bar) {
  background: linear-gradient(to right, #8b5cf6, #ec4899);
}

.tactic-form {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(148, 163, 184, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(139, 92, 246, 0.5);
    }
  }
}
</style>