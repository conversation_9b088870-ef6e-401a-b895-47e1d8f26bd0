#!/usr/bin/env python3
"""
简化AI系统测试

测试简化后的AI系统，专注于：
1. 固定的技能选择逻辑（能量够就超杀，否则普攻）
2. 使用技能自带的目标选择器
3. 条件性效果计算（核心功能）
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simplified_skill_selection():
    """测试简化的技能选择逻辑"""
    
    print("=== 简化技能选择测试 ===")
    
    try:
        from core.ai.action_generator import SimpleSkillSelector
        
        print("✅ SimpleSkillSelector 导入成功")
        
        # 创建测试对象
        class MockSkill:
            def __init__(self, name, cast_type='ACTIVE', energy_cost=0):
                self.metadata = type('Metadata', (), {
                    'name': name,
                    'cast_type': cast_type,
                    'energy_cost': energy_cost
                })()
        
        class MockSkillComponent:
            def __init__(self):
                self.skills = [
                    MockSkill("普通攻击", 'ACTIVE', 0),      # 普攻
                    MockSkill("千机罗网", 'ULTIMATE', 300)   # 超杀
                ]
            
            def get_skills_by_type(self, skill_type):
                return [s for s in self.skills if s.metadata.cast_type == skill_type]
        
        class MockComponents:
            def __init__(self):
                self.skill_component = MockSkillComponent()
            
            def get_component(self, component_type):
                if hasattr(component_type, '__name__') and component_type.__name__ == 'SkillComponent':
                    return self.skill_component
                return None
        
        class MockSpirit:
            def __init__(self, name, energy=100):
                self.name = name
                self.current_energy = energy
                self.components = MockComponents()
            
            def get_ultimate_threshold(self):
                return 300
        
        class MockBattleState:
            def __init__(self):
                self.round_num = 5
        
        # 测试技能选择器
        selector = SimpleSkillSelector()
        battle_state = MockBattleState()
        
        # 1. 测试能量不足时选择普攻
        print("\n1. 测试能量不足时选择普攻...")
        spirit_low_energy = MockSpirit("低能量精灵", 100)
        result1 = selector.select_skill(spirit_low_energy, battle_state)
        
        print(f"✅ 低能量选择结果:")
        print(f"  - 选择的技能: {result1.skill.metadata.name if result1.skill else 'None'}")
        print(f"  - 选择原因: {result1.reason}")
        print(f"  - 技能类型: {result1.metadata.get('skill_type', 'unknown')}")
        
        # 2. 测试能量充足时选择超杀
        print("\n2. 测试能量充足时选择超杀...")
        spirit_high_energy = MockSpirit("高能量精灵", 300)
        result2 = selector.select_skill(spirit_high_energy, battle_state)
        
        print(f"✅ 高能量选择结果:")
        print(f"  - 选择的技能: {result2.skill.metadata.name if result2.skill else 'None'}")
        print(f"  - 选择原因: {result2.reason}")
        print(f"  - 技能类型: {result2.metadata.get('skill_type', 'unknown')}")
        
        print("\n🎉 简化技能选择测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 简化技能选择测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_target_selection_from_skill():
    """测试使用技能自带的目标选择器"""
    
    print("\n=== 技能目标选择器测试 ===")
    
    try:
        from core.ai.action_generator import IntelligentActionGenerator
        from core.targeting.selectors import SingleEnemySelector, SelfSelector
        
        print("✅ 相关组件导入成功")
        
        # 创建测试对象
        class MockSpirit:
            def __init__(self, name, team=0):
                self.name = name
                self.team = team
                self.current_hp = 1000
                self.max_hp = 1000
                self.is_alive = True
        
        class MockBattleState:
            def __init__(self):
                self.team0_spirits = [MockSpirit("队友", 0)]
                self.team1_spirits = [MockSpirit("敌人", 1)]
            
            def get_living_spirits(self, team):
                if team == 0:
                    return [s for s in self.team0_spirits if s.is_alive]
                else:
                    return [s for s in self.team1_spirits if s.is_alive]
            
            def get_targetable_living_spirits(self, team):
                return self.get_living_spirits(team)
        
        class MockSkill:
            def __init__(self, name, target_selector):
                self.metadata = type('Metadata', (), {
                    'name': name,
                    'cast_type': 'ACTIVE'
                })()
                self.target_selector = target_selector
        
        # 测试目标选择
        generator = IntelligentActionGenerator()
        attacker = MockSpirit("攻击者", 0)
        battle_state = MockBattleState()
        
        # 1. 测试单体攻击目标选择
        print("\n1. 测试单体攻击目标选择...")
        single_attack_skill = MockSkill("单体攻击", SingleEnemySelector())
        targets1 = generator._select_targets_from_skill(attacker, single_attack_skill, battle_state)
        
        print(f"✅ 单体攻击目标选择:")
        print(f"  - 选择的目标数量: {len(targets1)}")
        if targets1:
            print(f"  - 目标名称: {targets1[0].name}")
        
        # 2. 测试自我目标选择
        print("\n2. 测试自我目标选择...")
        self_skill = MockSkill("自我强化", SelfSelector())
        targets2 = generator._select_targets_from_skill(attacker, self_skill, battle_state)
        
        print(f"✅ 自我目标选择:")
        print(f"  - 选择的目标数量: {len(targets2)}")
        if targets2:
            print(f"  - 目标名称: {targets2[0].name}")
        
        print("\n🎉 技能目标选择器测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 技能目标选择器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_conditional_effects():
    """测试条件性效果计算（核心功能）"""

    print("\n=== 条件性效果计算测试 ===")

    try:
        from core.ai.condition_evaluator import AttackConditionResult
        from core.ai.effect_calculator import ConditionalEffectResult

        print("✅ 条件性效果组件导入成功")

        # 测试条件结果数据结构
        print("\n1. 测试条件结果数据结构...")
        conditions = AttackConditionResult()
        conditions.set_condition('target_low_hp', True)
        conditions.set_condition('target_hp_percentage', 0.2)
        conditions.set_condition('attacker_has_spirit_wisdom', True)

        print(f"✅ 条件结果:")
        print(f"  - 条件数量: {len(conditions.conditions)}")
        for condition, value in conditions.conditions.items():
            print(f"    - {condition}: {value}")

        # 测试效果结果数据结构
        print("\n2. 测试效果结果数据结构...")
        effects = ConditionalEffectResult()
        effects.add_effect('crit_rate_bonus', 0.4)
        effects.add_effect('crit_damage_bonus', 0.4)
        effects.add_effect('energy_gain', 30)
        effects.add_trigger_event('spirit_wisdom_triggered')

        print(f"✅ 效果结果:")
        print(f"  - 效果数量: {len(effects.effects)}")
        print(f"  - 触发事件数量: {len(effects.trigger_events)}")

        for effect_name, effect_value in effects.effects.items():
            print(f"    - {effect_name}: {effect_value}")

        for event in effects.trigger_events:
            print(f"    - 事件: {event}")

        # 测试组件创建
        print("\n3. 测试组件创建...")
        from core.ai.condition_evaluator import DynamicConditionEvaluator
        from core.ai.effect_calculator import ConditionalEffectCalculator

        condition_evaluator = DynamicConditionEvaluator()
        effect_calculator = ConditionalEffectCalculator()

        print(f"✅ 组件创建成功:")
        print(f"  - 条件评估器: {len(condition_evaluator.evaluators)} 个评估器")
        print(f"  - 效果计算器: {len(effect_calculator.calculators)} 个计算器")

        print("\n🎉 条件性效果计算测试成功！")
        return True

    except Exception as e:
        print(f"\n❌ 条件性效果计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始简化AI系统测试...\n")
    
    # 运行各项测试
    test_results = []
    
    test_results.append(("简化技能选择", test_simplified_skill_selection()))
    test_results.append(("技能目标选择器", test_target_selection_from_skill()))
    test_results.append(("条件性效果计算", test_conditional_effects()))
    
    # 总结结果
    print("\n" + "="*60)
    print("简化AI系统测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！")
        print("\n📋 简化AI系统功能总结:")
        print("  ✅ 固定技能选择逻辑 - 能量够就超杀，否则普攻")
        print("  ✅ 技能目标选择器 - 使用技能自带的选择器")
        print("  ✅ 条件性效果计算 - 核心AI功能保留")
        print("  ✅ 简化架构 - 专注于真正需要AI的部分")
        
        print("\n🚀 简化后的AI系统更加专注和高效！")
        print("  📖 系统特点：")
        print("    - 技能选择：基于能量阈值的固定逻辑")
        print("    - 目标选择：使用技能定义的选择器")
        print("    - AI增强：专注于条件性效果计算")
        print("    - 架构简洁：去除不必要的复杂性")
        
    else:
        print("\n❌ 部分测试失败，请检查系统配置")
    
    print("="*60)
