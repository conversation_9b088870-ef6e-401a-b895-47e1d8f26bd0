{"name": "spirit-battle-simulator", "version": "2.0.0", "description": "现代化回合制战斗模拟器前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "three": "^0.159.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "gsap": "^3.12.2", "lodash-es": "^4.17.21", "@vueuse/core": "^10.7.0", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^20.10.5", "@types/three": "^0.159.0", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "sass": "^1.69.5", "tailwindcss": "^3.3.6", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}