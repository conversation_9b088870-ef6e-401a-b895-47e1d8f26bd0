"""
全局死亡管理器

提供全局的死亡管理功能，方便在整个系统中使用。
"""
from __future__ import annotations
from typing import TYPE_CHECKING, Optional, List

if TYPE_CHECKING:
    from ..interfaces import IBattleEntity
    from ..action import BattleAction

from .manager import DynamicDeathManager
from .types import DeathTriggerMode, DeathReason
from ..logging import battle_logger


# 全局死亡管理器实例
_global_death_manager: Optional[DynamicDeathManager] = None


def get_global_death_manager() -> DynamicDeathManager:
    """
    获取全局死亡管理器
    
    Returns:
        全局死亡管理器实例
    """
    global _global_death_manager
    if _global_death_manager is None:
        _global_death_manager = DynamicDeathManager()
        battle_logger.debug("创建全局死亡管理器")
    return _global_death_manager


def set_global_death_trigger_mode(mode: DeathTriggerMode):
    """
    设置全局死亡触发模式
    
    Args:
        mode: 死亡触发模式
    """
    manager = get_global_death_manager()
    manager.set_trigger_mode(mode)


def monitor_spirit_hp_change(
    entity: 'IBattleEntity', 
    old_hp: float, 
    new_hp: float,
    reason: DeathReason = DeathReason.DAMAGE
):
    """
    监控精灵HP变化（全局函数）
    
    Args:
        entity: 精灵实体
        old_hp: 变化前的HP
        new_hp: 变化后的HP
        reason: 变化原因
    """
    manager = get_global_death_manager()
    manager.monitor_hp_change(entity, old_hp, new_hp, reason)


def get_pending_death_actions() -> List['BattleAction']:
    """
    获取待处理的死亡动作
    
    Returns:
        待处理的死亡动作列表
    """
    manager = get_global_death_manager()
    return manager.get_pending_actions()


def process_all_deferred_deaths() -> List['BattleAction']:
    """
    处理所有延迟死亡
    
    Returns:
        生成的死亡动作列表
    """
    manager = get_global_death_manager()
    return manager.process_deferred_deaths()


def register_global_death_callback(callback):
    """
    注册全局死亡回调
    
    Args:
        callback: 回调函数，接收 (entity, reason) 参数
    """
    manager = get_global_death_manager()
    manager.register_custom_callback(callback)


def get_global_death_statistics() -> dict:
    """
    获取全局死亡统计信息
    
    Returns:
        统计信息字典
    """
    manager = get_global_death_manager()
    return manager.get_statistics()


def clear_global_death_state():
    """清空全局死亡状态"""
    manager = get_global_death_manager()
    manager.clear_all()


def force_global_entity_death(
    entity: 'IBattleEntity', 
    reason: DeathReason = DeathReason.OTHER
):
    """
    强制处理实体死亡（全局函数）
    
    Args:
        entity: 要处理死亡的实体
        reason: 死亡原因
    """
    manager = get_global_death_manager()
    manager.force_process_entity_death(entity, reason)


def reset_global_death_manager():
    """重置全局死亡管理器"""
    global _global_death_manager
    _global_death_manager = None
    battle_logger.info("重置全局死亡管理器")
