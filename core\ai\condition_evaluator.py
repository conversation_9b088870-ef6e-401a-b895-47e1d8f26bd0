"""
动态条件评估器

负责评估攻击时的各种动态条件，包括：
- 目标状态条件
- 攻击者状态条件  
- 战场环境条件
- 技能特定条件
"""

from __future__ import annotations
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from abc import ABC, abstractmethod
import time

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill

from core.status import battle_status_checker
from core.logging import get_logger

logger = get_logger("ai.condition")

@dataclass
class AttackConditionResult:
    """攻击条件评估结果"""
    conditions: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_condition(self, key: str, default: Any = None) -> Any:
        """获取条件值"""
        return self.conditions.get(key, default)
    
    def set_condition(self, key: str, value: Any):
        """设置条件值"""
        self.conditions[key] = value
    
    def has_condition(self, key: str) -> bool:
        """检查是否有指定条件"""
        return key in self.conditions
    
    def merge(self, other: 'AttackConditionResult'):
        """合并另一个条件结果"""
        self.conditions.update(other.conditions)
        self.metadata.update(other.metadata)

class IConditionEvaluator(ABC):
    """条件评估器接口"""
    
    @abstractmethod
    def evaluate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity', 
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> AttackConditionResult:
        """评估条件"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取评估器名称"""
        pass

class TargetStatusEvaluator(IConditionEvaluator):
    """目标状态评估器"""
    
    def get_name(self) -> str:
        return "target_status"
    
    def evaluate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill', 
        battle_state: 'IBattleState'
    ) -> AttackConditionResult:
        """评估目标状态条件"""
        
        result = AttackConditionResult()
        
        # 1. 目标是否无法行动
        result.set_condition(
            'target_unable_to_act',
            battle_status_checker.status_checker.is_unable_to_act(target)
        )
        
        # 2. 目标生命值状态
        if hasattr(target, 'current_hp') and hasattr(target, 'max_hp'):
            hp_percentage = target.current_hp / target.max_hp
            result.set_condition('target_hp_percentage', hp_percentage)
            result.set_condition('target_low_hp', hp_percentage <= 0.3)  # 低于30%算低血量
            result.set_condition('target_critical_hp', hp_percentage <= 0.1)  # 低于10%算濒死
        
        # 3. 目标能量状态
        if hasattr(target, 'current_energy') and hasattr(target, 'max_energy'):
            energy_percentage = target.current_energy / target.max_energy
            result.set_condition('target_energy_percentage', energy_percentage)
            result.set_condition('target_low_energy', energy_percentage <= 0.3)
        
        # 4. 目标护盾状态
        result.set_condition('target_has_shield', self._has_shield(target))
        
        # 5. 目标增益/减益状态
        result.set_condition('target_buff_count', self._count_buffs(target))
        result.set_condition('target_debuff_count', self._count_debuffs(target))
        
        # 6. 目标职业信息
        if hasattr(target, 'metadata') and hasattr(target.metadata, 'professions'):
            professions = [prof.name for prof in target.metadata.professions]
            result.set_condition('target_professions', professions)
            result.set_condition('target_is_hero', 'HERO' in professions)
            result.set_condition('target_is_tank', 'TANK' in professions)
        
        return result
    
    def _has_shield(self, target: 'IBattleEntity') -> bool:
        """检查目标是否有护盾"""
        try:
            if hasattr(target, 'effects'):
                for effect in target.effects:
                    if hasattr(effect, 'name') and '护盾' in effect.name:
                        return True
            return False
        except:
            return False
    
    def _count_buffs(self, target: 'IBattleEntity') -> int:
        """计算增益效果数量"""
        try:
            if hasattr(target, 'effects'):
                return len([e for e in target.effects if getattr(e, 'category', None) == 'BUFF'])
            return 0
        except:
            return 0
    
    def _count_debuffs(self, target: 'IBattleEntity') -> int:
        """计算减益效果数量"""
        try:
            if hasattr(target, 'effects'):
                return len([e for e in target.effects if getattr(e, 'category', None) == 'DEBUFF'])
            return 0
        except:
            return 0

class AttackerStatusEvaluator(IConditionEvaluator):
    """攻击者状态评估器"""
    
    def get_name(self) -> str:
        return "attacker_status"
    
    def evaluate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> AttackConditionResult:
        """评估攻击者状态条件"""
        
        result = AttackConditionResult()
        
        # 1. 攻击者生命值状态
        if hasattr(attacker, 'current_hp') and hasattr(attacker, 'max_hp'):
            hp_percentage = attacker.current_hp / attacker.max_hp
            result.set_condition('attacker_hp_percentage', hp_percentage)
            result.set_condition('attacker_low_hp', hp_percentage <= 0.3)
            result.set_condition('attacker_critical_hp', hp_percentage <= 0.1)
        
        # 2. 攻击者能量状态
        if hasattr(attacker, 'current_energy') and hasattr(attacker, 'max_energy'):
            energy_percentage = attacker.current_energy / attacker.max_energy
            result.set_condition('attacker_energy_percentage', energy_percentage)
            result.set_condition('attacker_high_energy', energy_percentage >= 0.8)
        
        # 3. 攻击者增益状态
        result.set_condition('attacker_buff_count', self._count_buffs(attacker))
        result.set_condition('attacker_has_damage_buff', self._has_damage_buff(attacker))
        result.set_condition('attacker_has_crit_buff', self._has_crit_buff(attacker))
        
        # 4. 特殊效果检查
        result.set_condition('attacker_has_spirit_wisdom', self._has_spirit_wisdom_boost(attacker))
        
        return result
    
    def _count_buffs(self, attacker: 'IBattleEntity') -> int:
        """计算增益效果数量"""
        try:
            if hasattr(attacker, 'effects'):
                return len([e for e in attacker.effects if getattr(e, 'category', None) == 'BUFF'])
            return 0
        except:
            return 0
    
    def _has_damage_buff(self, attacker: 'IBattleEntity') -> bool:
        """检查是否有伤害增益"""
        try:
            if hasattr(attacker, 'effects'):
                for effect in attacker.effects:
                    if hasattr(effect, 'name') and ('攻击' in effect.name or '伤害' in effect.name):
                        return True
            return False
        except:
            return False
    
    def _has_crit_buff(self, attacker: 'IBattleEntity') -> bool:
        """检查是否有暴击增益"""
        try:
            if hasattr(attacker, 'effects'):
                for effect in attacker.effects:
                    if hasattr(effect, 'name') and '暴击' in effect.name:
                        return True
            return False
        except:
            return False
    
    def _has_spirit_wisdom_boost(self, attacker: 'IBattleEntity') -> bool:
        """检查是否有灵目慧心战斗加成效果"""
        try:
            if hasattr(attacker, 'effects'):
                for effect in attacker.effects:
                    if hasattr(effect, 'name') and effect.name == "灵目慧心战斗加成":
                        return True
            return False
        except:
            return False

class BattlefieldEvaluator(IConditionEvaluator):
    """战场环境评估器"""
    
    def get_name(self) -> str:
        return "battlefield"
    
    def evaluate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> AttackConditionResult:
        """评估战场环境条件"""
        
        result = AttackConditionResult()
        
        # 1. 队伍信息
        attacker_team = getattr(attacker, 'team', 0)
        enemy_team = 1 - attacker_team
        
        # 2. 存活精灵数量
        ally_count = len(battle_state.get_living_spirits(attacker_team))
        enemy_count = len(battle_state.get_living_spirits(enemy_team))
        
        result.set_condition('ally_count', ally_count)
        result.set_condition('enemy_count', enemy_count)
        result.set_condition('ally_outnumbered', ally_count < enemy_count)
        result.set_condition('enemy_outnumbered', enemy_count < ally_count)
        
        # 3. 无法行动的精灵数量
        unable_ally_count = battle_status_checker.count_unable_to_act_spirits(battle_state, attacker_team)
        unable_enemy_count = battle_status_checker.count_unable_to_act_spirits(battle_state, enemy_team)
        
        result.set_condition('unable_ally_count', unable_ally_count)
        result.set_condition('unable_enemy_count', unable_enemy_count)
        
        # 4. 回合信息
        result.set_condition('round_number', getattr(battle_state, 'round_num', 0))
        result.set_condition('early_game', getattr(battle_state, 'round_num', 0) <= 3)
        result.set_condition('late_game', getattr(battle_state, 'round_num', 0) >= 10)
        
        return result

class SkillEvaluator(IConditionEvaluator):
    """技能特定评估器"""
    
    def get_name(self) -> str:
        return "skill"
    
    def evaluate(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> AttackConditionResult:
        """评估技能特定条件"""
        
        result = AttackConditionResult()
        
        # 1. 技能基本信息
        if hasattr(skill, 'metadata'):
            metadata = skill.metadata
            result.set_condition('skill_type', getattr(metadata, 'cast_type', 'UNKNOWN'))
            result.set_condition('skill_name', getattr(metadata, 'name', 'Unknown'))
            result.set_condition('skill_energy_cost', getattr(metadata, 'energy_cost', 0))
            
            # 技能标签
            tags = getattr(metadata, 'tags', [])
            result.set_condition('skill_tags', tags)
            result.set_condition('is_ultimate', 'ULTIMATE' in tags or getattr(metadata, 'cast_type', '') == 'ULTIMATE')
            result.set_condition('is_hero_skill', 'HERO' in tags or getattr(metadata, 'cast_type', '') == 'HERO')
        
        # 2. 预测暴击
        result.set_condition('will_critical_hit', self._predict_critical_hit(attacker, target))
        
        # 3. 伤害预估
        result.set_condition('estimated_damage', self._estimate_damage(attacker, target, skill))
        
        return result
    
    def _predict_critical_hit(self, attacker: 'IBattleEntity', target: 'IBattleEntity') -> bool:
        """预测是否会暴击"""
        try:
            crit_rate = getattr(attacker, 'crit_rate', 0.05)
            # 简单的随机预测，实际可以更复杂
            import random
            return random.random() < crit_rate
        except:
            return False
    
    def _estimate_damage(self, attacker: 'IBattleEntity', target: 'IBattleEntity', skill: 'Skill') -> float:
        """估算伤害"""
        try:
            # 简单的伤害估算
            attack = getattr(attacker, 'attack', 100)
            defense = getattr(target, 'defense', 50)
            
            # 获取技能倍率
            multiplier = 1.0
            if hasattr(skill, 'components'):
                for component in skill.components:
                    if hasattr(component, 'power_multiplier'):
                        multiplier = component.power_multiplier
                        break
            
            base_damage = attack * multiplier
            final_damage = max(base_damage - defense, base_damage * 0.1)  # 至少造成10%伤害
            
            return final_damage
        except:
            return 0.0

class DynamicConditionEvaluator:
    """动态条件评估器主类"""
    
    def __init__(self):
        self.evaluators: List[IConditionEvaluator] = []
        self._cache = {}
        self._cache_ttl = {}
        self._setup_default_evaluators()
    
    def _setup_default_evaluators(self):
        """设置默认评估器"""
        self.evaluators = [
            TargetStatusEvaluator(),
            AttackerStatusEvaluator(),
            BattlefieldEvaluator(),
            SkillEvaluator()
        ]
    
    def add_evaluator(self, evaluator: IConditionEvaluator):
        """添加自定义评估器"""
        self.evaluators.append(evaluator)
    
    def remove_evaluator(self, evaluator_name: str):
        """移除指定名称的评估器"""
        self.evaluators = [e for e in self.evaluators if e.get_name() != evaluator_name]
    
    def evaluate_attack_conditions(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState',
        use_cache: bool = True
    ) -> AttackConditionResult:
        """评估攻击时的动态条件"""
        
        # 生成缓存键
        cache_key = None
        if use_cache:
            cache_key = self._generate_cache_key(attacker, target, skill, battle_state)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
        
        logger.debug(f"评估 {getattr(attacker, 'name', 'Unknown')} 攻击 {getattr(target, 'name', 'Unknown')} 的条件")
        
        # 合并所有评估器的结果
        final_result = AttackConditionResult()
        
        for evaluator in self.evaluators:
            try:
                result = evaluator.evaluate(attacker, target, skill, battle_state)
                final_result.merge(result)
                
                logger.debug(f"评估器 {evaluator.get_name()} 完成")
                
            except Exception as e:
                logger.error(f"评估器 {evaluator.get_name()} 执行失败: {e}")
                continue
        
        # 缓存结果
        if use_cache and cache_key:
            self._cache_result(cache_key, final_result)
        
        return final_result
    
    def _generate_cache_key(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity', 
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> str:
        """生成缓存键"""
        try:
            attacker_id = getattr(attacker, 'id', 'unknown')
            target_id = getattr(target, 'id', 'unknown')
            skill_name = getattr(skill.metadata, 'name', 'unknown') if hasattr(skill, 'metadata') else 'unknown'
            round_num = getattr(battle_state, 'round_num', 0)
            
            return f"{attacker_id}_{target_id}_{skill_name}_{round_num}"
        except:
            return f"cache_{time.time()}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[AttackConditionResult]:
        """获取缓存结果"""
        if cache_key in self._cache:
            # 检查缓存是否过期（1秒TTL）
            if time.time() - self._cache_ttl.get(cache_key, 0) < 1.0:
                return self._cache[cache_key]
            else:
                # 清理过期缓存
                del self._cache[cache_key]
                if cache_key in self._cache_ttl:
                    del self._cache_ttl[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: AttackConditionResult):
        """缓存结果"""
        self._cache[cache_key] = result
        self._cache_ttl[cache_key] = time.time()
        
        # 限制缓存大小
        if len(self._cache) > 100:
            # 清理最旧的缓存项
            oldest_key = min(self._cache_ttl.keys(), key=lambda k: self._cache_ttl[k])
            del self._cache[oldest_key]
            del self._cache_ttl[oldest_key]
    
    def clear_cache(self):
        """清理缓存"""
        self._cache.clear()
        self._cache_ttl.clear()
