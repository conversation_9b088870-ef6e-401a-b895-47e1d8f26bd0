#!/usr/bin/env python3
"""
测试战斗中的时机问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_battle_timing():
    """测试战斗中的时机问题"""
    print("🔧 测试战斗中的时机问题...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 监控动作执行器的阶段队列
        executor = engine.action_executor
        
        def check_phase_queues():
            """检查阶段队列"""
            total_actions = 0
            for phase, queue in executor.phase_queues.items():
                queue_size = len(queue)
                if queue_size > 0:
                    print(f"    {phase.name}: {queue_size} 个动作")
                    for i, action in enumerate(queue):
                        action_type = type(action).__name__
                        print(f"      动作{i+1}: {action_type}")
                total_actions += queue_size
            return total_actions
        
        # 监控动作执行
        original_execute_actions = executor.execute_actions
        
        def monitored_execute_actions(actions):
            """监控动作列表执行"""
            if not actions:
                return
            
            print(f"  📋 开始执行动作列表: {len(actions)} 个动作")
            
            # 检查执行前的状态
            print(f"    执行前目标效果数量: {len(other_spirit.effect_manager.effects)}")
            
            # 检查执行前的阶段队列
            print(f"    执行前阶段队列:")
            total_queued_before = check_phase_queues()
            print(f"    总排队动作数量: {total_queued_before}")
            
            # 调用原始方法
            result = original_execute_actions(actions)
            
            # 检查执行后的状态
            print(f"    执行后目标效果数量: {len(other_spirit.effect_manager.effects)}")
            
            # 检查执行后的阶段队列
            print(f"    执行后阶段队列:")
            total_queued_after = check_phase_queues()
            print(f"    总排队动作数量: {total_queued_after}")
            
            return result
        
        # 替换执行方法
        executor.execute_actions = monitored_execute_actions
        
        # 检查初始状态
        print(f"\n📋 检查初始状态:")
        print(f"  伏妖效果数量: {len(fuyao_spirit.effect_manager.effects)}")
        print(f"  目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 执行一次精灵回合
        print(f"\n📋 执行精灵回合:")
        
        result = engine.execute_next_spirit_turn()
        
        print(f"  回合结果: {result.get('type', 'Unknown')}")
        if result.get("type") == "spirit_turn":
            spirit_name = result.get("spirit_name", "Unknown")
            print(f"  执行精灵: {spirit_name}")
        
        # 检查最终状态
        print(f"\n📋 检查最终状态:")
        print(f"  目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 显示目标的效果
        if len(other_spirit.effect_manager.effects) > 0:
            print(f"  目标效果:")
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"    - {effect_name}")
        
        # 检查被动效果的状态变化
        print(f"\n📋 检查被动效果状态变化:")
        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
            if hasattr(effect, 'first_attack_used'):
                print(f"  {effect.name} 首次攻击已使用: {effect.first_attack_used}")
        
        # 最终检查阶段队列
        print(f"\n📋 最终阶段队列状态:")
        total_queued_final = check_phase_queues()
        print(f"  总排队动作数量: {total_queued_final}")
        
        # 判断测试结果
        target_effects_after = len(other_spirit.effect_manager.effects)
        
        if target_effects_after > 0:
            print(f"\n✅ 战斗时机测试成功！")
            print(f"  - 被动效果成功触发: 目标获得了效果")
            return True
        else:
            print(f"\n❌ 战斗时机测试失败")
            print(f"  - 被动效果没有触发: 目标没有获得效果")
            
            # 如果有排队的动作，说明可能是时机问题
            if total_queued_final > 0:
                print(f"  - 可能的原因: 还有 {total_queued_final} 个动作在队列中等待执行")
            
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 战斗时机测试")
    print("="*60)
    
    result = test_battle_timing()
    
    print("\n" + "="*60)
    if result:
        print("✅ 战斗时机测试成功")
        print("被动效果在正确的时机触发")
    else:
        print("❌ 战斗时机测试失败")

if __name__ == "__main__":
    main()
