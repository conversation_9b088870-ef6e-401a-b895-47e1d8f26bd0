"""
神曜虚无·伏妖 - 精灵模块

这个模块包含了神曜虚无·伏妖精灵的基础实现。
属性：火，职业：魔法、通灵、神曜

等待具体的技能设计和效果描述后，将添加：
- 基础效果类
- 被动技能效果
- 神曜技能效果
- 技能组件
- 技能定义
"""

# 导入主要的创建函数
from .spirit import (
    create_fuyao_spirit,
    create_fuyao_passive_effects,
    create_shen_yao_xu_wu_fu_yao_spirit,  # 向后兼容
    create_passive_effects,  # 向后兼容
    get_spirit_data_from_json,  # 🔧 修复：替代已移除的SPIRIT_DATA
    FUYAO_INFO
)

# 导入效果类
from .effects import (
    XuWuStateEffect,
    BurnEffect,
    TongLingTransformEffect,
    create_xuwu_state_effect,
    create_burn_effect,
    create_tongling_transform_effect
)

# 导入被动效果
from .passive_effects import (
    BiAnShuShaPassiveEffect,
    create_bian_shusha_passive_effect
)

# 导入神曜和通灵效果
from .shenyao_effects import (
    WuNianChanShenShenYaoEffect,
    HuanLingXieZhuTongLingEffect,
    create_wunian_chanshen_effect,
    create_huanling_xiezhu_effect
)

# 导入技能组件
from .skill_components import (
    BurnTargetComponent,
    XuWuStateComponent,
    HighestEnergyTargetComponent,
    TongLingProgressComponent,
    ConditionalXuWuComponent,
    EnergyGainComponent
)

# 导入技能定义
from .skills import (
    create_fuyao_skills,
    FUYAO_SKILLS_DATA
)


# 主要导出接口
__all__ = [
    # 主要创建函数
    'create_fuyao_spirit',
    'create_fuyao_passive_effects',

    # 向后兼容函数
    'create_shen_yao_xu_wu_fu_yao_spirit',
    'create_passive_effects',

    # 效果类
    'XuWuStateEffect',
    'BurnEffect',
    'TongLingTransformEffect',
    'create_xuwu_state_effect',
    'create_burn_effect',
    'create_tongling_transform_effect',

    # 被动效果
    'BiAnShuShaPassiveEffect',
    'create_bian_shusha_passive_effect',

    # 神曜和通灵效果
    'WuNianChanShenShenYaoEffect',
    'HuanLingXieZhuTongLingEffect',
    'create_wunian_chanshen_effect',
    'create_huanling_xiezhu_effect',

    # 技能组件
    'BurnTargetComponent',
    'XuWuStateComponent',
    'HighestEnergyTargetComponent',
    'TongLingProgressComponent',
    'ConditionalXuWuComponent',
    'EnergyGainComponent',

    # 技能定义
    'create_fuyao_skills',
    'FUYAO_SKILLS_DATA',

    # 数据配置
    'get_spirit_data_from_json',  # 🔧 修复：替代已移除的SPIRIT_DATA
    'FUYAO_INFO'
]


# 模块信息
__version__ = "1.0.0"
__author__ = "Augment Agent"
__description__ = "神曜虚无·伏妖精灵模块 - 火属性魔法神曜通灵精灵"


def get_module_info():
    """获取模块信息"""
    return {
        "name": "神曜虚无·伏妖",
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "spirit_id": "shen_yao_xu_wu_fu_yao",
        "spirit_name": "神曜虚无·伏妖",
        "element": "火",
        "professions": ["魔法", "通灵", "神曜"],
        "files": [
            "effects.py",
            "passive_effects.py",
            "shenyao_effects.py",
            "skill_components.py",
            "skills.py",
            "spirit.py",
            "__init__.py"
        ],
        "main_features": [
            "虚无操控机制",
            "火焰魔法攻击",
            "神曜领域控制",
            "通灵能力增强",
            "复合伤害系统"
        ]
    }


def validate_module():
    """验证模块完整性"""
    try:
        # 测试主要功能
        spirit = create_fuyao_spirit()
        effects = create_fuyao_passive_effects(spirit)
        skills = create_fuyao_skills(spirit)

        return {
            "valid": True,
            "spirit_created": spirit is not None,
            "effects_count": len(effects),
            "skills_count": len(skills),
            "spirit_name": spirit.name if spirit else None,
            "element": spirit.metadata.element.value if spirit and spirit.metadata.element else None,
            "professions": [prof.value for prof in spirit.metadata.professions] if spirit else []
        }
    except Exception as e:
        return {
            "valid": False,
            "error": str(e)
        }


# 快速测试函数
def quick_test():
    """快速测试模块功能"""
    print("🔍 测试神曜虚无·伏妖模块...")
    
    # 获取模块信息
    info = get_module_info()
    print(f"📋 模块: {info['name']} v{info['version']}")
    print(f"   属性: {info['element']}")
    print(f"   职业: {', '.join(info['professions'])}")
    
    # 验证模块
    validation = validate_module()
    if validation["valid"]:
        print("✅ 模块验证通过")
        print(f"   精灵: {validation['spirit_name']}")
        print(f"   属性: {validation['element']}")
        print(f"   职业: {', '.join(validation['professions'])}")
        print(f"   被动效果: {validation['effects_count']}个")
        print(f"   技能: {validation['skills_count']}个")
    else:
        print(f"❌ 模块验证失败: {validation['error']}")
    
    return validation["valid"]


if __name__ == "__main__":
    # 如果直接运行此模块，执行快速测试
    quick_test()
