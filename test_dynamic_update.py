#!/usr/bin/env python3
"""
测试动态更新和超杀阈值修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dynamic_update_and_ultrakill():
    """测试动态更新和超杀阈值"""
    print("🔧 测试动态更新和超杀阈值修复...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 🔧 修复超杀阈值：设置正确的顺位加气和超杀气势
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=3,
            turn_order_bonus_energy=50  # 恢复正常的顺位加气
        )
        
        # 为天恩圣祭·空灵圣龙设置超杀气势
        if hasattr(spirit1, 'components') and spirit1.name == "天恩圣祭·空灵圣龙":
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"🔥 为 {spirit1.name} 设置超杀气势: 300")
        
        print(f"✅ 战斗引擎创建成功")
        print(f"  精灵1: {spirit1.name} (气势: {spirit1.energy})")
        print(f"  精灵2: {spirit2.name} (气势: {spirit2.energy})")
        
        # 测试单精灵回合执行和动态更新
        print(f"\n🎯 测试单精灵回合执行...")
        
        for i in range(4):  # 执行几个精灵回合
            print(f"\n--- 执行第 {i+1} 次精灵回合 ---")
            
            result = engine.execute_next_spirit_turn()
            result_type = result.get("type", "unknown")
            message = result.get("message", "")
            
            print(f"结果类型: {result_type}")
            print(f"消息: {message}")
            
            if result_type == "spirit_turn":
                spirit_name = result.get("spirit_name", "Unknown")
                actions_count = result.get("actions_generated", 0)
                
                # 🔧 检查动态状态信息
                spirit_state = result.get("spirit_state", {})
                hp_change = spirit_state.get("hp_change", 0)
                energy_change = spirit_state.get("energy_change", 0)
                
                print(f"  精灵: {spirit_name}")
                print(f"  生成动作: {actions_count}")
                print(f"  HP变化: {hp_change:+.0f}")
                print(f"  气势变化: {energy_change:+}")
                
                # 检查战斗状态快照
                battle_snapshot = result.get("battle_state_snapshot", {})
                if battle_snapshot:
                    all_spirits = battle_snapshot.get("all_spirits_state", [])
                    print(f"  实时状态快照:")
                    for spirit_info in all_spirits:
                        name = spirit_info.get("name", "Unknown")
                        hp = spirit_info.get("hp", 0)
                        max_hp = spirit_info.get("max_hp", 1)
                        energy = spirit_info.get("energy", 0)
                        print(f"    {name}: HP={hp:.0f}/{max_hp:.0f}, 气势={energy}")
                
                # 🔥 检查是否触发了超杀
                if actions_count >= 3 and spirit_name == "天恩圣祭·空灵圣龙":
                    print(f"  🔥 检测到超杀技能！{spirit_name} 生成了 {actions_count} 个动作")
                    
            elif result_type == "round_end":
                round_num = result.get("round_num", 0)
                print(f"  回合 {round_num} 结束")
                
            elif result_type == "battle_end":
                winner = result.get("winner", -1)
                print(f"  战斗结束，获胜方: {'平局' if winner == -1 else f'队伍{winner}'}")
                break
                
            else:
                print(f"  其他结果: {result_type}")
        
        # 检查最终统计
        print(f"\n📊 最终统计检查...")
        if hasattr(engine, 'stats_tracker') and engine.stats_tracker:
            stats_data = engine.stats_tracker.data
            total_damage = sum(data.get('total_damage_dealt', 0) for data in stats_data.values())
            print(f"  总伤害: {total_damage:.0f}")
            
            for spirit_id, data in stats_data.items():
                damage_dealt = data.get('total_damage_dealt', 0)
                if damage_dealt > 0:
                    print(f"  {spirit_id}: 造成伤害 {damage_dealt:.0f}")
        
        print(f"\n✅ 动态更新和超杀阈值测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 动态更新和超杀阈值修复测试")
    print("="*60)
    
    result = test_dynamic_update_and_ultrakill()
    
    print("\n" + "="*60)
    if result:
        print("✅ 修复验证成功")
        print("\n📋 修复内容:")
        print("  1. ✅ 恢复正确的超杀阈值设置")
        print("  2. ✅ 实现动态更新机制")
        print("  3. ✅ 每个动作后立即更新状态")
        print("  4. ✅ 提供详细的状态变化信息")
        print("\n🚀 现在可以运行UI验证修复效果：")
        print("  python ui/ux/enhanced_battle_ui.py")
    else:
        print("❌ 修复验证失败")

if __name__ == "__main__":
    main()
