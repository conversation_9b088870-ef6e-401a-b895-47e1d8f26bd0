import axios from 'axios'

const apiClient = axios.create({
  baseURL: 'http://localhost:8081/api',
  headers: {
    'Content-Type': 'application/json',
  },
})

// 添加请求拦截器
apiClient.interceptors.request.use((config) => {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  config.headers['X-Request-ID'] = requestId
  console.log(`[Platform API] ${config.method?.toUpperCase()} ${config.url} (ID: ${requestId})`)
  return config
})

// 添加响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    const requestId = response.config.headers['X-Request-ID']
    console.log(`[Platform API Success] ${response.config.method?.toUpperCase()} ${response.config.url} (ID: ${requestId})`)
    return response
  },
  (error) => {
    const requestId = error.config?.headers['X-Request-ID']
    console.error(`[Platform API Error] ${error.config?.method?.toUpperCase()} ${error.config?.url} (ID: ${requestId})`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    })
    return Promise.reject(error)
  }
)

// 类型定义
export interface Spirit {
  id: string
  name: string
  attributes: {
    hp: number
    attack: number
    defense: number
    speed: number
  }
  element?: string
  professions: string[]
  skills: string[]
}

export interface Player {
  id: string
  name: string
  rating: number
  wins: number
  losses: number
  current_room?: string
  is_online: boolean
}

export interface Room {
  id: string
  name: string
  mode: string
  players: number
  max_players: number
  status: string
  created_at: string
}

export interface BattleRecord {
  id: string
  room_id: string
  players: { id: string; name: string }[]
  winner: number | null
  history: any[]
  timestamp: string
}

export interface LeaderboardEntry {
  id: string
  name: string
  rating: number
  wins: number
  losses: number
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string
  data: any
}

export interface CreateRoomMessage extends WebSocketMessage {
  type: 'create_room'
  data: {
    name: string
    mode: string
  }
}

export interface JoinRoomMessage extends WebSocketMessage {
  type: 'join_room'
  data: {
    room_id: string
  }
}

export interface LeaveRoomMessage extends WebSocketMessage {
  type: 'leave_room'
  data: {
    room_id: string
  }
}

export interface StartBattleMessage extends WebSocketMessage {
  type: 'start_battle'
  data: {
    room_id: string
    formations: {
      [playerId: string]: {
        spirits: {
          spirit_name: string
          position: [number, number]
        }[]
      }
    }
  }
}

// API接口
export const platformAPI = {
  /**
   * 获取可用精灵列表
   */
  async getSpirits(): Promise<{ total_spirits: number; spirits: Record<string, Spirit> }> {
    try {
      const response = await apiClient.get('/spirits')
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取精灵列表失败'
      console.error('获取精灵列表失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },

  /**
   * 获取排行榜
   */
  async getLeaderboard(): Promise<{ leaderboard: LeaderboardEntry[] }> {
    try {
      const response = await apiClient.get('/leaderboard')
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取排行榜失败'
      console.error('获取排行榜失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },

  /**
   * 获取战斗历史
   */
  async getBattleHistory(limit: number = 10): Promise<{ battles: BattleRecord[]; total: number }> {
    try {
      const response = await apiClient.get('/battle_history', {
        params: { limit }
      })
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取战斗历史失败'
      console.error('获取战斗历史失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },

  /**
   * 获取房间列表
   */
  async getRooms(): Promise<{ rooms: Room[] }> {
    try {
      const response = await apiClient.get('/rooms')
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取房间列表失败'
      console.error('获取房间列表失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  }
}

// WebSocket管理类
export class PlatformWebSocket {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private messageHandlers: Map<string, (data: any) => void> = new Map()
  private connectionHandlers: {
    onOpen?: () => void
    onClose?: () => void
    onError?: (error: Event) => void
  } = {}

  constructor(private playerId: string, private playerName: string) {}

  /**
   * 连接到WebSocket服务器
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `ws://localhost:8081/ws/${this.playerId}/${encodeURIComponent(this.playerName)}`
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('[Platform WebSocket] 连接成功')
          this.reconnectAttempts = 0
          this.connectionHandlers.onOpen?.()
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            console.log('[Platform WebSocket] 收到消息:', message.type, message.data)
            
            const handler = this.messageHandlers.get(message.type)
            if (handler) {
              handler(message.data)
            }
          } catch (error) {
            console.error('[Platform WebSocket] 解析消息失败:', error)
          }
        }

        this.ws.onclose = () => {
          console.log('[Platform WebSocket] 连接关闭')
          this.connectionHandlers.onClose?.()
          this.attemptReconnect()
        }

        this.ws.onerror = (error) => {
          console.error('[Platform WebSocket] 连接错误:', error)
          this.connectionHandlers.onError?.(error)
          reject(error)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 发送消息
   */
  send(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('[Platform WebSocket] 发送消息:', message.type, message.data)
      this.ws.send(JSON.stringify(message))
    } else {
      console.error('[Platform WebSocket] 连接未就绪，无法发送消息')
    }
  }

  /**
   * 注册消息处理器
   */
  onMessage(type: string, handler: (data: any) => void) {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 注册连接事件处理器
   */
  onConnection(handlers: {
    onOpen?: () => void
    onClose?: () => void
    onError?: (error: Event) => void
  }) {
    this.connectionHandlers = handlers
  }

  /**
   * 尝试重连
   */
  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`[Platform WebSocket] 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('[Platform WebSocket] 重连失败:', error)
        })
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('[Platform WebSocket] 重连次数已达上限')
    }
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
}

// 便捷方法
export const createWebSocketConnection = (playerId: string, playerName: string): PlatformWebSocket => {
  return new PlatformWebSocket(playerId, playerName)
}

export default platformAPI
