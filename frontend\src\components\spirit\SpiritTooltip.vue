<template>
  <el-tooltip
    placement="top"
    :show-after="300"
    :hide-after="100"
    popper-class="spirit-tooltip-popper"
    :disabled="false"
    effect="dark"
  >
    <template #content>
      <div class="spirit-tooltip-content">
        <!-- 精灵头部信息 -->
        <div class="tooltip-header">
          <div class="flex items-center mb-3">
            <div class="spirit-avatar-small">
              <el-icon v-if="spirit.element" :class="`element-${spirit.element}`">
                <component :is="getElementIcon(spirit.element)" />
              </el-icon>
              <span v-else>{{ spirit.name.charAt(0) }}</span>
            </div>
            <div class="ml-3">
              <h4 class="spirit-name">{{ spirit.name }}</h4>
              <div class="spirit-meta">
                <span class="level">Lv.{{ spirit.level }}</span>
                <span v-if="spirit.shengeLevel > 0" class="shenge-level">
                  <el-icon><Star /></el-icon>{{ spirit.shengeLevel }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 元素和职业标签 -->
          <div class="tags-container">
            <el-tag v-if="spirit.element" size="small" :type="getElementTagType(spirit.element)" class="mr-1">
              {{ getElementLabel(spirit.element) }}
            </el-tag>
            <el-tag v-for="profession in spirit.professions" :key="profession" size="small" type="info" class="mr-1">
              {{ getProfessionLabel(profession) }}
            </el-tag>
          </div>
        </div>

        <!-- 属性信息 -->
        <div class="tooltip-attributes">
          <div class="attribute-grid">
            <div class="attribute-item">
              <span class="attr-label">生命值</span>
              <span class="attr-value">{{ spirit.attributes.hp }}/{{ spirit.attributes.maxHp }}</span>
              <div class="attr-bar">
                <div class="attr-fill hp-fill" :style="{ width: `${(spirit.attributes.hp / spirit.attributes.maxHp) * 100}%` }"></div>
              </div>
            </div>
            
            <div class="attribute-item" v-if="spirit.attributes.maxEnergy > 0">
              <span class="attr-label">能量值</span>
              <span class="attr-value">{{ spirit.attributes.energy }}/{{ spirit.attributes.maxEnergy }}</span>
              <div class="attr-bar">
                <div class="attr-fill energy-fill" :style="{ width: `${(spirit.attributes.energy / spirit.attributes.maxEnergy) * 100}%` }"></div>
              </div>
            </div>
            
            <div class="attribute-item">
              <span class="attr-label">攻击力</span>
              <span class="attr-value">{{ spirit.attributes.attack }}</span>
            </div>
            
            <div class="attribute-item">
              <span class="attr-label">防御力</span>
              <span class="attr-value">{{ spirit.attributes.defense }}</span>
            </div>
            
            <div class="attribute-item">
              <span class="attr-label">速度</span>
              <span class="attr-value">{{ spirit.attributes.speed }}</span>
            </div>
          </div>
        </div>

        <!-- 效果信息 -->
        <div class="tooltip-effects" v-if="spirit.effects.length > 0">
          <div class="effects-title">当前效果</div>
          <div class="effects-list">
            <div v-for="effect in spirit.effects" :key="effect.id" class="effect-item">
              <span class="effect-name" :class="`effect-${effect.category}`">{{ effect.name }}</span>
              <span class="effect-duration" v-if="effect.remainingDuration > 0">
                {{ effect.remainingDuration }}回合
              </span>
              <span class="effect-stacks" v-if="effect.stackCount > 1">
                x{{ effect.stackCount }}
              </span>
            </div>
          </div>
        </div>

        <!-- 技能信息 -->
        <div class="tooltip-skills" v-if="showSkills && spirit.skills.length > 0">
          <div class="skills-title">技能列表</div>
          <div class="skills-list">
            <div v-for="skill in spirit.skills.slice(0, 3)" :key="skill.id" class="skill-item">
              <span class="skill-name">{{ skill.name }}</span>
              <span class="skill-cooldown" v-if="skill.currentCooldown > 0">
                冷却: {{ skill.currentCooldown }}
              </span>
            </div>
            <div v-if="spirit.skills.length > 3" class="more-skills">
              还有 {{ spirit.skills.length - 3 }} 个技能...
            </div>
          </div>
        </div>

        <!-- 位置信息 -->
        <div class="tooltip-position" v-if="showPosition">
          <span class="position-label">位置:</span>
          <span class="position-value">({{ spirit.position[0] + 1 }}, {{ spirit.position[1] + 1 }})</span>
        </div>
      </div>
    </template>
    
    <slot></slot>
  </el-tooltip>
</template>

<script setup lang="ts">
import type { Spirit, ElementType, ProfessionType } from '@/types/battle'

interface Props {
  spirit: Spirit
  showSkills?: boolean
  showPosition?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSkills: true,
  showPosition: true
})

// 元素图标映射
const getElementIcon = (element: string) => {
  const iconMap: Record<string, string> = {
    fire: 'Sunny',
    water: 'Drizzling', 
    earth: 'Mountain',
    air: 'Wind',
    light: 'Sunrise',
    dark: 'Moon',
    neutral: 'CircleDot'
  }
  return iconMap[element] || 'CircleDot'
}

// 元素标签类型
const getElementTagType = (element: ElementType) => {
  const typeMap: Record<ElementType, string> = {
    [ElementType.FIRE]: 'danger',
    [ElementType.WATER]: 'primary',
    [ElementType.EARTH]: 'success',
    [ElementType.AIR]: 'info',
    [ElementType.LIGHT]: 'warning',
    [ElementType.DARK]: 'info',
    [ElementType.NEUTRAL]: 'info'
  }
  return typeMap[element] || 'info'
}

// 元素标签文本
const getElementLabel = (element: ElementType) => {
  const labelMap: Record<ElementType, string> = {
    [ElementType.FIRE]: '火',
    [ElementType.WATER]: '水',
    [ElementType.EARTH]: '土',
    [ElementType.AIR]: '风',
    [ElementType.LIGHT]: '光',
    [ElementType.DARK]: '暗',
    [ElementType.NEUTRAL]: '无'
  }
  return labelMap[element] || '无'
}

// 职业标签文本
const getProfessionLabel = (profession: ProfessionType) => {
  const labelMap: Record<ProfessionType, string> = {
    [ProfessionType.WARRIOR]: '战士',
    [ProfessionType.MAGE]: '法师',
    [ProfessionType.ARCHER]: '射手',
    [ProfessionType.HEALER]: '治疗',
    [ProfessionType.ASSASSIN]: '刺客',
    [ProfessionType.TANK]: '坦克'
  }
  return labelMap[profession] || profession
}
</script>


<style scoped lang="scss">
.spirit-tooltip-content {
  padding: 12px;
  color: #f8fafc;
  font-size: 11px;
  line-height: 1.3;
  max-width: 220px;

  .tooltip-header {
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
    padding-bottom: 12px;
    margin-bottom: 12px;

    .spirit-avatar-small {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(135deg, #8b5cf6, #06b6d4);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      color: white;
      flex-shrink: 0;

      .element-fire { color: #f59e0b; }
      .element-water { color: #06b6d4; }
      .element-earth { color: #84cc16; }
      .element-air { color: #e5e7eb; }
      .element-light { color: #fbbf24; }
      .element-dark { color: #6b7280; }
    }

    .spirit-name {
      font-size: 12px;
      font-weight: 600;
      color: #f8fafc;
      margin: 0;
    }

    .spirit-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 2px;

      .level {
        color: #cbd5e1;
        font-size: 10px;
      }

      .shenge-level {
        display: flex;
        align-items: center;
        gap: 2px;
        color: #ffd700;
        font-size: 10px;
        font-weight: bold;
      }
    }

    .tags-container {
      margin-top: 8px;
    }
  }

  .tooltip-attributes {
    margin-bottom: 12px;

    .attribute-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .attribute-item {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .attr-label {
        color: #94a3b8;
        font-size: 9px;
      }

      .attr-value {
        color: #f8fafc;
        font-size: 9px;
        font-weight: 500;
      }

      .attr-bar {
        height: 3px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;

        .attr-fill {
          height: 100%;
          transition: width 0.3s ease;

          &.hp-fill {
            background: linear-gradient(90deg, #10b981, #f59e0b, #ef4444);
          }

          &.energy-fill {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
          }
        }
      }
    }
  }

  .tooltip-effects {
    margin-bottom: 12px;

    .effects-title {
      color: #cbd5e1;
      font-size: 11px;
      font-weight: 500;
      margin-bottom: 6px;
    }

    .effects-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .effect-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 6px;

      .effect-name {
        font-size: 9px;
        font-weight: 500;

        &.effect-buff {
          color: #10b981;
        }

        &.effect-debuff {
          color: #ef4444;
        }

        &.effect-neutral {
          color: #6b7280;
        }
      }

      .effect-duration,
      .effect-stacks {
        font-size: 10px;
        color: #94a3b8;
      }
    }
  }

  .tooltip-skills {
    margin-bottom: 12px;

    .skills-title {
      color: #cbd5e1;
      font-size: 11px;
      font-weight: 500;
      margin-bottom: 6px;
    }

    .skills-list {
      display: flex;
      flex-direction: column;
      gap: 3px;
    }

    .skill-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 3px 6px;
      background: rgba(139, 92, 246, 0.1);
      border-radius: 4px;

      .skill-name {
        font-size: 9px;
        color: #e2e8f0;
      }

      .skill-cooldown {
        font-size: 10px;
        color: #f59e0b;
      }
    }

    .more-skills {
      font-size: 10px;
      color: #94a3b8;
      text-align: center;
      padding: 2px;
    }
  }

  .tooltip-position {
    border-top: 1px solid rgba(139, 92, 246, 0.2);
    padding-top: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .position-label {
      color: #94a3b8;
      font-size: 9px;
    }

    .position-value {
      color: #f8fafc;
      font-size: 9px;
      font-weight: 500;
    }
  }
}
</style>