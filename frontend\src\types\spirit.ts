/**
 * 精灵的属性定义
 */
export interface SpiritAttributes {
  base_hp: number;
  hp_p: number;
  hp_flat: number;
  base_attack: number;
  attack_p: number;
  attack_flat: number;
  base_pdef: number;
  pdef_p: number;
  pdef_flat: number;
  base_mdef: number;
  mdef_p: number;
  mdef_flat: number;
  speed: number;
  hit_rate: number;
  dodge_rate: number;
  break_rate: number;
  block_rate: number;
  crit_rate: number;
  crit_res_rate: number;
}

/**
 * 技能信息
 */
export interface SkillInfo {
  id: string;
  name: string;
  description: string;
  cast_type: string;
  category: string;
}

/**
 * 精灵原型的数据结构
 */
export interface SpiritPrototype {
  id: string; // 增加唯一ID
  team: number; // 增加队伍ID
  name_prefix: string;
  element: string; //
  professions: string[];
  attributes: SpiritAttributes;
  skills: SkillInfo[];
  shenge_level: number;
} 