"""
技能模块 (新架构)

此模块统一导出新一代的、基于组件的灵活技能系统的所有核心组件。
新代码应直接从这里导入所需的类，以构建模块化、可复用的技能。
"""

# 本地导入
from .skills import (
    # --- 核心协议与基类 ---
    ISkillComponent,
    ISkillCondition,
    ITargetSelector,
    Skill,

    # --- 元数据与上下文 ---
    SkillMetadata,
    SkillContext,
    CastType,

    # --- 内置条件实现 ---
    EnergyCondition,
    HealthPercentCondition,
    CooldownCondition,

    # --- 内置目标选择器 ---
    # 🔧 重构完成：目标选择器已统一到 targeting.selectors 模块
    # 这里重新导出以保持向后兼容
    SingleEnemySelector,
    AllEnemiesSelector,
    AllAlliesSelector,
    SelfSelector,
    LowestHpAllySelector,

    # --- 内置技能组件 ---
    DamageComponent,
    HealingComponent,
    SkillEffectComponent,
    SkillEnergyComponent,

    # --- 辅助工厂 ---
    SkillFactory
)

__all__ = [
    # Protocols and Base Classes
    'ISkillComponent', 'ISkillCondition', 'ITargetSelector', 'Skill',
    # Metadata and Context
    'SkillMetadata', 'SkillContext', 'CastType',
    # Conditions
    'EnergyCondition', 'HealthPercentCondition', 'CooldownCondition',
    # Target Selectors
    'SingleEnemySelector', 'AllEnemiesSelector', 'AllAlliesSelector', 'SelfSelector', 'LowestHpAllySelector',
    # Skill Components
    'DamageComponent', 'HealingComponent', 'SkillEffectComponent', 'SkillEnergyComponent',
    # Factory
    'SkillFactory'
]
