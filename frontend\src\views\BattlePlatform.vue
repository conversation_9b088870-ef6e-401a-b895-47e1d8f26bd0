<template>
  <div class="battle-platform">
    <!-- 顶部状态栏 -->
    <div class="platform-header">
      <div class="connection-status">
        <el-tag :type="connectionStatus.type" size="large">
          <el-icon><Connection /></el-icon>
          {{ connectionStatus.text }}
        </el-tag>
      </div>
      
      <div class="player-info" v-if="currentPlayer">
        <el-avatar :size="40">{{ currentPlayer.name.charAt(0) }}</el-avatar>
        <div class="player-details">
          <div class="player-name">{{ currentPlayer.name }}</div>
          <div class="player-rating">评分: {{ currentPlayer.rating }}</div>
        </div>
      </div>
    </div>

    <div class="platform-content">
      <!-- 左侧面板：房间和玩家 -->
      <div class="left-panel">
        <!-- 连接区域 -->
        <el-card class="connection-card" v-if="!isConnected">
          <template #header>
            <span>🎮 加入对战平台</span>
          </template>
          
          <el-form @submit.prevent="connectToPlatform">
            <el-form-item label="玩家昵称">
              <el-input 
                v-model="playerName" 
                placeholder="输入你的昵称"
                :disabled="connecting"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="connectToPlatform"
                :loading="connecting"
                style="width: 100%"
              >
                连接平台
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 房间管理 -->
        <el-card class="room-card" v-if="isConnected">
          <template #header>
            <span>🏠 房间管理</span>
          </template>
          
          <div class="create-room-section">
            <el-form @submit.prevent="createRoom">
              <el-form-item label="房间名称">
                <el-input v-model="newRoomName" placeholder="我的房间" />
              </el-form-item>
              <el-form-item label="游戏模式">
                <el-select v-model="newRoomMode" style="width: 100%">
                  <el-option label="1v1 对战" value="1v1" />
                  <el-option label="2v2 团战" value="2v2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="createRoom" style="width: 100%">
                  创建房间
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>

        <!-- 房间列表 -->
        <el-card class="rooms-list" v-if="isConnected">
          <template #header>
            <span>🏠 房间列表</span>
          </template>
          
          <div class="room-item" v-for="room in rooms" :key="room.id">
            <div class="room-info">
              <div class="room-name">{{ room.name }}</div>
              <div class="room-details">
                {{ room.mode }} | {{ room.players }}/{{ room.max_players }}
              </div>
            </div>
            <el-button 
              size="small" 
              @click="joinRoom(room.id)"
              :disabled="room.players >= room.max_players || room.status !== 'waiting'"
            >
              {{ room.status === 'waiting' ? '加入' : room.status }}
            </el-button>
          </div>
        </el-card>

        <!-- 在线玩家 -->
        <el-card class="players-list" v-if="isConnected">
          <template #header>
            <span>👥 在线玩家 ({{ onlinePlayers.length }})</span>
          </template>
          
          <div class="player-item" v-for="player in onlinePlayers" :key="player.id">
            <el-avatar :size="30">{{ player.name.charAt(0) }}</el-avatar>
            <div class="player-info">
              <div class="player-name">{{ player.name }}</div>
              <div class="player-rating">{{ player.rating }}分</div>
            </div>
            <el-tag v-if="player.current_room" size="small" type="info">房间中</el-tag>
          </div>
        </el-card>
      </div>

      <!-- 中间面板：战斗区域 -->
      <div class="center-panel">
        <el-card class="battle-area">
          <template #header>
            <div class="battle-header">
              <span>⚔️ 战斗竞技场</span>
              <div class="battle-controls" v-if="currentRoom">
                <el-button @click="showFormationDialog = true" type="primary" size="small">
                  配置阵型
                </el-button>
                <el-button 
                  @click="startBattle" 
                  type="success" 
                  size="small"
                  :disabled="!canStartBattle"
                >
                  开始战斗
                </el-button>
                <el-button @click="leaveRoom" type="danger" size="small">
                  离开房间
                </el-button>
              </div>
            </div>
          </template>

          <!-- 房间状态 -->
          <div class="room-status" v-if="currentRoom">
            <el-alert 
              :title="`房间: ${currentRoom.name}`" 
              :type="currentRoom.status === 'ready' ? 'success' : 'info'"
              :description="`模式: ${currentRoom.mode} | 玩家: ${currentRoom.players?.length || 0}/${currentRoom.max_players}`"
              show-icon
              :closable="false"
            />
          </div>

          <!-- 战斗日志 -->
          <div class="battle-log" ref="battleLogRef">
            <div 
              v-for="(log, index) in battleLogs" 
              :key="index" 
              class="log-entry"
              :class="log.type"
            >
              <span class="log-time">[{{ log.time }}]</span>
              <span class="log-content">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧面板：排行榜和统计 -->
      <div class="right-panel">
        <!-- 排行榜 -->
        <el-card class="leaderboard">
          <template #header>
            <span>🏆 排行榜</span>
          </template>
          
          <div class="leaderboard-item" v-for="(player, index) in leaderboard" :key="player.id">
            <div class="rank">
              <el-tag :type="getRankType(index)" size="small">#{{ index + 1 }}</el-tag>
            </div>
            <div class="player-info">
              <div class="player-name">{{ player.name }}</div>
              <div class="player-stats">
                {{ player.rating }}分 | {{ player.wins }}胜{{ player.losses }}负
              </div>
            </div>
          </div>
        </el-card>

        <!-- 战斗历史 -->
        <el-card class="battle-history">
          <template #header>
            <span>📊 最近战斗</span>
          </template>
          
          <div class="history-item" v-for="battle in battleHistory" :key="battle.id">
            <div class="battle-info">
              <div class="battle-players">
                {{ battle.players.map(p => p.name).join(' vs ') }}
              </div>
              <div class="battle-result">
                获胜者: {{ battle.winner !== null ? battle.players[battle.winner]?.name : '平局' }}
              </div>
              <div class="battle-time">
                {{ formatTime(battle.timestamp) }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 阵型配置对话框 -->
    <el-dialog 
      v-model="showFormationDialog" 
      title="⚔️ 配置战斗阵型" 
      width="800px"
      :close-on-click-modal="false"
    >
      <FormationEditor 
        v-model:formation="playerFormation"
        :available-spirits="availableSpirits"
        @save="saveFormation"
      />
      
      <template #footer>
        <el-button @click="showFormationDialog = false">取消</el-button>
        <el-button type="primary" @click="saveFormation">保存阵型</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import FormationEditor from '@/components/battle/FormationEditor.vue'

// 响应式数据
const isConnected = ref(false)
const connecting = ref(false)
const playerName = ref('')
const currentPlayer = ref<any>(null)
const currentRoom = ref<any>(null)
const rooms = ref<any[]>([])
const onlinePlayers = ref<any[]>([])
const leaderboard = ref<any[]>([])
const battleHistory = ref<any[]>([])
const battleLogs = ref<any[]>([])
const availableSpirits = ref<any[]>([])
const playerFormation = ref<any[]>([])

// 房间创建
const newRoomName = ref('我的房间')
const newRoomMode = ref('1v1')

// UI状态
const showFormationDialog = ref(false)
const battleLogRef = ref<HTMLElement>()

// WebSocket连接
let ws: WebSocket | null = null

// 计算属性
const connectionStatus = computed(() => {
  if (connecting.value) {
    return { type: 'warning', text: '连接中...' }
  }
  return isConnected.value 
    ? { type: 'success', text: '已连接' }
    : { type: 'danger', text: '未连接' }
})

const canStartBattle = computed(() => {
  return currentRoom.value && 
         currentRoom.value.status === 'ready' && 
         playerFormation.value.length > 0
})

// 方法
const connectToPlatform = async () => {
  if (!playerName.value.trim()) {
    ElMessage.error('请输入玩家昵称')
    return
  }

  connecting.value = true
  
  try {
    const playerId = `player_${Date.now()}`
    const wsUrl = `ws://localhost:8081/ws/${playerId}/${encodeURIComponent(playerName.value)}`
    
    ws = new WebSocket(wsUrl)
    
    ws.onopen = () => {
      isConnected.value = true
      connecting.value = false
      currentPlayer.value = {
        id: playerId,
        name: playerName.value,
        rating: 1000
      }
      addBattleLog('success', '成功连接到对战平台！')
      ElMessage.success('连接成功！')
    }
    
    ws.onmessage = (event) => {
      const message = JSON.parse(event.data)
      handleWebSocketMessage(message)
    }
    
    ws.onclose = () => {
      isConnected.value = false
      connecting.value = false
      addBattleLog('error', '与平台的连接已断开')
      ElMessage.error('连接已断开')
    }
    
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      connecting.value = false
      ElMessage.error('连接失败')
    }
    
  } catch (error) {
    connecting.value = false
    ElMessage.error('连接失败')
  }
}

const handleWebSocketMessage = (message: any) => {
  switch (message.type) {
    case 'welcome':
      currentPlayer.value = {
        ...currentPlayer.value,
        ...message.data
      }
      addBattleLog('info', `欢迎 ${message.data.player_name}！当前评分: ${message.data.rating}`)
      break
      
    case 'player_list':
      onlinePlayers.value = message.data.players
      break
      
    case 'room_list':
      rooms.value = message.data.rooms
      break
      
    case 'room_update':
      if (message.data.room_id === currentRoom.value?.id) {
        currentRoom.value = {
          ...currentRoom.value,
          players: message.data.players,
          status: message.data.status
        }
        addBattleLog('info', `房间状态更新: ${message.data.status}`)
      }
      break
      
    case 'battle_start':
      addBattleLog('success', '🚀 战斗开始！')
      break
      
    case 'battle_round':
      handleBattleRound(message.data)
      break
      
    case 'battle_end':
      handleBattleEnd(message.data)
      break
      
    case 'leaderboard':
      leaderboard.value = message.data.leaderboard
      break
  }
}

const createRoom = () => {
  if (!ws || ws.readyState !== WebSocket.OPEN) return
  
  ws.send(JSON.stringify({
    type: 'create_room',
    data: {
      name: newRoomName.value,
      mode: newRoomMode.value
    }
  }))
  
  addBattleLog('info', `创建房间: ${newRoomName.value}`)
}

const joinRoom = (roomId: string) => {
  if (!ws || ws.readyState !== WebSocket.OPEN) return
  
  ws.send(JSON.stringify({
    type: 'join_room',
    data: { room_id: roomId }
  }))
  
  const room = rooms.value.find(r => r.id === roomId)
  if (room) {
    currentRoom.value = room
    addBattleLog('info', `加入房间: ${room.name}`)
  }
}

const leaveRoom = () => {
  if (!ws || ws.readyState !== WebSocket.OPEN || !currentRoom.value) return
  
  ws.send(JSON.stringify({
    type: 'leave_room',
    data: { room_id: currentRoom.value.id }
  }))
  
  addBattleLog('info', `离开房间: ${currentRoom.value.name}`)
  currentRoom.value = null
}

const startBattle = () => {
  if (!ws || ws.readyState !== WebSocket.OPEN || !currentRoom.value) return
  
  if (playerFormation.value.length === 0) {
    ElMessage.error('请先配置阵型')
    return
  }
  
  const formations: any = {}
  formations[currentPlayer.value.id] = {
    spirits: playerFormation.value.map(spirit => ({
      spirit_name: spirit.name,
      position: spirit.position
    }))
  }
  
  ws.send(JSON.stringify({
    type: 'start_battle',
    data: {
      room_id: currentRoom.value.id,
      formations: formations
    }
  }))
}

const saveFormation = () => {
  showFormationDialog.value = false
  ElMessage.success('阵型配置已保存')
}

const handleBattleRound = (data: any) => {
  if (data.type === 'round_start') {
    addBattleLog('info', `--- 第 ${data.round_num} 回合 ---`)
  } else if (data.actions) {
    data.actions.forEach((action: any) => {
      addBattleLog('battle', `${action.actor}: ${action.description || action.type}`)
    })
  }
}

const handleBattleEnd = (data: any) => {
  const winner = data.winner !== null ? `玩家 ${data.winner + 1}` : '平局'
  addBattleLog('success', `🏆 战斗结束！获胜者: ${winner}`)
  
  // 更新战斗历史
  if (data.battle_record) {
    battleHistory.value.unshift(data.battle_record)
    if (battleHistory.value.length > 10) {
      battleHistory.value = battleHistory.value.slice(0, 10)
    }
  }
  
  currentRoom.value = null
}

const addBattleLog = (type: string, message: string) => {
  const log = {
    type,
    time: new Date().toLocaleTimeString(),
    message
  }
  
  battleLogs.value.push(log)
  
  // 限制日志数量
  if (battleLogs.value.length > 100) {
    battleLogs.value = battleLogs.value.slice(-50)
  }
  
  // 自动滚动到底部
  nextTick(() => {
    if (battleLogRef.value) {
      battleLogRef.value.scrollTop = battleLogRef.value.scrollHeight
    }
  })
}

const getRankType = (index: number) => {
  if (index === 0) return 'warning' // 金色
  if (index === 1) return 'info'    // 银色
  if (index === 2) return 'success' // 铜色
  return ''
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(async () => {
  // 加载可用精灵
  try {
    const response = await fetch('/api/spirits')
    const data = await response.json()
    availableSpirits.value = Object.entries(data.spirits).map(([key, spirit]: [string, any]) => ({
      id: key,
      name: spirit.name,
      ...spirit
    }))
  } catch (error) {
    console.error('加载精灵数据失败:', error)
  }
  
  // 添加欢迎日志
  addBattleLog('info', '🎮 欢迎来到 AoQiAI 对战平台！')
  addBattleLog('info', '📝 请先连接平台并创建或加入房间')
  addBattleLog('info', '⚔️ 配置好阵型后即可开始精彩的精灵对战！')
})

onUnmounted(() => {
  if (ws) {
    ws.close()
  }
})
</script>

<style scoped lang="scss">
.battle-platform {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.player-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .player-details {
    .player-name {
      font-weight: bold;
      color: #2c3e50;
    }
    
    .player-rating {
      font-size: 12px;
      color: #7f8c8d;
    }
  }
}

.platform-content {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.left-panel, .right-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.center-panel {
  display: flex;
  flex-direction: column;
}

.battle-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .battle-controls {
    display: flex;
    gap: 8px;
  }
}

.room-status {
  margin-bottom: 16px;
}

.battle-log {
  flex: 1;
  background: #1a1a1a;
  color: #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  overflow-y: auto;
  max-height: 400px;
  
  .log-entry {
    margin-bottom: 4px;
    
    .log-time {
      color: #888;
      margin-right: 8px;
    }
    
    &.success .log-content {
      color: #4caf50;
    }
    
    &.error .log-content {
      color: #f44336;
    }
    
    &.info .log-content {
      color: #2196f3;
    }
    
    &.battle .log-content {
      color: #ff9800;
    }
  }
}

.room-item, .player-item, .leaderboard-item, .history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s;
  
  &:hover {
    background: #e9ecef;
  }
}

.room-info, .player-info {
  flex: 1;
  
  .room-name, .player-name {
    font-weight: bold;
    color: #2c3e50;
  }
  
  .room-details, .player-rating, .player-stats {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.leaderboard-item {
  .rank {
    margin-right: 12px;
  }
}

.history-item {
  flex-direction: column;
  align-items: flex-start;
  
  .battle-info {
    width: 100%;
    
    .battle-players {
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .battle-result, .battle-time {
      font-size: 12px;
      color: #7f8c8d;
    }
  }
}

@media (max-width: 1200px) {
  .platform-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .left-panel, .right-panel {
    max-height: 300px;
  }
}
</style>
