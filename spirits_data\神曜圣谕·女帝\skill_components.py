"""
神曜圣谕·女帝 - 技能组件模块

包含女帝技能使用的特殊组件：
- ShiXingZhiShouComponent: 试星之手的生命值伤害组件
- MingDingZhuWeiComponent: 命定主位的复杂组件
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING, Dict, Any

if TYPE_CHECKING:
    from src.core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import SkillContext

from .effects import TauntEffect


class ShiXingZhiShouComponent:
    """试星之手的生命值伤害组件 - 造成自身最大生命值*15%的伤害"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from src.core.action import DamageAction, DamageType, LogAction
        
        actions = []
        
        # 计算基于生命值的伤害
        max_hp = getattr(caster, 'max_hp', 0)
        hp_damage = max_hp * 0.15  # 15%最大生命值
        
        for target in targets:
            if target.is_alive:
                actions.extend([
                    LogAction(
                        caster=caster,
                        target=target,
                        message=f"[试星之手] {caster.name} 对 {target.name} 造成生命值伤害！"
                    ),
                    DamageAction(
                        caster=caster,
                        target=target,
                        damage_value=int(hp_damage),
                        damage_type=DamageType.PHYSICAL,
                        skill_name="试星之手"
                    )
                ])
        
        return actions


class MingDingZhuWeiComponent:
    """命定主位的复杂组件 - 生命值伤害+嘲讽+免疫"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from src.core.action import DamageAction, DamageType, LogAction, ApplyEffectAction
        from src.core.effect.effects import AttackImmunityEffect
        
        actions = []
        
        # 计算基于生命值的伤害
        max_hp = getattr(caster, 'max_hp', 0)
        hp_damage = max_hp * 0.30  # 30%最大生命值
        
        # 对目标造成伤害
        for target in targets:
            if target.is_alive:
                actions.extend([
                    LogAction(
                        caster=caster,
                        target=target,
                        message=f"[命定主位] {caster.name} 对 {target.name} 造成生命值伤害！"
                    ),
                    DamageAction(
                        caster=caster,
                        target=target,
                        damage_value=int(hp_damage),
                        damage_type=DamageType.PHYSICAL,
                        skill_name="命定主位"
                    )
                ])
        
        # 给自己添加嘲讽和免疫
        taunt_effect = TauntEffect(charges=2)
        immunity_effect = AttackImmunityEffect(charges=1)
        
        actions.extend([
            ApplyEffectAction(
                caster=caster,
                target=caster,
                effect=taunt_effect
            ),
            ApplyEffectAction(
                caster=caster,
                target=caster,
                effect=immunity_effect
            ),
            LogAction(
                caster=caster,
                target=caster,
                message=f"[命定主位] {caster.name} 获得嘲讽和免疫！"
            )
        ])
        
        return actions


class TianLiZhaoZhaoComponent:
    """天理昭昭的复杂组件 - 通灵-超杀技能"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from src.core.action import DamageAction, DamageType, LogAction
        
        actions = []
        
        # 基础伤害倍率更高（通灵-超杀技能）
        base_damage = getattr(caster, 'attack', 0) * 4.0  # 400%攻击力
        
        # 计算基于生命值的额外伤害
        max_hp = getattr(caster, 'max_hp', 0)
        hp_damage = max_hp * 0.40  # 40%最大生命值
        
        total_damage = base_damage + hp_damage
        
        for target in targets:
            if target.is_alive:
                actions.extend([
                    LogAction(
                        caster=caster,
                        target=target,
                        message=f"[天理昭昭] {caster.name} 释放通灵-超杀技能！"
                    ),
                    DamageAction(
                        caster=caster,
                        target=target,
                        damage_value=int(total_damage),
                        damage_type=DamageType.PHYSICAL,
                        skill_name="天理昭昭",
                        is_ultimate=True
                    )
                ])
        
        return actions


class MingYaYiJiComponent:
    """命压一技的复杂组件 - 通灵-普攻技能"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from src.core.action import DamageAction, DamageType, LogAction
        
        actions = []
        
        # 通灵-普攻的伤害倍率
        base_damage = getattr(caster, 'attack', 0) * 1.5  # 150%攻击力
        
        # 计算基于生命值的额外伤害
        max_hp = getattr(caster, 'max_hp', 0)
        hp_damage = max_hp * 0.20  # 20%最大生命值
        
        total_damage = base_damage + hp_damage
        
        for target in targets:
            if target.is_alive:
                actions.extend([
                    LogAction(
                        caster=caster,
                        target=target,
                        message=f"[命压一技] {caster.name} 释放通灵-普攻技能！"
                    ),
                    DamageAction(
                        caster=caster,
                        target=target,
                        damage_value=int(total_damage),
                        damage_type=DamageType.PHYSICAL,
                        skill_name="命压一技"
                    )
                ])
        
        return actions


# 导出所有组件类
__all__ = [
    'ShiXingZhiShouComponent',
    'MingDingZhuWeiComponent',
    'TianLiZhaoZhaoComponent',
    'MingYaYiJiComponent'
]
