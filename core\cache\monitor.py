#!/usr/bin/env python3
"""
缓存监控工具
提供缓存使用情况的实时监控和分析
"""

import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict

from . import cache_manager
from ..logging import get_logger

logger = get_logger("cache.monitor")


@dataclass
class CacheUsageStats:
    """缓存使用统计"""
    cache_name: str
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    avg_response_time: float = 0.0
    peak_size: int = 0
    current_size: int = 0
    hit_rate: float = 0.0
    last_updated: float = field(default_factory=time.time)
    
    def update_hit_rate(self):
        """更新命中率"""
        total = self.cache_hits + self.cache_misses
        self.hit_rate = self.cache_hits / total if total > 0 else 0.0
        self.last_updated = time.time()


class CacheMonitor:
    """缓存监控器"""
    
    def __init__(self):
        self.stats: Dict[str, CacheUsageStats] = {}
        self.start_time = time.time()
        self.monitoring_enabled = True
        
        # 性能阈值
        self.hit_rate_threshold = 0.7  # 命中率阈值
        self.response_time_threshold = 0.01  # 响应时间阈值(秒)
        
        # 初始化各缓存的统计
        self._initialize_cache_stats()
    
    def _initialize_cache_stats(self):
        """初始化缓存统计"""
        cache_names = [
            "attribute_cache",
            "damage_cache", 
            "skill_cache",
            "formation_cache"
        ]
        
        for name in cache_names:
            self.stats[name] = CacheUsageStats(cache_name=name)
    
    def record_cache_access(self, cache_name: str, hit: bool, response_time: float = 0.0):
        """记录缓存访问"""
        if not self.monitoring_enabled:
            return
            
        if cache_name not in self.stats:
            self.stats[cache_name] = CacheUsageStats(cache_name=cache_name)
        
        stat = self.stats[cache_name]
        stat.total_requests += 1
        
        if hit:
            stat.cache_hits += 1
        else:
            stat.cache_misses += 1
        
        # 更新平均响应时间
        if response_time > 0:
            stat.avg_response_time = (
                (stat.avg_response_time * (stat.total_requests - 1) + response_time) 
                / stat.total_requests
            )
        
        stat.update_hit_rate()
    
    def update_cache_size(self, cache_name: str, current_size: int):
        """更新缓存大小"""
        if cache_name not in self.stats:
            return
            
        stat = self.stats[cache_name]
        stat.current_size = current_size
        stat.peak_size = max(stat.peak_size, current_size)
    
    def get_overall_stats(self) -> Dict[str, Any]:
        """获取整体统计信息"""
        total_requests = sum(stat.total_requests for stat in self.stats.values())
        total_hits = sum(stat.cache_hits for stat in self.stats.values())
        total_misses = sum(stat.cache_misses for stat in self.stats.values())
        
        overall_hit_rate = total_hits / (total_hits + total_misses) if (total_hits + total_misses) > 0 else 0.0
        
        uptime = time.time() - self.start_time
        
        return {
            "uptime_seconds": uptime,
            "total_requests": total_requests,
            "total_hits": total_hits,
            "total_misses": total_misses,
            "overall_hit_rate": overall_hit_rate,
            "requests_per_second": total_requests / uptime if uptime > 0 else 0,
            "cache_count": len(self.stats),
            "monitoring_enabled": self.monitoring_enabled
        }
    
    def get_cache_stats(self, cache_name: Optional[str] = None) -> Dict[str, Any]:
        """获取特定缓存或所有缓存的统计"""
        if cache_name:
            if cache_name in self.stats:
                return self.stats[cache_name].__dict__
            return {}
        
        return {name: stat.__dict__ for name, stat in self.stats.items()}
    
    def get_performance_alerts(self) -> List[Dict[str, Any]]:
        """获取性能警告"""
        alerts = []
        
        for name, stat in self.stats.items():
            # 命中率过低警告
            if stat.hit_rate < self.hit_rate_threshold and stat.total_requests > 100:
                alerts.append({
                    "type": "low_hit_rate",
                    "cache_name": name,
                    "current_hit_rate": stat.hit_rate,
                    "threshold": self.hit_rate_threshold,
                    "message": f"缓存 {name} 命中率过低: {stat.hit_rate:.2%}"
                })
            
            # 响应时间过高警告
            if stat.avg_response_time > self.response_time_threshold:
                alerts.append({
                    "type": "high_response_time",
                    "cache_name": name,
                    "current_response_time": stat.avg_response_time,
                    "threshold": self.response_time_threshold,
                    "message": f"缓存 {name} 响应时间过高: {stat.avg_response_time:.4f}s"
                })
        
        return alerts
    
    def sync_with_cache_manager(self):
        """与缓存管理器同步统计信息"""
        try:
            manager_stats = cache_manager.get_stats()
            
            for cache_name, cache_data in manager_stats.items():
                if cache_name in self.stats:
                    stat = self.stats[cache_name]
                    
                    # 更新统计信息
                    stat.cache_hits = cache_data.get("hits", 0)
                    stat.cache_misses = cache_data.get("misses", 0)
                    stat.current_size = cache_data.get("size", 0)
                    stat.hit_rate = cache_data.get("hit_rate", 0.0)
                    stat.total_requests = stat.cache_hits + stat.cache_misses
                    stat.peak_size = max(stat.peak_size, stat.current_size)
                    stat.last_updated = time.time()
        
        except Exception as e:
            logger.warning(f"同步缓存统计失败: {e}")
    
    def generate_report(self) -> str:
        """生成缓存使用报告"""
        self.sync_with_cache_manager()
        
        overall = self.get_overall_stats()
        alerts = self.get_performance_alerts()
        
        report = []
        report.append("=" * 60)
        report.append("缓存系统使用报告")
        report.append("=" * 60)
        
        # 整体统计
        report.append(f"运行时间: {overall['uptime_seconds']:.1f}秒")
        report.append(f"总请求数: {overall['total_requests']}")
        report.append(f"整体命中率: {overall['overall_hit_rate']:.2%}")
        report.append(f"请求频率: {overall['requests_per_second']:.2f} req/s")
        report.append("")
        
        # 各缓存详情
        report.append("各缓存详细统计:")
        report.append("-" * 40)
        
        for name, stat in self.stats.items():
            report.append(f"📊 {name}:")
            report.append(f"  命中率: {stat.hit_rate:.2%} ({stat.cache_hits}/{stat.total_requests})")
            report.append(f"  当前大小: {stat.current_size} (峰值: {stat.peak_size})")
            if stat.avg_response_time > 0:
                report.append(f"  平均响应时间: {stat.avg_response_time:.4f}s")
            report.append("")
        
        # 性能警告
        if alerts:
            report.append("⚠️  性能警告:")
            report.append("-" * 40)
            for alert in alerts:
                report.append(f"  {alert['message']}")
            report.append("")
        
        # 优化建议
        report.append("💡 优化建议:")
        report.append("-" * 40)
        
        low_hit_caches = [name for name, stat in self.stats.items() 
                         if stat.hit_rate < self.hit_rate_threshold and stat.total_requests > 50]
        
        if low_hit_caches:
            report.append(f"  考虑优化以下缓存的键生成策略: {', '.join(low_hit_caches)}")
        
        unused_caches = [name for name, stat in self.stats.items() if stat.total_requests == 0]
        if unused_caches:
            report.append(f"  以下缓存未被使用: {', '.join(unused_caches)}")
        
        if overall['overall_hit_rate'] > 0.9:
            report.append("  ✅ 缓存系统运行良好!")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats.clear()
        self.start_time = time.time()
        self._initialize_cache_stats()
        logger.info("缓存监控统计已重置")


# 全局监控实例
cache_monitor = CacheMonitor()


def get_cache_monitor() -> CacheMonitor:
    """获取缓存监控实例"""
    return cache_monitor


__all__ = [
    'CacheUsageStats',
    'CacheMonitor', 
    'cache_monitor',
    'get_cache_monitor'
]