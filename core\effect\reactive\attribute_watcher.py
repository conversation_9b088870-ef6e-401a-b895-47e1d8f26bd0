"""
属性变化监控器

实现属性变化的实时监听和自动触发机制
"""
from __future__ import annotations
import time
import weakref
from typing import Dict, List, Any, Callable, Optional, Set, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict

from ...logging import get_logger

logger = get_logger("core.effect.reactive.attribute_watcher")


class ChangeType(Enum):
    """属性变化类型"""
    SET = "set"           # 直接设置
    INCREMENT = "increment"  # 增加
    DECREMENT = "decrement"  # 减少
    MULTIPLY = "multiply"    # 乘法
    DIVIDE = "divide"       # 除法


@dataclass
class AttributeChange:
    """属性变化记录"""
    attribute_name: str
    old_value: Any
    new_value: Any
    change_type: ChangeType
    timestamp: float
    source: Optional[Any] = None  # 变化来源（如效果、技能等）
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def delta(self) -> Union[int, float]:
        """计算变化量"""
        if isinstance(self.old_value, (int, float)) and isinstance(self.new_value, (int, float)):
            return self.new_value - self.old_value
        return 0


class AttributeCondition:
    """属性条件检查器"""
    
    def __init__(self, attribute_name: str, condition: Callable[[Any, Any], bool], description: str = ""):
        self.attribute_name = attribute_name
        self.condition = condition
        self.description = description
    
    def check(self, old_value: Any, new_value: Any) -> bool:
        """检查条件是否满足"""
        try:
            return self.condition(old_value, new_value)
        except Exception as e:
            logger.error(f"属性条件检查失败 {self.attribute_name}: {e}")
            return False


class AttributeWatcher:
    """属性变化监控器
    
    监控对象属性的变化，并在满足条件时触发回调函数
    """
    
    def __init__(self, owner: Any):
        self.owner = weakref.ref(owner) if owner else None
        self.listeners: Dict[str, List[Callable]] = defaultdict(list)
        self.conditions: Dict[str, List[AttributeCondition]] = defaultdict(list)
        self.change_history: List[AttributeChange] = []
        self.max_history_size = 100
        self.enabled = True
        self.batch_mode = False
        self.pending_changes: List[AttributeChange] = []
        
        # 性能统计
        self.stats = {
            "total_changes": 0,
            "total_triggers": 0,
            "last_reset": time.time()
        }
    
    def watch_attribute(self, attribute_name: str, callback: Callable[[AttributeChange], None]):
        """监听属性变化
        
        Args:
            attribute_name: 属性名称
            callback: 变化时的回调函数
        """
        self.listeners[attribute_name].append(callback)
        logger.debug(f"添加属性监听器: {attribute_name}")
    
    def add_condition(self, condition: AttributeCondition, callback: Callable[[AttributeChange], None]):
        """添加条件触发器
        
        Args:
            condition: 触发条件
            callback: 满足条件时的回调函数
        """
        self.conditions[condition.attribute_name].append((condition, callback))
        logger.debug(f"添加条件触发器: {condition.attribute_name} - {condition.description}")
    
    def notify_change(self, attribute_name: str, old_value: Any, new_value: Any, 
                     change_type: ChangeType = ChangeType.SET, source: Any = None, **metadata):
        """通知属性变化
        
        Args:
            attribute_name: 属性名称
            old_value: 旧值
            new_value: 新值
            change_type: 变化类型
            source: 变化来源
            **metadata: 额外元数据
        """
        if not self.enabled:
            return
        
        # 创建变化记录
        change = AttributeChange(
            attribute_name=attribute_name,
            old_value=old_value,
            new_value=new_value,
            change_type=change_type,
            timestamp=time.time(),
            source=source,
            metadata=metadata
        )
        
        # 记录变化历史
        self._record_change(change)
        
        if self.batch_mode:
            # 批量模式：暂存变化
            self.pending_changes.append(change)
        else:
            # 立即模式：直接处理
            self._process_change(change)
    
    def _record_change(self, change: AttributeChange):
        """记录变化历史"""
        self.change_history.append(change)
        self.stats["total_changes"] += 1
        
        # 限制历史记录大小
        if len(self.change_history) > self.max_history_size:
            self.change_history.pop(0)
    
    def _process_change(self, change: AttributeChange):
        """处理属性变化"""
        try:
            # 触发普通监听器
            for callback in self.listeners[change.attribute_name]:
                try:
                    callback(change)
                    self.stats["total_triggers"] += 1
                except Exception as e:
                    logger.error(f"属性监听器回调失败 {change.attribute_name}: {e}")
            
            # 检查条件触发器
            for condition, callback in self.conditions[change.attribute_name]:
                if condition.check(change.old_value, change.new_value):
                    try:
                        callback(change)
                        self.stats["total_triggers"] += 1
                        logger.debug(f"条件触发器激活: {condition.description}")
                    except Exception as e:
                        logger.error(f"条件触发器回调失败 {condition.description}: {e}")
        
        except Exception as e:
            logger.error(f"处理属性变化失败 {change.attribute_name}: {e}")
    
    def start_batch(self):
        """开始批量模式"""
        self.batch_mode = True
        self.pending_changes.clear()
    
    def commit_batch(self):
        """提交批量变化"""
        if not self.batch_mode:
            return
        
        # 处理所有待处理的变化
        for change in self.pending_changes:
            self._process_change(change)
        
        self.pending_changes.clear()
        self.batch_mode = False
    
    def get_recent_changes(self, attribute_name: str = None, limit: int = 10) -> List[AttributeChange]:
        """获取最近的变化记录"""
        changes = self.change_history
        if attribute_name:
            changes = [c for c in changes if c.attribute_name == attribute_name]
        return changes[-limit:] if changes else []
    
    def clear_history(self):
        """清空变化历史"""
        self.change_history.clear()
        logger.debug("清空属性变化历史")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "listeners_count": sum(len(listeners) for listeners in self.listeners.values()),
            "conditions_count": sum(len(conditions) for conditions in self.conditions.values()),
            "history_size": len(self.change_history),
            "pending_changes": len(self.pending_changes) if self.batch_mode else 0
        }


# 便捷的条件创建函数
def hp_below_threshold(threshold: float) -> AttributeCondition:
    """HP低于阈值条件"""
    return AttributeCondition(
        "hp",
        lambda old, new: new < threshold <= old,
        f"HP低于{threshold}"
    )

def hp_above_threshold(threshold: float) -> AttributeCondition:
    """HP高于阈值条件"""
    return AttributeCondition(
        "hp",
        lambda old, new: new > threshold >= old,
        f"HP高于{threshold}"
    )

def attribute_increased(attribute_name: str) -> AttributeCondition:
    """属性增加条件"""
    return AttributeCondition(
        attribute_name,
        lambda old, new: isinstance(old, (int, float)) and isinstance(new, (int, float)) and new > old,
        f"{attribute_name}增加"
    )

def attribute_decreased(attribute_name: str) -> AttributeCondition:
    """属性减少条件"""
    return AttributeCondition(
        attribute_name,
        lambda old, new: isinstance(old, (int, float)) and isinstance(new, (int, float)) and new < old,
        f"{attribute_name}减少"
    )
