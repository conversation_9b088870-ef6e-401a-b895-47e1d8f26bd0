"""
示例扩展

演示如何使用AI行动生成系统的扩展接口来添加自定义的条件检查器、效果计算器和行动策略。

这些示例展示了扩展系统的强大功能和灵活性。
"""

from __future__ import annotations
from typing import Dict, Any, List, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill
    from core.ai.condition_evaluator import AttackConditionResult
    from core.ai.effect_calculator import ConditionalEffectResult

from core.ai.extensions import (
    register_condition_checker,
    register_effect_calculator,
    register_action_strategy,
    IActionStrategy,
    ExtensionInfo,
    ExtensionType
)
from core.status import battle_status_checker
from core.logging import get_logger

logger = get_logger("ai.examples")

# === 示例条件检查器 ===

@register_condition_checker(
    name="weather_condition_checker",
    version="1.0.0",
    author="AI System",
    description="检查战场天气条件对战斗的影响",
    priority=30
)
class WeatherConditionChecker:
    """天气条件检查器示例"""
    
    def check_condition(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> Dict[str, Any]:
        """检查天气条件"""
        
        conditions = {}
        
        # 模拟天气系统
        weather = getattr(battle_state, 'weather', 'sunny')
        
        conditions['weather_type'] = weather
        conditions['weather_bonus_fire'] = weather == 'sunny'  # 晴天火系加成
        conditions['weather_bonus_water'] = weather == 'rainy'  # 雨天水系加成
        conditions['weather_penalty_fire'] = weather == 'rainy'  # 雨天火系减益
        
        # 回合数影响
        round_num = getattr(battle_state, 'round_num', 0)
        conditions['late_battle'] = round_num >= 10
        conditions['early_battle'] = round_num <= 3
        
        return conditions

@register_condition_checker(
    name="formation_condition_checker", 
    version="1.0.0",
    author="AI System",
    description="检查阵型相关的战斗条件",
    priority=25
)
class FormationConditionChecker:
    """阵型条件检查器示例"""
    
    def check_condition(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> Dict[str, Any]:
        """检查阵型条件"""
        
        conditions = {}
        
        try:
            # 检查攻击者位置
            attacker_pos = getattr(attacker, 'position', (1, 1))
            target_pos = getattr(target, 'position', (1, 1))
            
            conditions['attacker_front_row'] = attacker_pos[0] == 1  # 前排
            conditions['attacker_back_row'] = attacker_pos[0] == 3   # 后排
            conditions['target_front_row'] = target_pos[0] == 1
            conditions['target_back_row'] = target_pos[0] == 3
            
            # 计算距离
            distance = abs(attacker_pos[0] - target_pos[0]) + abs(attacker_pos[1] - target_pos[1])
            conditions['attack_distance'] = distance
            conditions['close_range'] = distance <= 2
            conditions['long_range'] = distance >= 4
            
            # 检查侧翼攻击
            conditions['flanking_attack'] = (
                attacker_pos[1] != target_pos[1] and 
                abs(attacker_pos[1] - target_pos[1]) >= 2
            )
            
        except Exception as e:
            logger.warning(f"检查阵型条件时出错: {e}")
        
        return conditions

# === 示例效果计算器 ===

@register_effect_calculator(
    name="elemental_effect_calculator",
    version="1.0.0", 
    author="AI System",
    description="计算元素相克和天气加成效果",
    priority=20
)
class ElementalEffectCalculator:
    """元素效果计算器示例"""
    
    def calculate_effects(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> 'ConditionalEffectResult':
        """计算元素相关效果"""
        from core.ai.effect_calculator import ConditionalEffectResult
        
        result = ConditionalEffectResult()
        
        try:
            # 获取元素信息
            attacker_element = self._get_element(attacker)
            target_element = self._get_element(target)
            
            # 元素相克计算
            if self._is_element_advantage(attacker_element, target_element):
                result.add_effect('elemental_advantage_damage', 0.25, {
                    'source': 'elemental_advantage',
                    'description': f'{attacker_element}克制{target_element}，伤害+25%'
                })
                result.add_trigger_event('elemental_advantage_triggered')
            
            elif self._is_element_disadvantage(attacker_element, target_element):
                result.add_effect('elemental_disadvantage_damage', -0.15, {
                    'source': 'elemental_disadvantage', 
                    'description': f'{attacker_element}被{target_element}克制，伤害-15%'
                })
            
            # 天气加成
            if conditions.get_condition('weather_bonus_fire', False) and attacker_element == 'fire':
                result.add_effect('weather_fire_bonus', 0.2, {
                    'source': 'weather',
                    'description': '晴天火系加成+20%'
                })
            
            if conditions.get_condition('weather_bonus_water', False) and attacker_element == 'water':
                result.add_effect('weather_water_bonus', 0.2, {
                    'source': 'weather',
                    'description': '雨天水系加成+20%'
                })
            
        except Exception as e:
            logger.warning(f"计算元素效果时出错: {e}")
        
        return result
    
    def _get_element(self, entity: 'IBattleEntity') -> str:
        """获取实体的元素属性"""
        try:
            if hasattr(entity, 'metadata') and hasattr(entity.metadata, 'element'):
                return entity.metadata.element.name.lower()
            return 'neutral'
        except:
            return 'neutral'
    
    def _is_element_advantage(self, attacker_element: str, target_element: str) -> bool:
        """检查元素优势"""
        advantages = {
            'fire': ['ice', 'nature'],
            'water': ['fire', 'earth'],
            'earth': ['air', 'lightning'],
            'air': ['water', 'ice'],
            'ice': ['nature', 'earth'],
            'nature': ['water', 'air'],
            'lightning': ['water', 'air']
        }
        
        return target_element in advantages.get(attacker_element, [])
    
    def _is_element_disadvantage(self, attacker_element: str, target_element: str) -> bool:
        """检查元素劣势"""
        return self._is_element_advantage(target_element, attacker_element)

@register_effect_calculator(
    name="formation_effect_calculator",
    version="1.0.0",
    author="AI System", 
    description="计算阵型相关的战术效果",
    priority=25
)
class FormationEffectCalculator:
    """阵型效果计算器示例"""
    
    def calculate_effects(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> 'ConditionalEffectResult':
        """计算阵型相关效果"""
        from core.ai.effect_calculator import ConditionalEffectResult
        
        result = ConditionalEffectResult()
        
        try:
            # 侧翼攻击加成
            if conditions.get_condition('flanking_attack', False):
                result.add_effect('flanking_damage_bonus', 0.15, {
                    'source': 'formation',
                    'description': '侧翼攻击+15%伤害'
                })
                result.add_trigger_event('flanking_attack_triggered')
            
            # 远程攻击精度
            if conditions.get_condition('long_range', False):
                # 远程攻击命中率降低，但如果命中则伤害提升
                result.add_effect('long_range_damage_bonus', 0.1, {
                    'source': 'formation',
                    'description': '远程攻击+10%伤害'
                })
            
            # 近战冲锋
            if conditions.get_condition('close_range', False):
                result.add_effect('close_combat_crit_bonus', 0.1, {
                    'source': 'formation',
                    'description': '近战+10%暴击率'
                })
            
            # 后排保护
            if conditions.get_condition('attacker_back_row', False):
                result.add_effect('back_row_safety_bonus', 0.05, {
                    'source': 'formation',
                    'description': '后排位置+5%闪避率'
                })
            
        except Exception as e:
            logger.warning(f"计算阵型效果时出错: {e}")
        
        return result

# === 示例行动策略 ===

class BerserkStrategy(IActionStrategy):
    """狂暴策略示例 - 低血量时优先使用高伤害技能"""
    
    def get_extension_info(self):
        return ExtensionInfo(
            name="berserk_strategy",
            version="1.0.0",
            author="AI System",
            description="低血量时的狂暴战斗策略",
            extension_type=ExtensionType.ACTION_STRATEGY,
            priority=10
        )
    
    def should_apply(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> bool:
        """判断是否应该使用狂暴策略"""
        try:
            if hasattr(spirit, 'current_hp') and hasattr(spirit, 'max_hp'):
                hp_percentage = spirit.current_hp / spirit.max_hp
                return hp_percentage <= 0.3  # 血量低于30%时使用
            return False
        except:
            return False
    
    def generate_actions(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> List[Any]:
        """生成狂暴策略的行动"""
        from core.action import LogAction, AttackAction
        
        actions = []
        
        try:
            # 记录策略激活
            actions.append(LogAction(
                caster=spirit,
                message=f"🔥 {getattr(spirit, 'name', 'Unknown')} 进入狂暴状态！优先使用高伤害技能！"
            ))
            
            # 选择最高伤害的技能
            best_skill = self._select_highest_damage_skill(spirit)
            if best_skill:
                # 选择血量最低的敌人
                target = self._select_lowest_hp_enemy(spirit, battle_state)
                if target:
                    actions.append(AttackAction(
                        caster=spirit,
                        target=target,
                        skill=best_skill
                    ))
            
        except Exception as e:
            logger.error(f"狂暴策略执行失败: {e}")
        
        return actions
    
    def _select_highest_damage_skill(self, spirit: 'IBattleEntity'):
        """选择最高伤害的技能"""
        try:
            if hasattr(spirit, 'components'):
                from core.components import SkillComponent
                skill_component = spirit.components.get_component(SkillComponent)
                
                if skill_component and skill_component.skills:
                    # 简单选择第一个技能作为示例
                    return skill_component.skills[0]
        except:
            pass
        return None
    
    def _select_lowest_hp_enemy(self, spirit: 'IBattleEntity', battle_state: 'IBattleState'):
        """选择血量最低的敌人"""
        try:
            enemy_team = 1 - getattr(spirit, 'team', 0)
            enemies = battle_state.get_living_spirits(enemy_team)
            
            if enemies:
                # 选择血量最低的敌人
                return min(enemies, key=lambda e: getattr(e, 'current_hp', float('inf')))
        except:
            pass
        return None

# 手动注册狂暴策略（因为它不使用装饰器）
from core.ai.extensions import get_strategy_registry
_berserk_strategy = BerserkStrategy()
get_strategy_registry().register(_berserk_strategy)

# === 使用示例 ===

def demonstrate_extensions():
    """演示扩展系统的使用"""
    from core.ai.extensions import ExtensionManager
    
    logger.info("=== AI扩展系统演示 ===")
    
    # 列出所有扩展
    all_extensions = ExtensionManager.list_all_extensions()
    
    logger.info("已注册的扩展:")
    for category, extensions in all_extensions.items():
        logger.info(f"  {category}:")
        for name, info in extensions.items():
            logger.info(f"    - {name} v{info.version} by {info.author}")
            logger.info(f"      {info.description}")
    
    # 显示统计信息
    stats = ExtensionManager.get_extension_stats()
    logger.info(f"扩展统计: {stats}")

if __name__ == "__main__":
    demonstrate_extensions()
