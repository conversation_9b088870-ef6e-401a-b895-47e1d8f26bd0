#!/usr/bin/env python3
"""
增强版战斗系统UI

包含详细的精灵状态显示、效果管理、回合历史记录等功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ui.ux.models.battle_record import BattleRecorder, RoundSnapshot, SpiritSnapshot, ActionRecord
from ui.ux.components.spirit_detail_panel import SpiritDetailPanel
from ui.ux.components.round_history_panel import RoundHistoryPanel


class EnhancedBattleUI:
    """增强版战斗UI"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("奥奇传说AI战斗系统 - 增强版")
        self.root.geometry("1600x1000")
        
        # 战斗相关对象
        self.engine = None
        self.spirit_service = None
        self.available_spirits = []
        
        # 记录系统
        self.battle_recorder = BattleRecorder()
        
        # 当前选择的精灵
        self.selected_spirit_name = None
        
        # 创建界面
        self.create_widgets()
        
        # 自动初始化系统
        self.root.after(100, self.initialize_system)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部控制面板
        self.create_control_panel(main_frame)
        
        # 主内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 创建主要的分割面板
        main_paned = ttk.PanedWindow(content_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 战斗状态和精灵列表
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # 中间面板 - 精灵详情
        middle_frame = ttk.Frame(main_paned)
        main_paned.add(middle_frame, weight=1)
        
        # 右侧面板 - 回合历史
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        # 创建各个面板内容
        self.create_left_panel(left_frame)
        self.create_middle_panel(middle_frame)
        self.create_right_panel(right_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="战斗控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行 - 精灵选择和基本配置
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        # 精灵选择
        ttk.Label(row1, text="精灵1:").grid(row=0, column=0, sticky=tk.W)
        self.spirit1_var = tk.StringVar()
        self.spirit1_combo = ttk.Combobox(row1, textvariable=self.spirit1_var, width=20)
        self.spirit1_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(row1, text="精灵2:").grid(row=0, column=2, sticky=tk.W)
        self.spirit2_var = tk.StringVar()
        self.spirit2_combo = ttk.Combobox(row1, textvariable=self.spirit2_var, width=20)
        self.spirit2_combo.grid(row=0, column=3, padx=(5, 10))
        
        # 配置参数
        ttk.Label(row1, text="回合限制:").grid(row=0, column=4, sticky=tk.W)
        self.round_limit_var = tk.StringVar(value="10")
        ttk.Entry(row1, textvariable=self.round_limit_var, width=8).grid(row=0, column=5, padx=(5, 10))
        
        ttk.Label(row1, text="顺位加气:").grid(row=0, column=6, sticky=tk.W)
        self.bonus_energy_var = tk.StringVar(value="50")
        ttk.Entry(row1, textvariable=self.bonus_energy_var, width=8).grid(row=0, column=7, padx=(5, 0))
        
        # 第二行 - 控制按钮和状态
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=(5, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(row2)
        button_frame.pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="创建战斗", command=self.create_battle).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="执行一回合", command=self.execute_round).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="下一只精灵", command=self.execute_next_spirit).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="自动战斗", command=self.auto_battle).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置", command=self.reset_battle).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出记录", command=self.export_records).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态显示
        status_frame = ttk.LabelFrame(row2, text="状态", padding=5)
        status_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="系统未初始化", foreground="red")
        self.status_label.pack()
    
    def create_left_panel(self, parent):
        """创建左侧面板"""
        # 战斗概览
        overview_frame = ttk.LabelFrame(parent, text="战斗概览", padding=10)
        overview_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.overview_text = tk.Text(overview_frame, height=8, wrap=tk.WORD)
        overview_scroll = ttk.Scrollbar(overview_frame, orient="vertical", command=self.overview_text.yview)
        self.overview_text.configure(yscrollcommand=overview_scroll.set)
        self.overview_text.pack(side="left", fill="both", expand=True)
        overview_scroll.pack(side="right", fill="y")
        
        # 精灵列表
        spirits_frame = ttk.LabelFrame(parent, text="精灵列表", padding=10)
        spirits_frame.pack(fill=tk.BOTH, expand=True)
        
        # 精灵列表树形控件
        columns = ('name', 'team', 'hp', 'energy', 'status', 'effects')
        self.spirits_tree = ttk.Treeview(spirits_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        self.spirits_tree.heading('name', text='名称')
        self.spirits_tree.heading('team', text='队伍')
        self.spirits_tree.heading('hp', text='生命值')
        self.spirits_tree.heading('energy', text='气势')
        self.spirits_tree.heading('status', text='状态')
        self.spirits_tree.heading('effects', text='效果数')
        
        # 设置列宽
        self.spirits_tree.column('name', width=150)
        self.spirits_tree.column('team', width=50)
        self.spirits_tree.column('hp', width=100)
        self.spirits_tree.column('energy', width=80)
        self.spirits_tree.column('status', width=60)
        self.spirits_tree.column('effects', width=60)
        
        # 绑定选择事件
        self.spirits_tree.bind('<<TreeviewSelect>>', self.on_spirit_selected)
        
        # 滚动条
        spirits_scroll = ttk.Scrollbar(spirits_frame, orient="vertical", command=self.spirits_tree.yview)
        self.spirits_tree.configure(yscrollcommand=spirits_scroll.set)
        
        self.spirits_tree.pack(side="left", fill="both", expand=True)
        spirits_scroll.pack(side="right", fill="y")
    
    def create_middle_panel(self, parent):
        """创建中间面板"""
        # 🔧 添加战斗信息面板
        battle_info_frame = ttk.LabelFrame(parent, text="战斗信息", padding=10)
        battle_info_frame.pack(fill=tk.X, pady=(0, 10))

        # 当前行动信息
        current_action_frame = ttk.Frame(battle_info_frame)
        current_action_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(current_action_frame, text="当前行动:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.current_action_label = ttk.Label(current_action_frame, text="等待开始...", foreground="blue")
        self.current_action_label.pack(side=tk.LEFT, padx=(5, 0))

        # 最近伤害信息
        damage_info_frame = ttk.Frame(battle_info_frame)
        damage_info_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(damage_info_frame, text="伤害信息:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.damage_info_label = ttk.Label(damage_info_frame, text="暂无", foreground="red")
        self.damage_info_label.pack(side=tk.LEFT, padx=(5, 0))

        # 技能信息
        skill_info_frame = ttk.Frame(battle_info_frame)
        skill_info_frame.pack(fill=tk.X)

        ttk.Label(skill_info_frame, text="技能信息:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.skill_info_label = ttk.Label(skill_info_frame, text="暂无", foreground="green")
        self.skill_info_label.pack(side=tk.LEFT, padx=(5, 0))

        # 精灵详情面板
        detail_frame = ttk.LabelFrame(parent, text="精灵详情", padding=10)
        detail_frame.pack(fill=tk.BOTH, expand=True)

        self.spirit_detail_panel = SpiritDetailPanel(detail_frame)
        self.spirit_detail_panel.pack(fill=tk.BOTH, expand=True)
    
    def create_right_panel(self, parent):
        """创建右侧面板"""
        # 回合历史面板
        history_frame = ttk.LabelFrame(parent, text="回合历史", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.round_history_panel = RoundHistoryPanel(history_frame, self.on_round_selected)
        self.round_history_panel.pack(fill=tk.BOTH, expand=True)
        
        # 设置记录器
        self.round_history_panel.set_recorder(self.battle_recorder)
    
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def update_status(self, message, color="black"):
        """更新状态显示"""
        self.status_label.config(text=message, foreground=color)
        self.log(f"状态更新: {message}")
    
    def initialize_system(self):
        """初始化系统"""
        try:
            self.log("开始初始化核心系统...")
            self.update_status("初始化系统中...", "orange")
            
            # 初始化核心系统
            from core.system_manager import initialize_core_systems
            initialize_core_systems()
            
            # 获取精灵服务
            from core.spirit.spirit_service import get_spirit_service
            self.spirit_service = get_spirit_service()
            self.available_spirits = self.spirit_service.list_available_spirits()
            
            # 更新精灵选择列表
            self.spirit1_combo['values'] = self.available_spirits
            self.spirit2_combo['values'] = self.available_spirits
            
            # 设置默认选择
            if len(self.available_spirits) >= 2:
                self.spirit1_var.set(self.available_spirits[0])
                self.spirit2_var.set(self.available_spirits[1])
            
            self.log(f"系统初始化完成，发现 {len(self.available_spirits)} 个精灵")
            self.update_status(f"系统就绪 - {len(self.available_spirits)} 个精灵可用", "green")
            
        except Exception as e:
            self.log(f"系统初始化失败: {e}", "ERROR")
            self.update_status(f"初始化失败: {e}", "red")
            messagebox.showerror("错误", f"系统初始化失败: {e}")
    
    def create_battle(self):
        """创建战斗"""
        try:
            if not self.spirit_service:
                messagebox.showerror("错误", "系统未初始化")
                return
            
            spirit1_name = self.spirit1_var.get()
            spirit2_name = self.spirit2_var.get()
            
            if not spirit1_name or not spirit2_name:
                messagebox.showerror("错误", "请选择两个精灵")
                return
            
            self.log(f"创建战斗: {spirit1_name} vs {spirit2_name}")
            self.update_status("创建战斗中...", "orange")
            
            # 创建阵型和精灵
            from core.formation import Formation
            
            formation1 = Formation()
            formation2 = Formation()
            
            spirit1 = self.spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
            spirit2 = self.spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
            
            if not spirit1 or not spirit2:
                raise Exception("精灵创建失败")
            
            formation1.add_spirit(spirit1, 1, 1)
            formation2.add_spirit(spirit2, 3, 1)
            
            # 创建战斗引擎
            from core.battle.engines.factory import create_battle_engine
            
            self.engine = create_battle_engine(
                formation1,
                formation2,
                round_limit=int(self.round_limit_var.get()),
                turn_order_bonus_energy=int(self.bonus_energy_var.get())
            )

            # 🔧 修复超杀阈值：为演示精灵设置超杀气势
            for spirit in formation1.get_all_spirits() + formation2.get_all_spirits():
                if hasattr(spirit, 'components') and spirit.name == "天恩圣祭·空灵圣龙":
                    from core.components import EnergyComponent
                    energy_component = spirit.components.get_component(EnergyComponent)
                    if energy_component:
                        # 设置超杀气势以便演示
                        energy_component._current_energy = 300
                        self.log(f"🔥 为 {spirit.name} 设置超杀气势: 300")
            
            # 重置记录器
            self.battle_recorder = BattleRecorder()
            self.round_history_panel.set_recorder(self.battle_recorder)
            
            # 创建初始快照
            # 确保战斗状态能访问到引擎的统计数据
            if not hasattr(self.engine.battle_state, 'engine'):
                self.engine.battle_state.engine = self.engine

            initial_snapshot = self.battle_recorder.create_snapshot(self.engine.battle_state, 0)
            self.battle_recorder.add_snapshot(initial_snapshot)
            self.round_history_panel.update_round_list()
            
            self.log("战斗创建成功")
            self.update_status("战斗已创建", "green")
            
            # 更新所有显示
            self.update_all_displays()
            
        except Exception as e:
            self.log(f"创建战斗失败: {e}", "ERROR")
            self.update_status(f"创建失败: {e}", "red")
            messagebox.showerror("错误", f"创建战斗失败: {e}")
    
    def execute_round(self):
        """执行一回合"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return

        try:
            round_num = self.engine.battle_state.round_num + 1
            self.log(f"执行第 {round_num} 回合")
            self.update_status(f"执行第 {round_num} 回合", "orange")

            # 执行回合
            result = self.engine.execute_round()
            self.log("回合执行完成，开始创建快照...")

            # 创建回合快照
            try:
                # 确保战斗状态能访问到引擎的统计数据
                if not hasattr(self.engine.battle_state, 'engine'):
                    self.engine.battle_state.engine = self.engine

                snapshot = self.battle_recorder.create_snapshot(self.engine.battle_state, round_num)
                self.log("快照创建完成，添加到历史记录...")
                self.round_history_panel.add_round(snapshot)
                self.log("历史记录添加完成")
            except Exception as snapshot_error:
                self.log(f"创建快照失败: {snapshot_error}", "ERROR")
                import traceback
                traceback.print_exc()

            # 更新显示
            try:
                self.log("开始更新显示...")
                self.update_all_displays()
                self.log("显示更新完成")
            except Exception as display_error:
                self.log(f"更新显示失败: {display_error}", "ERROR")
                import traceback
                traceback.print_exc()

            # 检查战斗结果
            if result.get("type") == "battle_end":
                winner = result.get("winner")
                if winner == -1:
                    self.log("战斗结束！平局")
                    self.update_status("战斗结束 - 平局", "blue")
                    messagebox.showinfo("战斗结束", "战斗结束！\n结果: 平局")
                else:
                    self.log(f"战斗结束！获胜方: 队伍{winner}")
                    self.update_status(f"战斗结束 - 队伍{winner}获胜", "blue")
                    messagebox.showinfo("战斗结束", f"战斗结束！\n获胜方: 队伍{winner}")
            else:
                self.log(f"第 {round_num} 回合执行完成")
                self.update_status(f"第 {round_num} 回合完成", "green")

        except Exception as e:
            self.log(f"回合执行失败: {e}", "ERROR")
            self.update_status(f"执行失败: {e}", "red")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"回合执行失败: {e}")

    def execute_next_spirit(self):
        """执行下一只精灵的回合"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return

        try:
            # 调用战斗引擎的单精灵回合执行方法
            result = self.engine.execute_next_spirit_turn()

            result_type = result.get("type", "unknown")
            message = result.get("message", "")

            if result_type == "spirit_turn":
                # 精灵成功执行回合
                spirit_name = result.get("spirit_name", "Unknown")
                actions_count = result.get("actions_generated", 0)
                current_index = result.get("current_index", 0)
                total_spirits = result.get("total_spirits", 0)

                # 获取详细的状态变化信息
                spirit_state = result.get("spirit_state", {})
                hp_change = spirit_state.get("hp_change", 0)
                energy_change = spirit_state.get("energy_change", 0)

                # 显示详细的动作结果
                status_parts = [f"{spirit_name} 行动完成 ({current_index + 1}/{total_spirits})"]
                if hp_change != 0:
                    status_parts.append(f"HP{hp_change:+.0f}")
                if energy_change != 0:
                    status_parts.append(f"气势{energy_change:+}")

                status_text = " ".join(status_parts)
                self.log(f"✅ {spirit_name} 执行回合完成，生成 {actions_count} 个动作 (HP{hp_change:+.0f}, 气势{energy_change:+})")
                self.update_status(status_text, "green")

                # 🔧 更新战斗信息显示
                damage_info = result.get("damage_info", {})
                self._update_battle_info(spirit_name, actions_count, hp_change, energy_change, result, damage_info)

                # 🔧 动态更新：立即更新显示
                self.update_all_displays()

                # 🔧 动态更新：使用战斗引擎提供的实时状态快照
                battle_snapshot = result.get("battle_state_snapshot", {})
                if battle_snapshot:
                    self._update_spirits_display_from_snapshot(battle_snapshot)

                # 创建实时快照以显示当前状态
                try:
                    if not hasattr(self.engine.battle_state, 'engine'):
                        self.engine.battle_state.engine = self.engine

                    # 创建当前状态的临时快照
                    current_round = self.engine.battle_state.round_num
                    temp_snapshot = self.battle_recorder.create_snapshot(self.engine.battle_state, current_round)

                    # 记录实时统计信息
                    self.log(f"📊 实时统计 - 队伍0伤害: {temp_snapshot.total_damage_dealt.get(0, 0):.0f}, 队伍1伤害: {temp_snapshot.total_damage_dealt.get(1, 0):.0f}")

                except Exception as snapshot_error:
                    self.log(f"创建实时快照失败: {snapshot_error}", "ERROR")

            elif result_type == "spirit_skipped":
                # 精灵被跳过
                spirit_name = result.get("spirit_name", "Unknown")
                reason = result.get("reason", "")

                self.log(f"⏭️ {spirit_name} 被跳过: {reason}")
                self.update_status(f"{spirit_name} 被跳过", "orange")

                # 继续执行下一只精灵
                self.root.after(500, self.execute_next_spirit)

            elif result_type == "round_end":
                # 回合结束
                round_num = result.get("round_num", 0)

                self.log(f"🔄 第 {round_num} 回合结束")
                self.update_status(f"第 {round_num} 回合结束", "blue")

                # 创建回合快照
                try:
                    # 确保战斗状态能访问到引擎的统计数据
                    if not hasattr(self.engine.battle_state, 'engine'):
                        self.engine.battle_state.engine = self.engine

                    snapshot = self.battle_recorder.create_snapshot(self.engine.battle_state, round_num)
                    self.round_history_panel.add_round(snapshot)
                except Exception as snapshot_error:
                    self.log(f"创建快照失败: {snapshot_error}", "ERROR")

                # 更新显示
                self.update_all_displays()

                # 🔧 修复：回合结束后继续执行下一只精灵，而不是停止
                self.log(f"📋 准备开始下一回合...")
                self.root.after(1000, self.execute_next_spirit)  # 1秒后继续执行下一只精灵

            elif result_type == "battle_end":
                # 战斗结束
                winner = result.get("winner", -1)
                round_num = result.get("round_num", 0)

                if winner == -1:
                    self.log("🏁 战斗结束！平局")
                    self.update_status("战斗结束 - 平局", "blue")
                    messagebox.showinfo("战斗结束", "战斗结束！\n结果: 平局")
                else:
                    self.log(f"🏁 战斗结束！获胜方: 队伍{winner}")
                    self.update_status(f"战斗结束 - 队伍{winner}获胜", "blue")
                    messagebox.showinfo("战斗结束", f"战斗结束！\n获胜方: 队伍{winner}")

                # 创建最终快照
                try:
                    # 确保战斗状态能访问到引擎的统计数据
                    if not hasattr(self.engine.battle_state, 'engine'):
                        self.engine.battle_state.engine = self.engine

                    snapshot = self.battle_recorder.create_snapshot(self.engine.battle_state, round_num)
                    self.round_history_panel.add_round(snapshot)
                except Exception as snapshot_error:
                    self.log(f"创建最终快照失败: {snapshot_error}", "ERROR")

                # 更新显示
                self.update_all_displays()

            elif result_type == "no_spirits":
                # 没有精灵可以行动
                self.log("⚠️ 没有存活的精灵可以行动")
                self.update_status("没有精灵可以行动", "orange")

            elif result_type == "error":
                # 执行错误
                self.log(f"❌ 执行精灵回合失败: {message}", "ERROR")
                self.update_status(f"执行失败: {message}", "red")
                messagebox.showerror("错误", f"执行精灵回合失败: {message}")

            else:
                # 未知结果类型
                self.log(f"⚠️ 未知结果类型: {result_type}, 消息: {message}")
                self.update_status(f"未知结果: {message}", "orange")

        except Exception as e:
            self.log(f"执行精灵回合失败: {e}", "ERROR")
            self.update_status(f"执行失败: {e}", "red")
            messagebox.showerror("错误", f"执行精灵回合失败: {e}")

    def auto_battle(self):
        """自动战斗"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return
        
        def run_battle():
            try:
                max_rounds = int(self.round_limit_var.get())
                
                while (self.engine.battle_state.winner is None and 
                       self.engine.battle_state.round_num < max_rounds):
                    
                    # 在主线程中执行
                    self.root.after(0, self.execute_round)
                    
                    # 延迟观察
                    import time
                    time.sleep(1)
                
                self.root.after(0, lambda: self.log("自动战斗完成"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log(f"自动战斗失败: {e}", "ERROR"))
        
        import threading
        threading.Thread(target=run_battle, daemon=True).start()
    
    def reset_battle(self):
        """重置战斗"""
        self.engine = None
        self.selected_spirit_name = None
        
        # 重置记录器
        self.battle_recorder = BattleRecorder()
        self.round_history_panel.set_recorder(self.battle_recorder)
        
        # 清空显示
        self.overview_text.delete(1.0, tk.END)
        
        # 清空精灵列表
        for item in self.spirits_tree.get_children():
            self.spirits_tree.delete(item)
        
        # 清空精灵详情
        self.spirit_detail_panel.clear()
        
        # 清空历史记录
        self.round_history_panel.clear()
        
        self.log("战斗已重置")
        self.update_status("已重置", "orange")

    def export_records(self):
        """导出战斗记录"""
        if not self.battle_recorder.snapshots:
            messagebox.showwarning("警告", "没有战斗记录可导出")
            return

        try:
            from tkinter import filedialog
            import json

            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="保存战斗记录"
            )

            if filename:
                summary = self.battle_recorder.export_summary()

                export_data = {
                    'summary': summary,
                    'snapshots': []
                }

                # 导出所有快照
                for snapshot in self.battle_recorder.snapshots:
                    snapshot_data = {
                        'round_num': snapshot.round_num,
                        'timestamp': snapshot.timestamp.isoformat(),
                        'winner': snapshot.winner,
                        'battle_ended': snapshot.battle_ended,
                        'changes_summary': snapshot.changes_summary,
                        'spirits': {
                            name: {
                                'name': spirit.name,
                                'team': spirit.team,
                                'current_hp': spirit.current_hp,
                                'max_hp': spirit.max_hp,
                                'energy': spirit.energy,
                                'is_alive': spirit.is_alive,
                                'effects_count': len(spirit.effects),
                                'hp_change': spirit.hp_change,
                                'energy_change': spirit.energy_change
                            }
                            for name, spirit in snapshot.spirits.items()
                        }
                    }
                    export_data['snapshots'].append(snapshot_data)

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                self.log(f"战斗记录已导出到: {filename}")
                messagebox.showinfo("成功", f"战斗记录已导出到:\n{filename}")

        except Exception as e:
            self.log(f"导出失败: {e}", "ERROR")
            messagebox.showerror("错误", f"导出失败: {e}")

    def update_all_displays(self):
        """更新所有显示"""
        if not self.engine:
            return

        self.update_battle_overview()
        self.update_spirits_list()

        # 如果有选中的精灵，更新详情
        if self.selected_spirit_name:
            self.update_spirit_detail()

    def update_battle_overview(self):
        """更新战斗概览"""
        try:
            self.overview_text.delete(1.0, tk.END)

            info = []
            info.append("=== 战斗状态 ===")
            info.append(f"当前回合: {self.engine.battle_state.round_num}")
            info.append(f"回合限制: {self.engine.round_limit}")
            info.append(f"战斗状态: {'进行中' if self.engine.battle_state.winner is None else '已结束'}")

            if self.engine.battle_state.winner is not None:
                if self.engine.battle_state.winner == -1:
                    info.append(f"结果: 平局")
                else:
                    info.append(f"获胜方: 队伍{self.engine.battle_state.winner}")

            info.append("")
            info.append("=== 队伍状态 ===")

            team0_spirits = self.engine.battle_state.get_living_spirits(0)
            team1_spirits = self.engine.battle_state.get_living_spirits(1)

            info.append(f"队伍0: {len(team0_spirits)} 个存活精灵")
            info.append(f"队伍1: {len(team1_spirits)} 个存活精灵")

            info.append("")
            info.append("=== 引擎信息 ===")
            info.append(f"顺位加气: {self.engine.turn_order_bonus_energy}")
            info.append(f"策略类型: {self.engine.turn_order_strategy.__class__.__name__}")

            # 获取最新快照的统计信息
            latest_snapshot = self.battle_recorder.get_latest_snapshot()
            if latest_snapshot:
                info.append("")
                info.append("=== 本回合统计 ===")
                for team in [0, 1]:
                    damage = latest_snapshot.total_damage_dealt.get(team, 0)
                    healing = latest_snapshot.total_healing_done.get(team, 0)
                    info.append(f"队伍{team}: 伤害 {damage:.0f}, 治疗 {healing:.0f}")

            self.overview_text.insert(tk.END, "\n".join(info))

        except Exception as e:
            self.log(f"更新战斗概览失败: {e}", "ERROR")

    def update_spirits_list(self):
        """更新精灵列表"""
        try:
            # 清空现有项目
            for item in self.spirits_tree.get_children():
                self.spirits_tree.delete(item)

            # 添加精灵信息
            all_spirits = self.engine.battle_state.get_all_spirits()

            for spirit in all_spirits:
                # 获取效果数量 - 使用与战斗记录器一致的逻辑
                effects_count = 0

                # 检查 effect_manager.effects
                if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
                    effects_count += len(spirit.effect_manager.effects)

                # 检查 spirit.effects 列表
                if hasattr(spirit, 'effects') and spirit.effects:
                    effects_count += len(spirit.effects)

                # 检查被动技能作为效果
                if hasattr(spirit, 'skills'):
                    for skill in spirit.skills:
                        if hasattr(skill, 'metadata'):
                            cast_type = getattr(skill.metadata, 'cast_type', None)
                            if cast_type == 'PASSIVE':
                                effects_count += 1

                # 格式化显示信息
                hp_text = f"{spirit.current_hp:.0f}/{spirit.max_hp:.0f}"

                # 获取超杀阈值 - 从超杀技能配置中获取
                ultimate_threshold = 300  # 默认值
                if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                    # 从超杀技能配置中获取最低阈值
                    ultimate_skills = getattr(spirit.ultimate_manager, 'ultimate_skills', {})
                    if ultimate_skills:
                        thresholds = []
                        for skill_config in ultimate_skills.values():
                            threshold = getattr(skill_config, 'energy_threshold', 300)
                            thresholds.append(threshold)
                        if thresholds:
                            ultimate_threshold = min(thresholds)  # 使用最低阈值

                current_energy = getattr(spirit, 'energy', 0)
                energy_text = f"{current_energy}/{ultimate_threshold}"  # 显示超杀阈值而不是气势上限
                status_text = "存活" if spirit.is_alive else "死亡"

                # 插入到树形控件
                item_id = self.spirits_tree.insert('', 'end', values=(
                    spirit.name,
                    spirit.team,
                    hp_text,
                    energy_text,
                    status_text,
                    effects_count
                ))

                # 根据状态设置颜色
                if not spirit.is_alive:
                    self.spirits_tree.set(item_id, 'status', '💀 死亡')
                elif spirit.current_hp < spirit.max_hp * 0.3:
                    self.spirits_tree.set(item_id, 'status', '🔴 危险')
                elif spirit.current_hp < spirit.max_hp * 0.7:
                    self.spirits_tree.set(item_id, 'status', '🟡 受伤')
                else:
                    self.spirits_tree.set(item_id, 'status', '🟢 健康')

        except Exception as e:
            self.log(f"更新精灵列表失败: {e}", "ERROR")

    def on_spirit_selected(self, event):
        """精灵选择事件"""
        selection = self.spirits_tree.selection()
        if selection:
            item = selection[0]
            spirit_name = self.spirits_tree.item(item, 'values')[0]
            self.selected_spirit_name = spirit_name
            self.update_spirit_detail()

    def update_spirit_detail(self):
        """更新精灵详情"""
        if not self.selected_spirit_name or not self.engine:
            return

        try:
            # 从最新快照获取精灵信息
            latest_snapshot = self.battle_recorder.get_latest_snapshot()
            if latest_snapshot and self.selected_spirit_name in latest_snapshot.spirits:
                spirit_snapshot = latest_snapshot.spirits[self.selected_spirit_name]
                self.spirit_detail_panel.update_spirit(spirit_snapshot)
            else:
                # 如果没有快照，从引擎直接获取
                all_spirits = self.engine.battle_state.get_all_spirits()
                for spirit in all_spirits:
                    if spirit.name == self.selected_spirit_name:
                        # 创建临时快照
                        spirit_snapshot = self.battle_recorder._create_spirit_snapshot(spirit)
                        self.spirit_detail_panel.update_spirit(spirit_snapshot)
                        break

        except Exception as e:
            self.log(f"更新精灵详情失败: {e}", "ERROR")

    def on_round_selected(self, round_num: int):
        """回合选择事件"""
        try:
            self.log(f"🔍 选择回合 {round_num}")

            # 更新精灵列表显示选定回合的状态
            snapshot = self.battle_recorder.get_snapshot(round_num)
            if snapshot:
                # 清空现有项目
                for item in self.spirits_tree.get_children():
                    self.spirits_tree.delete(item)

                # 添加该回合的精灵状态
                for name, spirit in snapshot.spirits.items():
                    hp_text = f"{spirit.current_hp:.0f}/{spirit.max_hp:.0f}"

                    # 🔧 修复气势显示：显示当前气势/气势阈值
                    energy_threshold = self._get_spirit_ultimate_threshold(name)
                    energy_text = f"{spirit.energy}/{energy_threshold}"

                    status_text = "存活" if spirit.is_alive else "死亡"

                    item_id = self.spirits_tree.insert('', 'end', values=(
                        spirit.name,
                        spirit.team,
                        hp_text,
                        energy_text,
                        status_text,
                        len(spirit.effects)
                    ))

                    # 设置状态颜色
                    if not spirit.is_alive:
                        self.spirits_tree.set(item_id, 'status', '💀 死亡')
                    elif spirit.current_hp < spirit.max_hp * 0.3:
                        self.spirits_tree.set(item_id, 'status', '🔴 危险')
                    elif spirit.current_hp < spirit.max_hp * 0.7:
                        self.spirits_tree.set(item_id, 'status', '🟡 受伤')
                    else:
                        self.spirits_tree.set(item_id, 'status', '🟢 健康')

                # 如果有选中的精灵，更新详情
                if self.selected_spirit_name and self.selected_spirit_name in snapshot.spirits:
                    spirit_snapshot = snapshot.spirits[self.selected_spirit_name]
                    self.spirit_detail_panel.update_spirit(spirit_snapshot)

                # 🔧 修复：确保回合历史面板也更新显示
                if hasattr(self.round_history_panel, 'selected_round'):
                    self.round_history_panel.selected_round = round_num
                    self.round_history_panel.update_display()

                self.log(f"✅ 回合 {round_num} 状态更新完成")
            else:
                self.log(f"⚠️ 找不到回合 {round_num} 的快照", "WARNING")

        except Exception as e:
            self.log(f"回合选择处理失败: {e}", "ERROR")
            import traceback
            traceback.print_exc()

    def _update_spirits_display_from_snapshot(self, battle_snapshot: dict):
        """从战斗状态快照更新精灵显示"""
        try:
            all_spirits_state = battle_snapshot.get("all_spirits_state", [])

            # 清空现有项目
            for item in self.spirits_tree.get_children():
                self.spirits_tree.delete(item)

            # 添加精灵状态
            for spirit_state in all_spirits_state:
                name = spirit_state.get("name", "Unknown")
                team = spirit_state.get("team", 0)
                hp = spirit_state.get("hp", 0)
                max_hp = spirit_state.get("max_hp", 1)
                energy = spirit_state.get("energy", 0)
                is_alive = spirit_state.get("is_alive", True)

                hp_text = f"{hp:.0f}/{max_hp:.0f}"

                # 🔧 修复气势显示：显示当前气势/气势阈值
                energy_threshold = self._get_spirit_ultimate_threshold(name)
                energy_text = f"{energy}/{energy_threshold}"

                status_text = "存活" if is_alive else "死亡"

                item_id = self.spirits_tree.insert('', 'end', values=(
                    name,
                    team,
                    hp_text,
                    energy_text,
                    status_text,
                    0  # 效果数量，暂时设为0
                ))

                # 设置状态颜色
                if not is_alive:
                    self.spirits_tree.set(item_id, 'status', '💀 死亡')
                elif hp < max_hp * 0.3:
                    self.spirits_tree.set(item_id, 'status', '🔴 危险')
                elif hp < max_hp * 0.7:
                    self.spirits_tree.set(item_id, 'status', '🟡 受伤')
                else:
                    self.spirits_tree.set(item_id, 'status', '🟢 健康')

            self.log(f"🔄 动态更新精灵显示完成，共 {len(all_spirits_state)} 只精灵")

        except Exception as e:
            self.log(f"更新精灵显示失败: {e}", "ERROR")

    def _get_spirit_ultimate_threshold(self, spirit_name: str) -> int:
        """获取精灵的超杀气势阈值"""
        try:
            if self.engine and hasattr(self.engine, 'battle_state'):
                # 从战斗状态中找到精灵
                all_spirits = self.engine.battle_state.get_all_spirits()
                for spirit in all_spirits:
                    if spirit.name == spirit_name:
                        # 方法1: 从超杀管理器获取阈值
                        if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                            if hasattr(spirit.ultimate_manager, 'get_lowest_threshold'):
                                return spirit.ultimate_manager.get_lowest_threshold()

                        # 方法2: 从气势组件获取阈值
                        if hasattr(spirit, 'components'):
                            from core.components import EnergyComponent
                            energy_component = spirit.components.get_component(EnergyComponent)
                            if energy_component and hasattr(energy_component, 'get_ultimate_threshold'):
                                return energy_component.get_ultimate_threshold()

                        # 方法3: 从属性获取阈值
                        if hasattr(spirit, 'attributes') and hasattr(spirit.attributes, 'ultimate_threshold'):
                            return spirit.attributes.ultimate_threshold

                        break

            # 默认阈值
            return 300
        except Exception as e:
            self.log(f"获取精灵 {spirit_name} 的超杀阈值失败: {e}", "ERROR")
            return 300

    def _update_battle_info(self, spirit_name: str, actions_count: int, hp_change: float, energy_change: int, result: dict, damage_info: dict = None):
        """更新战斗信息显示"""
        try:
            # 更新当前行动信息
            action_text = f"{spirit_name} 执行了 {actions_count} 个动作"
            if hasattr(self, 'current_action_label'):
                self.current_action_label.config(text=action_text)

            # 🔧 修复伤害显示：使用详细的伤害信息
            damage_parts = []

            if damage_info:
                total_damage = damage_info.get("total_damage_dealt", 0)
                total_healing = damage_info.get("total_healing_done", 0)
                all_hp_changes = damage_info.get("all_hp_changes", {})

                if total_damage > 0:
                    damage_parts.append(f"造成伤害 {total_damage:.0f}")

                if total_healing > 0:
                    damage_parts.append(f"治疗 {total_healing:.0f}")

                # 显示具体的HP变化
                if all_hp_changes:
                    hp_details = []
                    for target_name, hp_diff in all_hp_changes.items():
                        if target_name != spirit_name:  # 不显示自己的HP变化
                            if hp_diff < 0:
                                hp_details.append(f"{target_name} -{abs(hp_diff):.0f}HP")
                            elif hp_diff > 0:
                                hp_details.append(f"{target_name} +{hp_diff:.0f}HP")

                    if hp_details:
                        damage_parts.extend(hp_details)

            # 添加气势变化
            if energy_change != 0:
                damage_parts.append(f"气势{energy_change:+}")

            damage_text = ", ".join(damage_parts) if damage_parts else "无变化"
            if hasattr(self, 'damage_info_label'):
                self.damage_info_label.config(text=damage_text)

            # 更新技能信息
            skill_info = self._extract_skill_info(spirit_name, actions_count, result)
            if hasattr(self, 'skill_info_label'):
                self.skill_info_label.config(text=skill_info)

        except Exception as e:
            self.log(f"更新战斗信息失败: {e}", "ERROR")

    def _extract_skill_info(self, spirit_name: str, actions_count: int, result: dict) -> str:
        """提取技能信息"""
        try:
            # 判断技能类型
            if actions_count >= 3:
                # 可能是超杀技能
                spirit_state = result.get("spirit_state", {})
                energy_change = spirit_state.get("energy_change", 0)

                if energy_change <= -200:  # 消耗了大量气势
                    return f"🔥 超杀技能 (消耗气势{abs(energy_change)})"
                else:
                    return f"⚡ 多重技能 ({actions_count}个动作)"
            elif actions_count == 2:
                return "🎯 组合技能"
            elif actions_count == 1:
                return "⚔️ 普通攻击"
            else:
                return "💤 跳过回合"
        except:
            return f"未知技能 ({actions_count}个动作)"


def main():
    """主函数"""
    root = tk.Tk()
    app = EnhancedBattleUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
