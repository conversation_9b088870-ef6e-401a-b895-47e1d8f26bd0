#!/usr/bin/env python3
"""
快速启动UI脚本

自动选择最佳的UI版本启动
"""

import sys
import os

def main():
    """主函数"""
    print("🚀 启动奥奇传说AI战斗系统UI...")
    
    # 添加项目根目录到路径
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    try:
        # 优先尝试启动增强版UI
        print("📊 尝试启动增强版UI...")
        from ui.ux.enhanced_battle_ui import main as run_enhanced_ui
        run_enhanced_ui()
        
    except ImportError as e:
        print(f"⚠️ 增强版UI启动失败: {e}")
        
        try:
            # 备选：启动简化版UI
            print("🔄 启动简化版UI...")
            from ui.ux.simple_battle_ui import main as run_simple_ui
            run_simple_ui()
            
        except ImportError as e2:
            print(f"❌ 简化版UI也启动失败: {e2}")
            
            try:
                # 最后备选：启动原始UI
                print("🔄 启动原始UI...")
                from simple_battle_ui import main as run_original_ui
                run_original_ui()
                
            except ImportError as e3:
                print(f"❌ 所有UI都无法启动: {e3}")
                print("请检查依赖和环境配置")
        
    except Exception as e:
        print(f"❌ UI启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
