"""
能量组件

管理精灵的能量/气势系统，包括当前能量、最大能量、能量获得和消耗。
"""
from __future__ import annotations
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..spirit.spirit import Spirit

from ..logging import spirit_logger


class EnergyComponent:
    """能量组件 - 管理精灵的能量/气势系统"""
    
    def __init__(self, owner: 'Spirit', max_energy: int = 300):
        """
        初始化能量组件
        
        Args:
            owner: 拥有此组件的精灵
            max_energy: 最大能量值
        """
        self.owner = owner
        self._max_energy = max_energy
        self._current_energy = 0  # 初始能量为0
    
    @property
    def current_energy(self) -> int:
        """当前能量"""
        return self._current_energy
    
    @current_energy.setter
    def current_energy(self, value: int) -> None:
        """设置当前能量"""
        max_energy = self.max_energy  # 使用动态最大气势
        self._current_energy = max(0, min(value, max_energy))
    
    @property
    def max_energy(self) -> int:
        """最大能量 - 从精灵属性获取"""
        if hasattr(self.owner, 'attributes') and hasattr(self.owner.attributes, 'max_energy'):
            return self.owner.attributes.max_energy
        return self._max_energy
    
    @max_energy.setter
    def max_energy(self, value: int) -> None:
        """设置最大能量"""
        self._max_energy = max(1, value)  # 最大能量至少为1
        # 如果当前能量超过新的最大值，调整当前能量
        if self._current_energy > self._max_energy:
            self._current_energy = self._max_energy
    
    @property
    def energy_percentage(self) -> float:
        """能量百分比 (0.0 - 1.0)"""
        return self._current_energy / self._max_energy if self._max_energy > 0 else 0.0
    
    @property
    def is_full_energy(self) -> bool:
        """是否满能量"""
        return self._current_energy >= self._max_energy
    
    @property
    def is_empty_energy(self) -> bool:
        """是否没有能量"""
        return self._current_energy <= 0
    
    def gain_energy(self, amount: int) -> int:
        """
        获得能量
        
        Args:
            amount: 获得的能量量
            
        Returns:
            实际获得的能量量
        """
        if amount <= 0:
            return 0
        
        # 记录获得前的能量
        old_energy = self._current_energy
        
        # 应用能量获得 - 使用动态最大气势
        max_energy = self.max_energy  # 使用属性获取最大气势
        self._current_energy = min(max_energy, self._current_energy + amount)
        
        # 计算实际获得量
        actual_gain = self._current_energy - old_energy
        
        # 记录日志
        if actual_gain > 0:
            spirit_logger.debug(
                f"{self.owner.name} 获得 {actual_gain} 点能量 "
                f"(Energy: {old_energy} -> {self._current_energy})"
            )
        
        return actual_gain
    
    def consume_energy(self, amount: int) -> bool:
        """
        消耗能量
        
        Args:
            amount: 要消耗的能量量
            
        Returns:
            是否成功消耗（能量是否足够）
        """
        if amount <= 0:
            return True
        
        if self._current_energy < amount:
            spirit_logger.debug(
                f"{self.owner.name} 能量不足，无法消耗 {amount} 点能量 "
                f"(当前: {self._current_energy})"
            )
            return False
        
        # 记录消耗前的能量
        old_energy = self._current_energy
        
        # 消耗能量
        self._current_energy -= amount
        
        # 记录日志
        spirit_logger.debug(
            f"{self.owner.name} 消耗 {amount} 点能量 "
            f"(Energy: {old_energy} -> {self._current_energy})"
        )
        
        return True
    
    def has_enough_energy(self, amount: int) -> bool:
        """
        检查是否有足够的能量
        
        Args:
            amount: 需要的能量量
            
        Returns:
            是否有足够的能量
        """
        return self._current_energy >= amount
    
    def set_energy_directly(self, value: int) -> None:
        """
        直接设置能量（用于特殊情况，不记录日志）

        Args:
            value: 新的能量值
        """
        max_energy = self.max_energy  # 使用动态最大气势
        self._current_energy = max(0, min(value, max_energy))
    
    def get_missing_energy(self) -> int:
        """获取缺失的能量"""
        return self.max_energy - self._current_energy
    
    def reset_energy(self) -> None:
        """重置能量为0"""
        old_energy = self._current_energy
        self._current_energy = 0
        
        if old_energy > 0:
            spirit_logger.debug(
                f"{self.owner.name} 能量重置 "
                f"(Energy: {old_energy} -> 0)"
            )
    
    def fill_energy(self) -> None:
        """填满能量"""
        old_energy = self._current_energy
        max_energy = self.max_energy  # 使用动态最大气势
        self._current_energy = max_energy

        if old_energy < max_energy:
            spirit_logger.debug(
                f"{self.owner.name} 能量填满 "
                f"(Energy: {old_energy} -> {max_energy})"
            )
    
    def can_use_ultimate(self) -> bool:
        """检查是否可以使用超杀技能（气势达到阈值）"""
        if hasattr(self.owner, 'attributes') and hasattr(self.owner.attributes, 'can_use_ultimate'):
            return self.owner.attributes.can_use_ultimate(self._current_energy)
        # 默认阈值为满气势（300）
        return self._current_energy >= 300

    def get_ultimate_threshold(self) -> int:
        """获取超杀阈值"""
        if hasattr(self.owner, 'attributes') and hasattr(self.owner.attributes, 'ultimate_threshold'):
            return self.owner.attributes.ultimate_threshold
        return 300  # 默认阈值

    def is_at_ultimate_threshold(self) -> bool:
        """检查是否达到超杀阈值"""
        return self._current_energy >= self.get_ultimate_threshold()

    def get_energy_progress_to_ultimate(self) -> float:
        """获取到超杀阈值的进度（0.0-1.0）"""
        threshold = self.get_ultimate_threshold()
        if threshold <= 0:
            return 1.0
        return min(1.0, self._current_energy / threshold)

    def __repr__(self) -> str:
        """字符串表示"""
        ultimate_status = "✨" if self.can_use_ultimate() else ""
        return f"EnergyComponent(Energy: {self._current_energy}/{self.max_energy}{ultimate_status})"
