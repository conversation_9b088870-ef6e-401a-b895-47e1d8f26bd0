#!/usr/bin/env python3
"""
简单的UI测试

直接测试战斗创建和执行
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_battle():
    """测试简单战斗"""
    print("🔧 测试简单战斗...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"可用精灵: {available_spirits}")
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        if not spirit1 or not spirit2:
            print("❌ 精灵创建失败")
            return False
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"精灵1: {spirit1.name}, HP={spirit1.current_hp}")
        print(f"精灵2: {spirit2.name}, HP={spirit2.current_hp}")
        
        # 创建战斗引擎
        from core.battle.engines.factory import create_battle_engine
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        print("✅ 战斗引擎创建成功")
        
        # 创建战斗记录器
        from ui.ux.models.battle_record import BattleRecorder
        
        recorder = BattleRecorder()
        
        # 创建初始快照
        print("创建初始快照...")
        initial_snapshot = recorder.create_snapshot(engine.battle_state, 0)
        recorder.add_snapshot(initial_snapshot)
        print("✅ 初始快照创建成功")
        
        # 执行一回合
        print("执行第一回合...")
        result = engine.execute_round()
        print(f"回合执行结果: {result}")
        
        # 创建回合快照
        print("创建回合快照...")
        round_snapshot = recorder.create_snapshot(engine.battle_state, 1)
        recorder.add_snapshot(round_snapshot)
        print("✅ 回合快照创建成功")
        
        # 测试精灵详情面板
        print("测试精灵详情面板...")
        import tkinter as tk
        from ui.ux.components.spirit_detail_panel import SpiritDetailPanel
        
        root = tk.Tk()
        root.withdraw()
        
        panel = SpiritDetailPanel(root)
        
        # 获取精灵快照
        for name, spirit_snapshot in round_snapshot.spirits.items():
            print(f"更新精灵详情: {name}")
            panel.update_spirit(spirit_snapshot)
            print(f"  - 效果数量: {len(spirit_snapshot.effects)}")
        
        root.destroy()
        print("✅ 精灵详情面板测试成功")
        
        # 测试回合历史面板
        print("测试回合历史面板...")
        root2 = tk.Tk()
        root2.withdraw()
        
        from ui.ux.components.round_history_panel import RoundHistoryPanel
        history_panel = RoundHistoryPanel(root2)
        history_panel.set_recorder(recorder)
        
        print("更新回合显示...")
        history_panel.update_display()
        
        root2.destroy()
        print("✅ 回合历史面板测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 简单UI测试")
    print("="*60)
    
    success = test_simple_battle()
    
    print("\n" + "="*60)
    print("📊 测试结果")
    print("="*60)
    
    if success:
        print("✅ 测试成功")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
