from __future__ import annotations
from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, List, Literal

class EventType(Enum):
    """Enumeration of all possible event types in the battle system."""
    # 战斗生命周期事件
    BATTLE_START = "battle_start"
    BATTLE_END = "battle_end"

    # 回合事件
    ROUND_START = "round_start"
    ROUND_END = "round_end"

    # 精灵生命周期事件
    SPIRIT_FAINT = "spirit_faint"
    SPIRIT_REVIVE = "spirit_revive"
    SPIRIT_SUMMONED = "spirit_summoned"

    # 行动事件
    ACTION_START = "action_start"
    BEFORE_ACTION = "before_action"
    ACTION_DECISION = "action_decision"
    MODIFY_ACTION = "modify_action"
    ACTION_COMPLETE = "action_complete"
    AFTER_ACTION_END = "after_action_end"

    # 攻击事件
    DECLARE_ATTACK = "declare_attack"
    BEFORE_ATTACK = "before_attack"
    MODIFY_ATTACK = "modify_attack"
    APPLY_ATTACK_EFFECTS = "apply_attack_effects"
    AFTER_ATTACK = "after_attack"

    # 伤害事件
    BEFORE_DAMAGE_APPLIED = "before_damage_applied"
    AFTER_DAMAGE = "after_damage"

    # 效果事件
    EFFECT_APPLIED = "effect_applied"
    ON_IMMUNITY = "on_immunity"

    # 特殊事件
    COMMUNING_TRIGGERED = "communing_triggered"
    ON_DIE = "on_die"

@dataclass
class TriggerCondition:
    """Base class for all trigger conditions."""
    event_type: EventType

ActorTargetRelation = Literal["self", "ally", "enemy", "any"]

@dataclass
class AfterDamageCondition(TriggerCondition):
    """Triggers after damage has been dealt."""
    event_type: EventType = field(default=EventType.AFTER_DAMAGE, init=False)
    from_skill_type: Optional[List[str]] = None  # e.g., ["ULTIMATE", "ACTIVE"]
    attacker: ActorTargetRelation = "any"
    defender: ActorTargetRelation = "any"

@dataclass
class RoundStartCondition(TriggerCondition):
    """Triggers at the start of a round."""
    event_type: EventType = field(default=EventType.ROUND_START, init=False)

@dataclass
class BeforeAttackCondition(TriggerCondition):
    """Triggers before an attack is executed."""
    event_type: EventType = field(default=EventType.BEFORE_ATTACK, init=False)
    # Future fields for more granularity can be added here
    # e.g., attacker: ActorTargetRelation = "any"

@dataclass
class RoundEndCondition(TriggerCondition):
    """Triggers at the end of a round."""
    event_type: EventType = field(default=EventType.ROUND_END, init=False)

@dataclass
class ImmunityCondition(TriggerCondition):
    """Triggers after a spirit becomes immune to an effect or damage."""
    event_type: EventType = field(default=EventType.ON_IMMUNITY, init=False)
    target: ActorTargetRelation = "any"

@dataclass
class BattleStartCondition(TriggerCondition):
    """Triggers at the start of a battle."""
    event_type: EventType = field(default=EventType.BATTLE_START, init=False)

@dataclass
class BattleEndCondition(TriggerCondition):
    """Triggers at the end of a battle."""
    event_type: EventType = field(default=EventType.BATTLE_END, init=False)

@dataclass
class SpiritFaintCondition(TriggerCondition):
    """Triggers when a spirit faints."""
    event_type: EventType = field(default=EventType.SPIRIT_FAINT, init=False)
    target: ActorTargetRelation = "any"

@dataclass
class SpiritReviveCondition(TriggerCondition):
    """Triggers when a spirit is revived."""
    event_type: EventType = field(default=EventType.SPIRIT_REVIVE, init=False)
    target: ActorTargetRelation = "any"

@dataclass
class ActionStartCondition(TriggerCondition):
    """Triggers when an action starts."""
    event_type: EventType = field(default=EventType.ACTION_START, init=False)
    actor: ActorTargetRelation = "any"

@dataclass
class ActionCompleteCondition(TriggerCondition):
    """Triggers when an action completes."""
    event_type: EventType = field(default=EventType.ACTION_COMPLETE, init=False)
    actor: ActorTargetRelation = "any"

@dataclass
class BeforeDamageAppliedCondition(TriggerCondition):
    """Triggers before damage is applied."""
    event_type: EventType = field(default=EventType.BEFORE_DAMAGE_APPLIED, init=False)
    target: ActorTargetRelation = "any"

@dataclass
class EffectAppliedCondition(TriggerCondition):
    """Triggers when an effect is applied."""
    event_type: EventType = field(default=EventType.EFFECT_APPLIED, init=False)
    target: ActorTargetRelation = "any"

# As we migrate more effects, we can add more specific condition classes here.