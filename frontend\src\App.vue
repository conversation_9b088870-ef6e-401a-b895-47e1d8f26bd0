<template>
  <div id="app" class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
    <!-- 全局加载遮罩 -->
    <div v-if="appStore.isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
      <div class="text-center">
        <el-icon class="animate-spin text-6xl text-purple-400 mb-4">
          <Loading />
        </el-icon>
        <p class="text-white text-lg">{{ appStore.loadingText }}</p>
      </div>
    </div>

    <!-- 主应用内容 -->
    <div v-else class="flex h-screen">
      <!-- 侧边栏导航 -->
      <aside 
        class="bg-slate-800/90 backdrop-blur-sm border-r border-purple-500/20 transition-all duration-300 relative"
        :class="[isCollapsed ? 'w-14' : 'w-40']"
      >
        <!-- 标题区域，点击可展开/收起 -->
        <div 
          class="p-3 flex items-center justify-between cursor-pointer hover:bg-slate-700/50 transition-colors rounded-lg m-1"
          @click="toggleSidebar"
        >
          <template v-if="!isCollapsed">
            <h1 class="text-base font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 truncate flex items-center">
              <span class="mr-1">🏟️</span> 精灵战斗模拟器
            </h1>
          </template>
          <template v-else>
            <h1 class="text-xl font-bold text-center w-full text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
              🏟️
            </h1>
          </template>
        </div>
        
        <div v-if="!isCollapsed" class="px-3 pb-1">
          <p class="text-slate-400 text-xs">v2.0 现代化版本</p>
        </div>
        
        <nav class="px-2 mt-2">
          <router-link
            v-for="route in navigationRoutes"
            :key="route.path"
            :to="route.path"
            class="flex items-center px-2 py-2 mb-1 rounded-lg transition-all duration-200 hover:bg-purple-600/20 group"
            :class="{ 'bg-purple-600/30 text-purple-300': $route.path === route.path, 'justify-center': isCollapsed }"
          >
            <el-icon :class="[isCollapsed ? 'text-xl' : 'mr-2 text-lg']">
              <component :is="route.icon" />
            </el-icon>
            <span v-if="!isCollapsed" class="font-medium text-sm">{{ route.name }}</span>
          </router-link>
        </nav>
      </aside>

      <!-- 主内容区域 -->
      <main class="flex-1 overflow-hidden">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>

    <!-- 全局通知 -->
    <Teleport to="body">
      <NotificationCenter />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAppStore } from './stores/app'
import NotificationCenter from './components/common/NotificationCenter.vue'

const appStore = useAppStore()
const isCollapsed = ref(false)

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
  localStorage.setItem('sidebarCollapsed', isCollapsed.value.toString())
}

const navigationRoutes = [
  { path: '/battle', name: '战斗竞技场', icon: 'Trophy' },
  { path: '/platform', name: '对战平台', icon: 'Connection' },
  { path: '/spirits', name: '精灵管理', icon: 'Avatar' },
  { path: '/analytics', name: '数据分析', icon: 'DataAnalysis' },
  { path: '/tactics', name: '战术规划', icon: 'Guide' },
  { path: '/settings', name: '系统设置', icon: 'Setting' }
]

// 初始化应用
onMounted(() => {
  appStore.initialize()
  // 从本地存储中恢复侧边栏状态
  const savedState = localStorage.getItem('sidebarCollapsed')
  if (savedState) {
    isCollapsed.value = savedState === 'true'
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>