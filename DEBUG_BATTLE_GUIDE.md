# 🐛 AoQiAI 战斗引擎调试指南

专门用于本地调试战斗引擎的完整工具集，帮助你深入了解和优化战斗系统。

## ✨ 调试工具概览

### 🎯 **主要调试工具**

1. **`simple_debug_battle.py`** - 简化调试工具 ✅ **推荐使用**
   - 无复杂依赖，开箱即用
   - 完整的步进调试功能
   - 实时状态监控
   - 断点和监视功能

2. **`debug_battle_engine.py`** - 完整调试工具
   - 集成真实战斗引擎
   - 更详细的调试信息
   - 会话保存功能

3. **`test_debug_tool.py`** - 测试验证工具
   - 验证调试工具是否正常工作
   - 自动化测试套件

## 🚀 快速开始

### 1. 使用简化调试工具（推荐）

```bash
python simple_debug_battle.py
```

选择调试模式：
- **1. 快速测试** - 自动运行完整战斗
- **2. 交互式调试** - 步进控制，详细分析

### 2. 使用完整调试工具

```bash
python debug_battle_engine.py
```

需要确保项目依赖正确安装。

## 🎮 调试功能详解

### 📊 **步进调试**

在交互式模式下，你可以：

```
调试> [Enter]     # 执行下一步
调试> c           # 切换到连续模式
调试> s           # 显示详细精灵状态
调试> h           # 显示帮助信息
调试> q           # 退出调试
```

### 👁️ **监视功能**

监视特定精灵的状态变化：

```
调试> w 神曜圣谕·女帝    # 监视女帝的状态
调试> w                 # 显示所有监视的精灵
```

监视输出示例：
```
👁️ 监视 神曜圣谕·女帝: 存活, HP 823/1200 (68.6%)
```

### 🔴 **断点系统**

设置条件断点，在特定情况下暂停：

```
调试> b battle_end      # 在战斗结束时暂停
调试> b 神曜圣谕·女帝    # 当涉及女帝时暂停
调试> b                 # 显示所有断点
```

### 📈 **状态监控**

实时查看精灵状态：

```
调试> s
```

输出示例：
```
============================================================
📊 精灵状态
============================================================

队伍 0: 2/3 存活
  🟢 神曜圣谕·女帝
     HP: 823/1200 (68.6%)
     攻击: 180, 防御: 160, 速度: 120
  🟢 神曜创世·以撒
     HP: 689/1000 (68.9%)
     攻击: 200, 防御: 140, 速度: 140
  🔴 乐律之神·音织
     HP: 0/900 (0.0%)
     攻击: 160, 防御: 120, 速度: 160

队伍 1: 3/3 存活
  🟢 天恩圣祭·空灵圣龙
     HP: 567/1100 (51.5%)
     攻击: 170, 防御: 150, 速度: 130
  🟢 赤妖王·御神
     HP: 1076/1300 (82.8%)
     攻击: 190, 防御: 170, 速度: 110
  🟢 神曜虚无·伏妖
     HP: 459/950 (48.3%)
     攻击: 210, 防御: 130, 速度: 150
============================================================
```

## 🔧 调试场景示例

### 场景1：分析战斗平衡性

```bash
python simple_debug_battle.py
# 选择 2 (交互式调试)
# 启用步进模式
# 使用 's' 命令观察每回合后的血量变化
# 使用 'w' 命令监视关键精灵
```

### 场景2：测试特定精灵组合

修改 `simple_debug_battle.py` 中的精灵配置：

```python
# 在 create_test_spirits 方法中修改精灵组合
spirit_templates = [
    {"name": "自定义精灵1", "hp": 1500, "attack": 200, "defense": 180, "speed": 100},
    {"name": "自定义精灵2", "hp": 1000, "attack": 250, "defense": 120, "speed": 150},
    # ... 更多自定义配置
]
```

### 场景3：调试战斗算法

在战斗逻辑中添加调试输出：

```python
# 在 generate_spirit_action 方法中添加
print(f"DEBUG: {spirit.name} 选择攻击 {target.name}")
print(f"DEBUG: 基础伤害={base_damage}, 最终伤害={reduced_damage}")
```

## 📊 调试数据分析

### 战斗日志结构

每个战斗事件都包含：

```json
{
  "round": 1,
  "type": "round_actions",
  "timestamp": 1703123456.789,
  "data": {
    "actions": [
      {
        "actor_name": "神曜圣谕·女帝",
        "action_type": "attack",
        "target_name": "天恩圣祭·空灵圣龙",
        "damage": 118,
        "description": "神曜圣谕·女帝 攻击 天恩圣祭·空灵圣龙，造成 118 点伤害"
      }
    ]
  }
}
```

### 性能分析

观察关键指标：
- **回合数** - 战斗长度
- **伤害分布** - 各精灵的输出能力
- **存活时间** - 精灵的生存能力
- **速度影响** - 行动顺序的重要性

## 🛠️ 自定义调试功能

### 添加新的调试命令

在 `handle_command` 方法中添加：

```python
elif cmd == 'damage':
    # 显示伤害统计
    self.show_damage_stats(engine)
    return False
```

### 添加新的监控指标

```python
def check_battle_balance(self, engine):
    """检查战斗平衡性"""
    team0_total_hp = sum(s.hp for s in engine.all_spirits.values() if s.team == 0)
    team1_total_hp = sum(s.hp for s in engine.all_spirits.values() if s.team == 1)
    
    balance_ratio = team0_total_hp / team1_total_hp if team1_total_hp > 0 else float('inf')
    print(f"⚖️ 血量平衡比: {balance_ratio:.2f}")
```

### 导出调试数据

```python
def export_battle_data(self, engine, filename="battle_debug.json"):
    """导出战斗数据用于分析"""
    data = {
        "battle_log": engine.battle_log,
        "final_state": {sid: s.to_dict() for sid, s in engine.all_spirits.items()},
        "rounds": engine.round_num
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"📁 战斗数据已导出到: {filename}")
```

## 🎯 调试最佳实践

### 1. 系统性调试

```bash
# 第一步：快速测试，了解整体情况
python simple_debug_battle.py
# 选择 1 (快速测试)

# 第二步：详细分析问题回合
python simple_debug_battle.py  
# 选择 2 (交互式调试)
# 设置断点在问题出现的地方
```

### 2. 数据驱动调试

- 记录多次战斗的结果
- 分析胜率和平均回合数
- 识别过强或过弱的精灵

### 3. 渐进式优化

1. **识别问题** - 使用监视功能发现异常
2. **定位原因** - 使用断点精确定位
3. **验证修复** - 重新运行测试确认改进

## 🔍 常见调试场景

### 问题1：某个精灵过强

```bash
# 监视该精灵的表现
调试> w 过强精灵名
# 观察其伤害输出和承受能力
调试> s
```

### 问题2：战斗时间过长

```bash
# 设置回合数断点
调试> b round_10
# 分析高回合数时的状态
```

### 问题3：随机性过大

```bash
# 多次运行快速测试
# 记录结果差异
# 分析伤害计算的随机因子
```

## 📈 调试输出解读

### 日志级别说明

- **INFO** - 重要事件（回合开始/结束、战斗结果）
- **DEBUG** - 详细信息（具体动作、伤害数值）
- **TRACE** - 完整数据（内部状态、计算过程）

### 战斗流程

```
battle_start → round_start → round_actions → round_end → ... → battle_end
```

每个阶段都有对应的调试信息输出。

## 🤝 贡献调试工具

欢迎改进调试工具！可以添加：

- 新的调试命令
- 更详细的状态显示
- 数据可视化功能
- 自动化测试场景

## 📄 相关文件

- `simple_debug_battle.py` - 主调试工具
- `debug_battle_engine.py` - 完整调试工具
- `test_debug_tool.py` - 测试验证
- `DEBUG_BATTLE_GUIDE.md` - 本指南

---

🐛 **开始你的调试之旅！**

使用这些工具深入了解战斗系统的每个细节，优化游戏平衡性，创造更好的游戏体验！
