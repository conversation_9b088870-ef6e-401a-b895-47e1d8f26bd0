"""
赤妖王·御神 - 技能定义模块

包含御神的所有技能定义：
- 狐念之力 - 被动技能
- 青焰燎尾 - 普攻技能  
- 千机罗网 - 超杀技能
- 灵目慧心 - 英雄技能
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from src.core.spirit.refactored_spirit import RefactoredSpirit

from core.skill.skills import (
    Skill, SkillMetadata, DamageComponent, SkillEffectComponent,
    SingleEnemySelector, SelfSelector, AllAlliesSelector
)
from .skill_components import (
    AzureFlameDebuffComponent, ThousandWebSwitchComponent,
    SpiritWisdomTeamBuffComponent, SpiritWisdomBattleBoostComponent,
    EnergyManipulationComponent
)
from .passive_effects import create_fox_spirit_power_effect


def create_yushen_skills(spirit: RefactoredSpirit) -> List[Skill]:
    """创建赤妖王·御神的技能"""
    
    skills = [
        # 狐念之力 - 被动技能
        Skill(
            metadata=SkillMetadata(
                name="狐念之力",
                description="被动 Lv.1：1. 受到致命伤害时，若敌方有X位精灵处于无法行动状态，则令自身重生并恢复50%最大生命值（X初始为1，每次触发+1）；2. 已阵亡精灵攻击无法行动的精灵时，若自身存活，则随机烧伤敌阵一位精灵并令自身恢复30气势；3. 受击时，敌阵每有1位处于无法行动状态的存活精灵，则自身获得25%减伤（最多75%减伤）。烧伤：对目标造成御神攻击*80%的伤害",
                cast_type="PASSIVE",
                tags=["被动", "重生", "减伤", "烧伤", "气势恢复"]
            ),
            target_selector=SelfSelector(),
            components=[SkillEffectComponent(effect_factory=lambda: create_fox_spirit_power_effect(spirit))]
        ),
        
        # 青焰燎尾 - 普攻技能
        Skill(
            metadata=SkillMetadata(
                name="青焰燎尾",
                description="攻击对手，造成攻击120%的物理伤害，攻击后，降低目标攻击力20%和物理防御15%，持续2回合",
                cast_type="ACTIVE",
                energy_cost=0,
                tags=["普攻", "减益", "攻击力下降", "防御力下降"]
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=1.2, damage_type="physical"),
                AzureFlameDebuffComponent()
            ]
        ),
        
        # 千机罗网 - 超杀技能
        Skill(
            metadata=SkillMetadata(
                name="千机罗网",
                description="攻击目标，造成攻击*300%的物理伤害，攻击后，若目标死亡，则对敌方生命值最低的精灵造成额外50%攻击力的物理伤害",
                cast_type="ULTIMATE",
                energy_cost=300,
                tags=["超杀", "目标切换", "连锁攻击"]
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=3.0, damage_type="physical"),
                ThousandWebSwitchComponent()
            ]
        ),
        
        # 灵目慧心 - 英雄技能
        Skill(
            metadata=SkillMetadata(
                name="灵目慧心",
                description="英雄技：为全体队友增加25%攻击力和15%暴击率，持续3回合，并获得50点气势；全体队友攻击无法行动的精灵时，暴击率、暴击伤害、破击率各增加40%，并获得30点气势",
                cast_type="HERO",
                energy_cost=200,
                tags=["英雄技", "团队增益", "战斗加成", "气势增加"]
            ),
            target_selector=AllAlliesSelector(),
            components=[
                SpiritWisdomTeamBuffComponent(),
                SpiritWisdomBattleBoostComponent(),
                EnergyManipulationComponent()
            ]
        )
    ]
    
    return skills


# 技能数据配置
YUSHEN_SKILLS_DATA = [
    {
        "name": "狐念之力",
        "type": "PASSIVE",
        "description": "被动 Lv.1：1. 受到致命伤害时，若敌方有X位精灵处于无法行动状态，则令自身重生并恢复50%最大生命值（X初始为1，每次触发+1）；2. 已阵亡精灵攻击无法行动的精灵时，若自身存活，则随机烧伤敌阵一位精灵并令自身恢复30气势；3. 受击时，敌阵每有1位处于无法行动状态的存活精灵，则自身获得25%减伤（最多75%减伤）。烧伤：对目标造成御神攻击*80%的伤害"
    },
    {
        "name": "青焰燎尾",
        "type": "ACTIVE",
        "description": "攻击对手，造成攻击120%的物理伤害，攻击后，降低目标攻击力20%和物理防御15%，持续2回合"
    },
    {
        "name": "千机罗网",
        "type": "ULTIMATE",
        "description": "攻击目标，造成攻击*300%的物理伤害，攻击后，若目标死亡，则对敌方生命值最低的精灵造成额外50%攻击力的物理伤害"
    },
    {
        "name": "灵目慧心",
        "type": "HERO",
        "description": "英雄技：为全体队友增加25%攻击力和15%暴击率，持续3回合，并获得50点气势；全体队友攻击无法行动的精灵时，暴击率、暴击伤害、破击率各增加40%，并获得30点气势"
    }
]


# 导出函数
__all__ = [
    'create_yushen_skills',
    'YUSHEN_SKILLS_DATA'
]
