"""
战斗引擎模块

包含各种类型的战斗引擎实现：
- RefactoredBattleEngine: 重构后的主要战斗引擎
- StreamingBattleEngine: 流式战斗引擎
- 工厂函数: 便捷的引擎创建函数
"""

from .battle_engine import RefactoredBattleEngine, BattleConditionChecker
from .streaming_engine import StreamingBattleEngine, StreamLevel, create_streaming_engine
from .factory import create_battle_engine

__all__ = [
    # 主要引擎
    'RefactoredBattleEngine',
    'StreamingBattleEngine',
    
    # 向后兼容
    'BattleConditionChecker',
    
    # 流式引擎相关
    'StreamLevel',
    'create_streaming_engine',
    
    # 工厂函数
    'create_battle_engine',
]
