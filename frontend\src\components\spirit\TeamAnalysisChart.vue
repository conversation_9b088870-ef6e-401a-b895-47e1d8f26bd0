<template>
  <div class="team-analysis-chart">
    <!-- 职业分布 -->
    <div class="profession-distribution mb-4">
      <h5 class="text-sm text-slate-300 mb-2">职业分布</h5>
      <div class="grid grid-cols-2 gap-2">
        <div 
          v-for="(count, profession) in professionCounts" 
          :key="profession"
          class="profession-item flex items-center justify-between text-xs"
        >
          <span class="text-slate-400">{{ getProfessionLabel(profession) }}</span>
          <div class="flex items-center">
            <div class="w-8 bg-slate-600 rounded-full h-2 mr-2">
              <div 
                class="bg-blue-400 h-2 rounded-full transition-all"
                :style="{ width: `${(count / Math.max(...Object.values(professionCounts))) * 100}%` }"
              ></div>
            </div>
            <span class="text-white">{{ count }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 元素分布 -->
    <div class="element-distribution mb-4">
      <h5 class="text-sm text-slate-300 mb-2">元素分布</h5>
      <div class="flex flex-wrap gap-1">
        <el-tag 
          v-for="(count, element) in elementCounts" 
          :key="element"
          :type="getElementTagType(element)"
          size="small"
        >
          {{ getElementLabel(element) }} × {{ count }}
        </el-tag>
      </div>
    </div>

    <!-- 属性统计 -->
    <div class="attribute-stats">
      <h5 class="text-sm text-slate-300 mb-2">属性统计</h5>
      <div class="grid grid-cols-2 gap-2 text-xs">
        <div class="stat-item">
          <span class="text-slate-400">平均攻击</span>
          <span class="text-red-400 font-bold">{{ Math.round(averageStats.attack) }}</span>
        </div>
        <div class="stat-item">
          <span class="text-slate-400">平均防御</span>
          <span class="text-blue-400 font-bold">{{ Math.round(averageStats.defense) }}</span>
        </div>
        <div class="stat-item">
          <span class="text-slate-400">平均生命</span>
          <span class="text-green-400 font-bold">{{ Math.round(averageStats.hp) }}</span>
        </div>
        <div class="stat-item">
          <span class="text-slate-400">平均速度</span>
          <span class="text-yellow-400 font-bold">{{ Math.round(averageStats.speed) }}</span>
        </div>
      </div>
    </div>

    <!-- 队伍评级 -->
    <div class="team-rating mt-4 pt-3 border-t border-slate-600/30">
      <div class="flex items-center justify-between">
        <span class="text-sm text-slate-300">综合评级</span>
        <div class="flex items-center">
          <el-rate 
            v-model="teamRating" 
            :max="5" 
            :show-score="false" 
            :allow-half="true"
            disabled
            size="small"
          />
          <span class="ml-2 text-sm text-white">{{ teamRating.toFixed(1) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Spirit, ElementType, ProfessionType } from '../../types/battle'
import { useSpiritRecommendation } from '../../composables/useSpiritRecommendation'

const props = defineProps<{
  team: Spirit[]
}>()

const { analyzeTeamComposition } = useSpiritRecommendation()

// 分析队伍组成
const teamAnalysis = computed(() => analyzeTeamComposition(props.team))

const professionCounts = computed(() => teamAnalysis.value.professionCounts)
const elementCounts = computed(() => teamAnalysis.value.elementCounts)

// 计算平均属性
const averageStats = computed(() => {
  if (props.team.length === 0) {
    return { attack: 0, defense: 0, hp: 0, speed: 0 }
  }

  const totals = props.team.reduce((acc, spirit) => ({
    attack: acc.attack + spirit.attributes.attack,
    defense: acc.defense + spirit.attributes.defense,
    hp: acc.hp + spirit.attributes.maxHp,
    speed: acc.speed + spirit.attributes.speed
  }), { attack: 0, defense: 0, hp: 0, speed: 0 })

  return {
    attack: totals.attack / props.team.length,
    defense: totals.defense / props.team.length,
    hp: totals.hp / props.team.length,
    speed: totals.speed / props.team.length
  }
})

// 计算队伍评级
const teamRating = computed(() => {
  if (props.team.length === 0) return 0

  let rating = 0
  
  // 基础评分：基于平均属性
  const avgPower = (averageStats.value.attack + averageStats.value.defense + averageStats.value.hp) / 3
  rating += Math.min(avgPower / 2000, 2) // 最高2分

  // 职业多样性加分
  const professionCount = Object.keys(professionCounts.value).length
  rating += Math.min(professionCount * 0.3, 1.5) // 最高1.5分

  // 元素多样性加分
  const elementCount = Object.keys(elementCounts.value).length
  rating += Math.min(elementCount * 0.2, 1) // 最高1分

  // 队伍规模加分
  rating += Math.min(props.team.length * 0.1, 0.5) // 最高0.5分

  return Math.min(rating, 5)
})

// 辅助函数
const getProfessionLabel = (profession: string) => {
  const labels: Record<string, string> = {
    warrior: '战士',
    mage: '法师',
    archer: '射手',
    healer: '治疗',
    assassin: '刺客',
    tank: '坦克'
  }
  return labels[profession] || profession
}

const getElementLabel = (element: string) => {
  const labels: Record<string, string> = {
    fire: '火',
    water: '水',
    earth: '土',
    air: '风',
    light: '光',
    dark: '暗',
    neutral: '无'
  }
  return labels[element] || element
}

const getElementTagType = (element: string) => {
  const types: Record<string, string> = {
    fire: 'danger',
    water: 'primary',
    earth: 'warning',
    air: 'success',
    light: 'info',
    dark: 'warning',
    neutral: 'info'
  }
  return types[element] || 'info'
}
</script>

<style scoped>
.stat-item {
  @apply flex items-center justify-between;
}

.profession-item {
  @apply py-1;
}
</style>