import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info' | 'battle' | 'system'
  title?: string
  message: string
  duration?: number // 毫秒，0表示不自动消失
  persistent?: boolean
  timestamp: number
  paused?: boolean
}

export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref<Notification[]>([])
  const maxNotifications = ref(5)

  // 添加通知
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const timestamp = Date.now()
    
    const newNotification: Notification = {
      id,
      timestamp,
      duration: 5000, // 默认5秒
      persistent: false,
      ...notification
    }

    notifications.value.unshift(newNotification)

    // 限制通知数量
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }

    // 自动移除非持久化通知
    if (!newNotification.persistent && newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }

    return id
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清空所有通知
  const clearAllNotifications = () => {
    notifications.value = []
  }

  // 暂停/恢复通知自动消失
  const pauseNotification = (id: string, paused: boolean) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.paused = paused
    }
  }

  // 便捷方法
  const success = (message: string, title?: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  const error = (message: string, title?: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 8000, // 错误消息显示更久
      ...options
    })
  }

  const warning = (message: string, title?: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 6000,
      ...options
    })
  }

  const info = (message: string, title?: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  const battle = (message: string, title?: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'battle',
      title,
      message,
      duration: 3000, // 战斗消息显示较短
      ...options
    })
  }

  const system = (message: string, title?: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'system',
      title,
      message,
      ...options
    })
  }

  return {
    // 状态
    notifications,
    maxNotifications,

    // 方法
    addNotification,
    removeNotification,
    clearAllNotifications,
    pauseNotification,

    // 便捷方法
    success,
    error,
    warning,
    info,
    battle,
    system
  }
})