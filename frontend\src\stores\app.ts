import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isLoading = ref(false)
  const loadingText = ref('加载中...')
  const theme = ref<'light' | 'dark'>('dark')
  const sidebarCollapsed = ref(false)
  
  // 计算属性
  const isDarkMode = computed(() => theme.value === 'dark')
  
  // 方法
  const setLoading = (loading: boolean, text = '加载中...') => {
    isLoading.value = loading
    loadingText.value = text
  }
  
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    document.documentElement.classList.toggle('dark', isDarkMode.value)
  }
  
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  const initialize = async () => {
    setLoading(true, '初始化应用...')
    
    try {
      // 设置主题
      document.documentElement.classList.toggle('dark', isDarkMode.value)
      
      // 模拟初始化过程
      await new Promise(resolve => setTimeout(resolve, 1000))
      
    } catch (error) {
      console.error('应用初始化失败:', error)
    } finally {
      setLoading(false)
    }
  }
  
  return {
    // 状态
    isLoading,
    loadingText,
    theme,
    sidebarCollapsed,
    
    // 计算属性
    isDarkMode,
    
    // 方法
    setLoading,
    toggleTheme,
    toggleSidebar,
    initialize
  }
})