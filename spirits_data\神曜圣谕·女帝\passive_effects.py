"""
神曜圣谕·女帝 - 被动效果模块

包含女帝的所有被动技能效果：
- ShunTianYingRenPassiveEffect: 顺天应人被动效果
- XingGuiNiZhuanTongLingEffect: 星轨逆转通灵效果
"""
from __future__ import annotations
from typing import List, Optional, TYPE_CHECKING, Dict, Any
import uuid

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.spirit.refactored_spirit import RefactoredSpirit
from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition, ActionCompleteCondition, AfterDamageCondition, SpiritFaintCondition
from .effects import DamageReductionEffect


class ShunTianYingRenPassiveEffect(IEffect):
    """
    顺天应人被动效果

    技能描述：受击时获得20%的减伤，每次通灵后，减伤倍率提高10%，若自身拥有嘲讽则额外获得30%减伤
    """

    def __init__(self, owner_spirit: RefactoredSpirit):
        super().__init__(
            effect_id=f"shuntian_yingren_{owner_spirit.id}",
            name="顺天应人",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=-1  # 永久效果
        )

        self.owner_spirit = owner_spirit
        self.tongling_count: int = 0  # 通灵次数计数
        self.set_data("immediate_action_used_this_round", False)  # 本回合是否已使用立即出手
        
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True
    
    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        return [
            ActionCompleteCondition(actor="self"),  # 自身行动完成后触发（检测通灵技能）
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理事件触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "ACTION_COMPLETE":
                return self._handle_action_complete(event_data, battle_state)

            return EffectResult.success()

        except Exception as e:
            from core.logging import spirit_logger
            spirit_logger.error(f"顺天应人被动效果触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)

    def _handle_action_complete(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理行动完成，检测通灵技能"""
        from core.action import LogAction

        actor = event_data.get("actor")
        if actor != self.owner_spirit:
            return EffectResult.success()

        # 检查是否释放了通灵技能
        skill_name = event_data.get("skill_name", "")
        if "通灵" in skill_name or "星轨逆转" in skill_name:  # 女帝的通灵技能
            self.tongling_count += 1

            # 更新减伤值
            self._update_damage_reduction()

            actions = [
                LogAction(
                    caster=self.owner_spirit,
                    message=f"✨ {self.owner_spirit.name} 释放通灵技能，减伤倍率提升！（当前通灵次数: {self.tongling_count}）",
                    level="INFO"
                )
            ]

            return EffectResult.success_with_actions(actions)

        return EffectResult.success()

    def _update_damage_reduction(self):
        """更新减伤数值"""
        # 基础减伤20%
        base_reduction = 0.20
        
        # 每次通灵增加10%减伤
        tongling_bonus = self.tongling_count * 0.10
        
        # 检查是否有嘲讽效果，额外增加30%减伤
        taunt_bonus = 0.30 if self._has_taunt_effect() else 0.0
        
        # 计算总减伤
        total_reduction = base_reduction + tongling_bonus + taunt_bonus
        
        # 更新效果数据
        self.set_data("base_reduction", base_reduction)
        self.set_data("tongling_bonus", tongling_bonus)
        self.set_data("taunt_bonus", taunt_bonus)
        self.set_data("total_reduction", total_reduction)
        
        # 记录日志
        from core.logging import spirit_logger
        spirit_logger.info(f"🛡️ {self.owner_spirit.name} 顺天应人减伤更新: {total_reduction*100:.0f}%"
                          f"（基础{base_reduction*100:.0f}%+通灵{tongling_bonus*100:.0f}%+嘲讽{taunt_bonus*100:.0f}%）")

    def _has_taunt_effect(self) -> bool:
        """检查是否拥有嘲讽效果"""
        return (self.owner_spirit.has_effect("Taunt") or
                self.owner_spirit.has_effect("嘲讽") or
                self.owner_spirit.has_effect("TauntEffect"))

    def on_apply(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果应用时触发"""
        # 初始化减伤数据
        self._update_damage_reduction()
        
        return EffectResult.success_with_data(
            {"applied": True}, 
            f"{target.name} 获得顺天应人被动效果"
        )

    def on_remove(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{target.name} 失去顺天应人效果"
        )

    def on_update(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果更新时触发（每回合）"""
        # 每回合更新减伤值（考虑嘲讽状态变化）
        self._update_damage_reduction()
        return EffectResult.success_with_data({}, "顺天应人效果更新")

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        total_reduction = self.get_data("total_reduction", 0.2)
        return {
            "name": self.name,
            "description": f"顺天应人被动效果（减伤{total_reduction*100:.0f}%）",
            "tongling_count": self.tongling_count,
            "total_reduction": f"{total_reduction*100:.0f}%",
            "duration": -1  # 永久效果
        }


class XingGuiNiZhuanTongLingEffect(IEffect):
    """
    星轨逆转通灵技效果
    
    改写自旧的 XingGuiNiZhuanTongLingEffect，使用统一效果系统
    """
    
    def __init__(self, owner_spirit: RefactoredSpirit):
        super().__init__(
            effect_id=f"xinggui_nizhuan_{owner_spirit.id}",
            name="星轨逆转",
            effect_type=EffectType.PASSIVE,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.NORMAL,
            duration=-1  # 永久效果
        )
        
        self.owner_spirit = owner_spirit
        self.tongling_progress = 0
        self.max_tongling_progress = 100
        self.tongling_used_count = 0
        self.max_tongling_uses = 2

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        self.set_data("tongling_progress", self.tongling_progress)
        self.set_data("max_progress", self.max_tongling_progress)
        self.set_data("uses_remaining", self.max_tongling_uses - self.tongling_used_count)
        
        return EffectResult.success_with_data(
            {"applied": True},
            f"{target.name} 获得星轨逆转通灵效果"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True},
            f"{target.name} 失去星轨逆转通灵效果"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data({}, "星轨逆转通灵效果更新")

    def add_progress(self, amount: int, reason: str = "未知") -> bool:
        """增加通灵进度"""
        if self.tongling_used_count >= self.max_tongling_uses:
            return False  # 已达到最大使用次数
        
        old_progress = self.tongling_progress
        self.tongling_progress = min(self.tongling_progress + amount, self.max_tongling_progress)
        
        # 更新数据
        self.set_data("tongling_progress", self.tongling_progress)
        
        # 发出进度变化事件
        self._emit_progress_change_event(old_progress, self.tongling_progress, reason)
        
        # 检查是否可以触发通灵
        if self.tongling_progress >= self.max_tongling_progress:
            return self._trigger_tongling()
        
        return True

    def _trigger_tongling(self) -> bool:
        """触发通灵效果"""
        if self.tongling_used_count >= self.max_tongling_uses:
            return False
        
        # 重置进度
        self.tongling_progress = 0
        self.tongling_used_count += 1
        
        # 更新数据
        self.set_data("tongling_progress", 0)
        self.set_data("uses_remaining", self.max_tongling_uses - self.tongling_used_count)
        
        # 这里应该触发实际的通灵效果
        # 复活并获得100%生命上限的护盾，清除所有负面效果，永久变身为命运女神
        
        return True

    def _emit_progress_change_event(self, old_progress: int, new_progress: int, reason: str):
        """发出通灵进度变化事件"""
        try:
            # 尝试导入内部数据收集器
            # from core.event.system import emit_tongling_progress_change  # 暂时注释掉
            
            # emit_tongling_progress_change(  # 暂时注释掉
            #     spirit_id=self.owner_spirit.id,
            #     old_progress=old_progress,
            #     new_progress=new_progress,
            #     reason=reason
            # )
            pass  # 暂时跳过
        except ImportError:
            # 如果导入失败，只记录调试信息
            from core.logging import spirit_logger
            spirit_logger.debug(f"通灵进度变化: {self.owner_spirit.name} {old_progress}->{new_progress} ({reason})")

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"星轨逆转通灵效果（进度{self.tongling_progress}/{self.max_tongling_progress}）",
            "progress": self.tongling_progress,
            "max_progress": self.max_tongling_progress,
            "uses_remaining": self.max_tongling_uses - self.tongling_used_count,
            "duration": -1  # 永久效果
        }


# 导出函数
def create_shuntian_yingren_effect(owner_spirit: RefactoredSpirit) -> ShunTianYingRenPassiveEffect:
    """创建顺天应人被动效果"""
    return ShunTianYingRenPassiveEffect(owner_spirit)


def create_xinggui_nizhuan_effect(owner_spirit: RefactoredSpirit) -> XingGuiNiZhuanTongLingEffect:
    """创建星轨逆转通灵效果"""
    return XingGuiNiZhuanTongLingEffect(owner_spirit)


# 导出所有效果类
__all__ = [
    'ShunTianYingRenPassiveEffect',
    'XingGuiNiZhuanTongLingEffect',
    'create_shuntian_yingren_effect',
    'create_xinggui_nizhuan_effect'
]
