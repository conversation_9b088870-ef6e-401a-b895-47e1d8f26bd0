<template>
  <div 
    class="spirit-card-wrapper"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    ref="cardRef"
  >
    <div 
      class="spirit-card"
      :class="{
        'selected': isSelected,
        'dragging': isDragging
      }"
      @click="emit('click', props.spirit)"
      @contextmenu.prevent="emit('contextmenu', props.spirit)"
      draggable="true"
      @dragstart="handleDragStart"
      @dragend="handleDragEnd"
    >
      <!-- 神格等级 -->
      <div class="shenge-level" v-if="props.spirit.shenge_level > 0">
        <el-icon class="shenge-icon"><Star /></el-icon>
        <span>{{ props.spirit.shenge_level }}</span>
      </div>
      
      <!-- 精灵信息 -->
      <div class="spirit-info">
        <div class="spirit-name">{{ props.spirit.name_prefix }}</div>
        
        <!-- 元素和职业标签 -->
        <div class="tags-container">
          <span v-if="props.spirit.element" :class="['tag', getElementClass(props.spirit.element)]">
            {{ getElementLabel(props.spirit.element) }}
          </span>
          <span v-for="p in props.spirit.professions" :key="p" :class="['tag', ...getProfessionClass(p)]">
            {{ getProfessionLabel(p) }}
          </span>
        </div>
      </div>

      <div class="separator"></div>

      <!-- 技能列表 -->
      <div class="skills-list">
        <div 
          v-for="skill in props.spirit.skills" 
          :key="skill.id"
          :class="getSkillChipClass(skill)"
          :title="skill.description"
        >
          {{ skill.name }}
        </div>
      </div>

    </div>
  </div>
  
  <Teleport to="body">
    <div 
      v-if="showTooltip" 
      class="spirit-tooltip-teleport"
      :style="tooltipStyle"
    >
      <div class="tooltip-header">
        <strong>{{ props.spirit.name_prefix }}</strong> 
        <span class="level">(Lv.{{ props.spirit.shenge_level }})</span>
        <span v-if="props.spirit.shenge_level > 0" class="shenge">⭐{{ props.spirit.shenge_level }}</span>
      </div>
      
      <div class="tooltip-tags" v-if="props.spirit.element || (props.spirit.professions && props.spirit.professions.length > 0)">
        <span v-if="props.spirit.element" class="tag element-tag">{{ getElementLabel(props.spirit.element) }}</span>
        <span v-for="profession in props.spirit.professions.slice(0, 2)" :key="profession" class="tag profession-tag">
          {{ getProfessionLabel(profession) }}
        </span>
      </div>
      
      <div class="tooltip-stats">
        <div class="stat-row">
          <span>生命值:</span> 
          <span class="stat-value">{{ props.spirit.attributes.base_hp }}</span>
        </div>
        <div class="stat-row">
          <span>攻击力:</span> 
          <span class="stat-value">{{ props.spirit.attributes.base_attack }}</span>
        </div>
        <div class="stat-row">
          <span>防御力:</span> 
          <span class="stat-value">{{ props.spirit.attributes.base_pdef }} / {{ props.spirit.attributes.base_mdef }}</span>
        </div>
        <div class="stat-row">
          <span>速度:</span> 
          <span class="stat-value">{{ props.spirit.attributes.speed }}</span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SpiritPrototype, SkillInfo } from '../../types/spirit'
import {
  Star,
} from '@element-plus/icons-vue'

interface Props {
  spirit: SpiritPrototype
  isSelected?: boolean
  showActions?: boolean
  allowRemove?: boolean
  isDragging?: boolean
  tooltipDisabled?: boolean
}

interface Emits {
  (e: 'click', spirit: SpiritPrototype): void
  (e: 'contextmenu', spirit: SpiritPrototype): void
  (e: 'details', spirit: SpiritPrototype): void
  (e: 'remove', spirit: SpiritPrototype): void
  (e: 'dragstart', spirit: SpiritPrototype): void
  (e: 'dragend', spirit: SpiritPrototype): void
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  showActions: true,
  allowRemove: true,
  isDragging: false,
  tooltipDisabled: false,
})

const emit = defineEmits<Emits>()

const showTooltip = ref(false)
const cardRef = ref<HTMLElement>()

const tooltipStyle = computed(() => {
  if (!cardRef.value) return {}
  const rect = cardRef.value.getBoundingClientRect()
  return {
    position: 'fixed',
    top: `${rect.bottom + 10}px`,
    left: `${rect.left}px`,
    zIndex: 99999,
  }
})

const handleMouseEnter = () => {
  if (!props.tooltipDisabled) {
    showTooltip.value = true
  }
}

const handleMouseLeave = () => {
  showTooltip.value = false
}

const handleDragStart = (event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    const payload = {
      spiritId: props.spirit.id,
      spiritName: props.spirit.name_prefix,
      spiritTeam: props.spirit.team
    };
    event.dataTransfer.setData('application/json', JSON.stringify(payload))
  }
  emit('dragstart', props.spirit)
}

const handleDragEnd = () => {
  emit('dragend', props.spirit)
}

const getElementClass = (element: string) => {
  // Special colors for specific elements
  if (element === '创') {
    return 'element-creator'; // Use a special class for '创'
  }
  if (element === '空') {
    return 'element-void'; // Use a special class for '空'
  }
  return `element-${element.toLowerCase()}`;
};

const getProfessionClass = (profession: string) => {
  // Simplified: just return a generic class and a specific one for default styling
  return [`profession-${profession.toLowerCase()}`, 'profession-tag'];
};

const getElementIcon = (element: string) => {
  // Return icon component based on element string
  return 'Star' // Placeholder
}

const getElementLabel = (element: string) => {
  return element;
}

const getProfessionLabel = (profession: string) => {
  return profession;
}

const heroSkills = ["天籁之音", "神谕交响"]; // Hardcoded hero skills
const shenyaoSkills = ["万象归元"]; // Hardcoded shenyao skills

const getSkillChipClass = (skill: SkillInfo) => {
  const classes = ['skill-chip'];
  if (heroSkills.includes(skill.name)) {
    classes.push('skill-hero');
  } else if (shenyaoSkills.includes(skill.name)) {
    classes.push('skill-shenyao');
  } else {
    switch (skill.cast_type) {
      case '奥义': // Ultimate
        classes.push('skill-ultimate');
        break;
      case '被动': // Passive
        classes.push('skill-passive');
        break;
      case '主动': // Active
        if (skill.category === '攻击') {
          classes.push('skill-attack');
        }
        break;
      default:
        break;
    }
  }
  return classes;
};
</script>

<style scoped>
.spirit-card {
  background: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 8px;
  padding: 1rem;
  color: #e2e8f0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.spirit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.4);
}
.shenge-level {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}
.spirit-info {
  text-align: center;
  margin-bottom: 1rem;
}
.spirit-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}
.tags-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap; /* Allow tags to wrap */
  gap: 0.5rem;
  margin-bottom: 1rem;
}
.tag {
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center; /* Center the text */
  white-space: nowrap; /* Prevent text wrapping */
}
.element-tag {
  background: #4a5568;
  color: #a0aec0;
}
.profession-tag {
  padding: 0.2rem 0.8rem; /* Add more horizontal padding */
  background: #4a5568; /* Default background for unstyled professions */
  color: #a0aec0;
}
.separator {
  height: 1px;
  background-color: #4a5568; /* A subtle line color */
  margin: 1rem 0; /* Space above and below the line */
  opacity: 0.5;
}
.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.skill-chip {
  background-color: #4a5568; /* A slightly different background */
  color: #e2e8f0;
  padding: 0.3rem 0.6rem;
  border-radius: 16px; /* More rounded to look like a chip */
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  flex-shrink: 0;
  transition: background-color 0.2s;
}

.skill-chip:hover {
  background-color: #718096; /* Highlight on hover */
}

/* --- Skill Type Colors --- */

/* 英雄技 (Hero): Blue */
.skill-hero {
  background-color: #3182ce; /* Blue 600 */
  color: white;
}
.skill-hero:hover {
  background-color: #2b6cb0; /* Blue 700 */
}

/* 普通攻击 (Normal Attack): White */
.skill-attack {
  background-color: #a0aec0; /* Gray 500 */
  color: #1a202c;
}
.skill-attack:hover {
  background-color: #cbd5e0; /* Gray 400 */
}

/* 超杀 (Ultimate): Orange */
.skill-ultimate {
  background-color: #dd6b20; /* Orange 600 */
  color: white;
}
.skill-ultimate:hover {
  background-color: #c05621; /* Orange 700 */
}

/* 神曜技 (Shenyao): Gold */
.skill-shenyao {
  background: linear-gradient(45deg, #f6e05e, #ecc94b);
  color: #422006;
  border: 1px solid #f6e05e;
}
.skill-shenyao:hover {
  background: linear-gradient(45deg, #f6e36f, #eec04b);
}


/* 被动 (Passive): Purple */
.skill-passive {
  background-color: #805ad5; /* Purple 600 */
  color: white;
}
.skill-passive:hover {
  background-color: #6b46c1; /* Purple 700 */
}

/* --- Element Type Colors --- */
/* Special colors first */
.element-creator {
  background: linear-gradient(45deg, #fbc2eb 0%, #a6c1ee 100%);
  color: #1a202c;
  font-weight: 600;
}
.element-void {
  background-color: #718096; /* Gray 600 */
  color: #e2e8f0;
}

.element-火 { background-color: #e53e3e; color: white; } /* Red */
.element-水 { background-color: #3182ce; color: white; } /* Blue */
.element-草 { background-color: #38a169; color: white; } /* Green */
.element-光 { background-color: #f6e05e; color: #2d3748; } /* Yellow */
.element-暗 { background-color: #5a32a3; color: white; } /* Purple */


/* --- Profession Type Colors --- */
/* Remove old profession colors for creator/void */
</style>