#!/usr/bin/env python3
"""
直接测试组件
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_component_direct():
    """直接测试组件"""
    print("🔧 直接测试组件...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        print(f"📊 测试精灵: {spirit1.name}")
        
        # 设置气势为300
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置气势为: {spirit1.energy}")
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 直接创建组件并测试
        try:
            from spirits_data.天恩圣祭·空灵圣龙.skill_components import JianYanJingFuUltimateDamageComponent
            print("  ✅ 成功导入组件")
            
            component = JianYanJingFuUltimateDamageComponent()
            print("  ✅ 成功创建组件")
            
            # 创建模拟的SkillContext
            class MockSkillContext:
                def __init__(self):
                    self.is_ultimate = True
                    self.skill_level = 1
                    self.is_critical = False
                    self.additional_data = {'skill_name': '缄言净缚'}
            
            context = MockSkillContext()
            targets = [spirit2]
            
            # 直接调用组件
            actions = component.execute(spirit1, targets, battle_state, context)
            print(f"  生成动作数量: {len(actions)}")
            
            # 检查动作类型
            energy_consumed = False
            damage_actions = []
            
            for i, action in enumerate(actions):
                action_type = type(action).__name__
                print(f"    动作 {i}: {action_type}")
                
                if action_type == 'ConsumeEnergyAction':
                    energy_consumed = True
                    consume_amount = getattr(action, 'amount', 0)
                    print(f"      消耗气势: {consume_amount}")
                
                elif action_type == 'DamageAction':
                    damage_actions.append(action)
                    overflow_energy = getattr(action, 'overflow_energy', None)
                    ultimate_threshold = getattr(action, 'ultimate_threshold', None)
                    is_ultimate = getattr(action, 'is_ultimate', False)
                    print(f"      是否超杀: {is_ultimate}")
                    print(f"      溢出气势: {overflow_energy}")
                    print(f"      超杀阈值: {ultimate_threshold}")
                
                elif action_type == 'LogAction':
                    message = getattr(action, 'message', '')
                    print(f"      日志: {message}")
            
            # 检查结果
            if energy_consumed:
                print(f"  ✅ 检测到气势消耗动作")
            else:
                print(f"  ❌ 没有检测到气势消耗动作")
            
            if damage_actions:
                for j, damage_action in enumerate(damage_actions):
                    overflow_energy = getattr(damage_action, 'overflow_energy', None)
                    ultimate_threshold = getattr(damage_action, 'ultimate_threshold', None)
                    if overflow_energy is not None and ultimate_threshold is not None:
                        print(f"  ✅ 伤害动作 {j} 包含完整的超杀信息")
                        
                        # 计算期望的气势系数
                        expected_q_coeff = 1.0 + (overflow_energy / ultimate_threshold)
                        print(f"    期望气势系数: {expected_q_coeff:.3f}")
                        
                        return True
                    else:
                        print(f"  ❌ 伤害动作 {j} 缺少超杀信息")
            
            return len(actions) > 0
            
        except ImportError as e:
            print(f"  ❌ 导入组件失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 直接组件测试")
    print("="*60)
    
    result = test_component_direct()
    
    print("\n" + "="*60)
    print("📊 测试结果:")
    print("="*60)
    
    if result:
        print("✅ 测试通过！组件正常工作")
        print("\n📋 验证内容:")
        print("  ✅ 组件正确导入和创建")
        print("  ✅ 气势消耗动作存在")
        print("  ✅ 伤害动作包含溢出气势信息")
        print("  ✅ 气势系数计算正确")
    else:
        print("❌ 测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
