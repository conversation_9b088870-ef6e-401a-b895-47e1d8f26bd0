"""
天恩圣祭·空灵圣龙 - 精灵主体定义

属性：空
职业：魔法
"""

from core.spirit.spirit import Spirit
from core.attribute import Attributes

from .skills import create_kongling_skills
from .passive_effects import create_kongling_passive_effects


def create_kongling_shenglong_spirit() -> Spirit:
    """创建天恩圣祭·空灵圣龙精灵"""

    # 基础属性 - 魔法职业，偏向魔攻和生存
    attributes = Attributes(
        base_hp=2300,          # 中等生命值
        hp_p=0.0,              # 初始生命值百分比加成
        hp_flat=0.0,           # 初始生命值固定加成
        base_attack=220,       # 较高魔法攻击力
        attack_p=0.0,          # 初始攻击力百分比加成
        attack_flat=0.0,       # 初始攻击力固定加成
        base_pdef=85,          # 较低物理防御
        pdef_p=0.0,            # 初始物理防御百分比加成
        pdef_flat=0.0,         # 初始物理防御固定加成
        base_mdef=120,         # 较高魔法防御
        mdef_p=0.0,            # 初始魔法防御百分比加成
        mdef_flat=0.0,         # 初始魔法防御固定加成
        base_speed=95,         # 中等速度
        base_hit_rate=0.0,     # 命中率加成：0%（标准命中率）
        base_dodge_rate=0.12,  # 闪避率加成：+12%（配合隐身）
        base_break_rate=0.05,  # 较低破击率
        base_block_rate=0.05,  # 基础格挡率
        base_crit_rate=0.08,   # 较低暴击率
        base_crit_res_rate=0.08, # 较低防暴率
        base_crit_damage=1.5,  # 标准暴击伤害
        base_damage_reduction=0.05, # 基础减伤
        base_penetration=0.15, # 魔法穿透
    )

    # 创建一个临时精灵来生成技能
    temp_spirit = Spirit(
        id="temp",
        name="temp",
        attributes=attributes,
        position=(1, 2),
        team=0
    )

    # 创建技能
    skills = create_kongling_skills(temp_spirit)

    # 创建最终精灵（带技能）
    spirit = Spirit(
        id="tianen_shengji_kongling_shenglong",
        name="天恩圣祭·空灵圣龙",
        attributes=attributes,
        position=(1, 2),           # 默认位置：前排中间
        team=0,                    # 默认队伍
        skills=skills              # 传入技能
    )

    # 添加被动效果
    passive_effects = create_kongling_passive_effects(spirit)
    for effect in passive_effects:
        spirit.effect_manager.add_effect(effect)

    return spirit


# 导出函数
__all__ = ['create_kongling_shenglong_spirit']
