#!/usr/bin/env python3
"""
简单的EnhancedAttackAction测试

验证EnhancedAttackAction处理器是否正确注册
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_handler_registration():
    """测试处理器注册"""
    print("🔧 测试EnhancedAttackAction处理器注册...")
    
    try:
        # 导入必要的模块
        from core.battle.execution import _handler_registry, enhanced_actions
        from core.action import EnhancedAttackAction
        
        print(f"✅ 成功导入模块")
        print(f"📊 当前注册的处理器数量: {len(_handler_registry)}")
        
        # 检查EnhancedAttackAction是否已注册
        if EnhancedAttackAction in _handler_registry:
            handler_func = _handler_registry[EnhancedAttackAction]
            print(f"✅ EnhancedAttackAction处理器已注册: {handler_func.__name__}")
            
            # 显示处理器的模块信息
            print(f"  - 处理器模块: {handler_func.__module__}")
            print(f"  - 处理器文档: {handler_func.__doc__[:50] if handler_func.__doc__ else 'None'}...")
            
            return True
        else:
            print("❌ EnhancedAttackAction处理器未注册")
            print("📋 已注册的动作类型:")
            for i, action_type in enumerate(_handler_registry.keys(), 1):
                print(f"  {i:2d}. {action_type.__name__}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_action_creation():
    """测试动作创建"""
    print("\n🔧 测试EnhancedAttackAction创建...")
    
    try:
        from core.action import EnhancedAttackAction
        
        # 创建模拟对象
        class MockEntity:
            def __init__(self, name):
                self.name = name
                self.id = name.lower()
        
        class MockSkill:
            def __init__(self, name, damage=100):
                self.name = name
                self.base_damage = damage
        
        caster = MockEntity("攻击者")
        target = MockEntity("目标")
        skill = MockSkill("测试技能", 150)
        
        # 创建EnhancedAttackAction
        enhanced_action = EnhancedAttackAction(
            caster=caster,
            target=target,
            skill=skill,
            conditional_effects={
                'damage_multiplier': 1.3,
                'critical_hit': True,
                'pierce_hit': False
            }
        )
        
        print(f"✅ EnhancedAttackAction创建成功")
        print(f"  - 攻击者: {enhanced_action.caster.name}")
        print(f"  - 目标: {enhanced_action.target.name}")
        print(f"  - 技能: {enhanced_action.skill.name}")
        print(f"  - 条件性效果数量: {len(enhanced_action.conditional_effects)}")
        print(f"  - 增强伤害: {enhanced_action.get_enhanced_damage()}")
        
        return True
        
    except Exception as e:
        print(f"❌ EnhancedAttackAction创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_handler_function():
    """测试处理器函数"""
    print("\n🔧 测试EnhancedAttackAction处理器函数...")
    
    try:
        from core.battle.execution import _handler_registry
        from core.action import EnhancedAttackAction
        
        if EnhancedAttackAction not in _handler_registry:
            print("❌ 处理器未注册")
            return False
        
        handler_func = _handler_registry[EnhancedAttackAction]
        
        # 检查处理器函数的签名
        import inspect
        sig = inspect.signature(handler_func)
        params = list(sig.parameters.keys())
        
        print(f"✅ 处理器函数信息:")
        print(f"  - 函数名: {handler_func.__name__}")
        print(f"  - 参数: {params}")
        print(f"  - 返回类型: {sig.return_annotation}")
        
        # 检查是否有必要的装饰器
        if hasattr(handler_func, '__wrapped__'):
            print(f"  - 有装饰器包装")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 简单EnhancedAttackAction处理器测试")
    print("="*60)
    
    tests = [
        ("处理器注册测试", test_handler_registration),
        ("动作创建测试", test_action_creation),
        ("处理器函数测试", test_handler_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 验证结果:")
        print("  ✅ EnhancedAttackAction处理器已正确注册")
        print("  ✅ EnhancedAttackAction可以正常创建")
        print("  ✅ 处理器函数结构正确")
        print("\n🚀 EnhancedAttackAction处理器修复成功！")
        print("💡 之前的'没有为 EnhancedAttackAction 注册处理器'错误已解决")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
