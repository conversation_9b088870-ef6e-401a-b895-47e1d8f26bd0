"""
受击加气系统

当精灵受到直接攻击伤害时，获得气势奖励。
只有直接攻击（物理、魔法、毁灭）才触发，间接伤害（持续伤害、反射伤害等）不触发。
"""
from __future__ import annotations
from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from ...interfaces import IBattleEntity, IBattleState
    from ...event.events import AfterDamageEvent
    from ...action import DamageAction

from ...logging import battle_logger
from ...action import DamageType


class HitEnergyBonus:
    """受击加气奖励管理器"""
    
    def __init__(self, energy_bonus: int = 20):
        """
        初始化受击加气系统
        
        Args:
            energy_bonus: 受击获得的气势奖励（默认20点）
        """
        self.energy_bonus = energy_bonus
        self.total_bonus_given = 0  # 统计总共给出的奖励
        self.hit_count = 0  # 统计触发次数
    
    def should_trigger_bonus(self, damage_event: 'AfterDamageEvent') -> bool:
        """
        判断是否应该触发受击加气奖励
        
        Args:
            damage_event: 伤害后事件
            
        Returns:
            是否应该触发奖励
        """
        # 1. 必须实际造成了伤害
        if damage_event.damage_dealt <= 0:
            return False
        
        # 2. 目标必须存活（死亡的精灵不获得气势）
        if not damage_event.target.is_alive:
            return False
        
        # 3. 必须有攻击者（自然伤害等不触发）
        if not damage_event.attacker:
            return False
        
        # 4. 攻击者和目标不能是同一个（自伤不触发）
        if damage_event.attacker.id == damage_event.target.id:
            return False
        
        # 5. 必须是直接攻击伤害
        return self._is_direct_attack_damage(damage_event)
    
    def _is_direct_attack_damage(self, damage_event: 'AfterDamageEvent') -> bool:
        """
        判断是否为直接攻击伤害
        
        Args:
            damage_event: 伤害后事件
            
        Returns:
            是否为直接攻击伤害
        """
        # 从事件中获取伤害动作信息
        # 注意：这里需要根据实际的事件结构来获取伤害类型信息
        
        # 方法1：如果事件中有伤害动作引用
        if hasattr(damage_event, 'damage_action'):
            damage_action = damage_event.damage_action
            if damage_action:
                # 检查是否标记为间接伤害
                if getattr(damage_action, 'is_indirect', False):
                    return False
                
                # 检查伤害类型
                damage_type = getattr(damage_action, 'damage_type', None)
                if damage_type:
                    return DamageType.get_damage_type_category(damage_type) == "direct"
        
        # 方法2：如果事件中有伤害类型信息
        if hasattr(damage_event, 'damage_type'):
            return DamageType.get_damage_type_category(damage_event.damage_type) == "direct"
        
        # 方法3：如果事件中有间接伤害标记
        if hasattr(damage_event, 'is_indirect'):
            return not damage_event.is_indirect
        
        # 方法4：通过技能名称判断（备用方案）
        skill_name = damage_event.skill_name
        if skill_name:
            # 一些明显的间接伤害技能关键词
            indirect_keywords = ['持续', '反射', '溅射', '扩散', '燃烧', '中毒', '流血', '诅咒']
            skill_name_lower = skill_name.lower()
            for keyword in indirect_keywords:
                if keyword in skill_name_lower:
                    return False
        
        # 默认情况下，如果无法确定，认为是直接攻击
        # 这样可以确保大部分正常攻击都能触发受击加气
        battle_logger.debug(f"无法确定伤害类型，默认认为是直接攻击: {skill_name}")
        return True
    
    def apply_hit_energy_bonus(self, damage_event: 'AfterDamageEvent') -> int:
        """
        应用受击加气奖励
        
        Args:
            damage_event: 伤害后事件
            
        Returns:
            实际获得的气势奖励
        """
        if not self.should_trigger_bonus(damage_event):
            return 0
        
        target = damage_event.target
        attacker = damage_event.attacker
        
        # 应用气势奖励
        if hasattr(target, 'gain_energy'):
            actual_gain = target.gain_energy(self.energy_bonus)
            
            # 更新统计
            self.total_bonus_given += actual_gain
            self.hit_count += 1
            
            battle_logger.info(
                f"💥 受击加气：{target.name} 受到 {attacker.name} 的直接攻击，"
                f"获得 {actual_gain} 点气势 (当前: {target.energy}/{target.max_energy})"
            )
            
            return actual_gain
        else:
            battle_logger.warning(f"精灵 {target.name} 不支持气势系统，无法获得受击加气奖励")
            return 0
    
    def get_statistics(self) -> dict:
        """获取受击加气统计信息"""
        return {
            'energy_bonus_per_hit': self.energy_bonus,
            'total_bonus_given': self.total_bonus_given,
            'hit_count': self.hit_count,
            'average_bonus_per_hit': self.total_bonus_given / max(1, self.hit_count)
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_bonus_given = 0
        self.hit_count = 0


class HitEnergyBonusEventHandler:
    """受击加气事件处理器"""
    
    def __init__(self, bonus_manager: Optional[HitEnergyBonus] = None):
        """
        初始化事件处理器
        
        Args:
            bonus_manager: 奖励管理器，如果为None则创建默认的
        """
        self.bonus_manager = bonus_manager or HitEnergyBonus()
    
    def handle_after_damage_event(self, event: 'AfterDamageEvent') -> int:
        """
        处理伤害后事件，应用受击加气奖励
        
        Args:
            event: 伤害后事件
            
        Returns:
            获得的气势奖励
        """
        return self.bonus_manager.apply_hit_energy_bonus(event)
    
    def get_bonus_manager(self) -> HitEnergyBonus:
        """获取奖励管理器"""
        return self.bonus_manager


# 全局实例（可选）
_global_hit_energy_handler = None

def get_global_hit_energy_handler() -> HitEnergyBonusEventHandler:
    """获取全局受击加气处理器"""
    global _global_hit_energy_handler
    if _global_hit_energy_handler is None:
        _global_hit_energy_handler = HitEnergyBonusEventHandler()
    return _global_hit_energy_handler

def set_global_hit_energy_bonus(energy_bonus: int):
    """设置全局受击加气奖励数量"""
    handler = get_global_hit_energy_handler()
    handler.bonus_manager.energy_bonus = energy_bonus
