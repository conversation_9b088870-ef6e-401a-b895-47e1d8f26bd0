from __future__ import annotations
from typing import Dict, Tuple, Optional, List, Any

# 第三方库导入
# from sqlalchemy import Column, Integer, String, JSON  # 暂时注释掉，避免循环导入

# 本地导入
from ..interfaces import IBattleEntity
from ..spirit import Spirit
# from src.database import Base  # 暂时注释掉，避免循环导入

"""阵型系统。

本模块定义了战斗中使用的阵型结构。

主要组件:
- `Formation`: 一个运行时类，用于表示和管理战斗中一个3x3的精灵网格。
"""

try:
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    Column = Integer = String = JSON = None



class Formation:
    """
    表示一个3x3的战斗阵型。

    管理精灵在网格中的位置，并提供用于查询阵型状态的辅助方法。

    Attributes:
        grid (Dict[Tuple[int, int], Optional[Spirit]]): 
            一个字典，将 (row, col) 坐标映射到该位置的精灵或None。
            坐标范围: row (1-3), col (1-3)。
    """

    def __init__(self):
        """初始化一个空的3x3阵型。"""
        self.grid: Dict[Tuple[int, int], Optional[Spirit]] = {
            (r, c): None for r in range(1, 4) for c in range(1, 4)
        }

    def add_spirit(self, spirit: Spirit, row: int, col: int) -> None:
        """
        在阵型的指定位置添加一个精灵。

        Args:
            spirit: 要添加的精灵实例。
            row: 目标行 (1-3)。
            col: 目标列 (1-3)。
        
        Raises:
            ValueError: 如果提供的行或列超出范围 (1-3)。
        """
        if not (1 <= row <= 3 and 1 <= col <= 3):
            raise ValueError("阵型位置的行和列必须在 1-3 的范围内。")
        self.grid[(row, col)] = spirit

    def get_spirit_at(self, row: int, col: int) -> Optional[Spirit]:
        """获取指定位置的精灵。"""
        return self.grid.get((row, col))
    
    def get_all_spirits(self) -> List[Spirit]:
        """获取阵型中的所有精灵（包括死亡的）"""
        spirits = []
        for spirit in self.grid.values():
            if spirit is not None:
                spirits.append(spirit)
        return spirits

    def get_living_spirits(self) -> List[IBattleEntity]:
        """
        以标准优先级顺序获取所有存活的精灵列表。

        顺序: 前排到后排, 同排内从上到下。
        (1,1), (1,2), (1,3), (2,1), ...

        Returns:
            一个按顺序排序的存活精灵列表。
        """
        living_spirits: List[IBattleEntity] = []
        for r in range(1, 4):
            for c in range(1, 4):
                spirit = self.grid.get((r, c))
                if spirit and spirit.is_alive:
                    living_spirits.append(spirit)
        return living_spirits

    def is_position_empty(self, position: Tuple[int, int]) -> bool:
        """
        检查指定位置是否为空。

        Args:
            position: 要检查的位置 (row, col)。

        Returns:
            如果位置为空则返回 True，否则返回 False。
        """
        return self.grid.get(position) is None

    def find_first_empty_slot(self) -> Optional[Tuple[int, int]]:
        """
        按标准顺序查找第一个可用的空位。

        Returns:
            第一个空位的 (row, col) 坐标，如果阵型已满则返回 None。
        """
        for r in range(1, 4):
            for c in range(1, 4):
                if self.grid.get((r, c)) is None:
                    return (r, c)
        return None

    def to_dict(self) -> Dict[str, Any]:
        """将阵型对象序列化为字典。"""
        grid_data = {}
        for pos, spirit in self.grid.items():
            grid_data[f"{pos[0]},{pos[1]}"] = spirit.to_dict() if spirit else None
        return {"grid": grid_data}

    def __repr__(self) -> str:
        """返回阵型的字符串表示形式，用于调试。"""
        formation_str = ""
        for r in range(1, 4):
            row_str = []
            for c in range(1, 4):
                spirit = self.grid.get((r, c))
                row_str.append(spirit.name if spirit else "-")
            formation_str += " ".join(f"{s:<10}" for s in row_str) + "\n"
        return formation_str


# 暂时注释掉数据库相关类，避免循环导入
# class SavedFormation(Base):
#     """用户保存的阵型 ORM 模型。"""
#     __tablename__ = "saved_formations"
#     __table_args__ = {'extend_existing': True}
#
#     id = Column(Integer, primary_key=True, index=True)
#     name = Column(String, unique=True, nullable=False)
#     data = Column(JSON, nullable=False)


__all__ = [
    'Formation',
    # 'SavedFormation'  # 暂时注释掉
]
