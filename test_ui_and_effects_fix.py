#!/usr/bin/env python3
"""
测试UI和效果系统修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_and_effects_fix():
    """测试UI和效果系统修复"""
    print("🔧 测试UI和效果系统修复...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        # 创建伏妖和其他精灵
        fuyao_spirit = None
        other_spirit = None
        
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            fuyao_spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        other_spirit = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1 = Formation()
        formation2 = Formation()
        
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=3,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗引擎创建成功")
        
        # 测试1: UI精灵切换修复
        print(f"\n📋 测试1: UI精灵切换修复")
        
        spirit_turn_count = 0
        round_end_count = 0
        battle_end_count = 0
        
        for i in range(8):  # 执行多次精灵回合
            print(f"\n--- 执行第 {i+1} 次精灵回合 ---")
            
            result = engine.execute_next_spirit_turn()
            result_type = result.get("type", "unknown")
            message = result.get("message", "")
            
            print(f"结果类型: {result_type}")
            print(f"消息: {message}")
            
            if result_type == "spirit_turn":
                spirit_turn_count += 1
                spirit_name = result.get("spirit_name", "Unknown")
                actions_count = result.get("actions_generated", 0)
                print(f"  精灵: {spirit_name}, 动作: {actions_count}")
                
            elif result_type == "round_end":
                round_end_count += 1
                round_num = result.get("round_num", 0)
                print(f"  回合结束: 第{round_num}回合")
                
                # 🔧 测试：回合结束后应该能继续执行下一只精灵
                print(f"  ✅ 回合结束检测正常，应该能继续执行")
                
            elif result_type == "battle_end":
                battle_end_count += 1
                winner = result.get("winner", -1)
                print(f"  战斗结束: 获胜方 {winner}")
                break
                
            elif result_type == "no_spirits":
                print(f"  没有存活的精灵")
                break
        
        print(f"\n📊 UI切换测试结果:")
        print(f"  精灵回合: {spirit_turn_count}")
        print(f"  回合结束: {round_end_count}")
        print(f"  战斗结束: {battle_end_count}")
        
        ui_fix_success = spirit_turn_count > 0 and round_end_count > 0
        
        if ui_fix_success:
            print(f"  ✅ UI精灵切换修复成功！")
        else:
            print(f"  ❌ UI精灵切换仍有问题")
        
        # 测试2: 效果系统修复
        print(f"\n📋 测试2: 效果系统修复")
        
        # 检查伏妖是否有虚无效果相关的被动技能
        fuyao_effects_count = 0
        if hasattr(fuyao_spirit, 'effect_manager') and fuyao_spirit.effect_manager:
            fuyao_effects_count = len(fuyao_spirit.effect_manager.effects)
            print(f"  伏妖当前效果数量: {fuyao_effects_count}")
            
            for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"    - {effect_name} (ID: {effect_id})")
        
        other_effects_count = 0
        if hasattr(other_spirit, 'effect_manager') and other_spirit.effect_manager:
            other_effects_count = len(other_spirit.effect_manager.effects)
            print(f"  {other_spirit.name} 当前效果数量: {other_effects_count}")
            
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"    - {effect_name} (ID: {effect_id})")
        
        # 测试虚无效果的触发条件
        print(f"\n🔍 测试虚无效果触发机制:")
        
        # 导入虚无效果
        try:
            from spirits_data.神曜虚无·伏妖.effects import XuWuStateEffect
            
            # 创建一个测试虚无效果
            test_xuwu_effect = XuWuStateEffect(fuyao_spirit, other_spirit, duration=2)
            
            # 检查触发条件
            trigger_conditions = test_xuwu_effect.get_trigger_conditions()
            print(f"  虚无效果触发条件数量: {len(trigger_conditions)}")
            
            for condition in trigger_conditions:
                condition_type = getattr(condition, 'event_type', 'Unknown')
                print(f"    - 触发条件: {condition_type}")
            
            # 测试回合结束触发
            if trigger_conditions:
                event_data = {
                    "event_type": "ROUND_END",
                    "round_num": 1,
                    "spirit": other_spirit
                }
                
                try:
                    trigger_result = test_xuwu_effect.on_triggered(event_data, engine.battle_state)
                    if trigger_result and hasattr(trigger_result, 'actions'):
                        print(f"  ✅ 虚无效果回合结束触发成功，生成 {len(trigger_result.actions)} 个动作")
                        effects_working = True
                    else:
                        print(f"  ❌ 虚无效果回合结束触发失败")
                        effects_working = False
                except Exception as e:
                    print(f"  ❌ 虚无效果触发异常: {e}")
                    effects_working = False
            else:
                print(f"  ❌ 虚无效果没有触发条件")
                effects_working = False
                
        except Exception as e:
            print(f"  ❌ 虚无效果导入失败: {e}")
            effects_working = False
        
        print(f"\n📊 效果系统测试结果:")
        print(f"  伏妖效果数量: {fuyao_effects_count}")
        print(f"  其他精灵效果数量: {other_effects_count}")
        print(f"  虚无效果触发: {'✅ 正常' if effects_working else '❌ 异常'}")
        
        overall_success = ui_fix_success and effects_working
        
        if overall_success:
            print(f"\n✅ UI和效果系统修复完成！")
            print(f"  📋 修复内容:")
            print(f"    1. UI精灵切换：回合结束后继续执行下一只精灵")
            print(f"    2. 效果系统：修复了回合结束事件的触发机制")
            print(f"    3. 虚无效果：现在应该能正确触发和生效")
            print(f"  🎯 现在用户可以:")
            print(f"    - 正常切换精灵而不会直接跳到战斗结束")
            print(f"    - 看到虚无效果和其他效果的实际作用")
        else:
            print(f"\n❌ 修复未完全成功")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 UI和效果系统修复测试")
    print("="*60)
    
    result = test_ui_and_effects_fix()
    
    print("\n" + "="*60)
    if result:
        print("✅ UI和效果系统修复验证成功")
        print("\n🎉 两个严重问题都已解决！")
        print("现在UI和效果系统都正常工作")
    else:
        print("❌ 修复验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
