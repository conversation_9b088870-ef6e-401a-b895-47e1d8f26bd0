<template>
  <div class="battle-effects pointer-events-none w-full h-full flex items-center justify-center">
  </div>
</template>

<script setup lang="ts">
/**
 * BattleEffects.vue
 * 战斗特效层，负责展示技能、伤害等战斗特效
 */

defineProps<{ actions?: any[] }>()
</script>

<style scoped>
.battle-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}
</style> 