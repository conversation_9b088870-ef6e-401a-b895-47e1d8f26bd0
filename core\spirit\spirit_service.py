"""
统一精灵服务

提供统一的精灵创建、管理和查询接口，整合 JSON 配置系统和传统注册表系统
"""
from __future__ import annotations
from typing import List, Optional, Dict, Any, Callable
import logging

from .json_factory import get_json_spirit_factory
from .registry import spirit_registry
from .spirit import Spirit

logger = logging.getLogger(__name__)


class SpiritService:
    """统一精灵服务"""
    
    def __init__(self):
        self.json_factory = get_json_spirit_factory()
        self._cache = {}
    
    def create_spirit(self, spirit_id: str, team: int = 0, position: Optional[tuple] = None) -> Optional[Spirit]:
        """
        创建精灵，优先使用 JSON 配置系统
        
        Args:
            spirit_id: 精灵ID
            team: 队伍编号
            position: 位置
            
        Returns:
            精灵实例，如果创建失败返回 None
        """
        # 优先尝试使用 JSON 配置系统
        try:
            spirit = self.json_factory.create_spirit(spirit_id, team, position)
            if spirit:
                logger.info(f"通过 JSON 配置创建精灵: {spirit_id}")
                return spirit
        except Exception as e:
            logger.warning(f"JSON 配置创建精灵失败 {spirit_id}: {e}")
        
        # 回退到传统的注册表方式
        try:
            creator = spirit_registry.get_creator(spirit_id)
            if creator:
                spirit = creator()
                spirit.team = team
                if position:
                    spirit.position = position
                logger.info(f"通过注册表创建精灵: {spirit_id}")
                return spirit
        except Exception as e:
            logger.warning(f"注册表创建精灵失败 {spirit_id}: {e}")
        
        logger.error(f"无法创建精灵: {spirit_id}")
        return None
    
    def list_available_spirits(self) -> List[str]:
        """列出所有可用的精灵ID"""
        available_spirits = set()
        
        # 从 JSON 配置系统获取
        try:
            json_spirits = self.json_factory.list_available_spirits()
            available_spirits.update(json_spirits)
        except Exception as e:
            logger.warning(f"获取 JSON 配置精灵列表失败: {e}")
        
        # 从注册表获取
        try:
            registry_spirits = spirit_registry.list_spirits()
            available_spirits.update(registry_spirits)
        except Exception as e:
            logger.warning(f"获取注册表精灵列表失败: {e}")
        
        return sorted(list(available_spirits))
    
    def get_spirit_info(self, spirit_id: str) -> Optional[Dict[str, Any]]:
        """
        获取精灵信息
        
        Args:
            spirit_id: 精灵ID
            
        Returns:
            精灵信息字典，如果不存在返回 None
        """
        # 优先从 JSON 配置获取
        try:
            config = self.json_factory.loader.load_spirit_config(spirit_id)
            if config:
                return {
                    "id": config.id,
                    "name": config.name,
                    "element": config.element,
                    "professions": config.professions,
                    "tags": config.tags,
                    "shenge_level": config.shenge_level,
                    "skills_count": len(config.skills),
                    "source": "json_config"
                }
        except Exception as e:
            logger.warning(f"从 JSON 配置获取精灵信息失败 {spirit_id}: {e}")
        
        # 从注册表获取
        try:
            spirit_info = spirit_registry.get_spirit_info(spirit_id)
            if spirit_info:
                spirit_info["source"] = "registry"
                return spirit_info
        except Exception as e:
            logger.warning(f"从注册表获取精灵信息失败 {spirit_id}: {e}")
        
        return None
    
    def spirit_exists(self, spirit_id: str) -> bool:
        """检查精灵是否存在"""
        return spirit_id in self.list_available_spirits()
    
    def get_spirits_by_element(self, element: str) -> List[str]:
        """根据元素类型获取精灵列表"""
        spirits = []
        for spirit_id in self.list_available_spirits():
            info = self.get_spirit_info(spirit_id)
            if info and info.get("element") == element:
                spirits.append(spirit_id)
        return spirits
    
    def get_spirits_by_profession(self, profession: str) -> List[str]:
        """根据职业获取精灵列表"""
        spirits = []
        for spirit_id in self.list_available_spirits():
            info = self.get_spirit_info(spirit_id)
            if info and profession in info.get("professions", []):
                spirits.append(spirit_id)
        return spirits
    
    def get_spirits_by_tag(self, tag: str) -> List[str]:
        """根据标签获取精灵列表"""
        spirits = []
        for spirit_id in self.list_available_spirits():
            info = self.get_spirit_info(spirit_id)
            if info and tag in info.get("tags", []):
                spirits.append(spirit_id)
        return spirits
    
    def validate_spirit_config(self, spirit_id: str) -> Dict[str, Any]:
        """
        验证精灵配置
        
        Args:
            spirit_id: 精灵ID
            
        Returns:
            验证结果字典
        """
        result = {
            "spirit_id": spirit_id,
            "exists": False,
            "json_config": False,
            "registry": False,
            "can_create": False,
            "errors": []
        }
        
        # 检查 JSON 配置
        try:
            config = self.json_factory.loader.load_spirit_config(spirit_id)
            if config:
                result["json_config"] = True
                result["exists"] = True
        except Exception as e:
            result["errors"].append(f"JSON 配置错误: {e}")
        
        # 检查注册表
        try:
            creator = spirit_registry.get_creator(spirit_id)
            if creator:
                result["registry"] = True
                result["exists"] = True
        except Exception as e:
            result["errors"].append(f"注册表错误: {e}")
        
        # 测试创建
        try:
            spirit = self.create_spirit(spirit_id)
            if spirit:
                result["can_create"] = True
        except Exception as e:
            result["errors"].append(f"创建测试失败: {e}")
        
        return result
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            "json_factory_available": False,
            "registry_available": False,
            "total_spirits": 0,
            "json_spirits": 0,
            "registry_spirits": 0,
            "errors": []
        }
        
        # 检查 JSON 工厂
        try:
            json_spirits = self.json_factory.list_available_spirits()
            status["json_factory_available"] = True
            status["json_spirits"] = len(json_spirits)
        except Exception as e:
            status["errors"].append(f"JSON 工厂错误: {e}")
        
        # 检查注册表
        try:
            registry_spirits = spirit_registry.list_spirits()
            status["registry_available"] = True
            status["registry_spirits"] = len(registry_spirits)
        except Exception as e:
            status["errors"].append(f"注册表错误: {e}")
        
        # 计算总数
        all_spirits = self.list_available_spirits()
        status["total_spirits"] = len(all_spirits)
        
        return status


# 全局服务实例
_unified_service: Optional[SpiritService] = None


def get_spirit_service() -> SpiritService:
    """获取统一精灵服务实例"""
    global _unified_service
    if _unified_service is None:
        _unified_service = SpiritService()
    return _unified_service


# 便捷函数
def create_spirit(spirit_id: str, team: int = 0, position: Optional[tuple] = None) -> Optional[Spirit]:
    """创建精灵的便捷函数"""
    service = get_spirit_service()
    return service.create_spirit(spirit_id, team, position)


def list_available_spirits() -> List[str]:
    """列出所有可用精灵的便捷函数"""
    service = get_spirit_service()
    return service.list_available_spirits()


def spirit_exists(spirit_id: str) -> bool:
    """检查精灵是否存在的便捷函数"""
    service = get_spirit_service()
    return service.spirit_exists(spirit_id)


def get_spirit_info(spirit_id: str) -> Optional[Dict[str, Any]]:
    """获取精灵信息的便捷函数"""
    service = get_spirit_service()
    return service.get_spirit_info(spirit_id)
