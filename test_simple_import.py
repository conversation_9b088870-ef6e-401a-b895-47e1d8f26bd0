#!/usr/bin/env python3
"""
测试简单导入
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_import():
    """测试简单导入"""
    print("🔧 测试简单导入...")
    
    try:
        print("1. 测试导入EventPriority...")
        from core.event.unified_manager import EventPriority
        print(f"✅ EventPriority导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_import()
