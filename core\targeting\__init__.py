from __future__ import annotations
from typing import List, Callable, TYPE_CHECKING, Dict

# 第三方库导入
import warnings

# 本地导入
from ..battle.models import BattleState
from .selectors import (
    SingleEnemySelector,
    AllEnemiesSelector,
    AllAlliesSelector,
    SelfSelector,
    LowestHpAllySelector
)
from ..interfaces import IBattleEntity

"""目标选择策略模块。

🔧 重构完成：
目标选择器已统一到 selectors.py 模块。
此模块现在只提供向后兼容的接口，建议新代码使用 selectors 模块。

该模块提供了一个策略注册表 `TARGETING_STRATEGIES`，
将策略名称映射到统一选择器的实现。
"""

if TYPE_CHECKING:
    pass


# 定义类型别名，表示一个目标选择策略函数
TargetingStrategy = Callable[[IBattleEntity, "BattleState", int], List[IBattleEntity]]

# 🔧 导入新的统一选择器
try:
    from .selectors import (
        PriorityFrontRowSelector, LowestHpPercentageSelector,
        SameRowFirstSelector, CleaveSelector,
        create_n_targets_selector
    )
    _NEW_SELECTORS_AVAILABLE = True
except ImportError:
    _NEW_SELECTORS_AVAILABLE = False
    warnings.warn(
        "统一选择器模块不可用，降级到基础实现",
        ImportWarning
    )


# 🔧 向后兼容的策略适配器
def _create_legacy_adapter(selector_class, *args, **kwargs):
    """创建旧式函数接口的适配器"""
    def legacy_function(attacker: IBattleEntity, battle_state: "BattleState", target_team_id: int):
        # 创建临时的caster对象来适配新接口
        class TempCaster:
            def __init__(self):
                self.team = 1 - target_team_id  # 反推攻击者队伍
                # 复制攻击者的其他必要属性
                if hasattr(attacker, 'id'):
                    self.id = attacker.id
                else:
                    self.id = f"temp_caster_{target_team_id}"

        temp_caster = TempCaster()
        if callable(selector_class):
            selector = selector_class(*args, **kwargs)
        else:
            selector = selector_class
        return selector.select_targets(temp_caster, battle_state)

    return legacy_function


# 🔧 策略注册表
if _NEW_SELECTORS_AVAILABLE:
    # 使用新的统一选择器（推荐）
    TARGETING_STRATEGIES: Dict[str, TargetingStrategy] = {
        "priority_front_row": _create_legacy_adapter(PriorityFrontRowSelector),
        "lowest_hp_percentage": _create_legacy_adapter(LowestHpPercentageSelector),
        "same_row_first": _create_legacy_adapter(SameRowFirstSelector),
        "cleave": _create_legacy_adapter(CleaveSelector),
        "all": _create_legacy_adapter(AllEnemiesSelector),
        "front_two": _create_legacy_adapter(lambda: create_n_targets_selector(2)),
        "front_three": _create_legacy_adapter(lambda: create_n_targets_selector(3)),
    }
else:
    # 🔧 降级到基础实现（向后兼容）
    def _basic_priority_front_row(attacker: IBattleEntity, battle_state: "BattleState", target_team_id: int):
        """基础的优先前排选择"""
        living_spirits = battle_state.get_living_spirits(target_team_id)
        return [living_spirits[0]] if living_spirits else []

    def _basic_lowest_hp(attacker: IBattleEntity, battle_state: "BattleState", target_team_id: int):
        """基础的最低血量选择"""
        living_spirits = battle_state.get_living_spirits(target_team_id)
        if not living_spirits:
            return []
        lowest_hp_spirit = min(living_spirits, key=lambda s: s.current_hp / s.max_hp)
        return [lowest_hp_spirit]

    def _basic_all_targets(attacker: IBattleEntity, battle_state: "BattleState", target_team_id: int):
        """基础的全体目标选择"""
        return battle_state.get_living_spirits(target_team_id)

    TARGETING_STRATEGIES: Dict[str, TargetingStrategy] = {
        "priority_front_row": _basic_priority_front_row,
        "lowest_hp_percentage": _basic_lowest_hp,
        "same_row_first": _basic_priority_front_row,  # 降级到优先前排
        "cleave": _basic_priority_front_row,  # 降级到单目标
        "all": _basic_all_targets,
        "front_two": _basic_all_targets,  # 降级到全体
        "front_three": _basic_all_targets,  # 降级到全体
    }


# 🔧 导出接口
__all__ = [
    'TARGETING_STRATEGIES',
    'TargetingStrategy',
]


# 🧹 所有旧代码已清理完毕
