#!/usr/bin/env python3
"""
详细调试cast方法的每一步
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_cast_step_by_step():
    """逐步调试cast方法"""
    print("🔍 逐步调试cast方法...")
    
    try:
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.models import BattleState
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 设置高气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
        
        # 创建战斗状态
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 获取超杀技能
        ultimate_skill = None
        for skill in spirit1.skills:
            if hasattr(skill, 'metadata') and skill.metadata.cast_type == 'ULTIMATE':
                ultimate_skill = skill
                break
        
        if ultimate_skill:
            print(f"  找到超杀技能: {ultimate_skill.metadata.name}")
            
            # 手动执行cast方法的每一步
            print(f"\n🔍 手动执行cast方法的每一步...")
            
            # 步骤1：检查owner
            print(f"  步骤1：检查owner")
            print(f"    技能owner: {ultimate_skill.owner}")
            print(f"    owner是否为None: {ultimate_skill.owner is None}")
            
            if ultimate_skill.owner is None:
                print(f"    ❌ owner为None，这是问题所在")
                return False
            
            # 步骤2：检查can_cast
            print(f"  步骤2：检查can_cast")
            can_cast_result = ultimate_skill.can_cast(battle_state)
            print(f"    can_cast结果: {can_cast_result}")
            
            if not can_cast_result:
                print(f"    ❌ can_cast返回False，这是问题所在")
                
                # 详细检查can_cast内部
                print(f"    详细检查can_cast内部...")
                print(f"      owner检查: {ultimate_skill.owner is not None}")
                print(f"      条件数量: {len(ultimate_skill.conditions)}")
                
                # 检查每个条件
                from core.skill.skills import SkillContext
                context = SkillContext(
                    skill_level=ultimate_skill.current_level,
                    additional_data={
                        'skill_id': ultimate_skill.id,
                        'cooldown': ultimate_skill.metadata.cooldown
                    }
                )
                
                for i, condition in enumerate(ultimate_skill.conditions):
                    try:
                        result = condition.check(ultimate_skill.owner, battle_state, context)
                        print(f"        条件{i} ({type(condition).__name__}): {result}")
                    except Exception as e:
                        print(f"        条件{i} ({type(condition).__name__}): 异常 - {e}")
                
                return False
            
            # 步骤3：创建context并选择目标
            print(f"  步骤3：创建context并选择目标")
            
            from core.skill.skills import SkillContext
            context = SkillContext(
                skill_level=ultimate_skill.current_level,
                additional_data={'skill_name': ultimate_skill.metadata.name}
            )
            print(f"    创建初始context成功")
            
            try:
                targets = ultimate_skill.target_selector.select_targets(ultimate_skill.owner, battle_state, context)
                print(f"    选择的目标数量: {len(targets)}")
                
                if len(targets) == 0:
                    print(f"    ❌ 没有选择到目标，这是问题所在")
                    return False
                
                for i, target in enumerate(targets):
                    print(f"      目标{i}: {getattr(target, 'name', 'Unknown')}")
                
            except Exception as e:
                print(f"    ❌ 目标选择失败: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # 步骤4：创建执行context
            print(f"  步骤4：创建执行context")
            
            try:
                execution_context = SkillContext(
                    skill_level=ultimate_skill.current_level,
                    is_critical=ultimate_skill._check_critical(),
                    is_ultimate=ultimate_skill.metadata.cast_type == "ULTIMATE",
                    additional_data={'skill_name': ultimate_skill.metadata.name}
                )
                print(f"    创建执行context成功")
                print(f"      is_ultimate: {execution_context.is_ultimate}")
                print(f"      skill_name: {execution_context.additional_data.get('skill_name')}")
                
            except Exception as e:
                print(f"    ❌ 创建执行context失败: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # 步骤5：执行组件
            print(f"  步骤5：执行组件")
            
            all_actions = []
            
            for i, component in enumerate(ultimate_skill.components):
                comp_type = type(component).__name__
                print(f"    执行组件{i}: {comp_type}")
                
                try:
                    component_actions = component.execute(ultimate_skill.owner, targets, battle_state, execution_context)
                    print(f"      返回动作数量: {len(component_actions)}")
                    
                    for j, action in enumerate(component_actions):
                        action_type = type(action).__name__
                        print(f"        动作{j}: {action_type}")
                    
                    all_actions.extend(component_actions)
                    
                except Exception as e:
                    print(f"      ❌ 组件执行失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            
            print(f"  总动作数量: {len(all_actions)}")
            
            if len(all_actions) > 0:
                print(f"  ✅ 手动执行cast方法成功")
                print(f"  问题可能在于cast方法的某个步骤有异常被忽略")
                
                # 现在测试实际的cast方法
                print(f"\n🔍 测试实际的cast方法...")
                try:
                    actual_actions = ultimate_skill.cast(battle_state)
                    print(f"  实际cast返回动作数量: {len(actual_actions)}")
                    
                    if len(actual_actions) == 0:
                        print(f"  ❌ 实际cast仍然返回0个动作")
                        print(f"  这说明cast方法内部有异常被忽略或逻辑错误")
                    else:
                        print(f"  ✅ 实际cast现在正常工作")
                        return True
                        
                except Exception as e:
                    print(f"  ❌ 实际cast方法异常: {e}")
                    import traceback
                    traceback.print_exc()
                
                return False
            else:
                print(f"  ❌ 手动执行也没有生成动作")
                return False
        else:
            print(f"  ❌ 没有找到超杀技能")
            return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 详细调试cast方法")
    print("="*60)
    
    debug_cast_step_by_step()

if __name__ == "__main__":
    main()
