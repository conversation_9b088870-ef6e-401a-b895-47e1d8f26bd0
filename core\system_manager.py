"""
核心系统管理器

负责管理所有核心组件的生命周期，包括：
1. 初始化顺序
2. 依赖管理
3. 配置加载
4. 系统状态维护
"""
from __future__ import annotations

import logging
import time
from pathlib import Path
from typing import Dict, Optional, Any, List
from enum import Enum

from .logging import GameLogger

class SystemStatus(Enum):
    """系统状态枚举"""
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    ERROR = "error"
    SHUTTING_DOWN = "shutting_down"
    SHUTDOWN = "shutdown"

class CoreSystemManager:
    """核心系统管理器 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.logger = None  # 将在_init_logging中设置
            self._systems: Dict[str, Any] = {}
            self._system_status: Dict[str, SystemStatus] = {}
            self._status = SystemStatus.NOT_INITIALIZED
            self._config: Dict[str, Any] = {}
            self._auto_discover = True  # 默认启用自动发现
            self._initialization_order = [
                "logging",
                "config",
                "cache",
                "event",
                "registry",
                "attribute",
                "effect",
                "skill",
                "spirit_registry",
                "statistics",
                "battle"
            ]
            CoreSystemManager._initialized = True
    
    def initialize(self, config_path: Optional[str] = None, auto_discover: bool = True) -> None:
        """
        初始化所有核心系统

        Args:
            config_path: 配置文件路径，可选
            auto_discover: 是否自动发现并注册所有精灵，默认True
        """
        if self._status == SystemStatus.INITIALIZED:
            if self.logger:
                self.logger.warning("核心系统已经初始化")
            return

        if self._status == SystemStatus.INITIALIZING:
            if self.logger:
                self.logger.warning("核心系统正在初始化中")
            return

        self._status = SystemStatus.INITIALIZING
        self._auto_discover = auto_discover  # 保存设置

        try:
            # 按照预定义的顺序初始化各个系统
            for system_name in self._initialization_order:
                self._init_system(system_name, config_path)

            self._status = SystemStatus.INITIALIZED
            if self.logger:
                self.logger.debug("核心系统初始化完成")
                self._log_system_status()

            # 🔧 根据设置决定是否执行延迟的自动发现
            if auto_discover:
                self._perform_delayed_discovery()
            else:
                if self.logger:
                    self.logger.debug("跳过自动发现，将使用按需注册")

        except Exception as e:
            self._status = SystemStatus.ERROR
            if self.logger:
                self.logger.error(f"核心系统初始化失败: {e}")
            raise
    
    def _init_system(self, system_name: str, config_path: Optional[str] = None) -> None:
        """初始化指定的系统"""
        try:
            self._system_status[system_name] = SystemStatus.INITIALIZING
            
            if system_name == "logging":
                self._init_logging()
            elif system_name == "config":
                self._init_config(config_path)
            elif system_name == "cache":
                self._init_cache_system()
            elif system_name == "event":
                self._init_event_system()
            elif system_name == "registry":
                self._init_registry_system()
            elif system_name == "attribute":
                self._init_attribute_system()
            elif system_name == "effect":
                self._init_effect_system()
            elif system_name == "skill":
                self._init_skill_system()
            elif system_name == "spirit_registry":
                self._init_spirit_registry()
            elif system_name == "statistics":
                self._init_statistics_system()
            elif system_name == "battle":
                self._init_battle_system()
            else:
                raise ValueError(f"未知的系统: {system_name}")
                
            self._system_status[system_name] = SystemStatus.INITIALIZED
            
        except Exception as e:
            self._system_status[system_name] = SystemStatus.ERROR
            if self.logger:
                self.logger.error(f"初始化系统 {system_name} 失败: {e}")
            raise
    
    def _init_logging(self) -> None:
        """初始化日志系统"""
        GameLogger.setup_logging(
            level="INFO",
            log_file="logs/game.log"
        )
        self.logger = GameLogger.get_logger("core.system")
        self._systems['logging'] = GameLogger
        self.logger.debug("开始初始化核心系统...")
        self.logger.debug("日志系统初始化完成")
    
    def _init_config(self, config_path: Optional[str] = None) -> None:
        """加载配置文件"""
        try:
            from .config import ConfigManager
            config_manager = ConfigManager(config_path)
            # 如果指定了配置文件路径，重新加载配置
            if config_path:
                config_manager.config_file = config_path
                config_manager.reload()
                if self.logger:
                    self.logger.debug(f"从文件加载配置: {config_path}")
            self._systems['config'] = config_manager
            if self.logger:
                self.logger.debug("配置系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("配置系统不可用，使用默认配置")
            self._systems['config'] = {}
        except Exception as e:
            if self.logger:
                self.logger.error(f"配置系统初始化失败: {e}")
            # 使用默认配置管理器
            from .config import ConfigManager
            self._systems['config'] = ConfigManager()
    
    def _init_cache_system(self) -> None:
        """初始化缓存系统"""
        try:
            from .cache import GameCacheManager
            cache_manager = GameCacheManager()
            self._systems['cache'] = cache_manager
            if self.logger:
                self.logger.debug("缓存系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("缓存系统不可用")

    def _init_event_system(self) -> None:
        """初始化统一事件系统"""
        try:
            from .event.unified_manager import unified_event_manager
            self._systems['event'] = unified_event_manager
            if self.logger:
                self.logger.debug("统一事件系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("统一事件系统不可用")

    def _init_registry_system(self) -> None:
        """初始化注册管理系统"""
        try:
            from .registry.manager import RegistryManager
            registry_manager = RegistryManager()
            self._systems['registry'] = registry_manager
            if self.logger:
                self.logger.debug("注册管理系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("注册管理系统不可用")

    def _init_attribute_system(self) -> None:
        """初始化属性系统"""
        try:
            from .attribute.system import AttributeManager
            attribute_manager = AttributeManager()
            self._systems['attribute'] = attribute_manager
            if self.logger:
                self.logger.debug("属性系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("属性系统不可用")

    def _init_effect_system(self) -> None:
        """初始化效果系统（使用响应式管理器）"""
        try:
            from .effect import EffectManager  # 现在默认是响应式管理器
            # EffectManager 需要 owner 参数，这里使用系统管理器本身作为占位符
            effect_manager = EffectManager(owner=self)
            self._systems['effect'] = effect_manager
            if self.logger:
                self.logger.debug("响应式效果系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("响应式效果系统不可用")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"响应式效果系统初始化失败: {e}")

    def _init_skill_system(self) -> None:
        """初始化技能系统"""
        try:
            from .skill.system import SkillRegistry
            skill_registry = SkillRegistry()
            self._systems['skill'] = skill_registry
            if self.logger:
                self.logger.debug("技能系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("技能系统不可用")

    def _init_spirit_registry(self) -> None:
        """初始化精灵注册表"""
        try:
            from .spirit.registry import initialize_spirit_registry, spirit_registry
            count = initialize_spirit_registry()
            self._systems['spirit_registry'] = spirit_registry
            if self.logger:
                self.logger.debug(f"精灵注册系统初始化完成，已注册 {count} 个精灵")
        except ImportError:
            if self.logger:
                self.logger.warning("精灵注册系统不可用")

    def _init_statistics_system(self) -> None:
        """初始化统计系统"""
        try:
            from .battle.monitoring.statistics import BattleStatsTracker
            stats_tracker = BattleStatsTracker()
            self._systems['statistics'] = stats_tracker
            if self.logger:
                self.logger.debug("统计系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("统计系统不可用")

    def _init_battle_system(self) -> None:
        """初始化战斗系统"""
        try:
            # 战斗引擎通常在实际战斗时才创建，这里只是验证可以导入
            from .battle.engines.battle_engine import RefactoredBattleEngine
            # 存储类而不是实例，因为战斗引擎需要具体的战斗参数
            self._systems['battle'] = RefactoredBattleEngine
            if self.logger:
                self.logger.debug("战斗系统初始化完成")
        except ImportError:
            if self.logger:
                self.logger.warning("战斗系统不可用")

    def _perform_delayed_discovery(self) -> None:
        """执行延迟的自动发现"""
        try:
            from .spirit.registry import auto_discover_spirits
            count = auto_discover_spirits()
            if self.logger:
                self.logger.debug(f"延迟自动发现完成，注册了 {count} 个精灵")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"延迟自动发现失败: {e}")

    def _log_system_status(self) -> None:
        """记录系统状态"""
        if self.logger:
            self.logger.debug("系统初始化状态:")
            for system_name, status in self._system_status.items():
                status_symbol = "✅" if status == SystemStatus.INITIALIZED else "❌"
                self.logger.debug(f"  {status_symbol} {system_name}: {status.value}")
    
    def get_system(self, name: str) -> Any:
        """获取指定的系统组件"""
        if self._status != SystemStatus.INITIALIZED:
            raise RuntimeError("核心系统尚未初始化")
        return self._systems.get(name)
    
    def get_system_status(self, name: str) -> SystemStatus:
        """获取指定系统的状态"""
        return self._system_status.get(name, SystemStatus.NOT_INITIALIZED)
    
    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self._status == SystemStatus.INITIALIZED
    
    def is_system_available(self, name: str) -> bool:
        """检查指定系统是否可用"""
        return (name in self._systems and 
                self._system_status.get(name) == SystemStatus.INITIALIZED)
    
    def get_available_systems(self) -> List[str]:
        """获取所有可用的系统列表"""
        return [name for name, status in self._system_status.items()
                if status == SystemStatus.INITIALIZED]

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统详细信息"""
        return {
            "status": self._status.value,
            "initialized": self.is_initialized(),
            "systems": {
                name: {
                    "status": status.value,
                    "available": status == SystemStatus.INITIALIZED,
                    "instance": self._systems.get(name) is not None
                }
                for name, status in self._system_status.items()
            },
            "initialization_order": self._initialization_order,
            "total_systems": len(self._initialization_order),
            "available_systems": len(self.get_available_systems())
        }

    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        health_info = {
            "overall_status": "healthy" if self.is_initialized() else "unhealthy",
            "timestamp": time.time(),
            "systems": {}
        }

        for system_name in self._initialization_order:
            system_status = self.get_system_status(system_name)
            system_instance = self._systems.get(system_name)

            health_info["systems"][system_name] = {
                "status": system_status.value,
                "healthy": system_status == SystemStatus.INITIALIZED,
                "has_instance": system_instance is not None,
                "type": type(system_instance).__name__ if system_instance else None
            }

            # 特殊健康检查
            if system_name == "cache" and system_instance:
                try:
                    if hasattr(system_instance, 'get_stats'):
                        health_info["systems"][system_name]["cache_stats"] = system_instance.get_stats()
                except Exception:
                    pass

            elif system_name == "spirit_registry" and system_instance:
                try:
                    if hasattr(system_instance, 'list_spirits'):
                        spirit_count = len(system_instance.list_spirits())
                        health_info["systems"][system_name]["registered_spirits"] = spirit_count
                except Exception:
                    pass

        return health_info
    
    def shutdown(self) -> None:
        """关闭所有系统组件"""
        if not self._initialized:
            return

        try:
            self._status = SystemStatus.SHUTTING_DOWN
            # 按照依赖关系的反序关闭系统
            if self.logger:
                self.logger.debug("开始关闭核心系统...")

            # 1. 关闭战斗系统
            if 'battle' in self._systems:
                if self.logger:
                    self.logger.debug("关闭战斗系统")
                # 战斗系统通常不需要特殊关闭逻辑

            # 2. 关闭游戏核心组件
            if 'effect' in self._systems:
                if self.logger:
                    self.logger.debug("关闭效果系统")
                effect_manager = self._systems['effect']
                if hasattr(effect_manager, 'clear_all_effects'):
                    effect_manager.clear_all_effects()

            if 'spirit_registry' in self._systems:
                if self.logger:
                    self.logger.debug("关闭精灵注册系统")
                spirit_registry = self._systems['spirit_registry']
                if hasattr(spirit_registry, 'clear'):
                    spirit_registry.clear()

            if 'skill' in self._systems:
                if self.logger:
                    self.logger.debug("关闭技能系统")
                skill_registry = self._systems['skill']
                if hasattr(skill_registry, 'clear'):
                    skill_registry.clear()

            # 3. 关闭基础设施
            if 'cache' in self._systems:
                if self.logger:
                    self.logger.debug("关闭缓存系统")
                cache_manager = self._systems['cache']
                if hasattr(cache_manager, 'clear_all'):
                    cache_manager.clear_all()

            if 'event' in self._systems:
                if self.logger:
                    self.logger.debug("关闭事件系统")
                trigger_manager = self._systems['event']
                if hasattr(trigger_manager, 'clear_all_triggers'):
                    trigger_manager.clear_all_triggers()

            if 'registry' in self._systems:
                if self.logger:
                    self.logger.debug("关闭注册管理系统")
                registry_manager = self._systems['registry']
                if hasattr(registry_manager, 'clear_all'):
                    registry_manager.clear_all()

            # 清空系统状态
            for system_name in self._systems:
                self._system_status[system_name] = SystemStatus.SHUTDOWN

            self._systems.clear()
            self._status = SystemStatus.SHUTDOWN
            CoreSystemManager._initialized = False
            if self.logger:
                self.logger.debug("核心系统已关闭")

        except Exception as e:
            self._status = SystemStatus.ERROR
            if self.logger:
                self.logger.error(f"关闭核心系统时发生错误: {e}")
            raise

# 便利函数
def get_logger(name: str):
    """获取指定名称的日志记录器"""
    return GameLogger.get_logger(name)

def get_system(name: str):
    """获取指定的系统组件"""
    return system_manager.get_system(name)

def is_system_available(name: str) -> bool:
    """检查指定系统是否可用"""
    return system_manager.is_system_available(name)

def initialize_core_systems(config_path: Optional[str] = None):
    """初始化核心系统的便利函数"""
    system_manager.initialize(config_path)

def shutdown_core_systems():
    """关闭核心系统的便利函数"""
    system_manager.shutdown()

# 全局系统管理器实例
system_manager = CoreSystemManager()

__all__ = [
    'CoreSystemManager',
    'system_manager',
    'SystemStatus',
    'get_logger',
    'get_system',
    'is_system_available',
    'initialize_core_systems',
    'shutdown_core_systems'
]