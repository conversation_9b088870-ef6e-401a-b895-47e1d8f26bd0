"""
胜负判定策略 (Battle End Conditions)

本模块抽象了战斗结束条件的判断逻辑，提供以下能力：
1. `IBattleEndCondition` 抽象基类：所有判定策略需实现 `check_battle_end`。
2. `KnockoutCondition` ：默认 KO 制（任意一队全灭即结束）。
3. `CompositeCondition` ：可组合多种策略，按顺序依次判定。
4. `RoundLimitCondition` ：示例策略——达到指定回合上限后比较剩余 HP。

后续若需要新增计分制、占点制等，只需继承 `IBattleEndCondition` 并实现自定义逻辑即可。
"""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import List, Optional

from ..models import BattleState

__all__: List[str] = [
    "IBattleEndCondition",
    "KnockoutCondition",
    "CompositeCondition",
    "RoundLimitCondition",
]


class IBattleEndCondition(ABC):
    """战斗结束条件接口。

    子类必须实现 :meth:`check_battle_end`，并返回下列值之一：
        * ``0`` / ``1`` – 对应队伍 ID 获胜
        * ``-1``        – 平局
        * ``None``      – 战斗继续
    """

    @abstractmethod
    def check_battle_end(self, battle_state: BattleState) -> Optional[int]:
        """检查战斗是否结束并给出结果。

        参数
        ------
        battle_state : BattleState
            当前战斗状态。

        返回
        ------
        Optional[int]
            - ``0`` / ``1`` : 获胜方队伍 ID。
            - ``-1``        : 平局。
            - ``None``      : 继续战斗。
        """
        raise NotImplementedError


class KnockoutCondition(IBattleEndCondition):
    """经典 KO 制：任意一方全部阵亡即战斗结束。"""

    def check_battle_end(self, battle_state: BattleState) -> Optional[int]:  # noqa: D401
        team0_alive = len(battle_state.get_living_spirits(0)) > 0
        team1_alive = len(battle_state.get_living_spirits(1)) > 0

        if not team0_alive and not team1_alive:
            return -1  # 平局
        if not team0_alive:
            return 1
        if not team1_alive:
            return 0
        return None


class CompositeCondition(IBattleEndCondition):
    """将多种判定策略按优先级串联。

    * 第一个返回非 ``None`` 的结果即为最终结果。
    """

    def __init__(self, *conditions: IBattleEndCondition):
        if not conditions:
            raise ValueError("CompositeCondition 至少需要一个子策略")
        self.conditions: List[IBattleEndCondition] = list(conditions)

    def check_battle_end(self, battle_state: BattleState) -> Optional[int]:  # noqa: D401
        for condition in self.conditions:
            result = condition.check_battle_end(battle_state)
            if result is not None:
                return result
        return None


class RoundLimitCondition(IBattleEndCondition):
    """示例策略：到达回合上限后比较双方剩余 HP。

    参数
    ------
    max_rounds : int
        回合上限。达到该上限时触发比较。
    """

    def __init__(self, max_rounds: int = 20):
        self.max_rounds = max_rounds
        # 复用 KO 制确保在上限内也能及时结束
        self._ko = KnockoutCondition()

    def check_battle_end(self, battle_state: BattleState) -> Optional[int]:  # noqa: D401
        # 1) 先检查 KO（性能最优）
        ko_result = self._ko.check_battle_end(battle_state)
        if ko_result is not None:
            return ko_result

        # 2) 检查回合上限
        if battle_state.round_num >= self.max_rounds:
            hp0 = sum(s.current_hp for s in battle_state.get_living_spirits(0))
            hp1 = sum(s.current_hp for s in battle_state.get_living_spirits(1))
            if hp0 == hp1:
                return -1
            return 0 if hp0 > hp1 else 1

        return None 