"""
神曜虚无·伏妖 - 被动技能效果

包含伏妖的被动技能：
- BiAnShuShaPassiveEffect: 彼岸殊沙被动效果
"""
from __future__ import annotations
from typing import List, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition, BeforeAttackCondition, BeforeDamageAppliedCondition
from .effects import create_xuwu_state_effect


class BiAnShuShaPassiveEffect(IEffect):
    """
    彼岸殊沙被动效果
    
    1. 优先攻击可以行动的精灵
    2. 自身首次攻击及受到攻击后，令目标进入虚无状态
    """

    def __init__(self, owner_spirit):
        super().__init__(
            effect_id=f"bian_shusha_passive_{owner_spirit.id}",
            name="彼岸殊沙",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGHEST,
            duration=-1  # 永久效果
        )

        self.owner_spirit = owner_spirit
        self.first_attack_used = False  # 是否已使用首次攻击
        self.first_damaged_used = False  # 是否已受到首次攻击

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        return [
            BeforeAttackCondition(),  # 攻击前触发（用于目标选择）
            BeforeDamageAppliedCondition(target="self"),  # 自身受到伤害前触发
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理事件触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "BEFORE_ATTACK":
                return self._handle_before_attack(event_data, battle_state)
            elif event_type == "BEFORE_DAMAGE_APPLIED":
                return self._handle_before_damage(event_data, battle_state)

            return EffectResult.success()

        except Exception as e:
            from core.logging import spirit_logger
            spirit_logger.error(f"彼岸殊沙被动效果触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)

    def _handle_before_attack(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理攻击前事件 - 目标选择和首次攻击虚无"""
        attacker = event_data.get("attacker")
        if attacker != self.owner_spirit:
            return EffectResult.success("不是效果拥有者攻击")

        from core.action import LogAction, ApplyEffectAction
        
        actions = []

        # 1. 优先攻击可以行动的精灵（修改目标选择逻辑）
        original_target = event_data.get("target")
        better_target = self._select_actionable_target(battle_state, original_target)
        if better_target and better_target != original_target:
            event_data["target"] = better_target
            actions.append(LogAction(
                caster=self.owner_spirit,
                message=f"🎯 [彼岸殊沙] {getattr(self.owner_spirit, 'name', '伏妖')} 优先攻击可行动的 {getattr(better_target, 'name', '目标')}！"
            ))

        # 2. 首次攻击令目标进入虚无状态
        if not self.first_attack_used:
            target = event_data.get("target")
            if target:
                # 🔧 修复：现在持续时间管理机制已修复，可以设置为正确的持续时间
                xuwu_effect = create_xuwu_state_effect(self.owner_spirit, target, duration=2)
                actions.append(ApplyEffectAction(
                    caster=self.owner_spirit,
                    target=target,
                    effect=xuwu_effect
                ))
                
                actions.append(LogAction(
                    caster=self.owner_spirit,
                    message=f"🌀 [彼岸殊沙] 首次攻击！{getattr(target, 'name', '目标')} 进入虚无状态！"
                ))
                
                self.first_attack_used = True

        return EffectResult.success_with_actions(actions, "彼岸殊沙攻击前触发")

    def _handle_before_damage(self, event_data: Dict[str, Any], battle_state: "IBattleState") -> EffectResult:
        """处理受到伤害前事件 - 首次受攻击虚无"""
        target = event_data.get("target")
        if target != self.owner_spirit:
            return EffectResult.success("不是效果拥有者受伤")

        # 首次受到攻击令攻击者进入虚无状态
        if not self.first_damaged_used:
            attacker = event_data.get("attacker") or event_data.get("caster")
            if attacker:
                from core.action import LogAction, ApplyEffectAction
                
                actions = []
                
                # 🔧 修复：现在持续时间管理机制已修复，可以设置为正确的持续时间
                xuwu_effect = create_xuwu_state_effect(self.owner_spirit, attacker, duration=2)
                actions.append(ApplyEffectAction(
                    caster=self.owner_spirit,
                    target=attacker,
                    effect=xuwu_effect
                ))
                
                actions.append(LogAction(
                    caster=self.owner_spirit,
                    message=f"🌀 [彼岸殊沙] 首次受攻击！{getattr(attacker, 'name', '攻击者')} 进入虚无状态！"
                ))
                
                self.first_damaged_used = True
                
                return EffectResult.success_with_actions(actions, "彼岸殊沙受攻击触发")

        return EffectResult.success()

    def _select_actionable_target(self, battle_state: IBattleState, original_target) -> IBattleEntity:
        """选择可以行动的目标"""
        try:
            # 获取敌方队伍
            enemy_team = 1 if getattr(self.owner_spirit, 'team', 0) == 0 else 0
            
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                
                # 筛选可以行动的精灵
                actionable_spirits = []
                for spirit in enemy_spirits:
                    if (getattr(spirit, 'is_alive', False) and 
                        not self._is_unable_to_act(spirit) and
                        not self._is_in_xuwu_state(spirit)):
                        actionable_spirits.append(spirit)
                
                # 如果有可行动的精灵，优先选择
                if actionable_spirits:
                    # 如果原目标不可行动，选择第一个可行动的
                    if (original_target not in actionable_spirits and 
                        original_target in enemy_spirits):
                        return actionable_spirits[0]
                
            return original_target
            
        except Exception:
            return original_target

    def _is_unable_to_act(self, spirit) -> bool:
        """检查精灵是否无法行动"""
        try:
            if hasattr(spirit, 'is_unable_to_act'):
                return spirit.is_unable_to_act()
            return False
        except:
            return False

    def _is_in_xuwu_state(self, spirit) -> bool:
        """检查精灵是否处于虚无状态"""
        try:
            if hasattr(spirit, 'has_effect'):
                return spirit.has_effect("虚无状态")
            return False
        except:
            return False

    def on_apply(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果应用时触发"""
        return EffectResult.success_with_data(
            {"applied": True}, 
            f"{getattr(target, 'name', '精灵')} 获得彼岸殊沙被动效果"
        )

    def on_remove(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{getattr(target, 'name', '精灵')} 失去彼岸殊沙被动效果"
        )

    def on_update(self, target: "IBattleEntity", battle_state: "IBattleState") -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data(
            {
                "first_attack_used": self.first_attack_used,
                "first_damaged_used": self.first_damaged_used
            }, 
            "彼岸殊沙被动效果更新"
        )

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "彼岸殊沙：优先攻击可行动精灵，首次攻击和受攻击令目标进入虚无状态",
            "duration": -1,
            "first_attack_used": self.first_attack_used,
            "first_damaged_used": self.first_damaged_used
        }


# 被动效果创建函数
def create_bian_shusha_passive_effect(owner_spirit) -> BiAnShuShaPassiveEffect:
    """创建彼岸殊沙被动效果"""
    return BiAnShuShaPassiveEffect(owner_spirit)


# 导出所有被动效果类和创建函数
__all__ = [
    'BiAnShuShaPassiveEffect',
    'create_bian_shusha_passive_effect'
]
