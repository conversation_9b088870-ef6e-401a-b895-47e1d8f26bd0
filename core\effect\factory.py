#!/usr/bin/env python3
"""
效果工厂

提供便捷的效果创建方法和效果模板。
"""

from typing import Dict, Any, Optional, Type, Callable, List
from .system import IEffect, EffectType, EffectCategory, EffectPriority
from .effects import (
    DamageOverTimeEffect, HealOverTimeEffect, AttributeModifierEffect,
    ShieldEffect, StunEffect, AttackImmunityEffect
)
import logging


class EffectFactory:
    """效果工厂类"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._effect_registry = {}
            cls._instance._effect_templates = {}
            cls._instance._register_builtin_effects()
            cls._instance._register_builtin_templates()
        return cls._instance
    
    def _register_builtin_effects(self):
        """注册内置效果类型"""
        self._effect_registry.update({
            'damage_over_time': DamageOverTimeEffect,
            'dot': DamageOverTimeEffect,
            'heal_over_time': HealOverTimeEffect,
            'hot': HealOverTimeEffect,
            'attribute_modifier': AttributeModifierEffect,
            'attr_mod': AttributeModifierEffect,
            'shield': ShieldEffect,
            'stun': StunEffect,
            'attack_immunity': AttackImmunityEffect,
            'immunity': AttackImmunityEffect,
        })
    
    def _register_builtin_templates(self):
        """注册内置效果模板"""
        self._effect_templates.update({
            # 伤害类模板
            'poison': {
                'type': 'damage_over_time',
                'damage_per_turn': 50,
                'duration': 3,
                'name': '中毒'
            },
            'burn': {
                'type': 'damage_over_time',
                'damage_per_turn': 80,
                'duration': 2,
                'name': '燃烧'
            },
            'bleed': {
                'type': 'damage_over_time',
                'damage_per_turn': 30,
                'duration': 5,
                'name': '流血'
            },
            
            # 治疗类模板
            'regeneration': {
                'type': 'heal_over_time',
                'heal_per_turn': 40,
                'duration': 4,
                'name': '再生'
            },
            'blessing': {
                'type': 'heal_over_time',
                'heal_per_turn': 60,
                'duration': 3,
                'name': '祝福'
            },
            
            # 属性修改模板
            'strength_boost': {
                'type': 'attribute_modifier',
                'attribute_name': 'attack',
                'modifier': 0.2,
                'duration': 5,
                'name': '力量提升'
            },
            'speed_boost': {
                'type': 'attribute_modifier',
                'attribute_name': 'speed',
                'modifier': 0.3,
                'duration': 3,
                'name': '速度提升'
            },
            'weakness': {
                'type': 'attribute_modifier',
                'attribute_name': 'attack',
                'modifier': -0.3,
                'duration': 4,
                'name': '虚弱'
            },
            'slow': {
                'type': 'attribute_modifier',
                'attribute_name': 'speed',
                'modifier': -0.5,
                'duration': 2,
                'name': '缓慢'
            },
            
            # 护盾模板
            'magic_shield': {
                'type': 'shield',
                'shield_amount': 200,
                'duration': -1,
                'name': '魔法护盾'
            },
            'barrier': {
                'type': 'shield',
                'shield_amount': 150,
                'duration': 3,
                'name': '屏障'
            },
            
            # 控制模板
            'paralyze': {
                'type': 'stun',
                'duration': 1,
                'name': '麻痹'
            },
            'freeze': {
                'type': 'stun',
                'duration': 2,
                'name': '冰冻'
            },
            
            # 免疫类模板
            'attack_immunity_1': {
                'type': 'attack_immunity',
                'charges': 1,
                'name': '攻击免疫'
            },
            'attack_immunity_2': {
                'type': 'attack_immunity',
                'charges': 2,
                'name': '攻击免疫'
            },
            'temporary_immunity': {
                'type': 'attack_immunity',
                'charges': 1,
                'duration': 3,
                'name': '临时攻击免疫'
            },
        })
    
    def register_effect_type(self, name: str, effect_class: Type[IEffect]):
        """注册新的效果类型"""
        if name in self._effect_registry:
            logger.warning(f"效果类型 {name} 已存在，将被覆盖")
        self._effect_registry[name] = effect_class
        logger.info(f"注册效果类型: {name} -> {effect_class.__name__}")
    
    def register_template(self, name: str, template: Dict[str, Any]):
        """注册新的效果模板"""
        if name in self._effect_templates:
            logger.warning(f"效果模板 {name} 已存在，将被覆盖")
        self._effect_templates[name] = template
        logger.info(f"注册效果模板: {name}")
    
    def create_effect(self, effect_type: str, caster=None, **kwargs) -> Optional[IEffect]:
        """创建效果实例"""
        if effect_type not in self._effect_registry:
            # 尝试模糊匹配
            matched_types = [t for t in self._effect_registry.keys() if effect_type.lower() in t.lower() or t.lower() in effect_type.lower()]
            if len(matched_types) == 1:
                logger.info(f"模糊匹配效果类型: {effect_type} -> {matched_types[0]}")
                effect_type = matched_types[0]
            else:
                raise ValueError(f"未知的效果类型: {effect_type}")
        
        effect_class = self._effect_registry[effect_type]
        
        try:
            # 根据不同效果类型传递不同参数
            if effect_type in ['damage_over_time', 'dot']:
                return effect_class(
                    damage_per_turn=kwargs.get('damage_per_turn', 50),
                    duration=kwargs.get('duration', 3),
                    caster=caster
                )
            elif effect_type in ['heal_over_time', 'hot']:
                return effect_class(
                    heal_per_turn=kwargs.get('heal_per_turn', 40),
                    duration=kwargs.get('duration', 3),
                    caster=caster
                )
            elif effect_type in ['attribute_modifier', 'attr_mod']:
                return effect_class(
                    attribute_name=kwargs.get('attribute_name', 'attack'),
                    modifier=kwargs.get('modifier', 10.0),
                    duration=kwargs.get('duration', -1),
                    caster=caster
                )
            elif effect_type == 'shield':
                return effect_class(
                    shield_amount=kwargs.get('shield_amount', 100),
                    duration=kwargs.get('duration', -1),
                    caster=caster
                )
            elif effect_type == 'stun':
                return effect_class(
                    duration=kwargs.get('duration', 1),
                    caster=caster
                )
            elif effect_type in ['attack_immunity', 'immunity']:
                return effect_class(
                    charges=kwargs.get('charges', 1),
                    duration=kwargs.get('duration', -1),
                    caster=caster
                )
            else:
                # 通用创建方式
                return effect_class(caster=caster, **kwargs)
                
        except Exception as e:
            raise ValueError(f"创建效果失败: {e}")
    
    def create_effect_from_template(self, template_name: str, caster=None, **overrides) -> Optional[IEffect]:
        """根据模板创建效果实例"""
        if template_name not in self._effect_templates:
            raise ValueError(f"未知的效果模板: {template_name}")
        
        template = self._effect_templates[template_name].copy()
        # 应用覆盖参数
        template.update(overrides)
        
        effect_type = template.pop('type')
        name = template.pop('name', template_name)
        
        # 创建效果实例
        effect = self.create_effect(effect_type, caster=caster, **template)
        if effect and not hasattr(effect, 'name'):
            effect.name = name
            
        return effect
    
    def get_effect_types(self) -> Dict[str, Type[IEffect]]:
        """获取所有注册的效果类型"""
        return self._effect_registry.copy()
    
    def get_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取所有注册的效果模板"""
        return self._effect_templates.copy()

    # 🔧 添加便捷创建方法
    def create_poison(self, damage: float = 50, duration: int = 3, caster=None) -> IEffect:
        """创建毒效果"""
        return self.create_effect('dot', damage_per_turn=damage, duration=duration, caster=caster)

    def create_burn(self, damage: float = 60, duration: int = 2, caster=None) -> IEffect:
        """创建燃烧效果"""
        return self.create_effect('dot', damage_per_turn=damage, duration=duration, caster=caster)

    def create_regeneration(self, heal: float = 40, duration: int = 4, caster=None) -> IEffect:
        """创建再生效果"""
        return self.create_effect('hot', heal_per_turn=heal, duration=duration, caster=caster)

    def create_shield(self, amount: float = 100, duration: int = 5, caster=None) -> IEffect:
        """创建护盾效果"""
        return self.create_effect('shield', shield_amount=amount, duration=duration, caster=caster)

    def create_stun(self, duration: int = 1, caster=None) -> IEffect:
        """创建眩晕效果"""
        return self.create_effect('stun', duration=duration, caster=caster)

    def create_attack_immunity(self, charges: int = 1, duration: int = -1, caster=None) -> IEffect:
        """创建攻击免疫效果"""
        return self.create_effect('attack_immunity', charges=charges, duration=duration, caster=caster)

    def create_attribute_boost(self, attribute: str, modifier: float, duration: int = 3, caster=None) -> IEffect:
        """创建属性增强效果"""
        return self.create_effect('attr_mod', attribute_name=attribute, modifier=modifier, duration=duration, caster=caster)


# 全局效果工厂实例
effect_factory = EffectFactory()


def create_effect(effect_type: str, caster=None, **kwargs) -> Optional[IEffect]:
    """便捷函数：创建效果"""
    return effect_factory.create_effect(effect_type, caster=caster, **kwargs)


def create_from_template(template_name: str, caster=None, **overrides) -> Optional[IEffect]:
    """便捷函数：从模板创建效果"""
    return effect_factory.create_effect_from_template(template_name, caster=caster, **overrides)


def poison(damage: float = 50, duration: int = 3, caster=None) -> IEffect:
    """创建中毒效果"""
    return create_from_template('poison', caster=caster, damage_per_turn=damage, duration=duration)


def burn(damage: float = 80, duration: int = 2, caster=None) -> IEffect:
    """创建燃烧效果"""
    return create_from_template('burn', caster=caster, damage_per_turn=damage, duration=duration)


def regeneration(heal: float = 40, duration: int = 4, caster=None) -> IEffect:
    """创建再生效果"""
    return create_from_template('regeneration', caster=caster, heal_per_turn=heal, duration=duration)


def shield(amount: float = 200, duration: int = -1, caster=None) -> IEffect:
    """创建护盾效果"""
    return create_from_template('magic_shield', caster=caster, shield_amount=amount, duration=duration)


def stun(duration: int = 1, caster=None) -> IEffect:
    """创建眩晕效果"""
    return create_from_template('paralyze', caster=caster, duration=duration)


def attribute_boost(attribute: str, boost: float, duration: int = 5, caster=None) -> IEffect:
    """创建属性提升效果"""
    return create_effect('attribute_modifier', caster=caster, 
                        attribute_name=attribute, modifier=boost, duration=duration)


def attribute_debuff(attribute: str, reduction: float, duration: int = 3, caster=None) -> IEffect:
    """创建属性削弱效果"""
    return create_effect('attribute_modifier', caster=caster,
                        attribute_name=attribute, modifier=-abs(reduction), duration=duration)


def attack_immunity(charges: int = 1, duration: int = -1, caster=None) -> IEffect:
    """创建攻击免疫效果
    
    创建完全攻击免疫效果，不仅阻止伤害，还阻止攻击的所有效果。
    """
    return create_effect('attack_immunity', caster=caster, 
                        charges=charges, duration=duration)


__all__ = [
    'EffectFactory',
    'effect_factory',
    'create_effect',
    'create_from_template',
    'poison',
    'burn', 
    'regeneration',
    'shield',
    'stun',
    'attribute_boost',
    'attribute_debuff',
    'attack_immunity'
]