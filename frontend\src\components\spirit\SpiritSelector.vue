<template>
  <el-dialog v-model="visible" title="选择精灵" width="90%" class="spirit-selector-dialog">
    <div class="spirit-selector">
      <!-- 优化的搜索和过滤栏 -->
      <div class="search-filters-enhanced bg-slate-700/30 rounded-lg p-3 mb-3 space-y-3">
        <!-- 第一行：搜索框和操作按钮 -->
        <div class="flex items-center gap-3">
          <el-input 
            v-model="searchQuery" 
            placeholder="搜索精灵名称..."
            clearable
            class="flex-1"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <div class="flex items-center gap-2">
            <el-button size="small" @click="resetFilters">
              <el-icon><RefreshRight /></el-icon>
              重置
            </el-button>
          </div>
        </div>

        <!-- 第二行：元素筛选 -->
        <div class="filter-section">
          <div class="filter-label">
            <el-icon class="text-orange-400"><Sunny /></el-icon>
            <span class="text-slate-300 text-sm font-medium">元素类型</span>
          </div>
          <div class="filter-options flex flex-wrap gap-2">
            <el-tag
              v-for="element in elementTypes"
              :key="element.value"
              :type="elementFilter === element.value ? 'primary' : ''"
              :effect="elementFilter === element.value ? 'dark' : 'plain'"
              size="small"
              class="cursor-pointer filter-tag"
              :class="{ 'filter-active': elementFilter === element.value }"
              @click="toggleElementFilter(element.value)"
            >
              {{ element.label }}
            </el-tag>
          </div>
        </div>

        <!-- 第三行：职业筛选 -->
        <div class="filter-section">
          <div class="filter-label">
            <el-icon class="text-blue-400"><UserFilled /></el-icon>
            <span class="text-slate-300 text-sm font-medium">职业类型</span>
          </div>
          <div class="filter-options flex flex-wrap gap-2">
            <el-tag
              v-for="profession in professionTypes"
              :key="profession.value"
              :type="professionFilter === profession.value ? 'success' : ''"
              :effect="professionFilter === profession.value ? 'dark' : 'plain'"
              size="small"
              class="cursor-pointer filter-tag"
              :class="{ 'filter-active': professionFilter === profession.value }"
              @click="toggleProfessionFilter(profession.value)"
            >
              {{ profession.label }}
            </el-tag>
          </div>
        </div>

        <!-- 第四行：排序选项 -->
        <div class="filter-section">
          <div class="filter-label">
            <el-icon class="text-purple-400"><Operation /></el-icon>
            <span class="text-slate-300 text-sm font-medium">排序方式</span>
          </div>
          <div class="filter-options flex flex-wrap gap-2">
            <el-tag
              v-for="sort in sortOptions"
              :key="sort.value"
              :type="sortBy === sort.value ? 'warning' : ''"
              :effect="sortBy === sort.value ? 'dark' : 'plain'"
              size="small"
              class="cursor-pointer filter-tag"
              @click="sortBy = sort.value"
            >
              {{ sort.label }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 精灵列表 -->
      <div class="spirits-container bg-slate-800/30 rounded-lg overflow-hidden">
        <div class="list-header bg-slate-700/50 px-3 py-2 border-b border-slate-600/30">
          <span class="text-slate-300">找到 {{ filteredSpirits.length }} 个精灵</span>
        </div>

        <div class="spirits-grid" style="max-height: 50vh; overflow-y: auto;">
          <div v-if="filteredSpirits.length > 0" class="p-3 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
            <SpiritCard
              v-for="spirit in filteredSpirits"
              :key="spirit.name_prefix"
              :spirit="spirit"
              :tooltip-disabled="true" 
              @click="selectSpirit(spirit)"
            />
          </div>
          <div v-else class="text-center py-8">
            <el-icon class="text-4xl text-slate-500 mb-2"><Avatar /></el-icon>
            <div class="text-slate-400">未找到符合条件的精灵</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
        <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage } from 'element-plus'
import type { Spirit, ElementType, ProfessionType } from '../../types/battle'
import { Search, View, CircleCheck, Timer, Star, RefreshRight, QuestionFilled, Grid, List, Avatar, Sunny, UserFilled, Operation } from '@element-plus/icons-vue'
import { useSpiritRecommendation } from '../../composables/useSpiritRecommendation'
import { getSpirits } from '../../api/spirits';
import type { SpiritPrototype } from '../../types/spirit';
import SpiritCard from './SpiritCard.vue';

/**
 * SpiritSelector.vue
 * 精灵选择组件，用于在战斗中选择精灵并将其放置在阵型中
 */

const props = defineProps<{ 
  modelValue: boolean,
  availableSpirits?: Spirit[],
  currentTeam?: Spirit[],
  enemyTeam?: Spirit[]
}>()

const emit = defineEmits<{
  (e: 'select', spirit: SpiritPrototype): void;
}>()

const visible = ref(false);
const allSpirits = ref<SpiritPrototype[]>([]);
const searchQuery = ref('');
const elementFilter = ref<string | null>(null);
const professionFilter = ref<string | null>(null);
const sortBy = ref('recommendation');
const selectedSpiritDetail = ref<SpiritPrototype | null>(null);
const showRecommendations = ref(true)
const viewMode = ref<'grid' | 'list'>('grid')

// 使用智能推荐系统
const { getRecommendedSpirits } = useSpiritRecommendation()

const recommendations = ref<any[]>([]);

const elementTypes = ref([
  { label: '火', value: '火' },
  { label: '水', value: '水' },
  { label: '草', value: '草' },
  { label: '光', value: '光' },
  { label: '暗', value: '暗' },
  { label: '创', value: '创' },
  { label: '空', value: '空' },
]);

const professionTypes = ref([
    { label: '英雄', value: '英雄' },
    { label: '魔法', value: '魔法' },
    { label: '平衡', value: '平衡' },
    { label: '利爪', value: '利爪' },
    { label: '肉盾', value: '肉盾' },
    { label: '神曜', value: '神曜' },
    { label: '通灵师', value: '通灵师' },
    { label: '召唤师', value: '召唤师' },
    { label: '疾速', value: '疾速' },
    { label: '治疗', value: '治疗' },
    { label: '控制', value: '控制' },
    { label: '神启', value: '神启' },
    { label: '天觉者', value: '天觉者' },
]);

const sortOptions = ref([
  { label: '推荐度', value: 'recommendation' },
  { label: '名称', value: 'name' },
  { label: '等级', value: 'shenge_level' },
  { label: '攻击力', value: 'attack' },
  { label: '生命值', value: 'hp' },
  { label: '速度', value: 'speed' },
]);


const filteredSpirits = computed(() => {
  let spirits = [...allSpirits.value];

  // 1. Filter by search query
  if (searchQuery.value) {
    spirits = spirits.filter(s => s.name_prefix.toLowerCase().includes(searchQuery.value.toLowerCase()));
  }

  // 2. Filter by element
  if (elementFilter.value) {
    spirits = spirits.filter(s => s.element === elementFilter.value);
  }

  // 3. Filter by profession
  if (professionFilter.value) {
    spirits = spirits.filter(s => s.professions.includes(professionFilter.value!));
  }
  
  // 4. Sort
  spirits.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name_prefix.localeCompare(b.name_prefix);
      case 'shenge_level':
        return b.shenge_level - a.shenge_level;
      case 'attack':
        return b.attributes.base_attack - a.attributes.base_attack;
      case 'hp':
        return b.attributes.base_hp - a.attributes.base_hp;
      case 'speed':
        return b.attributes.speed - a.attributes.speed;
      default: // recommendation (default)
        return 0; // No recommendation logic for now
    }
  });

  return spirits;
});

const open = async (team: any, existingSpirits: any[]) => {
  visible.value = true;
  if (allSpirits.value.length === 0) {
    try {
      allSpirits.value = await getSpirits();
    } catch (e) {
      console.error("Failed to load spirits:", e);
    }
  }
};

const close = () => {
  visible.value = false;
};

const selectSpirit = (spirit: SpiritPrototype) => {
  emit('select', spirit);
  close();
};

const resetFilters = () => {
  searchQuery.value = '';
  elementFilter.value = null;
  professionFilter.value = null;
  sortBy.value = 'recommendation';
  selectedSpiritDetail.value = null;
  showRecommendations.value = true;
};

const toggleElementFilter = (element: string | null) => {
  elementFilter.value = elementFilter.value === element ? null : element;
};

const toggleProfessionFilter = (profession: string | null) => {
  professionFilter.value = professionFilter.value === profession ? null : profession;
};


defineExpose({ open, close });

// 获取精灵推荐信息
const getSpiritRecommendation = (spiritId: string) => {
  return recommendations.value.find(r => r.spirit.id === spiritId)
}

// 辅助函数：获取元素标签文本
const getElementLabel = (element: ElementType) => {
  const elementMap = {
    fire: '火',
    water: '水',
    earth: '土',
    air: '风',
    light: '光',
    dark: '暗',
    neutral: '无'
  }
  return elementMap[element] || element
}

// 辅助函数：获取职业标签文本
const getProfessionLabel = (profession: ProfessionType) => {
  const professionMap = {
    warrior: '战士',
    mage: '法师',
    archer: '射手',
    healer: '治疗',
    assassin: '刺客',
    tank: '坦克'
  }
  return professionMap[profession] || profession
}

// 辅助函数：获取元素标签类型
const getElementTagType = (element: ElementType) => {
  const typeMap = {
    fire: 'danger',
    water: 'primary',
    earth: 'warning',
    air: 'success',
    light: 'info',
    dark: 'warning',
    neutral: 'info'
  }
  return typeMap[element] || 'info'
}

// 辅助函数：获取元素背景类
const getElementClass = (element?: ElementType) => {
  const classMap = {
    fire: 'from-red-600 to-orange-500',
    water: 'from-blue-600 to-cyan-500',
    earth: 'from-amber-600 to-yellow-500',
    air: 'from-emerald-600 to-green-500',
    light: 'from-indigo-600 to-blue-400',
    dark: 'from-violet-800 to-purple-600',
    neutral: 'from-slate-600 to-slate-500'
  }
  return element ? classMap[element] : 'from-slate-600 to-slate-500'
}
</script>

<style scoped>
.spirit-selector-dialog :deep(.el-dialog__body) {
  padding: 16px;
  max-height: 80vh;
  overflow: hidden;
}

.spirit-selector-dialog :deep(.el-dialog) {
  margin-top: 5vh;
  margin-bottom: 5vh;
}

/* 搜索过滤栏样式 */
.search-filters-enhanced {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  flex-shrink: 0;
}

.filter-options {
  flex: 1;
  min-width: 0;
}

.filter-tag {
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
}

.filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.5);
}

.filter-tag.el-tag--primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  color: white;
}

.filter-tag.el-tag--success {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
  color: white;
}

.filter-tag.el-tag--warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-color: #f59e0b;
  color: white;
}

.filter-tag.filter-active {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.4);
  transform: scale(1.05);
}

/* 推荐区域样式 */
.recommendations-compact {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 8px;
  padding: 12px;
}

.recommendation-chip {
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
}

.recommendation-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 精灵卡片样式 */
.spirit-card-compact {
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.spirit-card-compact:hover {
  transform: translateY(-2px);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.2);
}

.spirit-card-compact.ring-2 {
  border-color: rgba(139, 92, 246, 0.8);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.4);
}

/* 列表视图样式 */
.spirit-row {
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 0 4px;
}

.spirit-row:hover {
  background: rgba(139, 92, 246, 0.1) !important;
  transform: translateX(4px);
}

/* 列表头部样式 */
.list-header {
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

/* 选中精灵详情样式 */
.selected-spirit-compact {
  backdrop-filter: blur(8px);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.spirits-grid::-webkit-scrollbar,
.spirits-list::-webkit-scrollbar,
.recommendations-horizontal::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.spirits-grid::-webkit-scrollbar-track,
.spirits-list::-webkit-scrollbar-track,
.recommendations-horizontal::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 3px;
}

.spirits-grid::-webkit-scrollbar-thumb,
.spirits-list::-webkit-scrollbar-thumb,
.recommendations-horizontal::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #8B5CF6, #A855F7);
  border-radius: 3px;
}

.spirits-grid::-webkit-scrollbar-thumb:hover,
.spirits-list::-webkit-scrollbar-thumb:hover,
.recommendations-horizontal::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #7C3AED, #9333EA);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .spirit-selector-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 2vh auto;
  }
  
  .search-filters-enhanced .filter-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .search-filters-enhanced .filter-label {
    min-width: auto;
  }
  
  .search-filters-enhanced .filter-options {
    width: 100%;
  }
  
  .spirits-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 480px) {
  .spirits-grid {
    grid-template-columns: 1fr !important;
  }
  
  .selected-spirit-compact .flex {
    flex-direction: column;
    text-align: center;
  }
  
  .selected-spirit-compact .grid {
    grid-template-columns: repeat(4, 1fr);
    justify-items: center;
  }
  
  .search-filters-enhanced {
    padding: 12px;
  }
  
  .filter-options .filter-tag {
    font-size: 11px;
    padding: 2px 6px;
  }
}

/* 动画效果 */
.spirit-card-compact,
.spirit-row,
.recommendation-chip {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.spirits-container {
  position: relative;
}

.spirits-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style> 