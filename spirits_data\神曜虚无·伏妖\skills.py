"""
神曜虚无·伏妖 - 技能定义模块

包含伏妖的所有技能定义：
- 彼岸殊沙 - 被动技能
- 咒缚锁妖 - 普攻技能  
- 破妄如空 - 超杀技能
- 无念缠身 - 神曜技能
- 唤灵协诛 - 通灵技能
- 饿鬼噬焰 - 通灵普攻技能
- 虚吞灭界 - 通灵超杀技能
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from core.spirit.refactored_spirit import RefactoredSpirit

from core.skill.skills import (
    Skill, SkillMetadata, DamageComponent, SkillEffectComponent,
    SingleEnemySelector, SelfSelector
)
from .skill_components import (
    BurnTargetComponent, XuWuStateComponent, TongLingProgressComponent,
    EnergyGainComponent
)
from .passive_effects import create_bian_shusha_passive_effect
from .shenyao_effects import create_wunian_chanshen_effect, create_huanling_xiezhu_effect


def create_fuyao_skills(spirit: RefactoredSpirit) -> List[Skill]:
    """创建神曜虚无·伏妖的技能"""
    
    skills = [
        # 彼岸殊沙 - 被动技能（被动效果已在精灵初始化时自动添加）
        Skill(
            metadata=SkillMetadata(
                name="彼岸殊沙",
                description="被动 Lv.1：1.优先攻击可以行动的精灵；2.自身首次攻击及受到攻击后，令目标进入虚无状态（虚无状态：非负面，不可叠加，无法攻击与被攻击，该效果结束时，对自身造成施加者攻击200%的伤害）",
                cast_type="PASSIVE",
                tags=["被动", "虚无状态", "目标选择"]
            ),
            target_selector=SelfSelector(),
            components=[]  # 🔧 修复：被动技能不需要组件，被动效果已在精灵初始化时添加
        ),
        
        # 咒缚锁妖 - 普攻技能
        Skill(
            metadata=SkillMetadata(
                name="咒缚锁妖",
                description="普攻 Lv.1：攻击目标，造成攻击*120%的魔法伤害，攻击后烧伤目标及敌阵气势最高的存活精灵（烧伤：对目标造成裁决攻击*80%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）",
                cast_type="ACTIVE",
                energy_cost=0,
                tags=["普攻", "魔法伤害", "烧伤", "虚无状态"]
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=1.2, damage_type="magic"),
                BurnTargetComponent(),
                TongLingProgressComponent()  # 火系精灵出手获得通灵进度
            ]
        ),
        
        # 破妄如空 - 超杀技能
        Skill(
            metadata=SkillMetadata(
                name="破妄如空",
                description="超杀 Lv.1：攻击目标，造成攻击*300%的魔法伤害，攻击后令目标进入虚无状态并烧伤敌阵气势最高的存活精灵（烧伤：对目标造成伏妖攻击*160%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）",
                cast_type="ULTIMATE",
                energy_cost=0,  # 超杀技能不需要固定消耗，只需要阈值判断
                tags=["超杀", "魔法伤害", "虚无状态", "烧伤"]
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=3.0, damage_type="magic"),
                XuWuStateComponent(),
                BurnTargetComponent(),
                TongLingProgressComponent()
            ]
        ),
        
        # 无念缠身 - 神曜技能
        Skill(
            metadata=SkillMetadata(
                name="无念缠身",
                description="神曜技：每个大回合开始时，对敌阵随机一位处于无法行动但不处于虚无状态的目标额外施加虚无状态。3级神格：成功触发后，令自身攻击永久提高15%（最多叠加三次）；6级神格：除首次触发外，触发时若没有无法行动且不处于虚无状态的目标则令敌阵生命值最低的可行动目标进入虚无状态；10级神格：通灵时额外触发一次",
                cast_type="SHENYAO",
                energy_cost=0,
                tags=["神曜技", "虚无状态", "攻击提升", "自动触发"]
            ),
            target_selector=SelfSelector(),
            components=[SkillEffectComponent(effect_factory=lambda: create_wunian_chanshen_effect(spirit, 6))]
        ),
        
        # 唤灵协诛 - 通灵技能
        Skill(
            metadata=SkillMetadata(
                name="唤灵协诛",
                description="通灵技：通灵条件：集齐100点进度后，触发通灵，最多通灵两次。进度获得：1.火系精灵每次出手获得5点通灵进度；2.敌阵精灵每被施加一次无法行动效果，则己方获得20点通灵进度。通灵效果：复活并获得100%生命上限的护盾，清除所有负面效果，永久变身为终昼神御；通灵时获得150点气势，二次通灵时额外获得一次立即出手",
                cast_type="TONGLING",
                energy_cost=0,
                tags=["通灵技", "变身", "复活", "护盾", "气势"]
            ),
            target_selector=SelfSelector(),
            components=[
                SkillEffectComponent(effect_factory=lambda: create_huanling_xiezhu_effect(spirit)),
                EnergyGainComponent()
            ]
        ),
        
        # 饿鬼噬焰 - 通灵普攻技能
        Skill(
            metadata=SkillMetadata(
                name="饿鬼噬焰",
                description="通灵-普攻 Lv.1：攻击目标，造成攻击*120%的魔法伤害，攻击后烧伤目标及敌阵气势最高的两位存活精灵（烧伤：对目标造成伏妖攻击*80%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）",
                cast_type="TONGLING_ACTIVE",
                energy_cost=0,
                tags=["通灵普攻", "魔法伤害", "烧伤", "虚无状态"]
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=1.2, damage_type="magic"),
                BurnTargetComponent()  # 烧伤两个目标
            ]
        ),
        
        # 虚吞灭界 - 通灵超杀技能
        Skill(
            metadata=SkillMetadata(
                name="虚吞灭界",
                description="通灵-超杀 Lv.1：攻击目标，造成攻击*300%的魔法伤害，攻击后令目标进入虚无状态并烧伤敌阵气势最高的两位存活精灵（烧伤：对目标造成伏妖攻击*160%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）",
                cast_type="TONGLING_ULTIMATE",
                energy_cost=0,  # 超杀技能不需要固定消耗，只需要阈值判断
                tags=["通灵超杀", "魔法伤害", "虚无状态", "烧伤"]
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=3.0, damage_type="magic"),
                XuWuStateComponent(),
                BurnTargetComponent()  # 烧伤两个目标
            ]
        )
    ]
    
    return skills


# 技能数据配置
FUYAO_SKILLS_DATA = [
    {
        "name": "彼岸殊沙",
        "type": "PASSIVE",
        "description": "被动 Lv.1：1.优先攻击可以行动的精灵；2.自身首次攻击及受到攻击后，令目标进入虚无状态（虚无状态：非负面，不可叠加，无法攻击与被攻击，该效果结束时，对自身造成施加者攻击200%的伤害）"
    },
    {
        "name": "咒缚锁妖",
        "type": "ACTIVE",
        "description": "普攻 Lv.1：攻击目标，造成攻击*120%的魔法伤害，攻击后烧伤目标及敌阵气势最高的存活精灵（烧伤：对目标造成裁决攻击*80%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）"
    },
    {
        "name": "破妄如空",
        "type": "ULTIMATE",
        "description": "超杀 Lv.1：气势消耗150，攻击目标，造成攻击*300%的魔法伤害，攻击后令目标进入虚无状态并烧伤敌阵气势最高的存活精灵（烧伤：对目标造成伏妖攻击*160%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）"
    },
    {
        "name": "无念缠身",
        "type": "SHENYAO",
        "description": "神曜技：每个大回合开始时，对敌阵随机一位处于无法行动但不处于虚无状态的目标额外施加虚无状态。3级神格：成功触发后，令自身攻击永久提高15%（最多叠加三次）；6级神格：除首次触发外，触发时若没有无法行动且不处于虚无状态的目标则令敌阵生命值最低的可行动目标进入虚无状态；10级神格：通灵时额外触发一次"
    },
    {
        "name": "唤灵协诛",
        "type": "TONGLING",
        "description": "通灵技：通灵条件：集齐100点进度后，触发通灵，最多通灵两次。进度获得：1.火系精灵每次出手获得5点通灵进度；2.敌阵精灵每被施加一次无法行动效果，则己方获得20点通灵进度。通灵效果：复活并获得100%生命上限的护盾，清除所有负面效果，永久变身为终昼神御；通灵时获得150点气势，二次通灵时额外获得一次立即出手"
    },
    {
        "name": "饿鬼噬焰",
        "type": "TONGLING_ACTIVE",
        "description": "通灵-普攻 Lv.1：攻击目标，造成攻击*120%的魔法伤害，攻击后烧伤目标及敌阵气势最高的两位存活精灵（烧伤：对目标造成伏妖攻击*80%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）"
    },
    {
        "name": "虚吞灭界",
        "type": "TONGLING_ULTIMATE",
        "description": "通灵-超杀 Lv.1：气势消耗150，攻击目标，造成攻击*300%的魔法伤害，攻击后令目标进入虚无状态并烧伤敌阵气势最高的两位存活精灵（烧伤：对目标造成伏妖攻击*160%的伤害，若目标处于无法行动状态，则额外令其进入虚无状态）"
    }
]


# 导出函数
__all__ = [
    'create_fuyao_skills',
    'FUYAO_SKILLS_DATA'
]
