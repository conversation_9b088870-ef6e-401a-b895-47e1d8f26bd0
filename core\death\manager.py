"""
动态死亡管理器

管理死亡检测器并处理死亡相关的动作生成。
"""
from __future__ import annotations
from typing import TYPE_CHECKING, Optional, List

if TYPE_CHECKING:
    from ..interfaces import IBattleEntity, IBattleState
    from ..action import BattleAction

from .detector import DynamicDeathDetector
from .types import DeathTriggerMode, DeathReason
from ..logging import battle_logger


class DynamicDeathManager:
    """动态死亡管理器"""
    
    def __init__(self, battle_state: Optional['IBattleState'] = None):
        """
        初始化动态死亡管理器
        
        Args:
            battle_state: 战斗状态引用
        """
        self.battle_state = battle_state
        self.detector = DynamicDeathDetector()
        self.action_queue: List['BattleAction'] = []
        
        # 注册默认的死亡处理回调
        self.detector.register_death_callback(self._create_die_action)
    
    def _create_die_action(self, entity: 'IBattleEntity', reason: DeathReason):
        """
        创建死亡动作
        
        Args:
            entity: 死亡的实体
            reason: 死亡原因
        """
        from ..action import DieAction
        
        die_action = DieAction(
            caster=None,
            target=entity,
            reason=f"动态死亡检测: {reason.value}"
        )
        
        self.action_queue.append(die_action)
        battle_logger.debug(f"创建死亡动作: {entity.name} ({reason.value})")
    
    def monitor_hp_change(
        self, 
        entity: 'IBattleEntity', 
        old_hp: float, 
        new_hp: float,
        reason: DeathReason = DeathReason.DAMAGE
    ):
        """
        监控HP变化
        
        Args:
            entity: 实体
            old_hp: 变化前的HP
            new_hp: 变化后的HP
            reason: 变化原因
        """
        self.detector.check_and_trigger_death(entity, old_hp, new_hp, reason)
    
    def get_pending_actions(self) -> List['BattleAction']:
        """
        获取待处理的动作
        
        Returns:
            待处理的动作列表
        """
        actions = self.action_queue.copy()
        self.action_queue.clear()
        return actions
    
    def process_deferred_deaths(self) -> List['BattleAction']:
        """
        处理延迟死亡，返回生成的动作
        
        Returns:
            生成的动作列表
        """
        processed_deaths = self.detector.process_pending_deaths()
        actions = self.get_pending_actions()
        
        if processed_deaths:
            battle_logger.info(f"延迟处理了 {len(processed_deaths)} 个死亡，生成 {len(actions)} 个动作")
        
        return actions
    
    def set_trigger_mode(self, mode: DeathTriggerMode):
        """
        设置触发模式
        
        Args:
            mode: 新的触发模式
        """
        old_mode = self.detector.trigger_mode
        self.detector.trigger_mode = mode
        battle_logger.info(f"死亡触发模式: {old_mode.value} -> {mode.value}")
    
    def register_custom_callback(self, callback):
        """
        注册自定义死亡回调
        
        Args:
            callback: 回调函数，接收 (entity, reason) 参数
        """
        self.detector.register_death_callback(callback)
    
    def get_statistics(self) -> dict:
        """获取管理器统计信息"""
        detector_stats = self.detector.get_statistics()
        return {
            **detector_stats,
            'pending_actions': len(self.action_queue),
            'battle_state_attached': self.battle_state is not None
        }
    
    def clear_all(self):
        """清空所有状态"""
        self.detector.clear_history()
        self.detector.clear_pending_deaths()
        self.action_queue.clear()
        battle_logger.info("清空死亡管理器所有状态")
    
    def force_process_entity_death(
        self, 
        entity: 'IBattleEntity', 
        reason: DeathReason = DeathReason.OTHER
    ):
        """
        强制处理实体死亡（不检查HP）
        
        Args:
            entity: 要处理死亡的实体
            reason: 死亡原因
        """
        battle_logger.info(f"强制处理死亡: {entity.name} ({reason.value})")
        self.detector._process_death_immediately(entity, reason)
