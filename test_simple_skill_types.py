#!/usr/bin/env python3
"""
简单的技能类型测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_skill_types():
    """简单的技能类型测试"""
    print("🔧 简单的技能类型测试...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        print(f"✅ 创建伏妖精灵: {fuyao_spirit.name}")
        
        # 检查技能类型
        print(f"\n📋 技能类型检查:")
        
        for i, skill in enumerate(fuyao_spirit.skills[:3]):  # 只检查前3个技能
            skill_name = getattr(skill.metadata, 'name', f'技能{i+1}')
            cast_type = getattr(skill.metadata, 'cast_type', 'UNKNOWN')
            components_count = len(skill.components)
            
            print(f"  {skill_name}: {cast_type} (组件: {components_count})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_skill_types()
