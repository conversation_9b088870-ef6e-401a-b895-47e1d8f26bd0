<template>
  <div class="formation-editor">
    <div class="editor-content">
      <!-- 精灵选择区域 -->
      <div class="spirit-selection">
        <h3>选择精灵</h3>
        <div class="spirit-search">
          <el-input
            v-model="searchQuery"
            placeholder="搜索精灵..."
            prefix-icon="Search"
            clearable
          />
        </div>
        
        <div class="spirit-list">
          <div
            v-for="spirit in filteredSpirits"
            :key="spirit.id"
            class="spirit-card"
            :class="{ selected: selectedSpirit?.id === spirit.id }"
            @click="selectSpirit(spirit)"
          >
            <div class="spirit-avatar">
              <el-avatar :size="40">{{ spirit.name.charAt(0) }}</el-avatar>
            </div>
            <div class="spirit-info">
              <div class="spirit-name">{{ spirit.name }}</div>
              <div class="spirit-stats">
                <span>HP: {{ spirit.attributes?.hp || 1000 }}</span>
                <span>攻击: {{ spirit.attributes?.attack || 150 }}</span>
                <span>速度: {{ spirit.attributes?.speed || 100 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阵型配置区域 -->
      <div class="formation-config">
        <h3>阵型布局 (3x3)</h3>
        <div class="formation-grid">
          <div
            v-for="(slot, index) in formationSlots"
            :key="index"
            class="formation-slot"
            :class="{ 
              occupied: slot.spirit,
              highlight: selectedSpirit && !slot.spirit 
            }"
            @click="placeSpirit(index)"
          >
            <div v-if="slot.spirit" class="placed-spirit">
              <el-avatar :size="50">{{ slot.spirit.name.charAt(0) }}</el-avatar>
              <div class="spirit-name">{{ slot.spirit.name }}</div>
              <el-button
                size="small"
                type="danger"
                circle
                class="remove-btn"
                @click.stop="removeSpirit(index)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
            <div v-else class="empty-slot">
              <div class="slot-position">{{ getSlotPosition(index) }}</div>
              <div class="slot-hint">点击放置精灵</div>
            </div>
          </div>
        </div>

        <!-- 阵型统计 -->
        <div class="formation-stats">
          <h4>阵型统计</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">精灵数量:</span>
              <span class="stat-value">{{ placedSpirits.length }}/6</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总血量:</span>
              <span class="stat-value">{{ totalHP }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总攻击:</span>
              <span class="stat-value">{{ totalAttack }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均速度:</span>
              <span class="stat-value">{{ averageSpeed }}</span>
            </div>
          </div>
        </div>

        <!-- 预设阵型 -->
        <div class="preset-formations">
          <h4>预设阵型</h4>
          <div class="preset-buttons">
            <el-button size="small" @click="loadPreset('balanced')">
              平衡型
            </el-button>
            <el-button size="small" @click="loadPreset('offensive')">
              攻击型
            </el-button>
            <el-button size="small" @click="loadPreset('defensive')">
              防守型
            </el-button>
            <el-button size="small" type="danger" @click="clearFormation">
              清空阵型
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'

interface Spirit {
  id: string
  name: string
  attributes?: {
    hp: number
    attack: number
    defense: number
    speed: number
  }
}

interface FormationSlot {
  position: [number, number]
  spirit: Spirit | null
}

// Props
const props = defineProps<{
  formation: Spirit[]
  availableSpirits: Spirit[]
}>()

// Emits
const emit = defineEmits<{
  'update:formation': [formation: Spirit[]]
  save: []
}>()

// 响应式数据
const searchQuery = ref('')
const selectedSpirit = ref<Spirit | null>(null)

// 初始化3x3阵型槽位
const formationSlots = ref<FormationSlot[]>([])

// 初始化阵型槽位
const initializeFormation = () => {
  formationSlots.value = []
  for (let row = 1; row <= 3; row++) {
    for (let col = 1; col <= 3; col++) {
      formationSlots.value.push({
        position: [row, col],
        spirit: null
      })
    }
  }
}

// 计算属性
const filteredSpirits = computed(() => {
  if (!searchQuery.value) return props.availableSpirits
  
  return props.availableSpirits.filter(spirit =>
    spirit.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const placedSpirits = computed(() => {
  return formationSlots.value
    .filter(slot => slot.spirit)
    .map(slot => slot.spirit!)
})

const totalHP = computed(() => {
  return placedSpirits.value.reduce((sum, spirit) => 
    sum + (spirit.attributes?.hp || 1000), 0
  )
})

const totalAttack = computed(() => {
  return placedSpirits.value.reduce((sum, spirit) => 
    sum + (spirit.attributes?.attack || 150), 0
  )
})

const averageSpeed = computed(() => {
  if (placedSpirits.value.length === 0) return 0
  
  const totalSpeed = placedSpirits.value.reduce((sum, spirit) => 
    sum + (spirit.attributes?.speed || 100), 0
  )
  
  return Math.round(totalSpeed / placedSpirits.value.length)
})

// 方法
const selectSpirit = (spirit: Spirit) => {
  selectedSpirit.value = spirit
}

const placeSpirit = (slotIndex: number) => {
  if (!selectedSpirit.value) {
    ElMessage.warning('请先选择一个精灵')
    return
  }

  const slot = formationSlots.value[slotIndex]
  
  if (slot.spirit) {
    ElMessage.warning('该位置已有精灵，请先移除')
    return
  }

  // 检查是否已达到最大精灵数量
  if (placedSpirits.value.length >= 6) {
    ElMessage.warning('最多只能放置6个精灵')
    return
  }

  // 检查精灵是否已被放置
  const alreadyPlaced = formationSlots.value.some(s => 
    s.spirit && s.spirit.id === selectedSpirit.value!.id
  )
  
  if (alreadyPlaced) {
    ElMessage.warning('该精灵已被放置')
    return
  }

  // 放置精灵
  slot.spirit = { ...selectedSpirit.value }
  
  // 更新formation
  updateFormation()
  
  ElMessage.success(`${selectedSpirit.value.name} 已放置在位置 ${getSlotPosition(slotIndex)}`)
}

const removeSpirit = (slotIndex: number) => {
  const slot = formationSlots.value[slotIndex]
  if (slot.spirit) {
    const spiritName = slot.spirit.name
    slot.spirit = null
    updateFormation()
    ElMessage.info(`${spiritName} 已从阵型中移除`)
  }
}

const getSlotPosition = (index: number) => {
  const row = Math.floor(index / 3) + 1
  const col = (index % 3) + 1
  return `${row}-${col}`
}

const updateFormation = () => {
  const formation = formationSlots.value
    .filter(slot => slot.spirit)
    .map(slot => ({
      ...slot.spirit!,
      position: slot.position
    }))
  
  emit('update:formation', formation)
}

const clearFormation = () => {
  formationSlots.value.forEach(slot => {
    slot.spirit = null
  })
  updateFormation()
  ElMessage.info('阵型已清空')
}

const loadPreset = (presetType: string) => {
  clearFormation()
  
  const availableSpirits = props.availableSpirits.slice(0, 6)
  
  if (availableSpirits.length === 0) {
    ElMessage.warning('没有可用的精灵')
    return
  }

  let positions: number[] = []
  
  switch (presetType) {
    case 'balanced':
      // 平衡型：前中后各2个
      positions = [0, 1, 3, 4, 6, 7] // 前排2个，中排2个，后排2个
      break
    case 'offensive':
      // 攻击型：前排多，后排少
      positions = [0, 1, 2, 3, 4, 6] // 前排3个，中排2个，后排1个
      break
    case 'defensive':
      // 防守型：后排多，前排少
      positions = [1, 4, 6, 7, 8, 5] // 前排1个，中排2个，后排3个
      break
  }

  positions.slice(0, availableSpirits.length).forEach((pos, index) => {
    if (formationSlots.value[pos]) {
      formationSlots.value[pos].spirit = { ...availableSpirits[index] }
    }
  })

  updateFormation()
  ElMessage.success(`已加载${presetType === 'balanced' ? '平衡' : presetType === 'offensive' ? '攻击' : '防守'}型阵型`)
}

// 监听props变化
watch(() => props.formation, (newFormation) => {
  // 根据传入的formation更新formationSlots
  clearFormation()
  
  newFormation.forEach(spirit => {
    if (spirit.position) {
      const [row, col] = spirit.position
      const index = (row - 1) * 3 + (col - 1)
      if (formationSlots.value[index]) {
        formationSlots.value[index].spirit = spirit
      }
    }
  })
}, { immediate: true })

// 初始化
initializeFormation()
</script>

<style scoped lang="scss">
.formation-editor {
  .editor-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    height: 500px;
  }
}

.spirit-selection {
  display: flex;
  flex-direction: column;
  
  h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
  }
  
  .spirit-search {
    margin-bottom: 16px;
  }
  
  .spirit-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 8px;
  }
}

.spirit-card {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &.selected {
    background: #e3f2fd;
    border-color: #2196f3;
  }
  
  .spirit-avatar {
    margin-right: 12px;
  }
  
  .spirit-info {
    flex: 1;
    
    .spirit-name {
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 4px;
    }
    
    .spirit-stats {
      display: flex;
      gap: 8px;
      font-size: 12px;
      color: #7f8c8d;
      
      span {
        background: #f1f3f4;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
  }
}

.formation-config {
  display: flex;
  flex-direction: column;
  
  h3, h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
  }
  
  .formation-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 24px;
  }
}

.formation-slot {
  aspect-ratio: 1;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  min-height: 100px;
  
  &:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }
  
  &.occupied {
    border-style: solid;
    border-color: #48bb78;
    background: rgba(72, 187, 120, 0.05);
  }
  
  &.highlight {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
  }
}

.placed-spirit {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  
  .spirit-name {
    margin-top: 8px;
    font-size: 12px;
    font-weight: bold;
    color: #2c3e50;
  }
  
  .remove-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    min-height: 20px;
  }
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #a0aec0;
  
  .slot-position {
    font-weight: bold;
    margin-bottom: 4px;
  }
  
  .slot-hint {
    font-size: 11px;
  }
}

.formation-stats {
  margin-bottom: 24px;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    
    .stat-label {
      color: #6c757d;
      font-size: 14px;
    }
    
    .stat-value {
      font-weight: bold;
      color: #2c3e50;
    }
  }
}

.preset-formations {
  .preset-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}
</style>
