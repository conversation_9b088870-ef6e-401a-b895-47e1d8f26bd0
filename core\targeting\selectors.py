"""
统一目标选择器模块

将所有目标选择器统一到这个模块中，提供一致的接口和易于维护的结构。
"""

from __future__ import annotations
from typing import List, Optional, Dict, Callable, TYPE_CHECKING
from dataclasses import dataclass
from abc import ABC, abstractmethod

if TYPE_CHECKING:
    from ..interfaces import IBattleEntity, IBattleState
    from ..skill.skills import SkillContext

# ============================================================================
# 核心接口
# ============================================================================

class ITargetSelector(ABC):
    """统一的目标选择器接口"""
    
    @abstractmethod
    def select_targets(
        self, 
        caster: "IBattleEntity", 
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        """选择目标"""
        pass

# ============================================================================
# 基础选择器
# ============================================================================

class SingleEnemySelector(ITargetSelector):
    """单个敌人选择器 - 选择血量最少的敌人"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            enemy_team = self._get_enemy_team(caster)
            enemies = battle_state.get_targetable_living_spirits(enemy_team)

            if not enemies:
                return []

            # 选择血量最少的敌人
            target = min(enemies, key=lambda e: getattr(e, 'current_hp', 0))
            return [target]
        except Exception:
            # 发生异常时返回空列表，避免崩溃
            return []

    def _get_enemy_team(self, caster: "IBattleEntity") -> int:
        """获取敌方队伍ID"""
        return 1 - getattr(caster, 'team', 0)


class AllEnemiesSelector(ITargetSelector):
    """所有敌人选择器"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            enemy_team = self._get_enemy_team(caster)
            return battle_state.get_targetable_living_spirits(enemy_team)
        except Exception:
            return []

    def _get_enemy_team(self, caster: "IBattleEntity") -> int:
        """获取敌方队伍ID"""
        return 1 - getattr(caster, 'team', 0)


class AllAlliesSelector(ITargetSelector):
    """所有友军选择器"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            ally_team = getattr(caster, 'team', 0)
            return battle_state.get_targetable_living_spirits(ally_team)
        except Exception:
            return []


class SelfSelector(ITargetSelector):
    """自身选择器"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        return [caster]


class LowestHpAllySelector(ITargetSelector):
    """血量最少友军选择器"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            ally_team = getattr(caster, 'team', 0)
            allies = battle_state.get_targetable_living_spirits(ally_team)

            if not allies:
                return []

            # 安全地计算血量百分比
            def safe_hp_ratio(spirit):
                current_hp = getattr(spirit, 'current_hp', 1)
                max_hp = getattr(spirit, 'max_hp', 1)
                return current_hp / max(max_hp, 1)  # 避免除零

            target = min(allies, key=safe_hp_ratio)
            return [target]
        except Exception:
            return []

# ============================================================================
# 位置策略选择器
# ============================================================================

class PriorityFrontRowSelector(ITargetSelector):
    """优先前排选择器"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            enemy_team = self._get_enemy_team(caster)
            targets = self._get_all_targets_by_priority(battle_state, enemy_team)
            return [targets[0]] if targets else []
        except Exception:
            return []

    def _get_enemy_team(self, caster: "IBattleEntity") -> int:
        """获取敌方队伍ID"""
        return 1 - getattr(caster, 'team', 0)

    def _get_all_targets_by_priority(self, battle_state: "IBattleState", target_team_id: int) -> List["IBattleEntity"]:
        """按优先级排序获取目标"""
        try:
            all_living_spirits = []
            # 安全地获取阵型
            get_formation = getattr(battle_state, 'get_formation_by_team_id', None)
            if not get_formation:
                # 降级到简单的获取存活精灵
                return getattr(battle_state, 'get_living_spirits', lambda x: [])(target_team_id)

            target_formation = get_formation(target_team_id)
            if not target_formation:
                return []

            # 按照从前到后，从上到下的顺序遍历阵型图
            for r in range(1, 4):  # Row 1 to 3
                for c in range(1, 4):  # Column 1 to 3
                    get_spirit_at = getattr(target_formation, 'get_spirit_at', None)
                    if get_spirit_at:
                        spirit = get_spirit_at(r, c)
                        if spirit and getattr(spirit, 'is_alive', False):
                            all_living_spirits.append(spirit)
            return all_living_spirits
        except Exception:
            # 发生异常时降级到简单获取
            return getattr(battle_state, 'get_living_spirits', lambda x: [])(target_team_id)


class LowestHpPercentageSelector(ITargetSelector):
    """最低血量百分比选择器"""

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            enemy_team = self._get_enemy_team(caster)
            living_spirits = battle_state.get_living_spirits(enemy_team)

            if not living_spirits:
                return []

            # 筛选出可被攻击的目标
            targetable_spirits = [s for s in living_spirits if getattr(s, 'is_alive', False)]
            if not targetable_spirits:
                return []

            # 安全地计算血量百分比
            def safe_hp_ratio(spirit):
                current_hp = getattr(spirit, 'current_hp', 1)
                max_hp = getattr(spirit, 'max_hp', 1)
                return current_hp / max(max_hp, 1)  # 避免除零

            lowest_hp_spirit = min(targetable_spirits, key=safe_hp_ratio)
            return [lowest_hp_spirit]
        except Exception:
            return []

    def _get_enemy_team(self, caster: "IBattleEntity") -> int:
        """获取敌方队伍ID"""
        return 1 - getattr(caster, 'team', 0)


class SameRowFirstSelector(ITargetSelector):
    """同排优先选择器"""
    
    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            enemy_team = self._get_enemy_team(caster)
            # 安全地获取位置信息
            attacker_pos = getattr(battle_state, 'get_spirit_position', lambda x: None)(caster.id)

            if not attacker_pos:
                # 如果攻击方没有位置信息，退化为默认的优先前排策略
                return PriorityFrontRowSelector().select_targets(caster, battle_state, context)

            attacker_row = attacker_pos[0]

            # 1. 寻找同排最前方的目标
            target = self._find_frontmost_in_row(battle_state, enemy_team, attacker_row)
            if target:
                return [target]

            # 2. 如果同排没有，则按优先级（中->上->下）寻找其他路最前方的目标
            for r in [2, 1, 3]:  # 优先顺序: 中路, 上路, 下路
                if r != attacker_row:
                    target = self._find_frontmost_in_row(battle_state, enemy_team, r)
                    if target:
                        return [target]

            return []
        except Exception:
            # 发生异常时返回空列表
            return []

    def _get_enemy_team(self, caster: "IBattleEntity") -> int:
        """获取敌方队伍ID"""
        return 1 - getattr(caster, 'team', 0)

    def _find_frontmost_in_row(self, battle_state, target_team_id, row):
        """在指定行中找到最前方的目标"""
        target_formation = battle_state.get_formation_by_team_id(target_team_id)
        for c in range(1, 4):  # Column 1 to 3
            spirit = target_formation.get_spirit_at(row, c)
            if spirit and spirit.is_alive:
                return spirit
        return None


class CleaveSelector(ITargetSelector):
    """溅射攻击选择器"""
    
    def select_targets(self, caster, battle_state, context=None):
        # 1. 确定主目标（同排或最前排）
        main_target_list = SameRowFirstSelector().select_targets(caster, battle_state, context)
        if not main_target_list:
            return []
        
        main_target = main_target_list[0]
        targets = {main_target}
        
        # 2. 找到主目标上下的单位
        try:
            enemy_team = self._get_enemy_team(caster)
            # 安全地获取位置信息
            main_target_pos = getattr(battle_state, 'get_spirit_position', lambda x: None)(main_target.id)
            if main_target_pos:
                main_target_row = main_target_pos[0]
                for r_offset in [-1, 1]:
                    adj_row = main_target_row + r_offset
                    if 1 <= adj_row <= 3:
                        adj_target = SameRowFirstSelector()._find_frontmost_in_row(
                            battle_state, enemy_team, adj_row
                        )
                        if adj_target:
                            targets.add(adj_target)
        except Exception:
            # 如果获取位置失败，只返回主目标
            pass

        return list(targets)

    def _get_enemy_team(self, caster: "IBattleEntity") -> int:
        """获取敌方队伍ID"""
        return 1 - getattr(caster, 'team', 0)

# ============================================================================
# 高级选择器
# ============================================================================

@dataclass
class ProfessionTargetSelector(ITargetSelector):
    """职业目标选择器"""

    profession: str
    team_type: str = "enemy"
    selection_method: str = "first"  # first, all, fastest

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            # 确定目标队伍
            if self.team_type == "ally":
                target_team = getattr(caster, 'team', 0)
            else:
                target_team = 1 - getattr(caster, 'team', 0)

            # 获取指定职业的精灵
            spirits = battle_state.get_living_spirits(target_team)
            profession_spirits = [
                s for s in spirits
                if hasattr(s, 'profession') and self.profession in getattr(s, 'profession', [])
            ]

            if not profession_spirits:
                return []

            # 根据选择方法返回目标
            if self.selection_method == "first":
                return [profession_spirits[0]]
            elif self.selection_method == "all":
                return profession_spirits
            elif self.selection_method == "fastest":
                # 安全地获取速度属性
                def safe_speed(spirit):
                    attributes = getattr(spirit, 'attributes', None)
                    return getattr(attributes, 'speed', 0) if attributes else 0

                profession_spirits.sort(key=safe_speed, reverse=True)
                return [profession_spirits[0]]

            return profession_spirits
        except Exception:
            return []


@dataclass
class ConditionalTargetSelector(ITargetSelector):
    """条件目标选择器"""

    condition: str
    team_type: str = "enemy"
    fallback_selector: Optional[ITargetSelector] = None

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            # 确定目标队伍
            if self.team_type == "ally":
                target_team = getattr(caster, 'team', 0)
            else:
                target_team = 1 - getattr(caster, 'team', 0)

            # 根据条件选择目标
            targets = self._select_by_condition(battle_state, target_team)

            # 如果没有找到目标且有备选选择器，使用备选选择器
            if not targets and self.fallback_selector:
                targets = self.fallback_selector.select_targets(caster, battle_state, context)

            return targets
        except Exception:
            return []

    def _select_by_condition(self, battle_state: "IBattleState", team: int) -> List["IBattleEntity"]:
        """根据条件选择目标"""
        try:
            spirits = battle_state.get_living_spirits(team)

            if self.condition == "lowest_hp":
                if spirits:
                    lowest_hp_spirit = min(spirits, key=lambda s: getattr(s, 'current_hp', 0))
                    return [lowest_hp_spirit]
            elif self.condition == "highest_attack":
                if spirits:
                    def safe_attack(spirit):
                        attributes = getattr(spirit, 'attributes', None)
                        return getattr(attributes, 'attack', 0) if attributes else 0

                    highest_attack_spirit = max(spirits, key=safe_attack)
                    return [highest_attack_spirit]
            # 可以继续添加更多条件

            return []
        except Exception:
            return []


@dataclass
class SmartTargetSelector(ITargetSelector):
    """智能目标选择器"""

    strategy: str = "adaptive"  # adaptive, aggressive, defensive
    team_type: str = "enemy"

    def select_targets(
        self,
        caster: "IBattleEntity",
        battle_state: "IBattleState",
        context: Optional["SkillContext"] = None
    ) -> List["IBattleEntity"]:
        try:
            # 确定目标队伍
            if self.team_type == "ally":
                target_team = getattr(caster, 'team', 0)
            else:
                target_team = 1 - getattr(caster, 'team', 0)

            spirits = battle_state.get_living_spirits(target_team)
            if not spirits:
                return []

            # 根据策略选择目标
            if self.strategy == "adaptive":
                return self._adaptive_selection(spirits)
            elif self.strategy == "aggressive":
                return self._aggressive_selection(spirits)
            elif self.strategy == "defensive":
                return self._defensive_selection(spirits)

            return [spirits[0]]  # 默认选择第一个
        except Exception:
            return []

    def _adaptive_selection(self, spirits: List["IBattleEntity"]) -> List["IBattleEntity"]:
        """自适应选择策略"""
        try:
            # 安全地计算血量百分比
            def safe_hp_ratio(spirit):
                current_hp = getattr(spirit, 'current_hp', 1)
                max_hp = getattr(spirit, 'max_hp', 1)
                return current_hp / max(max_hp, 1)

            # 优先选择血量最低的精灵
            low_hp_spirits = [s for s in spirits if safe_hp_ratio(s) < 0.3]
            if low_hp_spirits:
                return [min(low_hp_spirits, key=lambda s: getattr(s, 'current_hp', 0))]

            # 其次选择攻击力最高的精灵
            def safe_attack(spirit):
                attributes = getattr(spirit, 'attributes', None)
                return getattr(attributes, 'attack', 0) if attributes else 0

            return [max(spirits, key=safe_attack)]
        except Exception:
            return [spirits[0]] if spirits else []

    def _aggressive_selection(self, spirits: List["IBattleEntity"]) -> List["IBattleEntity"]:
        """激进选择策略"""
        try:
            def safe_attack(spirit):
                attributes = getattr(spirit, 'attributes', None)
                return getattr(attributes, 'attack', 0) if attributes else 0

            return [max(spirits, key=safe_attack)]
        except Exception:
            return [spirits[0]] if spirits else []

    def _defensive_selection(self, spirits: List["IBattleEntity"]) -> List["IBattleEntity"]:
        """防御选择策略"""
        try:
            return [min(spirits, key=lambda s: getattr(s, 'current_hp', 0))]
        except Exception:
            return [spirits[0]] if spirits else []

# ============================================================================
# 选择器注册表和工厂
# ============================================================================

# 🔧 优化：懒加载选择器注册表，避免在模块加载时创建实例
_SELECTOR_CLASSES: Dict[str, type] = {
    # 基础选择器
    "single_enemy": SingleEnemySelector,
    "all_enemies": AllEnemiesSelector,
    "all_allies": AllAlliesSelector,
    "self": SelfSelector,
    "lowest_hp_ally": LowestHpAllySelector,

    # 位置策略选择器
    "priority_front_row": PriorityFrontRowSelector,
    "lowest_hp_percentage": LowestHpPercentageSelector,
    "same_row_first": SameRowFirstSelector,
    "cleave": CleaveSelector,
}

# 智能选择器的特殊配置
_SMART_SELECTOR_CONFIGS = {
    "smart_adaptive": {"strategy": "adaptive"},
    "smart_aggressive": {"strategy": "aggressive"},
    "smart_defensive": {"strategy": "defensive"},
}

# 向后兼容的TARGET_SELECTORS（懒加载）
class _LazyTargetSelectors:
    def __init__(self):
        self._cache = {}

    def get(self, name: str) -> Optional[ITargetSelector]:
        if name not in self._cache:
            if name in _SELECTOR_CLASSES:
                self._cache[name] = _SELECTOR_CLASSES[name]()
            elif name in _SMART_SELECTOR_CONFIGS:
                config = _SMART_SELECTOR_CONFIGS[name]
                self._cache[name] = SmartTargetSelector(**config)
            else:
                return None
        return self._cache[name]

    def keys(self):
        return list(_SELECTOR_CLASSES.keys()) + list(_SMART_SELECTOR_CONFIGS.keys())

    def __len__(self):
        return len(_SELECTOR_CLASSES) + len(_SMART_SELECTOR_CONFIGS)

TARGET_SELECTORS = _LazyTargetSelectors()


def get_selector(name: str) -> Optional[ITargetSelector]:
    """获取选择器实例（懒加载）"""
    return TARGET_SELECTORS.get(name)


def create_n_targets_selector(n: int) -> ITargetSelector:
    """创建选择前N个目标的选择器"""
    class NTargetsSelector(ITargetSelector):
        def select_targets(
            self,
            caster: "IBattleEntity",
            battle_state: "IBattleState",
            context: Optional["SkillContext"] = None
        ) -> List["IBattleEntity"]:
            # context参数保留用于接口兼容，但当前未使用
            _ = context  # 明确标记未使用的参数
            try:
                enemy_team = 1 - getattr(caster, 'team', 0)
                all_targets = PriorityFrontRowSelector()._get_all_targets_by_priority(
                    battle_state, enemy_team
                )
                return all_targets[:n]
            except Exception:
                return []

    return NTargetsSelector()


# 向后兼容的函数式接口 - 简化实现，避免复杂的接口兼容问题
def select_target_priority_front_row(attacker: "IBattleEntity", battle_state: "IBattleState", target_team_id: int) -> List["IBattleEntity"]:
    """向后兼容的函数式接口 - 直接实现优先前排逻辑"""
    # attacker参数保留用于接口兼容，但当前未使用
    _ = attacker  # 明确标记未使用的参数
    try:
        # 直接实现优先前排逻辑，避免复杂的TempCaster
        living_spirits = []

        # 尝试使用阵型获取方法
        get_formation = getattr(battle_state, 'get_formation_by_team_id', None)
        if get_formation:
            try:
                target_formation = get_formation(target_team_id)
                if target_formation:
                    # 按照从前到后，从上到下的顺序遍历阵型图
                    for r in range(1, 4):  # Row 1 to 3
                        for c in range(1, 4):  # Column 1 to 3
                            get_spirit_at = getattr(target_formation, 'get_spirit_at', None)
                            if get_spirit_at:
                                spirit = get_spirit_at(r, c)
                                if spirit and getattr(spirit, 'is_alive', False):
                                    living_spirits.append(spirit)
            except Exception:
                pass

        # 如果阵型方法失败，降级到简单获取
        if not living_spirits:
            living_spirits = battle_state.get_living_spirits(target_team_id)

        return [living_spirits[0]] if living_spirits else []
    except Exception:
        return []


# 导出所有选择器
__all__ = [
    # 接口
    'ITargetSelector',
    
    # 基础选择器
    'SingleEnemySelector', 'AllEnemiesSelector', 'AllAlliesSelector', 
    'SelfSelector', 'LowestHpAllySelector',
    
    # 位置策略选择器
    'PriorityFrontRowSelector', 'LowestHpPercentageSelector', 
    'SameRowFirstSelector', 'CleaveSelector',
    
    # 高级选择器
    'ProfessionTargetSelector', 'ConditionalTargetSelector', 'SmartTargetSelector',
    
    # 工具函数
    'TARGET_SELECTORS', 'get_selector', 'create_n_targets_selector',
    
    # 向后兼容
    'select_target_priority_front_row'
]
