"""
AI行动生成系统扩展接口

提供插件化的扩展机制，便于后续添加新的条件检查器、效果计算器和行动策略。

设计原则：
1. 接口驱动：所有扩展都基于明确的接口
2. 注册机制：支持动态注册和注销扩展
3. 优先级控制：支持扩展的优先级排序
4. 热插拔：支持运行时添加和移除扩展
"""

from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, TYPE_CHECKING
from dataclasses import dataclass
from enum import Enum, auto

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill
    from .capability_checker import ActionCapabilityResult
    from .condition_evaluator import AttackConditionResult
    from .effect_calculator import ConditionalEffectResult

from core.logging import get_logger

logger = get_logger("ai.extensions")

class ExtensionType(Enum):
    """扩展类型枚举"""
    CONDITION_CHECKER = auto()
    EFFECT_CALCULATOR = auto()
    ACTION_STRATEGY = auto()
    SKILL_SELECTOR = auto()
    TARGET_SELECTOR = auto()

@dataclass
class ExtensionInfo:
    """扩展信息"""
    name: str
    version: str
    author: str
    description: str
    extension_type: ExtensionType
    priority: int = 50
    enabled: bool = True

# === 扩展接口定义 ===

class IConditionChecker(ABC):
    """条件检查器扩展接口"""
    
    @abstractmethod
    def get_extension_info(self) -> ExtensionInfo:
        """获取扩展信息"""
        pass
    
    @abstractmethod
    def check_condition(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> Dict[str, Any]:
        """检查自定义条件"""
        pass

class IEffectCalculator(ABC):
    """效果计算器扩展接口"""
    
    @abstractmethod
    def get_extension_info(self) -> ExtensionInfo:
        """获取扩展信息"""
        pass
    
    @abstractmethod
    def calculate_effects(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> 'ConditionalEffectResult':
        """计算自定义效果"""
        pass

class IActionStrategy(ABC):
    """行动策略扩展接口"""
    
    @abstractmethod
    def get_extension_info(self) -> ExtensionInfo:
        """获取扩展信息"""
        pass
    
    @abstractmethod
    def should_apply(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> bool:
        """判断是否应该应用此策略"""
        pass
    
    @abstractmethod
    def generate_actions(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> List[Any]:
        """生成行动"""
        pass

# === 注册表实现 ===

class ExtensionRegistry:
    """扩展注册表基类"""
    
    def __init__(self, extension_type: ExtensionType):
        self.extension_type = extension_type
        self._extensions: Dict[str, Any] = {}
        self._enabled_extensions: Dict[str, Any] = {}
        self._priority_cache: Optional[List[str]] = None
    
    def register(self, extension: Any) -> bool:
        """注册扩展"""
        try:
            info = extension.get_extension_info()
            
            if info.extension_type != self.extension_type:
                logger.error(f"扩展类型不匹配: 期望 {self.extension_type}, 实际 {info.extension_type}")
                return False
            
            self._extensions[info.name] = extension
            
            if info.enabled:
                self._enabled_extensions[info.name] = extension
            
            # 清除优先级缓存
            self._priority_cache = None
            
            logger.info(f"注册扩展: {info.name} v{info.version} by {info.author}")
            return True
            
        except Exception as e:
            logger.error(f"注册扩展失败: {e}")
            return False
    
    def unregister(self, name: str) -> bool:
        """注销扩展"""
        try:
            if name in self._extensions:
                del self._extensions[name]
            
            if name in self._enabled_extensions:
                del self._enabled_extensions[name]
            
            # 清除优先级缓存
            self._priority_cache = None
            
            logger.info(f"注销扩展: {name}")
            return True
            
        except Exception as e:
            logger.error(f"注销扩展失败: {e}")
            return False
    
    def enable(self, name: str) -> bool:
        """启用扩展"""
        if name in self._extensions:
            extension = self._extensions[name]
            self._enabled_extensions[name] = extension
            self._priority_cache = None
            logger.info(f"启用扩展: {name}")
            return True
        return False
    
    def disable(self, name: str) -> bool:
        """禁用扩展"""
        if name in self._enabled_extensions:
            del self._enabled_extensions[name]
            self._priority_cache = None
            logger.info(f"禁用扩展: {name}")
            return True
        return False
    
    def get_enabled_extensions(self) -> List[Any]:
        """获取已启用的扩展（按优先级排序）"""
        if self._priority_cache is None:
            # 按优先级排序
            sorted_names = sorted(
                self._enabled_extensions.keys(),
                key=lambda name: self._extensions[name].get_extension_info().priority
            )
            self._priority_cache = sorted_names
        
        return [self._enabled_extensions[name] for name in self._priority_cache]
    
    def get_extension_info(self, name: str) -> Optional[ExtensionInfo]:
        """获取扩展信息"""
        if name in self._extensions:
            return self._extensions[name].get_extension_info()
        return None
    
    def list_extensions(self) -> Dict[str, ExtensionInfo]:
        """列出所有扩展"""
        return {
            name: ext.get_extension_info() 
            for name, ext in self._extensions.items()
        }

class ConditionRegistry(ExtensionRegistry):
    """条件检查器注册表"""
    
    def __init__(self):
        super().__init__(ExtensionType.CONDITION_CHECKER)
    
    def check_all_conditions(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> Dict[str, Any]:
        """执行所有已启用的条件检查器"""
        all_conditions = {}
        
        for extension in self.get_enabled_extensions():
            try:
                conditions = extension.check_condition(attacker, target, skill, battle_state)
                if conditions:
                    # 使用扩展名作为前缀避免冲突
                    extension_name = extension.get_extension_info().name
                    for key, value in conditions.items():
                        all_conditions[f"{extension_name}_{key}"] = value
                        
            except Exception as e:
                extension_name = extension.get_extension_info().name
                logger.error(f"条件检查器 {extension_name} 执行失败: {e}")
        
        return all_conditions

class EffectRegistry(ExtensionRegistry):
    """效果计算器注册表"""
    
    def __init__(self):
        super().__init__(ExtensionType.EFFECT_CALCULATOR)
    
    def calculate_all_effects(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        conditions: 'AttackConditionResult',
        battle_state: 'IBattleState'
    ) -> 'ConditionalEffectResult':
        """执行所有已启用的效果计算器"""
        from .effect_calculator import ConditionalEffectResult
        
        combined_result = ConditionalEffectResult()
        
        for extension in self.get_enabled_extensions():
            try:
                result = extension.calculate_effects(attacker, target, skill, conditions, battle_state)
                if result:
                    combined_result.merge(result)
                    
            except Exception as e:
                extension_name = extension.get_extension_info().name
                logger.error(f"效果计算器 {extension_name} 执行失败: {e}")
        
        return combined_result

class StrategyRegistry(ExtensionRegistry):
    """行动策略注册表"""
    
    def __init__(self):
        super().__init__(ExtensionType.ACTION_STRATEGY)
    
    def find_applicable_strategy(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> Optional[IActionStrategy]:
        """查找适用的行动策略"""
        
        for extension in self.get_enabled_extensions():
            try:
                if extension.should_apply(spirit, battle_state):
                    return extension
            except Exception as e:
                extension_name = extension.get_extension_info().name
                logger.error(f"策略检查 {extension_name} 失败: {e}")
        
        return None

# === 全局注册表实例 ===

_condition_registry = ConditionRegistry()
_effect_registry = EffectRegistry()
_strategy_registry = StrategyRegistry()

def get_condition_registry() -> ConditionRegistry:
    """获取条件检查器注册表"""
    return _condition_registry

def get_effect_registry() -> EffectRegistry:
    """获取效果计算器注册表"""
    return _effect_registry

def get_strategy_registry() -> StrategyRegistry:
    """获取行动策略注册表"""
    return _strategy_registry

# === 便捷装饰器 ===

def register_condition_checker(name: str, version: str = "1.0.0", author: str = "Unknown", 
                              description: str = "", priority: int = 50):
    """条件检查器注册装饰器"""
    def decorator(cls):
        class WrappedChecker(cls, IConditionChecker):
            def get_extension_info(self) -> ExtensionInfo:
                return ExtensionInfo(
                    name=name,
                    version=version,
                    author=author,
                    description=description,
                    extension_type=ExtensionType.CONDITION_CHECKER,
                    priority=priority
                )
        
        # 自动注册
        instance = WrappedChecker()
        _condition_registry.register(instance)
        
        return WrappedChecker
    
    return decorator

def register_effect_calculator(name: str, version: str = "1.0.0", author: str = "Unknown",
                              description: str = "", priority: int = 50):
    """效果计算器注册装饰器"""
    def decorator(cls):
        class WrappedCalculator(cls, IEffectCalculator):
            def get_extension_info(self) -> ExtensionInfo:
                return ExtensionInfo(
                    name=name,
                    version=version,
                    author=author,
                    description=description,
                    extension_type=ExtensionType.EFFECT_CALCULATOR,
                    priority=priority
                )
        
        # 自动注册
        instance = WrappedCalculator()
        _effect_registry.register(instance)
        
        return WrappedCalculator
    
    return decorator

def register_action_strategy(name: str, version: str = "1.0.0", author: str = "Unknown",
                            description: str = "", priority: int = 50):
    """行动策略注册装饰器"""
    def decorator(cls):
        class WrappedStrategy(cls, IActionStrategy):
            def get_extension_info(self) -> ExtensionInfo:
                return ExtensionInfo(
                    name=name,
                    version=version,
                    author=author,
                    description=description,
                    extension_type=ExtensionType.ACTION_STRATEGY,
                    priority=priority
                )
        
        # 自动注册
        instance = WrappedStrategy()
        _strategy_registry.register(instance)
        
        return WrappedStrategy
    
    return decorator

# === 扩展管理器 ===

class ExtensionManager:
    """扩展管理器"""
    
    @staticmethod
    def list_all_extensions() -> Dict[str, Dict[str, ExtensionInfo]]:
        """列出所有扩展"""
        return {
            'condition_checkers': _condition_registry.list_extensions(),
            'effect_calculators': _effect_registry.list_extensions(),
            'action_strategies': _strategy_registry.list_extensions()
        }
    
    @staticmethod
    def get_extension_stats() -> Dict[str, Any]:
        """获取扩展统计信息"""
        return {
            'condition_checkers': {
                'total': len(_condition_registry._extensions),
                'enabled': len(_condition_registry._enabled_extensions)
            },
            'effect_calculators': {
                'total': len(_effect_registry._extensions),
                'enabled': len(_effect_registry._enabled_extensions)
            },
            'action_strategies': {
                'total': len(_strategy_registry._extensions),
                'enabled': len(_strategy_registry._enabled_extensions)
            }
        }
    
    @staticmethod
    def reload_extensions():
        """重新加载所有扩展（清除缓存）"""
        _condition_registry._priority_cache = None
        _effect_registry._priority_cache = None
        _strategy_registry._priority_cache = None
        
        logger.info("扩展系统已重新加载")
