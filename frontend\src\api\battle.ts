import type { Spirit, BattleState, BattleAction, BattleOutput, LogEntry } from '../types/battle'
import type { DashboardAnalytics, SpiritAnalytics, SkillAnalytics, BattleHistory } from '../types/analytics'
import axios from 'axios'

const apiClient = axios.create({
  baseURL: 'http://localhost:8000/api', // 强制指向后端API服务器
  headers: {
    'Content-Type': 'application/json',
  },
})

// 添加请求拦截器，为所有请求添加请求ID用于日志追踪
apiClient.interceptors.request.use((config) => {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  config.headers['X-Request-ID'] = requestId
  console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url} (ID: ${requestId})`)
  return config
})

// 添加响应拦截器，统一处理错误和日志
apiClient.interceptors.response.use(
  (response) => {
    const requestId = response.config.headers['X-Request-ID']
    console.log(`[API成功] ${response.config.method?.toUpperCase()} ${response.config.url} (ID: ${requestId})`)
    return response
  },
  (error) => {
    const requestId = error.config?.headers['X-Request-ID']
    console.error(`[API错误] ${error.config?.method?.toUpperCase()} ${error.config?.url} (ID: ${requestId})`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    })
    return Promise.reject(error)
  }
)

interface FormationInput {
  spirits: {
    spirit_name: string;
    position: [number, number];
  }[];
}

interface BattleInput {
  team0: FormationInput;
  team1: FormationInput;
}

export const battleAPI = {
  /**
   * 运行一个完整的战斗模拟
   * @param battleInput 包含双方阵型信息的战斗输入
   * @returns 包含获胜者和详细日志的战斗结果
   */
  async runBattle(battleInput: BattleInput): Promise<BattleOutput> {
    try {
      const response = await apiClient.post<BattleOutput>('/battles/run', battleInput)
      return response.data
    } catch (error: any) {
      // 统一的错误处理，提供更详细的错误信息
      const errorMessage = error.response?.data?.detail || error.message || '运行战斗失败'
      console.error('战斗API调用失败:', {
        message: errorMessage,
        status: error.response?.status,
        url: error.config?.url,
        requestId: error.config?.headers['X-Request-ID']
      })
      
      // 抛出格式化的错误
      const formattedError = new Error(errorMessage)
      ;(formattedError as any).status = error.response?.status
      ;(formattedError as any).requestId = error.config?.headers['X-Request-ID']
      throw formattedError
    }
  },

  // (可选) 如果需要，可以保留或添加其他API调用
  // 例如，获取可用精灵列表
  // async getAvailableSpirits(): Promise<Spirit[]> {
  //   ...
  // }
}

export const analyticsAPI = {
  /**
   * 获取仪表盘统计数据
   * @param dateRange 日期范围
   */
  async getDashboardAnalytics(dateRange: [Date, Date] | null): Promise<DashboardAnalytics> {
    try {
      const response = await apiClient.get('/analytics/dashboard', { params: { dateRange } })
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取仪表盘分析数据失败'
      console.error('分析API调用失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },

  /**
   * 获取精灵统计数据
   * @param dateRange 日期范围
   */
  async getSpiritAnalytics(dateRange: [Date, Date] | null): Promise<SpiritAnalytics> {
    try {
      const response = await apiClient.get('/analytics/spirits', { params: { dateRange } })
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取精灵分析数据失败'
      console.error('精灵分析API调用失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },

  /**
   * 获取技能统计数据
   * @param dateRange 日期范围
   */
  async getSkillAnalytics(dateRange: [Date, Date] | null): Promise<SkillAnalytics> {
    try {
      const response = await apiClient.get('/analytics/skills', { params: { dateRange } })
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取技能分析数据失败'
      console.error('技能分析API调用失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },
  
  /**
   * 获取战斗历史记录
   * @param dateRange 日期范围
   * @param filter 过滤条件
   * @param limit 数量
   * @param offset 偏移
   */
  async getBattleHistory(
    dateRange: [Date, Date] | null, 
    filter: string, 
    limit: number, 
    offset: number
  ): Promise<BattleHistory> {
    try {
      const response = await apiClient.get('/analytics/battles', { 
        params: { dateRange, filter, limit, offset } 
      })
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取战斗历史失败'
      console.error('战斗历史API调用失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },
}

export const spiritAPI = {
  /**
   * 获取所有可用的精灵原型
   */
  async getSpiritPrototypes(): Promise<Record<string, any>> {
    try {
      const response = await apiClient.get('/spirits/prototypes')
      return response.data
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || '获取精灵原型失败'
      console.error('精灵原型API调用失败:', {
        message: errorMessage,
        status: error.response?.status,
        requestId: error.config?.headers['X-Request-ID']
      })
      throw new Error(errorMessage)
    }
  },
}
