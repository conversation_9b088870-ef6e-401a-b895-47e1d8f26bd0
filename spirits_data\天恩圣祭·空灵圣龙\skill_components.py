"""
天恩圣祭·空灵圣龙 - 技能组件

包含特殊的目标选择器和技能组件：
- QianSiYinXianTargetSelector: 牵丝引线目标选择
- JianYanJingFuTargetSelector: 缄言净缚目标选择（根据隐身/神使状态改变）
- JianYanJingFuImmunityComponent: 缄言净缚免疫效果组件
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from core.skill.skills import SkillContext

from core.skill.skills import ITargetSelector, ISkillComponent
from core.action import ApplyEffectAction, LogAction
from .effects import ImmunityEffect
from .utils import (
    is_spirit_invisible, is_spirit_divine_messenger,
    filter_targetable_enemies, get_front_spirits_by_row
)


class QianSiYinXianTargetSelector(ITargetSelector):
    """
    牵丝引线目标选择器
    选择敌方单体目标
    """
    
    def select_targets(self, caster, battle_state, context):
        """选择目标"""
        # 获取敌方队伍
        enemy_team = 1 if getattr(caster, 'team', 0) == 0 else 0

        # 获取所有敌方存活精灵
        enemy_spirits = [
            spirit for spirit in battle_state.get_all_spirits()
            if spirit.team == enemy_team and spirit.is_alive
        ]

        if not enemy_spirits:
            return []

        # 使用工具函数过滤可选择的目标（排除隐身精灵）
        targetable_enemies = filter_targetable_enemies(caster, enemy_spirits)

        # 如果没有可选择的敌人，返回空列表
        if not targetable_enemies:
            return []

        # 简单选择第一个可选择的敌人
        return [targetable_enemies[0]]
    
    def _has_invisibility(self, spirit) -> bool:
        """检查精灵是否有隐身效果"""
        if not hasattr(spirit, 'effect_manager'):
            return False

        # 检查是否有隐身效果
        has_invisibility_effect = any(
            effect.name == "隐身"
            for effect in spirit.effect_manager.effects.values()
        )

        # 也检查精灵的隐身标记（如果存在）
        has_invisibility_flag = getattr(spirit, 'is_invisible', False)

        return has_invisibility_effect or has_invisibility_flag


class JianYanJingFuTargetSelector(ITargetSelector):
    """
    缄言净缚目标选择器
    
    正常情况：选择敌方单体目标
    隐身/神使状态：选择敌阵各横排最靠前的精灵
    """
    
    def select_targets(self, caster, battle_state, context):
        """选择目标"""
        # 检查施法者是否处于隐身或神使状态
        has_invisibility = is_spirit_invisible(caster)
        has_divine_messenger = is_spirit_divine_messenger(caster)

        if has_invisibility or has_divine_messenger:
            # 特殊模式：选择敌阵各横排最靠前的精灵
            return self._select_front_row_enemies(caster, battle_state)
        else:
            # 正常模式：选择敌方单体目标
            return self._select_single_enemy(caster, battle_state)
    
    def _select_single_enemy(self, caster, battle_state):
        """选择敌方单体目标"""
        enemy_team = 1 if getattr(caster, 'team', 0) == 0 else 0

        enemy_spirits = [
            spirit for spirit in battle_state.get_all_spirits()
            if spirit.team == enemy_team and spirit.is_alive
        ]

        if not enemy_spirits:
            return []

        # 使用工具函数过滤可选择的目标
        targetable_enemies = filter_targetable_enemies(caster, enemy_spirits)

        if not targetable_enemies:
            return []

        return [targetable_enemies[0]]
    
    def _select_front_row_enemies(self, caster, battle_state):
        """选择敌阵各横排最靠前的精灵"""
        enemy_team = 1 if getattr(caster, 'team', 0) == 0 else 0

        enemy_spirits = [
            spirit for spirit in battle_state.get_all_spirits()
            if spirit.team == enemy_team and spirit.is_alive
        ]

        if not enemy_spirits:
            return []

        # 使用工具函数获取每排最前的精灵
        front_enemies = get_front_spirits_by_row(enemy_spirits, enemy_team)

        return front_enemies
    
    # 这些方法已经被utils.py中的工具函数替代


class JianYanJingFuImmunityComponent(ISkillComponent):
    """
    缄言净缚免疫效果组件
    
    攻击后令各横排最靠前的精灵获得免疫(持续1次攻击)
    """
    
    def execute(self, caster, targets, battle_state, context: Optional["SkillContext"] = None):
        """执行组件效果"""
        actions = []
        
        # 获取己方队伍
        ally_team = getattr(caster, 'team', 0)

        ally_spirits = [
            spirit for spirit in battle_state.get_all_spirits()
            if spirit.team == ally_team and spirit.is_alive
        ]

        if not ally_spirits:
            return actions

        # 使用工具函数获取每排最前的精灵
        immunity_targets = get_front_spirits_by_row(ally_spirits, ally_team)
        
        # 为选中的精灵添加免疫效果
        for target in immunity_targets:
            immunity_effect = ImmunityEffect(duration=1)
            actions.extend([
                LogAction(
                    caster=caster,
                    message=f"[缄言净缚] {target.name} 获得免疫效果！"
                ),
                ApplyEffectAction(
                    caster=caster,
                    target=target,
                    effect=immunity_effect
                )
            ])
        
        return actions


class JianYanJingFuUltimateDamageComponent:
    """
    缄言净缚超杀伤害组件

    自动处理气势消耗和溢出气势计算的超杀伤害
    """

    def __init__(self):
        self.skill_name = "缄言净缚"
        self.power_multiplier = 3.0

    def execute(self, caster, targets, battle_state, context):
        """执行缄言净缚的超杀伤害"""
        from core.action import DamageAction, DamageType, LogAction, ConsumeEnergyAction

        actions = []

        # 检查是否是超杀技能
        if not context.is_ultimate:
            # 如果不是超杀技能，使用普通伤害逻辑
            for target in targets:
                if target.is_alive:
                    damage_action = DamageAction(
                        caster=caster,
                        target=target,
                        power_multiplier=self.power_multiplier,
                        damage_value=None,
                        damage_type=DamageType.MAGIC,
                        is_ultimate=False,
                        skill_name=self.skill_name
                    )
                    actions.append(damage_action)
            return actions

        # 超杀技能处理
        # 1. 获取超杀阈值（缄言净缚的阈值是150）
        ultimate_threshold = 150

        # 2. 获取当前气势
        current_energy = getattr(caster, 'energy', 0)

        # 3. 检查是否可以释放
        if current_energy < ultimate_threshold:
            return []  # 气势不足

        # 4. 计算溢出气势
        overflow_energy = max(0, current_energy - ultimate_threshold)

        # 5. 创建气势消耗动作
        consume_action = ConsumeEnergyAction(
            caster=caster,
            target=caster,
            amount=current_energy  # 消耗所有气势
        )
        actions.append(consume_action)

        # 6. 添加超杀释放日志
        log_message = (
            f"💥 [{self.skill_name}] {caster.name} 消耗所有气势({current_energy})释放超杀技能！"
            f"溢出气势: {overflow_energy}"
        )
        log_action = LogAction(caster=caster, message=log_message)
        actions.append(log_action)

        # 7. 对每个目标造成伤害
        for target in targets:
            if target.is_alive:
                # 创建增强的伤害动作
                damage_action = DamageAction(
                    caster=caster,
                    target=target,
                    power_multiplier=self.power_multiplier,
                    damage_value=None,  # 让伤害计算器处理
                    damage_type=DamageType.MAGIC,
                    is_ultimate=True,
                    skill_name=self.skill_name,
                    overflow_energy=overflow_energy,
                    ultimate_threshold=ultimate_threshold
                )

                actions.append(damage_action)

        return actions


# 导出所有组件
__all__ = [
    'QianSiYinXianTargetSelector',
    'JianYanJingFuTargetSelector',
    'JianYanJingFuImmunityComponent',
    'JianYanJingFuUltimateDamageComponent'
]
