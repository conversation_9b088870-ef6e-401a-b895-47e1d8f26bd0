"""
基于 JSON 配置的精灵工厂

提供通用的精灵创建功能，从 JSON 文件加载配置
"""
from __future__ import annotations
from typing import Optional, Dict, Any, List

from .spirit import Spirit, SpiritMetadata
from .json_loader import get_spirit_json_loader, SpiritJsonConfig
from ..logging import get_logger

logger = get_logger("core.spirit.json_factory")


class SpiritJsonFactory:
    """基于 JSON 配置的精灵工厂"""

    def __init__(self):
        self.loader = get_spirit_json_loader()

    def create_spirit(self, spirit_id: str, team: int = 0,
                     override_position: Optional[tuple] = None) -> Optional[Spirit]:
        """创建精灵的简化方法名"""
        return self.create_spirit_from_json(spirit_id, team, override_position)

    def list_available_spirits(self) -> List[str]:
        """列出所有可用的精灵ID"""
        try:
            # 获取所有可用的精灵配置
            available_spirits = []

            # 检查 JSON 配置目录
            import os
            json_dir = self.loader.json_dir
            if os.path.exists(json_dir):
                for filename in os.listdir(json_dir):
                    if filename.endswith('.json'):
                        # 尝试加载配置以验证有效性
                        spirit_name = filename[:-5]  # 移除 .json 扩展名

                        # 尝试通过中文文件名映射获取 spirit_id
                        chinese_to_id_map = {
                            "赤妖王·御神": "chiyaowang_yushen",
                            "神曜虚无·伏妖": "shenyao_xuwu_fuyao",
                            "天恩圣祭·空灵圣龙": "tianen_shengji_kongling_shenglong",
                            "神曜圣谕·女帝": "shenyao_shengyu_nudi"
                        }

                        spirit_id = chinese_to_id_map.get(spirit_name, spirit_name)

                        # 验证配置是否可以加载
                        config = self.loader.load_spirit_config(spirit_id)
                        if config:
                            available_spirits.append(spirit_id)

            return available_spirits
        except Exception as e:
            logger.warning(f"列出可用精灵时发生错误: {e}")
            return []

    def create_spirit_from_json(self, spirit_id: str, team: int = 0,
                               override_position: Optional[tuple] = None) -> Optional[Spirit]:
        """
        从 JSON 配置创建精灵
        
        Args:
            spirit_id: 精灵ID
            team: 队伍编号
            override_position: 覆盖位置（可选）
            
        Returns:
            精灵实例，如果创建失败则返回 None
        """
        try:
            # 加载配置
            config = self.loader.load_spirit_config(spirit_id)
            if config is None:
                logger.error(f"无法加载精灵配置: {spirit_id}")
                return None
            
            # 创建属性 - 使用优化后的方法
            from ..attribute import Attributes
            attributes = Attributes.create_with_validation(config.attributes)
            
            # 创建元数据
            metadata = SpiritMetadata(
                element=self.loader.get_element_type(config),
                professions=self.loader.get_profession_types(config),
                tags=set(config.tags),
                shenge_level=config.shenge_level
            )

            # 如果有神曜配置，添加到元数据中
            if config.shenyao_config:
                metadata.shenyao_config = config.shenyao_config
            
            # 确定位置
            if override_position:
                position = override_position
            else:
                position_list = config.position if config.position else [1, 1]
                position = (position_list[0], position_list[1]) if len(position_list) >= 2 else (1, 1)
            
            # 创建精灵实例
            spirit = Spirit(
                id=config.id,
                name=config.name,
                attributes=attributes,
                position=position,
                team=team,
                skills=[],  # 技能将在后续添加
                metadata=metadata
            )

            # 添加技能组件
            from ..components import SkillComponent
            skill_component = SkillComponent(owner=spirit)
            spirit.components.add_component(skill_component)

            # 处理普通技能（非超杀技能）
            # 排除所有超杀技能类型
            ultimate_types = ['ultimate', 'tongling_ultimate', 'shenyao_ultimate', 'hero_ultimate']
            regular_skills = [skill for skill in config.skills if skill.get('type') not in ultimate_types]
            if regular_skills:
                logger.debug(f"为精灵 {config.name} 处理 {len(regular_skills)} 个普通技能")
                # 这里可以添加具体的技能创建逻辑
                # 目前先创建基础的技能对象
                for skill_data in regular_skills:
                    try:
                        skill = self._create_skill_from_data(skill_data, spirit)
                        if skill:
                            skill_component.add_skill(skill)
                            logger.debug(f"添加技能: {skill.name}")
                    except Exception as e:
                        logger.warning(f"创建技能失败 {skill_data.get('name', 'Unknown')}: {e}")

            # 处理超杀技能（既要创建技能对象，也要加载到超杀管理器）
            # 支持多种超杀技能类型标识
            ultimate_types = ['ultimate', 'tongling_ultimate', 'shenyao_ultimate', 'hero_ultimate']
            ultimate_skills = [skill for skill in config.skills if skill.get('type') in ultimate_types]
            if ultimate_skills:
                logger.debug(f"为精灵 {config.name} 处理 {len(ultimate_skills)} 个超杀技能")

                # 1. 创建超杀技能对象并添加到技能组件
                for skill_data in ultimate_skills:
                    try:
                        skill = self._create_skill_from_data(skill_data, spirit)
                        if skill:
                            skill_component.add_skill(skill)
                            logger.debug(f"添加超杀技能: {skill.name}")
                    except Exception as e:
                        logger.warning(f"创建超杀技能失败 {skill_data.get('name', 'Unknown')}: {e}")

                # 2. 转换为超杀技能管理器需要的格式
                ultimate_configs = []
                for skill in ultimate_skills:
                    ultimate_config = {
                        'skill_id': skill['skill_id'],
                        'name': skill['name'],
                        'energy_threshold': skill.get('energy_threshold', 300),
                        'description': skill.get('description', ''),
                        'energy_cost': skill.get('energy_cost', 0)
                    }
                    ultimate_configs.append(ultimate_config)

                spirit.ultimate_manager.load_from_config(ultimate_configs)
                logger.debug(f"为精灵 {config.name} 加载了 {len(ultimate_configs)} 个超杀技能配置")

            logger.info(f"成功创建精灵: {config.name} ({spirit_id})")
            return spirit

        except Exception as e:
            logger.error(f"创建精灵失败 {spirit_id}: {e}")
            return None

    def _create_skill_from_data(self, skill_data: Dict[str, Any], spirit: 'Spirit') -> Optional[Any]:
        """
        从配置数据创建技能

        Args:
            skill_data: 技能配置数据
            spirit: 精灵实例

        Returns:
            技能实例，如果创建失败返回 None
        """
        try:
            from ..skill.skills import Skill, SkillMetadata, DamageComponent
            from ..targeting.selectors import SingleEnemySelector  # 导入目标选择器

            # 根据技能类型设置标签和类型
            skill_type = skill_data.get('type', 'active')
            tags = skill_data.get('tags', [])

            # 根据技能类型添加相应标签
            if skill_type == 'ultimate':
                cast_type = 'ULTIMATE'
                if 'ultimate' not in tags:
                    tags.append('ultimate')
            elif skill_type == 'passive':
                cast_type = 'PASSIVE'
                if 'passive' not in tags:
                    tags.append('passive')
            elif skill_type == 'shenyao':
                cast_type = 'SHENYAO'  # 🔧 修复：神曜技能应该有自己的类型
                if 'shenyao' not in tags:
                    tags.append('shenyao')
            elif skill_type == 'tongling':
                cast_type = 'TONGLING'  # 🔧 修复：通灵技能应该有自己的类型
                if 'tongling' not in tags:
                    tags.append('tongling')
            elif skill_type == 'tongling_active':
                cast_type = 'ACTIVE'
                if 'tongling' not in tags:
                    tags.append('tongling')
                if 'tongling_active' not in tags:
                    tags.append('tongling_active')
            elif skill_type == 'tongling_ultimate':
                cast_type = 'ULTIMATE'  # 修复：通灵超杀应该是ULTIMATE类型
                if 'tongling' not in tags:
                    tags.append('tongling')
                if 'tongling_ultimate' not in tags:
                    tags.append('tongling_ultimate')
            else:  # active 或其他
                cast_type = skill_data.get('cast_type', 'ACTIVE')

            # 创建技能元数据
            metadata = SkillMetadata(
                name=skill_data.get('name', '未知技能'),
                description=skill_data.get('description', ''),
                category=skill_data.get('category', 'ATTACK'),
                cast_type=cast_type,
                energy_cost=skill_data.get('energy_cost', 0),
                cooldown=skill_data.get('cooldown', 0),
                tags=tags
            )

            # 创建基础的技能组件
            components = []

            # 🔧 修复：被动技能不应该有任何组件，因为被动效果已在精灵初始化时添加
            if cast_type != 'PASSIVE':
                # 如果有伤害配置，添加伤害组件
                if 'damage' in skill_data:
                    damage_config = skill_data['damage']
                    power_multiplier = damage_config.get('power_multiplier', 1.0)
                    components.append(DamageComponent(power_multiplier=power_multiplier))
                else:
                    # 默认添加基础伤害组件（仅对非被动技能）
                    components.append(DamageComponent(power_multiplier=1.0))

            # 创建目标选择器（默认单体目标）
            target_selector = SingleEnemySelector()

            # 创建技能实例
            skill = Skill(
                metadata=metadata,
                target_selector=target_selector,
                components=components,
                owner=spirit
            )

            logger.debug(f"成功创建技能: {skill.metadata.name}")
            return skill

        except Exception as e:
            logger.error(f"创建技能失败 {skill_data.get('name', 'Unknown')}: {e}")
            return None
    
    def create_spirit_with_skills(self, spirit_id: str, skill_creator_func=None, 
                                 team: int = 0, override_position: Optional[tuple] = None) -> Optional[Spirit]:
        """
        创建带技能的精灵
        
        Args:
            spirit_id: 精灵ID
            skill_creator_func: 技能创建函数
            team: 队伍编号
            override_position: 覆盖位置（可选）
            
        Returns:
            精灵实例，如果创建失败则返回 None
        """
        # 创建基础精灵
        spirit = self.create_spirit_from_json(spirit_id, team, override_position)
        if spirit is None:
            return None
        
        # 添加技能
        if skill_creator_func:
            try:
                from ..components import SkillComponent
                
                skills = skill_creator_func(spirit)
                skill_component = spirit.components.get_component(SkillComponent)
                
                if skill_component and skills:
                    for skill in skills:
                        skill_component.add_skill(skill)
                    
                    logger.debug(f"为精灵 {spirit.name} 添加了 {len(skills)} 个技能")
                
            except Exception as e:
                logger.error(f"为精灵 {spirit_id} 添加技能失败: {e}")
        
        return spirit
    
    def get_spirit_info(self, spirit_id: str) -> Optional[Dict[str, Any]]:
        """
        获取精灵信息
        
        Args:
            spirit_id: 精灵ID
            
        Returns:
            精灵信息字典
        """
        config = self.loader.load_spirit_config(spirit_id)
        if config is None:
            return None
        
        return {
            'id': config.id,
            'name': config.name,
            'element': config.element,
            'professions': config.professions,
            'tags': config.tags,
            'shenge_level': config.shenge_level,
            'position': config.position,
            'skills': config.skills,
            'passive_effects': config.passive_effects,
            'description': config.description,
            'rarity': config.rarity,
            'version': config.version,
            'attributes': config.attributes
        }
    
    def list_available_spirits(self) -> List[str]:
        """列出所有可用的精灵"""
        return self.loader.list_available_spirits()
    
    def validate_spirit_config(self, spirit_id: str) -> Dict[str, Any]:
        """
        验证精灵配置
        
        Args:
            spirit_id: 精灵ID
            
        Returns:
            验证结果
        """
        config = self.loader.load_spirit_config(spirit_id)
        if config is None:
            return {
                'valid': False,
                'errors': [f'精灵配置文件不存在: {spirit_id}'],
                'warnings': []
            }
        
        errors = []
        warnings = []
        
        # 验证必需字段
        if not config.name:
            errors.append('精灵名称不能为空')
        
        if not config.attributes:
            errors.append('属性配置不能为空')
        else:
            # 验证属性值
            attrs = config.attributes
            if attrs.get('base_hp', 0) <= 0:
                errors.append('基础生命值必须大于0')
            if attrs.get('base_attack', 0) <= 0:
                errors.append('基础攻击力必须大于0')
            
            # 检查百分比属性范围
            for attr_name in ['base_crit_rate', 'base_dodge_rate', 'base_hit_rate']:
                value = attrs.get(attr_name, 0)
                if value < -1 or value > 1:
                    warnings.append(f'{attr_name} 值 {value} 超出正常范围 [-1, 1]')
        
        # 验证位置
        if len(config.position) != 2:
            errors.append('位置必须是包含两个元素的列表')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }


# 全局工厂实例
_global_factory: Optional[SpiritJsonFactory] = None

def get_json_spirit_factory() -> SpiritJsonFactory:
    """获取全局精灵工厂实例"""
    global _global_factory
    if _global_factory is None:
        _global_factory = SpiritJsonFactory()
    return _global_factory


def create_spirit_from_json(spirit_id: str, team: int = 0, 
                           override_position: Optional[tuple] = None) -> Optional[Spirit]:
    """
    从 JSON 配置创建精灵的便捷函数
    
    Args:
        spirit_id: 精灵ID
        team: 队伍编号
        override_position: 覆盖位置（可选）
        
    Returns:
        精灵实例，如果创建失败则返回 None
    """
    factory = get_json_spirit_factory()
    return factory.create_spirit_from_json(spirit_id, team, override_position)


def get_spirit_info_from_json(spirit_id: str) -> Optional[Dict[str, Any]]:
    """
    从 JSON 获取精灵信息的便捷函数
    
    Args:
        spirit_id: 精灵ID
        
    Returns:
        精灵信息字典
    """
    factory = get_json_spirit_factory()
    return factory.get_spirit_info(spirit_id)


def list_json_spirits() -> List[str]:
    """
    列出所有 JSON 精灵的便捷函数
    
    Returns:
        精灵ID列表
    """
    factory = get_json_spirit_factory()
    return factory.list_available_spirits()


def validate_json_spirit(spirit_id: str) -> Dict[str, Any]:
    """
    验证 JSON 精灵配置的便捷函数
    
    Args:
        spirit_id: 精灵ID
        
    Returns:
        验证结果
    """
    factory = get_json_spirit_factory()
    return factory.validate_spirit_config(spirit_id)
