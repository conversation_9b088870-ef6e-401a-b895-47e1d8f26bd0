#!/usr/bin/env python3
"""
AoQiAI 战斗引擎调试工具

专门用于本地调试战斗引擎的交互式工具
支持步进调试、状态检查、日志记录等功能
"""
import sys
import os
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.battle.engines.battle_engine import RefactoredBattleEngine
    from core.formation import Formation
    from core.battle.models import BattleState
    # from core.battle.config import get_battle_config  # 暂时注释掉
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保项目路径正确，并且所有依赖都已安装")
    sys.exit(1)


@dataclass
class DebugSession:
    """调试会话信息"""
    session_id: str
    start_time: float
    battle_engine: Optional[RefactoredBattleEngine] = None
    current_round: int = 0
    step_mode: bool = True
    auto_delay: float = 1.0
    log_level: str = "INFO"
    save_logs: bool = True
    
    def to_dict(self) -> Dict:
        return {
            "session_id": self.session_id,
            "start_time": self.start_time,
            "current_round": self.current_round,
            "step_mode": self.step_mode,
            "auto_delay": self.auto_delay,
            "log_level": self.log_level,
            "save_logs": self.save_logs
        }


class BattleEngineDebugger:
    """战斗引擎调试器"""
    
    def __init__(self):
        self.session: Optional[DebugSession] = None
        self.battle_logs: List[Dict] = []
        self.breakpoints: List[str] = []
        self.watch_variables: List[str] = []
        self.debug_output_dir = Path("debug_output")
        self.debug_output_dir.mkdir(exist_ok=True)
        
    def start_debug_session(self) -> DebugSession:
        """开始调试会话"""
        session_id = f"debug_{int(time.time())}"
        self.session = DebugSession(
            session_id=session_id,
            start_time=time.time()
        )
        
        self.log("INFO", f"🚀 开始调试会话: {session_id}")
        return self.session
    
    def log(self, level: str, message: str, data: Optional[Dict] = None):
        """记录调试日志"""
        timestamp = time.time()
        log_entry = {
            "timestamp": timestamp,
            "level": level,
            "message": message,
            "data": data or {},
            "round": self.session.current_round if self.session else 0
        }
        
        self.battle_logs.append(log_entry)
        
        # 控制台输出
        time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))
        print(f"[{time_str}] {level}: {message}")
        
        if data and level in ["DEBUG", "TRACE"]:
            print(f"    数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    def create_test_formations(self) -> tuple[Formation, Formation]:
        """创建测试阵型"""
        self.log("INFO", "🏗️ 创建测试阵型...")
        
        # 获取可用精灵列表
        available_spirits = list(SPIRIT_CREATORS.keys())[:6]  # 取前6个精灵
        
        if len(available_spirits) < 2:
            self.log("ERROR", "可用精灵数量不足，至少需要2个精灵")
            raise ValueError("精灵数量不足")
        
        # 创建阵型1
        formation1 = Formation()
        team1_spirits = available_spirits[:3]  # 前3个精灵
        
        for i, spirit_name in enumerate(team1_spirits):
            try:
                spirit = create_spirit_by_name(spirit_name, team=0, position=(1, i+1))
                if spirit:
                    formation1.add_spirit(spirit, 1, i+1)
                    self.log("DEBUG", f"队伍1添加精灵: {spirit_name} 位置(1,{i+1})")
            except Exception as e:
                self.log("WARNING", f"创建精灵失败: {spirit_name}, 错误: {e}")
        
        # 创建阵型2
        formation2 = Formation()
        team2_spirits = available_spirits[3:6] if len(available_spirits) >= 6 else available_spirits[1:4]
        
        for i, spirit_name in enumerate(team2_spirits):
            try:
                spirit = create_spirit_by_name(spirit_name, team=1, position=(3, i+1))
                if spirit:
                    formation2.add_spirit(spirit, 3, i+1)
                    self.log("DEBUG", f"队伍2添加精灵: {spirit_name} 位置(3,{i+1})")
            except Exception as e:
                self.log("WARNING", f"创建精灵失败: {spirit_name}, 错误: {e}")
        
        self.log("INFO", f"✅ 阵型创建完成 - 队伍1: {len(formation1.spirits)}个精灵, 队伍2: {len(formation2.spirits)}个精灵")
        return formation1, formation2
    
    def create_custom_formations(self, team1_config: List[Dict], team2_config: List[Dict]) -> tuple[Formation, Formation]:
        """创建自定义阵型"""
        self.log("INFO", "🎨 创建自定义阵型...")
        
        formation1 = Formation()
        formation2 = Formation()
        
        # 创建队伍1
        for config in team1_config:
            spirit_name = config["name"]
            position = config.get("position", (1, 1))
            
            try:
                spirit = create_spirit_by_name(spirit_name, team=0, position=position)
                if spirit:
                    formation1.add_spirit(spirit, position[0], position[1])
                    self.log("DEBUG", f"队伍1添加精灵: {spirit_name} 位置{position}")
            except Exception as e:
                self.log("ERROR", f"创建队伍1精灵失败: {spirit_name}, 错误: {e}")
        
        # 创建队伍2
        for config in team2_config:
            spirit_name = config["name"]
            position = config.get("position", (3, 1))
            
            try:
                spirit = create_spirit_by_name(spirit_name, team=1, position=position)
                if spirit:
                    formation2.add_spirit(spirit, position[0], position[1])
                    self.log("DEBUG", f"队伍2添加精灵: {spirit_name} 位置{position}")
            except Exception as e:
                self.log("ERROR", f"创建队伍2精灵失败: {spirit_name}, 错误: {e}")
        
        return formation1, formation2
    
    def setup_battle_engine(self, formation1: Formation, formation2: Formation) -> RefactoredBattleEngine:
        """设置战斗引擎"""
        self.log("INFO", "⚙️ 初始化战斗引擎...")
        
        try:
            # 创建战斗引擎
            engine = RefactoredBattleEngine(formation1, formation2)
            self.session.battle_engine = engine
            
            # 记录配置信息
            config = get_battle_config()
            self.log("DEBUG", "战斗配置", {
                "use_refactored_coordination": config.use_refactored_coordination,
                "max_rounds": config.max_rounds,
                "round_timeout": config.round_timeout_seconds
            })
            
            # 记录初始状态
            self.log_battle_state(engine.battle_state, "初始状态")
            
            self.log("INFO", "✅ 战斗引擎初始化完成")
            return engine
            
        except Exception as e:
            self.log("ERROR", f"战斗引擎初始化失败: {e}")
            raise
    
    def log_battle_state(self, battle_state: BattleState, context: str = ""):
        """记录战斗状态"""
        if not battle_state:
            return
            
        state_info = {
            "context": context,
            "round_num": battle_state.round_num,
            "total_spirits": len(battle_state.spirits),
            "alive_spirits": len([s for s in battle_state.spirits.values() if s.is_alive]),
            "team_0_alive": len([s for s in battle_state.spirits.values() if s.team == 0 and s.is_alive]),
            "team_1_alive": len([s for s in battle_state.spirits.values() if s.team == 1 and s.is_alive]),
        }
        
        # 详细精灵状态
        spirits_detail = {}
        for spirit_id, spirit in battle_state.spirits.items():
            spirits_detail[spirit_id] = {
                "name": spirit.name,
                "team": spirit.team,
                "hp": f"{spirit.attributes.hp}/{spirit.attributes.max_hp}",
                "is_alive": spirit.is_alive,
                "position": getattr(spirit, 'position', None)
            }
        
        state_info["spirits"] = spirits_detail
        
        self.log("DEBUG", f"战斗状态 - {context}", state_info)
    
    def step_through_battle(self, engine: RefactoredBattleEngine):
        """步进式战斗调试"""
        if not self.session:
            self.log("ERROR", "调试会话未初始化")
            return

        self.log("INFO", "🎮 开始步进式战斗调试")
        self.print_debug_commands()

        try:
            for round_result in engine.run_battle():
                self.session.current_round = round_result.get("round_num", self.session.current_round)

                # 记录回合结果
                result_type = round_result.get('type', 'unknown')
                self.log("INFO", f"📊 第{self.session.current_round}回合 - {result_type}")

                # 详细记录不同类型的回合结果
                if result_type == "round_start":
                    self.log("DEBUG", "回合开始", {"round_num": self.session.current_round})
                elif result_type == "round_actions":
                    actions = round_result.get("actions", [])
                    self.log("INFO", f"⚔️ 执行了 {len(actions)} 个动作")
                    for action in actions:
                        self.log("DEBUG", f"动作: {action.get('type', 'unknown')}", action)
                elif result_type == "round_end":
                    self.log("DEBUG", "回合结束", round_result)

                self.log("TRACE", "回合完整数据", round_result)

                # 记录当前状态
                self.log_battle_state(engine.battle_state, f"第{self.session.current_round}回合后")

                # 检查断点
                if self.should_break(round_result):
                    self.log("INFO", "🔴 触发断点")
                    self.session.step_mode = True  # 强制进入步进模式

                # 步进控制
                if self.session.step_mode:
                    while True:
                        command = self.wait_for_user_input()
                        if self.handle_debug_command(command, engine):
                            break  # 继续下一回合
                        # 如果返回False，继续等待命令
                else:
                    time.sleep(self.session.auto_delay)

                # 检查战斗是否结束
                if result_type == "battle_end":
                    winner = round_result.get("winner")
                    winner_text = f"队伍{winner}" if winner is not None else "平局"
                    self.log("INFO", f"🏆 战斗结束! 获胜者: {winner_text}")
                    self.print_battle_summary(engine, round_result)
                    break

        except KeyboardInterrupt:
            self.log("INFO", "🛑 用户中断调试")
        except Exception as e:
            self.log("ERROR", f"战斗执行异常: {e}")
            import traceback
            self.log("ERROR", "异常堆栈", {"traceback": traceback.format_exc()})

    def print_debug_commands(self):
        """打印调试命令帮助"""
        print("\n" + "="*50)
        print("🎮 调试命令:")
        print("  [Enter] - 下一步")
        print("  c       - 连续模式")
        print("  s       - 显示详细状态")
        print("  h       - 显示帮助")
        print("  b [条件] - 断点管理")
        print("  w [变量] - 监视变量")
        print("  l [级别] - 设置日志级别")
        print("  save    - 保存当前状态")
        print("  q       - 退出调试")
        print("="*50)

    def handle_debug_command(self, command: str, engine: RefactoredBattleEngine) -> bool:
        """处理调试命令，返回True表示继续执行，False表示继续等待命令"""
        if not command:  # Enter键
            return True

        cmd_parts = command.split()
        cmd = cmd_parts[0].lower()

        if cmd == 'q':
            self.log("INFO", "🛑 用户终止调试")
            raise KeyboardInterrupt()

        elif cmd == 'c':
            self.session.step_mode = False
            self.log("INFO", "🏃 切换到连续模式")
            return True

        elif cmd == 's':
            self.print_detailed_status(engine)
            return False

        elif cmd == 'h':
            self.print_debug_commands()
            return False

        elif cmd == 'b':
            self.handle_breakpoint_command(command)
            return False

        elif cmd == 'w':
            self.handle_watch_command(command, engine)
            return False

        elif cmd == 'l':
            self.handle_log_level_command(command)
            return False

        elif cmd == 'save':
            self.save_debug_session()
            self.log("INFO", "💾 当前状态已保存")
            return False

        else:
            print(f"未知命令: {command}，输入 'h' 查看帮助")
            return False

    def handle_watch_command(self, command: str, engine: RefactoredBattleEngine):
        """处理监视变量命令"""
        parts = command.split()
        if len(parts) == 1:
            # 显示当前监视的变量
            if self.watch_variables:
                print("当前监视变量:")
                for var in self.watch_variables:
                    value = self.get_watch_value(var, engine)
                    print(f"  {var} = {value}")
            else:
                print("没有监视变量")
        else:
            var_name = parts[1]
            if var_name in self.watch_variables:
                self.watch_variables.remove(var_name)
                print(f"移除监视变量: {var_name}")
            else:
                self.watch_variables.append(var_name)
                value = self.get_watch_value(var_name, engine)
                print(f"添加监视变量: {var_name} = {value}")

    def get_watch_value(self, var_name: str, engine: RefactoredBattleEngine) -> Any:
        """获取监视变量的值"""
        try:
            if var_name == "round":
                return engine.battle_state.round_num
            elif var_name == "alive_count":
                return len([s for s in engine.battle_state.spirits.values() if s.is_alive])
            elif var_name == "team0_alive":
                return len([s for s in engine.battle_state.spirits.values() if s.team == 0 and s.is_alive])
            elif var_name == "team1_alive":
                return len([s for s in engine.battle_state.spirits.values() if s.team == 1 and s.is_alive])
            elif var_name.startswith("spirit_"):
                spirit_name = var_name[7:]  # 去掉"spirit_"前缀
                for spirit in engine.battle_state.spirits.values():
                    if spirit.name == spirit_name:
                        return f"HP:{spirit.attributes.hp}/{spirit.attributes.max_hp}"
                return "未找到精灵"
            else:
                return "未知变量"
        except Exception as e:
            return f"错误: {e}"

    def handle_log_level_command(self, command: str):
        """处理日志级别命令"""
        if not self.session:
            print("调试会话未初始化")
            return

        parts = command.split()
        if len(parts) == 1:
            print(f"当前日志级别: {self.session.log_level}")
        else:
            level = parts[1].upper()
            if level in ["INFO", "DEBUG", "TRACE"]:
                self.session.log_level = level
                print(f"日志级别设置为: {level}")
            else:
                print("无效日志级别，支持: INFO, DEBUG, TRACE")

    def print_battle_summary(self, engine: RefactoredBattleEngine, final_result: Dict):
        """打印战斗总结"""
        print("\n" + "="*60)
        print("🏆 战斗总结")
        print("="*60)

        battle_state = engine.battle_state
        winner = final_result.get("winner")

        print(f"总回合数: {battle_state.round_num}")
        print(f"获胜者: {'队伍' + str(winner) if winner is not None else '平局'}")

        # 最终精灵状态
        for team in [0, 1]:
            team_spirits = [s for s in battle_state.spirits.values() if s.team == team]
            alive_count = len([s for s in team_spirits if s.is_alive])
            print(f"\n队伍 {team}: {alive_count}/{len(team_spirits)} 存活")

            for spirit in team_spirits:
                status = "🟢存活" if spirit.is_alive else "💀阵亡"
                hp_percent = (spirit.attributes.hp / spirit.attributes.max_hp) * 100
                print(f"  {status} {spirit.name} - HP: {spirit.attributes.hp}/{spirit.attributes.max_hp} ({hp_percent:.1f}%)")

        print("="*60)
    
    def wait_for_user_input(self) -> str:
        """等待用户输入"""
        try:
            return input("调试> ").strip().lower()
        except (EOFError, KeyboardInterrupt):
            return 'q'
    
    def should_break(self, round_result: Dict) -> bool:
        """检查是否应该触发断点"""
        for bp in self.breakpoints:
            if bp in str(round_result):
                return True
        return False
    
    def handle_breakpoint_command(self, command: str):
        """处理断点命令"""
        parts = command.split()
        if len(parts) == 1:
            # 显示当前断点
            self.log("INFO", f"当前断点: {self.breakpoints}")
        elif len(parts) == 2:
            bp = parts[1]
            if bp in self.breakpoints:
                self.breakpoints.remove(bp)
                self.log("INFO", f"移除断点: {bp}")
            else:
                self.breakpoints.append(bp)
                self.log("INFO", f"添加断点: {bp}")
    
    def print_detailed_status(self, engine: RefactoredBattleEngine):
        """打印详细状态"""
        print("\n" + "="*60)
        print("📊 详细战斗状态")
        print("="*60)
        
        battle_state = engine.battle_state
        print(f"回合数: {battle_state.round_num}")
        print(f"总精灵数: {len(battle_state.spirits)}")
        
        # 按队伍分组显示精灵
        for team in [0, 1]:
            team_spirits = [s for s in battle_state.spirits.values() if s.team == team]
            print(f"\n队伍 {team} ({len(team_spirits)} 个精灵):")
            
            for spirit in team_spirits:
                status = "🟢" if spirit.is_alive else "🔴"
                hp_percent = (spirit.attributes.hp / spirit.attributes.max_hp) * 100
                print(f"  {status} {spirit.name}")
                print(f"     HP: {spirit.attributes.hp}/{spirit.attributes.max_hp} ({hp_percent:.1f}%)")
                print(f"     攻击: {spirit.attributes.attack}, 防御: {spirit.attributes.defense}")
                print(f"     速度: {spirit.attributes.speed}")
        
        print("="*60 + "\n")
    
    def save_debug_session(self):
        """保存调试会话"""
        if not self.session or not self.session.save_logs:
            return
            
        session_file = self.debug_output_dir / f"{self.session.session_id}.json"
        
        session_data = {
            "session_info": self.session.to_dict(),
            "logs": self.battle_logs,
            "breakpoints": self.breakpoints,
            "watch_variables": self.watch_variables
        }
        
        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.log("INFO", f"💾 调试会话已保存: {session_file}")
        except Exception as e:
            self.log("ERROR", f"保存调试会话失败: {e}")
    
    def run_quick_test(self):
        """快速测试模式"""
        self.log("INFO", "🚀 快速测试模式")
        
        # 开始会话
        self.start_debug_session()
        self.session.step_mode = False
        self.session.auto_delay = 0.5
        
        try:
            # 创建测试阵型
            formation1, formation2 = self.create_test_formations()
            
            # 设置战斗引擎
            engine = self.setup_battle_engine(formation1, formation2)
            
            # 运行战斗
            self.step_through_battle(engine)
            
        except Exception as e:
            self.log("ERROR", f"快速测试失败: {e}")
        finally:
            self.save_debug_session()
    
    def run_interactive_debug(self):
        """交互式调试模式"""
        self.log("INFO", "🎮 交互式调试模式")
        
        # 开始会话
        self.start_debug_session()
        
        try:
            # 选择阵型创建方式
            print("\n选择阵型创建方式:")
            print("1. 自动创建测试阵型")
            print("2. 自定义阵型")
            
            choice = input("请选择 (1-2): ").strip()
            
            if choice == "2":
                formation1, formation2 = self.create_custom_formations_interactive()
            else:
                formation1, formation2 = self.create_test_formations()
            
            # 设置战斗引擎
            engine = self.setup_battle_engine(formation1, formation2)
            
            # 配置调试选项
            self.configure_debug_options()
            
            # 运行战斗
            self.step_through_battle(engine)
            
        except Exception as e:
            self.log("ERROR", f"交互式调试失败: {e}")
        finally:
            self.save_debug_session()
    
    def create_custom_formations_interactive(self) -> tuple[Formation, Formation]:
        """交互式创建自定义阵型"""
        print("\n可用精灵:")
        available_spirits = list(SPIRIT_CREATORS.keys())
        for i, spirit_name in enumerate(available_spirits, 1):
            print(f"{i}. {spirit_name}")
        
        # 简化版：每队选择3个精灵
        team1_config = []
        team2_config = []
        
        print("\n配置队伍1 (选择3个精灵):")
        for i in range(3):
            while True:
                try:
                    choice = int(input(f"选择第{i+1}个精灵 (1-{len(available_spirits)}): ")) - 1
                    if 0 <= choice < len(available_spirits):
                        team1_config.append({
                            "name": available_spirits[choice],
                            "position": (1, i+1)
                        })
                        break
                    else:
                        print("无效选择，请重新输入")
                except ValueError:
                    print("请输入数字")
        
        print("\n配置队伍2 (选择3个精灵):")
        for i in range(3):
            while True:
                try:
                    choice = int(input(f"选择第{i+1}个精灵 (1-{len(available_spirits)}): ")) - 1
                    if 0 <= choice < len(available_spirits):
                        team2_config.append({
                            "name": available_spirits[choice],
                            "position": (3, i+1)
                        })
                        break
                    else:
                        print("无效选择，请重新输入")
                except ValueError:
                    print("请输入数字")
        
        return self.create_custom_formations(team1_config, team2_config)
    
    def configure_debug_options(self):
        """配置调试选项"""
        if not self.session:
            print("调试会话未初始化")
            return

        print("\n调试选项配置:")

        # 步进模式
        step_choice = input("启用步进模式? (y/n, 默认y): ").strip().lower()
        self.session.step_mode = step_choice != 'n'

        if not self.session.step_mode:
            # 自动延迟
            try:
                delay = float(input("自动模式延迟秒数 (默认1.0): ") or "1.0")
                self.session.auto_delay = delay
            except ValueError:
                self.session.auto_delay = 1.0

        # 日志级别
        log_level = input("日志级别 (INFO/DEBUG/TRACE, 默认INFO): ").strip().upper()
        if log_level in ["INFO", "DEBUG", "TRACE"]:
            self.session.log_level = log_level

        self.log("INFO", f"调试配置: 步进={self.session.step_mode}, 延迟={self.session.auto_delay}s, 日志={self.session.log_level}")


def main():
    """主函数"""
    print("🎮 AoQiAI 战斗引擎调试工具")
    print("="*50)
    
    debugger = BattleEngineDebugger()
    
    print("\n选择调试模式:")
    print("1. 快速测试 (自动运行)")
    print("2. 交互式调试 (步进控制)")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择模式 (1-3): ").strip()
        
        if choice == "1":
            debugger.run_quick_test()
            break
        elif choice == "2":
            debugger.run_interactive_debug()
            break
        elif choice == "3":
            print("👋 退出调试工具")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 调试工具已退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
