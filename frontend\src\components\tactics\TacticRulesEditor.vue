<template>
  <div class="tactic-rules-editor h-full flex flex-col">
    <!-- 工具栏 -->
    <div class="toolbar bg-slate-700/50 rounded-lg p-4 mb-4 border border-slate-600/30">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h3 class="text-white font-medium">战术规则</h3>
          <el-tag type="info" size="small">{{ rules.length }} 条规则</el-tag>
        </div>
        
        <div class="flex items-center space-x-3">
          <el-button size="small" @click="addRule">
            <el-icon class="mr-1"><Plus /></el-icon>
            添加规则
          </el-button>
          <el-button size="small" @click="importRules">
            <el-icon class="mr-1"><Upload /></el-icon>
            导入
          </el-button>
          <el-button size="small" @click="exportRules">
            <el-icon class="mr-1"><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
    </div>

    <!-- 规则列表 -->
    <div class="rules-list flex-1 space-y-4 overflow-auto">
      <div
        v-for="(rule, index) in rules"
        :key="rule.id"
        class="rule-item bg-slate-800/50 rounded-lg border border-slate-600/30 p-4"
      >
        <!-- 规则头部 -->
        <div class="rule-header flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <el-switch
              v-model="rule.enabled"
              @change="onRuleChange"
            />
            <h4 class="text-white font-medium">规则 {{ index + 1 }}</h4>
            <el-tag :type="getRuleTypeColor(rule.type)" size="small">
              {{ getRuleTypeLabel(rule.type) }}
            </el-tag>
            <el-tag v-if="rule.priority" type="warning" size="small">
              优先级: {{ rule.priority }}
            </el-tag>
          </div>
          
          <div class="flex items-center space-x-2">
            <el-button size="small" @click="duplicateRule(index)">
              <el-icon><CopyDocument /></el-icon>
            </el-button>
            <el-button size="small" type="danger" @click="removeRule(index)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 规则配置 -->
        <div class="rule-config grid grid-cols-1 lg:grid-cols-3 gap-4">
          <!-- 基本信息 -->
          <div class="basic-info">
            <h5 class="text-slate-300 font-medium mb-3">基本信息</h5>
            
            <el-form-item label="规则名称">
              <el-input v-model="rule.name" placeholder="规则名称" @input="onRuleChange" />
            </el-form-item>
            
            <el-form-item label="规则类型">
              <el-select v-model="rule.type" @change="onRuleChange">
                <el-option label="攻击规则" value="attack" />
                <el-option label="防御规则" value="defense" />
                <el-option label="移动规则" value="movement" />
                <el-option label="技能规则" value="skill" />
                <el-option label="目标规则" value="targeting" />
                <el-option label="条件规则" value="condition" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优先级">
              <el-input-number v-model="rule.priority" :min="1" :max="10" @change="onRuleChange" />
            </el-form-item>
            
            <el-form-item label="描述">
              <el-input
                v-model="rule.description"
                type="textarea"
                :rows="3"
                placeholder="规则描述"
                @input="onRuleChange"
              />
            </el-form-item>
          </div>

          <!-- 条件设置 -->
          <div class="conditions">
            <h5 class="text-slate-300 font-medium mb-3">触发条件</h5>
            
            <div class="condition-list space-y-3">
              <div
                v-for="(condition, condIndex) in rule.conditions"
                :key="condIndex"
                class="condition-item bg-slate-700/50 rounded p-3 border border-slate-600/30"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-slate-300 text-sm">条件 {{ condIndex + 1 }}</span>
                  <el-button size="small" type="danger" @click="removeCondition(index, condIndex)">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                
                <div class="grid grid-cols-2 gap-2">
                  <el-select v-model="condition.type" size="small" @change="onRuleChange">
                    <el-option label="生命值" value="hp" />
                    <el-option label="能量值" value="energy" />
                    <el-option label="回合数" value="turn" />
                    <el-option label="敌人数量" value="enemy_count" />
                    <el-option label="技能冷却" value="skill_cooldown" />
                    <el-option label="效果状态" value="effect_status" />
                  </el-select>
                  
                  <el-select v-model="condition.operator" size="small" @change="onRuleChange">
                    <el-option label="等于" value="equals" />
                    <el-option label="大于" value="greater" />
                    <el-option label="小于" value="less" />
                    <el-option label="大于等于" value="greater_equal" />
                    <el-option label="小于等于" value="less_equal" />
                    <el-option label="包含" value="contains" />
                  </el-select>
                </div>
                
                <el-input
                  v-model="condition.value"
                  size="small"
                  placeholder="条件值"
                  class="mt-2"
                  @input="onRuleChange"
                />
              </div>
              
              <el-button size="small" @click="addCondition(index)" class="w-full">
                <el-icon class="mr-1"><Plus /></el-icon>
                添加条件
              </el-button>
            </div>
          </div>

          <!-- 动作设置 -->
          <div class="actions">
            <h5 class="text-slate-300 font-medium mb-3">执行动作</h5>
            
            <div class="action-list space-y-3">
              <div
                v-for="(action, actionIndex) in rule.actions"
                :key="actionIndex"
                class="action-item bg-slate-700/50 rounded p-3 border border-slate-600/30"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-slate-300 text-sm">动作 {{ actionIndex + 1 }}</span>
                  <el-button size="small" type="danger" @click="removeAction(index, actionIndex)">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                
                <el-select v-model="action.type" size="small" @change="onRuleChange" class="w-full mb-2">
                  <el-option label="攻击目标" value="attack" />
                  <el-option label="使用技能" value="use_skill" />
                  <el-option label="移动位置" value="move" />
                  <el-option label="防御姿态" value="defend" />
                  <el-option label="治疗队友" value="heal" />
                  <el-option label="切换目标" value="switch_target" />
                </el-select>
                
                <el-input
                  v-model="action.target"
                  size="small"
                  placeholder="目标选择"
                  class="mb-2"
                  @input="onRuleChange"
                />
                
                <el-input
                  v-model="action.parameters"
                  size="small"
                  placeholder="参数设置"
                  @input="onRuleChange"
                />
              </div>
              
              <el-button size="small" @click="addAction(index)" class="w-full">
                <el-icon class="mr-1"><Plus /></el-icon>
                添加动作
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="rules.length === 0" class="empty-state text-center py-12">
        <el-icon class="text-6xl text-slate-500 mb-4"><Document /></el-icon>
        <h3 class="text-xl text-slate-400 mb-2">暂无战术规则</h3>
        <p class="text-slate-500 mb-4">添加规则来定义精灵的战斗行为</p>
        <el-button type="primary" @click="addRule">
          <el-icon class="mr-2"><Plus /></el-icon>
          添加第一条规则
        </el-button>
      </div>
    </div>

    <!-- 规则模板 -->
    <div class="rule-templates mt-4 bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
      <h3 class="text-white font-medium mb-3">规则模板</h3>
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
        <el-button
          v-for="template in ruleTemplates"
          :key="template.id"
          size="small"
          @click="applyTemplate(template)"
        >
          {{ template.name }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface TacticRule {
  id: string
  name: string
  type: string
  enabled: boolean
  priority: number
  description: string
  conditions: Array<{
    type: string
    operator: string
    value: string
  }>
  actions: Array<{
    type: string
    target: string
    parameters: string
  }>
}

interface Props {
  modelValue: TacticRule[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: TacticRule[]]
  'rules-change': [value: TacticRule[]]
}>()

// 响应式数据
const rules = ref<TacticRule[]>([...props.modelValue])

// 规则模板
const ruleTemplates = ref([
  {
    id: 'template_1',
    name: '低血量治疗',
    type: 'condition',
    conditions: [{ type: 'hp', operator: 'less', value: '30' }],
    actions: [{ type: 'heal', target: 'self', parameters: '' }]
  },
  {
    id: 'template_2',
    name: '优先攻击低血量敌人',
    type: 'targeting',
    conditions: [{ type: 'enemy_count', operator: 'greater', value: '0' }],
    actions: [{ type: 'attack', target: 'lowest_hp_enemy', parameters: '' }]
  },
  {
    id: 'template_3',
    name: '能量满时使用大招',
    type: 'skill',
    conditions: [{ type: 'energy', operator: 'equals', value: '100' }],
    actions: [{ type: 'use_skill', target: 'ultimate', parameters: '' }]
  },
  {
    id: 'template_4',
    name: '防御姿态',
    type: 'defense',
    conditions: [{ type: 'hp', operator: 'less', value: '50' }],
    actions: [{ type: 'defend', target: 'self', parameters: '' }]
  }
])

// 监听器
watch(rules, (newValue) => {
  emit('update:modelValue', newValue)
  emit('rules-change', newValue)
}, { deep: true })

// 方法
const addRule = () => {
  const newRule: TacticRule = {
    id: `rule_${Date.now()}`,
    name: `规则 ${rules.value.length + 1}`,
    type: 'condition',
    enabled: true,
    priority: 5,
    description: '',
    conditions: [],
    actions: []
  }
  
  rules.value.push(newRule)
  ElMessage.success('规则添加成功')
}

const removeRule = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条规则吗？',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    rules.value.splice(index, 1)
    ElMessage.success('规则删除成功')
  } catch {
    // 用户取消删除
  }
}

const duplicateRule = (index: number) => {
  const originalRule = rules.value[index]
  const newRule: TacticRule = {
    ...originalRule,
    id: `rule_${Date.now()}`,
    name: `${originalRule.name} (副本)`
  }
  
  rules.value.splice(index + 1, 0, newRule)
  ElMessage.success('规则复制成功')
}

const addCondition = (ruleIndex: number) => {
  const newCondition = {
    type: 'hp',
    operator: 'less',
    value: ''
  }
  
  rules.value[ruleIndex].conditions.push(newCondition)
  onRuleChange()
}

const removeCondition = (ruleIndex: number, conditionIndex: number) => {
  rules.value[ruleIndex].conditions.splice(conditionIndex, 1)
  onRuleChange()
}

const addAction = (ruleIndex: number) => {
  const newAction = {
    type: 'attack',
    target: '',
    parameters: ''
  }
  
  rules.value[ruleIndex].actions.push(newAction)
  onRuleChange()
}

const removeAction = (ruleIndex: number, actionIndex: number) => {
  rules.value[ruleIndex].actions.splice(actionIndex, 1)
  onRuleChange()
}

const onRuleChange = () => {
  // 触发更新
  emit('update:modelValue', rules.value)
  emit('rules-change', rules.value)
}

const applyTemplate = (template: any) => {
  const newRule: TacticRule = {
    id: `rule_${Date.now()}`,
    name: template.name,
    type: template.type,
    enabled: true,
    priority: 5,
    description: `基于模板 "${template.name}" 创建的规则`,
    conditions: [...template.conditions],
    actions: [...template.actions]
  }
  
  rules.value.push(newRule)
  ElMessage.success(`模板 "${template.name}" 应用成功`)
}

const importRules = () => {
  ElMessage.info('导入功能开发中...')
}

const exportRules = () => {
  const data = JSON.stringify(rules.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'tactic_rules.json'
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('规则导出成功')
}

// 辅助函数
const getRuleTypeLabel = (type: string) => {
  const typeMap = {
    attack: '攻击',
    defense: '防御',
    movement: '移动',
    skill: '技能',
    targeting: '目标',
    condition: '条件'
  }
  return typeMap[type] || type
}

const getRuleTypeColor = (type: string) => {
  const colorMap = {
    attack: 'danger',
    defense: 'primary',
    movement: 'success',
    skill: 'warning',
    targeting: 'info',
    condition: ''
  }
  return colorMap[type] || ''
}
</script>

<style scoped lang="scss">
.tactic-rules-editor {
  .rule-item {
    transition: all 0.3s ease;
    
    &:hover {
      border-color: rgba(139, 92, 246, 0.3);
    }
  }
  
  .condition-item,
  .action-item {
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(51, 65, 85, 0.7);
    }
  }
  
  .rules-list {
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(148, 163, 184, 0.1);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(139, 92, 246, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(139, 92, 246, 0.5);
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-form-item__label) {
  color: #e2e8f0;
  font-size: 12px;
  line-height: 1.5;
}

:deep(.el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(51, 65, 85, 0.5);
}

:deep(.el-textarea__inner) {
  background-color: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
  color: #e2e8f0;
}
</style>