#!/usr/bin/env python3
"""
精灵详情面板

显示精灵的详细状态、属性和效果
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Dict, Any
from ui.ux.models.battle_record import SpiritSnapshot


class SpiritDetailPanel:
    """精灵详情面板"""
    
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        self.current_spirit: Optional[SpiritSnapshot] = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主滚动区域
        canvas = tk.Canvas(self.frame)
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建内容区域
        self.create_content_areas()
    
    def create_content_areas(self):
        """创建内容区域"""
        # 基本信息区域
        self.basic_info_frame = ttk.LabelFrame(self.scrollable_frame, text="基本信息", padding=10)
        self.basic_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.basic_info_text = tk.Text(self.basic_info_frame, height=6, width=50)
        self.basic_info_text.pack(fill=tk.X)
        
        # 详细属性区域
        self.attributes_frame = ttk.LabelFrame(self.scrollable_frame, text="详细属性", padding=10)
        self.attributes_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.attributes_text = tk.Text(self.attributes_frame, height=10, width=50)
        self.attributes_text.pack(fill=tk.X)
        
        # 效果区域
        self.effects_frame = ttk.LabelFrame(self.scrollable_frame, text="当前效果", padding=10)
        self.effects_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.effects_text = tk.Text(self.effects_frame, height=8, width=50)
        self.effects_text.pack(fill=tk.X)
        
        # 状态变化区域
        self.changes_frame = ttk.LabelFrame(self.scrollable_frame, text="状态变化", padding=10)
        self.changes_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.changes_text = tk.Text(self.changes_frame, height=6, width=50)
        self.changes_text.pack(fill=tk.X)
    
    def update_spirit(self, spirit: SpiritSnapshot):
        """更新精灵信息"""
        self.current_spirit = spirit
        
        # 更新基本信息
        self.update_basic_info(spirit)
        
        # 更新详细属性
        self.update_attributes(spirit)
        
        # 更新效果信息
        self.update_effects(spirit)
        
        # 更新状态变化
        self.update_changes(spirit)
    
    def update_basic_info(self, spirit: SpiritSnapshot):
        """更新基本信息"""
        self.basic_info_text.delete(1.0, tk.END)
        
        info = []
        info.append(f"名称: {spirit.name}")
        info.append(f"队伍: {spirit.team}")
        info.append(f"位置: {spirit.position}")
        info.append(f"生命值: {spirit.current_hp:.0f}/{spirit.max_hp:.0f} ({spirit.current_hp/spirit.max_hp*100:.1f}%)")
        info.append(f"气势: {spirit.energy}/{spirit.max_energy} ({spirit.energy/spirit.max_energy*100:.1f}%)")
        info.append(f"存活状态: {'存活' if spirit.is_alive else '死亡'}")
        
        self.basic_info_text.insert(tk.END, "\n".join(info))
        self.basic_info_text.config(state=tk.DISABLED)
    
    def update_attributes(self, spirit: SpiritSnapshot):
        """更新详细属性"""
        self.attributes_text.delete(1.0, tk.END)
        
        info = []
        info.append("=== 战斗属性 ===")
        info.append(f"实际攻击力: {spirit.actual_attack:.0f}")
        info.append(f"实际防御力: {spirit.actual_defense:.0f}")
        info.append(f"速度: {spirit.actual_speed:.0f}")
        info.append("")
        info.append("=== 命中与闪避 ===")
        info.append(f"命中率: {spirit.actual_hit_rate:.1%}")
        info.append(f"闪避率: {spirit.actual_dodge_rate:.1%}")
        info.append("")
        info.append("=== 暴击属性 ===")
        info.append(f"暴击率: {spirit.actual_crit_rate:.1%}")
        info.append(f"暴击伤害: {spirit.actual_crit_damage:.1%}")
        
        self.attributes_text.insert(tk.END, "\n".join(info))
        self.attributes_text.config(state=tk.DISABLED)
    
    def update_effects(self, spirit: SpiritSnapshot):
        """更新效果信息"""
        self.effects_text.delete(1.0, tk.END)

        if not spirit.effects:
            self.effects_text.insert(tk.END, "当前无效果")
            self.effects_text.config(state=tk.DISABLED)
        else:
            info = []
            info.append(f"当前效果数量: {len(spirit.effects)}")
            info.append("")

            for i, effect in enumerate(spirit.effects, 1):
                info.append(f"效果 {i}: {effect['name']}")
                info.append(f"  类型: {effect['type']}")
                info.append(f"  持续时间: {effect['duration'] if effect['duration'] >= 0 else '永久'}")
                info.append(f"  层数: {effect['stacks']}")
                if effect['description']:
                    info.append(f"  描述: {effect['description']}")
                info.append("")

            self.effects_text.insert(tk.END, "\n".join(info))
            self.effects_text.config(state=tk.DISABLED)
    
    def update_changes(self, spirit: SpiritSnapshot):
        """更新状态变化"""
        self.changes_text.delete(1.0, tk.END)
        
        info = []
        
        # HP变化
        if spirit.hp_change != 0:
            if spirit.hp_change > 0:
                info.append(f"🟢 HP恢复: +{spirit.hp_change:.0f}")
            else:
                info.append(f"🔴 HP损失: {spirit.hp_change:.0f}")
        
        # 气势变化
        if spirit.energy_change != 0:
            if spirit.energy_change > 0:
                info.append(f"⚡ 气势增加: +{spirit.energy_change}")
            else:
                info.append(f"💨 气势减少: {spirit.energy_change}")
        
        # 效果变化
        if spirit.effects_added:
            info.append(f"✨ 新增效果: {', '.join(spirit.effects_added)}")
        
        if spirit.effects_removed:
            info.append(f"❌ 移除效果: {', '.join(spirit.effects_removed)}")
        
        if not info:
            info.append("本回合无状态变化")
        
        self.changes_text.insert(tk.END, "\n".join(info))
        self.changes_text.config(state=tk.DISABLED)
    
    def clear(self):
        """清空显示"""
        for text_widget in [self.basic_info_text, self.attributes_text, 
                           self.effects_text, self.changes_text]:
            text_widget.config(state=tk.NORMAL)
            text_widget.delete(1.0, tk.END)
            text_widget.config(state=tk.DISABLED)
        
        self.current_spirit = None
    
    def pack(self, **kwargs):
        """打包布局"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)
