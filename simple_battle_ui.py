#!/usr/bin/env python3
"""
简化版战斗系统测试UI

专注于核心功能的简洁界面
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SimpleBattleUI:
    def __init__(self, root):
        self.root = root
        self.root.title("奥奇传说AI战斗系统测试")
        self.root.geometry("1200x800")
        
        # 战斗相关对象
        self.engine = None
        self.spirit_service = None
        self.available_spirits = []
        
        # 创建界面
        self.create_widgets()
        
        # 自动初始化系统
        self.root.after(100, self.initialize_system)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧配置
        config_frame = ttk.LabelFrame(control_frame, text="战斗配置", padding=10)
        config_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 精灵选择
        ttk.Label(config_frame, text="精灵1:").grid(row=0, column=0, sticky=tk.W)
        self.spirit1_var = tk.StringVar()
        self.spirit1_combo = ttk.Combobox(config_frame, textvariable=self.spirit1_var, width=20)
        self.spirit1_combo.grid(row=0, column=1, padx=(5, 0))
        
        ttk.Label(config_frame, text="精灵2:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.spirit2_var = tk.StringVar()
        self.spirit2_combo = ttk.Combobox(config_frame, textvariable=self.spirit2_var, width=20)
        self.spirit2_combo.grid(row=1, column=1, padx=(5, 0), pady=(5, 0))
        
        # 配置参数
        ttk.Label(config_frame, text="回合限制:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.round_limit_var = tk.StringVar(value="10")
        ttk.Entry(config_frame, textvariable=self.round_limit_var, width=10).grid(row=2, column=1, padx=(5, 0), pady=(5, 0))
        
        ttk.Label(config_frame, text="顺位加气:").grid(row=3, column=0, sticky=tk.W, pady=(5, 0))
        self.bonus_energy_var = tk.StringVar(value="50")
        ttk.Entry(config_frame, textvariable=self.bonus_energy_var, width=10).grid(row=3, column=1, padx=(5, 0), pady=(5, 0))
        
        # 右侧控制按钮
        button_frame = ttk.LabelFrame(control_frame, text="控制操作", padding=10)
        button_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        ttk.Button(button_frame, text="创建战斗", command=self.create_battle).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="执行一回合", command=self.execute_round).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="自动战斗", command=self.auto_battle).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="重置", command=self.reset_battle).pack(fill=tk.X, pady=(0, 5))
        
        # 中间状态显示
        status_frame = ttk.LabelFrame(control_frame, text="状态", padding=10)
        status_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 10))
        
        self.status_text = tk.Text(status_frame, height=6, width=40)
        self.status_text.pack(fill=tk.BOTH, expand=True)
        
        # 底部信息面板
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页
        notebook = ttk.Notebook(info_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 战斗信息标签页
        battle_frame = ttk.Frame(notebook)
        notebook.add(battle_frame, text="战斗信息")
        self.battle_text = scrolledtext.ScrolledText(battle_frame, height=20)
        self.battle_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 精灵状态标签页
        spirits_frame = ttk.Frame(notebook)
        notebook.add(spirits_frame, text="精灵状态")
        self.spirits_text = scrolledtext.ScrolledText(spirits_frame, height=20)
        self.spirits_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 引擎信息标签页
        engine_frame = ttk.Frame(notebook)
        notebook.add(engine_frame, text="引擎信息")
        self.engine_text = scrolledtext.ScrolledText(engine_frame, height=20)
        self.engine_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志标签页
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="系统日志")
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        print(f"{level}: {message}")
    
    def update_status(self, message):
        """更新状态显示"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_entry = f"[{timestamp}] {message}\n"
        
        self.status_text.insert(tk.END, status_entry)
        self.status_text.see(tk.END)
        
        # 保持最新的10条状态
        lines = self.status_text.get(1.0, tk.END).split('\n')
        if len(lines) > 12:  # 10条消息 + 空行
            self.status_text.delete(1.0, f"{len(lines)-10}.0")
    
    def initialize_system(self):
        """初始化系统"""
        try:
            self.log("开始初始化核心系统...")
            self.update_status("初始化系统中...")
            
            # 初始化核心系统
            from core.system_manager import initialize_core_systems
            initialize_core_systems()
            
            # 获取精灵服务
            from core.spirit.spirit_service import get_spirit_service
            self.spirit_service = get_spirit_service()
            self.available_spirits = self.spirit_service.list_available_spirits()
            
            # 更新精灵选择列表
            self.spirit1_combo['values'] = self.available_spirits
            self.spirit2_combo['values'] = self.available_spirits
            
            # 设置默认选择
            if len(self.available_spirits) >= 2:
                self.spirit1_var.set(self.available_spirits[0])
                self.spirit2_var.set(self.available_spirits[1])
            
            self.log(f"系统初始化完成，发现 {len(self.available_spirits)} 个精灵")
            self.update_status(f"系统就绪 - {len(self.available_spirits)} 个精灵可用")
            
        except Exception as e:
            self.log(f"系统初始化失败: {e}", "ERROR")
            self.update_status(f"初始化失败: {e}")
            messagebox.showerror("错误", f"系统初始化失败: {e}")
    
    def create_battle(self):
        """创建战斗"""
        try:
            if not self.spirit_service:
                messagebox.showerror("错误", "系统未初始化")
                return
            
            spirit1_name = self.spirit1_var.get()
            spirit2_name = self.spirit2_var.get()
            
            if not spirit1_name or not spirit2_name:
                messagebox.showerror("错误", "请选择两个精灵")
                return
            
            self.log(f"创建战斗: {spirit1_name} vs {spirit2_name}")
            self.update_status("创建战斗中...")
            
            # 创建阵型和精灵
            from core.formation import Formation
            
            formation1 = Formation()
            formation2 = Formation()
            
            spirit1 = self.spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
            spirit2 = self.spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
            
            if not spirit1 or not spirit2:
                raise Exception("精灵创建失败")
            
            formation1.add_spirit(spirit1, 1, 1)
            formation2.add_spirit(spirit2, 3, 1)
            
            # 创建战斗引擎
            from core.battle.engines.factory import create_battle_engine
            
            self.engine = create_battle_engine(
                formation1,
                formation2,
                round_limit=int(self.round_limit_var.get()),
                turn_order_bonus_energy=int(self.bonus_energy_var.get())
            )
            
            self.log("战斗创建成功")
            self.update_status("战斗已创建")
            
            # 更新所有信息显示
            self.update_all_displays()
            
        except Exception as e:
            self.log(f"创建战斗失败: {e}", "ERROR")
            self.update_status(f"创建失败: {e}")
            messagebox.showerror("错误", f"创建战斗失败: {e}")
    
    def execute_round(self):
        """执行一回合"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return
        
        try:
            round_num = self.engine.battle_state.round_num + 1
            self.log(f"执行第 {round_num} 回合")
            self.update_status(f"执行第 {round_num} 回合")
            
            # 执行回合
            result = self.engine.execute_round()
            
            # 更新显示
            self.update_all_displays()
            
            # 检查战斗结果
            if result.get("type") == "battle_end":
                winner = result.get("winner")
                self.log(f"战斗结束！获胜方: 队伍{winner}")
                self.update_status(f"战斗结束 - 队伍{winner}获胜")
                messagebox.showinfo("战斗结束", f"战斗结束！\n获胜方: 队伍{winner}")
            else:
                self.log(f"第 {round_num} 回合执行完成")
                self.update_status(f"第 {round_num} 回合完成")
            
        except Exception as e:
            self.log(f"回合执行失败: {e}", "ERROR")
            self.update_status(f"执行失败: {e}")
            messagebox.showerror("错误", f"回合执行失败: {e}")
    
    def auto_battle(self):
        """自动战斗"""
        if not self.engine:
            messagebox.showerror("错误", "请先创建战斗")
            return
        
        def run_battle():
            try:
                max_rounds = int(self.round_limit_var.get())
                
                while (self.engine.battle_state.winner is None and 
                       self.engine.battle_state.round_num < max_rounds):
                    
                    # 在主线程中执行
                    self.root.after(0, self.execute_round)
                    
                    # 延迟观察
                    import time
                    time.sleep(0.5)
                
                self.root.after(0, lambda: self.log("自动战斗完成"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log(f"自动战斗失败: {e}", "ERROR"))
        
        import threading
        threading.Thread(target=run_battle, daemon=True).start()
    
    def reset_battle(self):
        """重置战斗"""
        self.engine = None
        
        # 清空显示
        for text_widget in [self.battle_text, self.spirits_text, self.engine_text]:
            text_widget.delete(1.0, tk.END)
        
        self.log("战斗已重置")
        self.update_status("已重置")
    
    def update_all_displays(self):
        """更新所有显示"""
        if not self.engine:
            return
        
        self.update_battle_display()
        self.update_spirits_display()
        self.update_engine_display()
    
    def update_battle_display(self):
        """更新战斗信息显示"""
        try:
            info = []
            info.append("=== 战斗状态 ===")
            info.append(f"当前回合: {self.engine.battle_state.round_num}")
            info.append(f"回合限制: {self.engine.round_limit}")
            info.append(f"战斗状态: {'进行中' if self.engine.battle_state.winner is None else '已结束'}")

            if self.engine.battle_state.winner is not None:
                info.append(f"获胜方: 队伍{self.engine.battle_state.winner}")

            info.append("")
            info.append("=== 队伍状态 ===")

            team0_spirits = self.engine.battle_state.get_living_spirits(0)
            team1_spirits = self.engine.battle_state.get_living_spirits(1)

            info.append(f"队伍0: {len(team0_spirits)} 个存活精灵")
            info.append(f"队伍1: {len(team1_spirits)} 个存活精灵")

            # 添加详细的精灵状态
            info.append("")
            info.append("=== 详细状态 ===")
            all_spirits = self.engine.battle_state.get_all_spirits()
            for spirit in all_spirits:
                status = "存活" if spirit.is_alive else "死亡"
                info.append(f"队伍{spirit.team} - {spirit.name}: {status} (HP: {spirit.current_hp}/{spirit.max_hp})")

            self.battle_text.delete(1.0, tk.END)
            self.battle_text.insert(tk.END, "\n".join(info))

        except Exception as e:
            self.log(f"更新战斗显示失败: {e}", "ERROR")
    
    def update_spirits_display(self):
        """更新精灵状态显示"""
        try:
            info = []
            info.append("=== 精灵状态 ===")
            info.append("")
            
            all_spirits = self.engine.battle_state.get_all_spirits()
            
            for i, spirit in enumerate(all_spirits, 1):
                info.append(f"精灵 {i}: {spirit.name}")
                info.append(f"  队伍: {spirit.team}")
                info.append(f"  生命值: {spirit.current_hp}/{spirit.max_hp}")
                info.append(f"  气势: {getattr(spirit, 'energy', 0)}/{getattr(spirit, 'max_energy', 100)}")
                info.append(f"  状态: {'存活' if spirit.is_alive else '死亡'}")
                info.append("")
            
            self.spirits_text.delete(1.0, tk.END)
            self.spirits_text.insert(tk.END, "\n".join(info))
            
        except Exception as e:
            self.log(f"更新精灵显示失败: {e}", "ERROR")
    
    def update_engine_display(self):
        """更新引擎信息显示"""
        try:
            info = []
            info.append("=== 引擎信息 ===")
            info.append("")
            
            info.append(f"引擎类型: {self.engine.__class__.__name__}")
            info.append(f"回合限制: {self.engine.round_limit}")
            info.append(f"顺位加气: {self.engine.turn_order_bonus_energy}")
            info.append("")
            
            # 策略信息
            strategy = self.engine.turn_order_strategy
            info.append(f"策略类型: {strategy.__class__.__name__}")
            
            if hasattr(strategy, 'bonus_manager'):
                bonus_mgr = strategy.bonus_manager
                info.append(f"奖励管理器: {bonus_mgr.__class__.__name__}")
                info.append(f"当前顺位: {bonus_mgr.current_turn_index}")
                info.append(f"已获奖励: {len(bonus_mgr.spirits_received_bonus)}")
            
            self.engine_text.delete(1.0, tk.END)
            self.engine_text.insert(tk.END, "\n".join(info))
            
        except Exception as e:
            self.log(f"更新引擎显示失败: {e}", "ERROR")

def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleBattleUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
