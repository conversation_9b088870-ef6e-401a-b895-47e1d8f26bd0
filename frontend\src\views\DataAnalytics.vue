<template>
  <div style="height: 100vh; display: flex; flex-direction: column; background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); overflow: hidden;">
    <!-- 页面头部 -->
    <header class="analytics-header">
      <div class="header-content">
        <div class="header-title">
          <el-icon size="24" class="title-icon"><DataAnalysis /></el-icon>
          <div class="title-text">
            <h1>数据分析</h1>
            <p>深入分析战斗数据，优化你的战术策略</p>
          </div>
        </div>
        <div class="header-actions">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            @change="refreshData"
          />
          <el-button type="primary" size="small" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button size="small" @click="exportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
        </div>
      </div>
    </header>

    <!-- 统计卡片 -->
    <section style="padding: 8px 24px; flex-shrink: 0;">
      <div style="display: flex; gap: 16px; max-width: 1200px; margin: 0 auto;">
        <div 
          v-for="stat in statsCards" 
          :key="stat.key"
          style="flex: 1; height: 80px; padding: 12px; border-radius: 8px; color: white; display: flex; align-items: center; justify-content: space-between; min-width: 120px;"
          :style="{ background: stat.gradient }"
        >
          <div style="flex: 1; min-width: 0;">
            <div style="font-size: 11px; opacity: 0.7; margin-bottom: 2px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
              {{ stat.title }}
            </div>
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 2px;">
              {{ typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value }}{{ stat.suffix || '' }}
            </div>
            <div v-if="stat.change !== undefined" style="font-size: 10px; opacity: 0.8;">
              <span :style="{ color: stat.change >= 0 ? '#86efac' : '#fca5a5' }">
                {{ stat.change >= 0 ? '+' : '' }}{{ Math.abs(stat.change) }}{{ stat.title.includes('时长') ? 's' : '%' }}
              </span>
              {{ stat.changeLabel }}
            </div>
          </div>
          <el-icon style="font-size: 20px; opacity: 0.4; margin-left: 8px; flex-shrink: 0;">
            <component :is="stat.icon" />
          </el-icon>
        </div>
      </div>
    </section>

    <!-- 图表区域 -->
    <section style="flex: 1; padding: 12px 24px; overflow-y: auto; min-height: 0;">
      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; max-width: 1200px; margin: 0 auto; padding-bottom: 16px;">
        <div 
          v-for="chart in chartConfigs" 
          :key="chart.key"
          style="height: 350px; padding: 16px; border-radius: 8px; background: rgba(30, 41, 59, 0.5); border: 1px solid rgba(100, 116, 139, 0.3); display: flex; flex-direction: column;"
        >
          <div style="display: flex; align-items: center; margin-bottom: 12px; flex-shrink: 0;">
            <el-icon style="margin-right: 8px; color: #a855f7;">
              <component :is="chart.icon" />
            </el-icon>
            <h3 style="font-size: 16px; font-weight: 600; color: white; margin: 0;">{{ chart.title }}</h3>
          </div>
          <div :ref="(el) => chartElements[chart.key] = el as HTMLElement" style="flex: 1; width: 100%; overflow: hidden;"></div>
        </div>
      </div>

      <!-- 详细数据表格 -->
      <div style="max-width: 1200px; margin: 16px auto 0; flex-shrink: 0;">
        <el-tabs v-model="activeTab" style="background: rgba(30, 41, 59, 0.3); border-radius: 8px;">
          <!-- 精灵统计 -->
          <el-tab-pane label="精灵统计" name="spirits">
            <div style="height: 400px; background: rgba(30, 41, 59, 0.5); border-radius: 8px; border: 1px solid rgba(100, 116, 139, 0.3); display: flex; flex-direction: column; overflow: hidden;">
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; flex-shrink: 0; border-bottom: 1px solid rgba(100, 116, 139, 0.2);">
                <h3 style="font-size: 16px; font-weight: 600; color: white; margin: 0;">精灵战斗统计</h3>
                <el-input
                  v-model="spiritSearchQuery"
                  placeholder="搜索精灵..."
                  style="width: 180px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <div style="flex: 1; overflow: auto; margin: 16px;">
                <el-table :data="filteredSpiritStats" stripe style="width: 100%; background: transparent;"
                  :header-cell-style="{ background: 'rgba(30, 41, 59, 0.8)', color: '#f8fafc', borderBottom: '1px solid rgba(100, 116, 139, 0.3)' }"
                  :cell-style="{ background: 'transparent', color: '#cbd5e1', borderBottom: '1px solid rgba(100, 116, 139, 0.2)' }"
                  :row-style="{ background: 'transparent' }"
                >
                <el-table-column prop="name" label="精灵名称" min-width="180">
                  <template #default="{ row }">
                    <div class="flex items-center">
                      <div class="spirit-avatar w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-sm mr-3">
                        {{ row.name.charAt(0) }}
                      </div>
                      <span class="truncate">{{ row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="battles" label="战斗次数" width="100" sortable />
                <el-table-column prop="wins" label="胜利" width="80" sortable class-name="el-table__column--hidden-sm" />
                <el-table-column label="胜率" width="80" sortable :sort-method="(a, b) => a.winRate - b.winRate">
                  <template #default="{ row }">
                    <span :class="getWinRateColor(row.winRate)">{{ row.winRate }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="avgDamage" label="伤害" width="100" sortable class-name="el-table__column--hidden-sm" />
                <el-table-column prop="avgHealing" label="治疗" width="100" sortable class-name="el-table__column--hidden-sm" />
                <el-table-column prop="deathRate" label="死亡率" width="80" sortable>
                  <template #default="{ row }">
                    <span :class="getDeathRateColor(row.deathRate)">{{ row.deathRate }}%</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                  <template #default="{ row }">
                    <div class="flex space-x-1">
                      <el-button size="small" @click="viewSpiritDetails(row)" title="查看详情">
                        <el-icon><View /></el-icon>
                      </el-button>
                      <el-button size="small" @click="exportSpiritData(row)" title="导出数据" class="hidden sm:inline-flex">
                        <el-icon><Download /></el-icon>
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 技能统计 -->
          <el-tab-pane label="技能统计" name="skills">
            <div style="height: 400px; background: rgba(30, 41, 59, 0.5); border-radius: 8px; border: 1px solid rgba(100, 116, 139, 0.3); display: flex; flex-direction: column; overflow: hidden;">
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; flex-shrink: 0; border-bottom: 1px solid rgba(100, 116, 139, 0.2);">
                <h3 style="font-size: 16px; font-weight: 600; color: white; margin: 0;">技能使用统计</h3>
                <el-input
                  v-model="skillSearchQuery"
                  placeholder="搜索技能..."
                  style="width: 180px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <div style="flex: 1; overflow: auto; margin: 16px;">
                <el-table :data="filteredSkillStats" stripe style="width: 100%; background: transparent;"
                  :header-cell-style="{ background: 'rgba(30, 41, 59, 0.8)', color: '#f8fafc', borderBottom: '1px solid rgba(100, 116, 139, 0.3)' }"
                  :cell-style="{ background: 'transparent', color: '#cbd5e1', borderBottom: '1px solid rgba(100, 116, 139, 0.2)' }"
                  :row-style="{ background: 'transparent' }"
                >
                <el-table-column prop="name" label="技能名称" width="200" />
                <el-table-column prop="category" label="类型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getSkillCategoryType(row.category)" size="small">
                      {{ getSkillCategoryLabel(row.category) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="usageCount" label="使用次数" width="120" sortable />
                <el-table-column prop="successRate" label="成功率" width="100" sortable>
                  <template #default="{ row }">
                    <span :class="getSuccessRateColor(row.successRate)">{{ row.successRate }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="avgDamage" label="平均伤害" width="120" sortable />
                <el-table-column prop="avgHealing" label="平均治疗" width="120" sortable />
                <el-table-column prop="critRate" label="暴击率" width="100" sortable>
                  <template #default="{ row }">
                    <span class="text-yellow-400">{{ row.critRate }}%</span>
                  </template>
                </el-table-column>
              </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 战斗记录 -->
          <el-tab-pane label="战斗记录" name="battles">
            <div style="height: 400px; background: rgba(30, 41, 59, 0.5); border-radius: 8px; border: 1px solid rgba(100, 116, 139, 0.3); display: flex; flex-direction: column; overflow: hidden;">
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; flex-shrink: 0; border-bottom: 1px solid rgba(100, 116, 139, 0.2);">
                <h3 style="font-size: 16px; font-weight: 600; color: white; margin: 0;">最近战斗记录</h3>
                <div class="flex items-center space-x-2">
                  <el-select v-model="battleFilter" size="small" style="width: 120px">
                    <el-option label="全部" value="all" />
                    <el-option label="胜利" value="win" />
                    <el-option label="失败" value="lose" />
                  </el-select>
                  <el-button size="small" @click="loadBattleHistory">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </div>
              </div>
              
              <div style="flex: 1; overflow: auto; margin: 16px;">
                <el-table :data="filteredBattleHistory" stripe style="width: 100%; background: transparent;"
                  :header-cell-style="{ background: 'rgba(30, 41, 59, 0.8)', color: '#f8fafc', borderBottom: '1px solid rgba(100, 116, 139, 0.3)' }"
                  :cell-style="{ background: 'transparent', color: '#cbd5e1', borderBottom: '1px solid rgba(100, 116, 139, 0.2)' }"
                  :row-style="{ background: 'transparent' }"
                >
                <el-table-column prop="id" label="战斗ID" width="120" />
                <el-table-column prop="date" label="时间" width="180">
                  <template #default="{ row }">
                    {{ formatDate(row.date) }}
                  </template>
                </el-table-column>
                <el-table-column label="结果" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getBattleResultType(row.result)" size="small">
                      {{ getBattleResultLabel(row.result) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="时长" width="100">
                  <template #default="{ row }">
                    {{ row.duration }}s
                  </template>
                </el-table-column>
                <el-table-column prop="rounds" label="回合数" width="100" />
                <el-table-column prop="team1Score" label="队伍1得分" width="120" />
                <el-table-column prop="team2Score" label="队伍2得分" width="120" />
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="{ row }">
                    <el-button size="small" @click="viewBattleDetails(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button size="small" @click="replayBattle(row)">
                      <el-icon><VideoPlay /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="deleteBattle(row)" class="hidden sm:inline-flex">
                      <el-icon><Share /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import {
  DataAnalysis,
  Refresh,
  Download,
  Trophy,
  Clock,
  VideoPlay,
  Share,
  View,
  Search,
  PieChart,
  Histogram,
  TrendCharts,
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { analyticsAPI } from '../api/battle'
import type { SpiritStat, SkillStat, BattleHistoryEntry, StatCardData, ChartData } from '../types/analytics'
import { ElMessage, ElMessageBox } from 'element-plus'

type EChartsInstance = echarts.ECharts

// 响应式数据
const dateRange = ref<[Date, Date] | null>(null)
const loading = ref(true)

// 数据
const statsCards = ref<StatCardData[]>([])
const spiritStats = ref<SpiritStat[]>([])
const skillStats = ref<SkillStat[]>([])
const battleHistory = ref<BattleHistoryEntry[]>([])
const chartData = ref<ChartData | null>(null)

// 搜索和过滤
const spiritSearchQuery = ref('')
const skillSearchQuery = ref('')
const battleFilter = ref('all')
const activeTab = ref('spirits')

// ECharts 实例和元素引用
const chartElements = reactive<Record<string, HTMLElement | null>>({})
let winLossChart: EChartsInstance | null = null
let spiritUsageChart: EChartsInstance | null = null
let skillUsageChart: EChartsInstance | null = null
let battleOutcomesChart: EChartsInstance | null = null

const chartConfigs = [
  { key: 'winLossTrend', title: '胜负趋势', icon: TrendCharts },
  { key: 'spiritUsage', title: '精灵出场率', icon: PieChart },
  { key: 'skillUsage', title: '技能使用排行', icon: Histogram },
  { key: 'battleOutcomes', title: '战斗结果分布', icon: PieChart },
]

// 计算属性
const filteredSpiritStats = computed(() =>
  spiritStats.value.filter(s => s.name.toLowerCase().includes(spiritSearchQuery.value.toLowerCase()))
)

const filteredSkillStats = computed(() =>
  skillStats.value.filter(s => s.name.toLowerCase().includes(skillSearchQuery.value.toLowerCase()))
)

const filteredBattleHistory = computed(() => {
  if (battleFilter.value === 'all') return battleHistory.value
  return battleHistory.value.filter(b => b.result === battleFilter.value)
})


// 方法
const refreshData = async () => {
  loading.value = true
  await Promise.all([
    loadDashboardData(),
    loadBattleHistory(),
  ])
  loading.value = false
  ElMessage.success('数据已刷新')
}

const loadDashboardData = async () => {
  try {
    // In a real app, you would fetch this data from an API
    const analytics = await analyticsAPI.getDashboardAnalytics(dateRange.value)
    statsCards.value = analytics.statsCards
    chartData.value = analytics.chartData

    const spiritAnalytics = await analyticsAPI.getSpiritAnalytics(dateRange.value)
    spiritStats.value = spiritAnalytics.spiritStats

    const skillAnalytics = await analyticsAPI.getSkillAnalytics(dateRange.value)
    skillStats.value = skillAnalytics.skillStats

    await nextTick()
    updateCharts()
  } catch (error) {
    ElMessage.error('加载仪表盘数据失败')
    console.error(error)
  }
}

const loadBattleHistory = async () => {
  try {
    const history = await analyticsAPI.getBattleHistory(dateRange.value, battleFilter.value, 20, 0)
    battleHistory.value = history.battles
  } catch (error) {
    ElMessage.error('加载战斗记录失败')
    console.error(error)
  }
}

const setupCharts = () => {
  if (chartElements.winLossTrend) winLossChart = echarts.init(chartElements.winLossTrend)
  if (chartElements.spiritUsage) spiritUsageChart = echarts.init(chartElements.spiritUsage)
  if (chartElements.skillUsage) skillUsageChart = echarts.init(chartElements.skillUsage)
  if (chartElements.battleOutcomes) battleOutcomesChart = echarts.init(chartElements.battleOutcomes)

  const resizeObserver = new ResizeObserver(() => {
    winLossChart?.resize()
    spiritUsageChart?.resize()
    skillUsageChart?.resize()
    battleOutcomesChart?.resize()
  });

  Object.values(chartElements).forEach(el => {
    if (el) resizeObserver.observe(el)
  })
}

const updateCharts = () => {
  if (!chartData.value) return

  // Win/Loss Trend Chart
  winLossChart?.setOption({
    tooltip: { trigger: 'axis', backgroundColor: 'rgba(30, 41, 59, 0.8)', borderColor: '#38bdf8', textStyle: { color: '#fff' } },
    legend: { data: ['胜利', '失败'], textStyle: { color: '#cbd5e1' }, inactiveColor: '#64748b' },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'category', data: chartData.value.winLossTrend.dates, axisLabel: { color: '#94a3b8' }, axisLine: { lineStyle: { color: '#475569' } } },
    yAxis: { type: 'value', axisLabel: { color: '#94a3b8' }, splitLine: { lineStyle: { color: '#334155' } } },
    series: [
      { name: '胜利', type: 'line', data: chartData.value.winLossTrend.wins, smooth: true, color: '#22c55e', areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(34, 197, 94, 0.3)' }, { offset: 1, color: 'rgba(34, 197, 94, 0)' }])} },
      { name: '失败', type: 'line', data: chartData.value.winLossTrend.losses, smooth: true, color: '#ef4444', areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(239, 68, 68, 0.3)' }, { offset: 1, color: 'rgba(239, 68, 68, 0)' }])} }
    ]
  })

  // Spirit Usage Chart
  spiritUsageChart?.setOption({
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)', backgroundColor: 'rgba(30, 41, 59, 0.8)', borderColor: '#38bdf8', textStyle: { color: '#fff' } },
    legend: { show: false },
    series: [{
      name: '精灵出场率',
      type: 'pie',
      radius: ['50%', '70%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: { show: false },
      data: chartData.value.spiritUsage,
    }]
  })

  // Skill Usage Chart
  skillUsageChart?.setOption({
    tooltip: { trigger: 'axis', backgroundColor: 'rgba(30, 41, 59, 0.8)', borderColor: '#38bdf8', textStyle: { color: '#fff' } },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'category', data: chartData.value.skillUsage.map(s => s.name), axisLabel: { color: '#94a3b8', interval: 0, rotate: 30 }, axisLine: { lineStyle: { color: '#475569' } } },
    yAxis: { type: 'value', axisLabel: { color: '#94a3b8' }, splitLine: { lineStyle: { color: '#334155' } } },
    series: [{
        name: '使用次数',
        type: 'bar',
        data: chartData.value.skillUsage.map(s => s.value),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(168, 85, 247, 0.8)' },
            { offset: 1, color: 'rgba(124, 58, 237, 0.5)' }
          ]),
          borderRadius: [4, 4, 0, 0]
        }
    }]
  })
    
  // Battle Outcomes Chart
  battleOutcomesChart?.setOption({
      tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)', backgroundColor: 'rgba(30, 41, 59, 0.8)', borderColor: '#38bdf8', textStyle: { color: '#fff' } },
      legend: {
        orient: 'vertical',
        left: 'right',
        top: 'center',
        textStyle: { color: '#cbd5e1' },
        inactiveColor: '#64748b'
      },
      series: [{
          name: '战斗结果',
          type: 'pie',
          radius: '70%',
          center: ['40%', '50%'],
          data: chartData.value.battleOutcomes,
          label: {
              position: 'inner',
              formatter: '{d}%',
              color: '#fff',
              fontSize: 12,
          },
      }]
  })
}

onMounted(async () => {
  loading.value = true
  setupCharts()
  await refreshData()
  loading.value = false
})

onBeforeUnmount(() => {
  winLossChart?.dispose()
  spiritUsageChart?.dispose()
  skillUsageChart?.dispose()
  battleOutcomesChart?.dispose()
})

// 表格样式和格式化方法
const getWinRateColor = (rate: number) => {
  if (rate >= 70) return 'text-green-400'
  if (rate >= 50) return 'text-yellow-400'
  return 'text-red-400'
}

const getDeathRateColor = (rate: number) => {
  if (rate <= 15) return 'text-green-400'
  if (rate <= 30) return 'text-yellow-400'
  return 'text-red-400'
}

const getSkillCategoryType = (category: string) => {
  if (category === 'attack') return 'danger'
  if (category === 'support') return 'success'
  if (category === 'special') return 'warning'
  return 'info'
}

const getSkillCategoryLabel = (category: string) => {
  const labels: { [key: string]: string } = {
    attack: '攻击',
    support: '辅助',
    special: '特殊',
    passive: '被动',
  };
  return labels[category] || '未知';
}

const getSuccessRateColor = (rate: number) => {
  if (rate >= 90) return 'text-green-400'
  if (rate >= 75) return 'text-yellow-400'
  return 'text-red-400'
}

const getBattleResultType = (result: string) => (result === 'win' ? 'success' : 'danger')
const getBattleResultLabel = (result: string) => (result === 'win' ? '胜利' : '失败')
const formatDate = (dateStr: string) => new Date(dateStr).toLocaleString()


// 占位符/待实现方法
const exportReport = () => {
  ElMessage.info('导出报告功能正在开发中...')
  console.log('导出报告')
}

const viewSpiritDetails = (spirit: SpiritStat) => {
  ElMessage.info(`查看精灵 ${spirit.name} 的详细信息...`)
  console.log('查看精灵详情', spirit)
}

const exportSpiritData = (spirit: SpiritStat) => {
  ElMessage.info(`导出精灵 ${spirit.name} 的数据...`)
  console.log('导出精灵数据', spirit)
}

const viewBattleDetails = (battle: BattleHistoryEntry) => {
  ElMessage.info(`查看战斗 ${battle.id} 的详情...`)
  console.log('查看战斗详情', battle)
}

const replayBattle = (battle: BattleHistoryEntry) => {
  ElMessage.info(`回放战斗 ${battle.id}...`)
  console.log('回放战斗', battle)
}

const deleteBattle = (battle: BattleHistoryEntry) => {
  ElMessageBox.confirm(`确定要删除战斗记录 ${battle.id} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    ElMessage.success('战斗记录已删除（模拟）')
    console.log('删除战斗', battle)
  }).catch(() => {
    // catch cancel
  });
}
</script>

<style scoped>
/* 响应式布局修复 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column !important;
    gap: 12px !important;
  }
  
  .header-actions {
    width: 100% !important;
    justify-content: space-between !important;
  }
  
  /* 统计卡片在移动端保持一行，但允许横向滚动 */
  section[style*="padding: 8px 24px"] > div {
    overflow-x: auto !important;
    padding: 0 8px !important;
  }
  
  section[style*="padding: 8px 24px"] > div > div {
    min-width: 100px !important;
    gap: 8px !important;
  }
  
  /* 图表在移动端变为单列 */
  section[style*="flex: 1"] > div {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
    padding: 0 8px !important;
  }
  
  /* 表格区域在移动端调整 */
  div[style*="max-width: 1200px; margin: 16px auto 0"] {
    margin: 8px 8px 0 !important;
  }
  
  div[style*="height: 400px"] {
    height: 300px !important;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕进一步优化 */
  section[style*="padding: 8px 24px"] {
    padding: 4px 8px !important;
  }
  
  section[style*="flex: 1"] {
    padding: 8px !important;
  }
  
  div[style*="height: 400px"] {
    height: 250px !important;
  }
  
  /* 统计卡片字体调整 */
  div[style*="font-size: 18px"] {
    font-size: 14px !important;
  }
  
  div[style*="font-size: 11px"] {
    font-size: 9px !important;
  }
}

/* 确保表格滚动正常 */
:deep(.el-table) {
  background: transparent !important;
}

:deep(.el-table .el-table__body tr:hover > td) {
  background: rgba(139, 92, 246, 0.1) !important;
}

/* 修复tabs样式 */
:deep(.el-tabs__header) {
  margin: 0 !important;
  background: rgba(30, 41, 59, 0.5) !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.el-tabs__item) {
  color: #cbd5e1 !important;
  padding: 8px 16px !important;
}

:deep(.el-tabs__item.is-active) {
  color: #a855f7 !important;
}

:deep(.el-tabs__content) {
  padding: 0 !important;
}

/* 优化页面头部样式 */
.analytics-header {
  padding: 16px 24px;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(100, 116, 139, 0.3);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 24px;
  color: #a855f7;
  margin-right: 12px;
}

.title-text h1 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0 0 4px 0;
  line-height: 1;
}

.title-text p {
  font-size: 12px;
  color: #94a3b8;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>