from __future__ import annotations
from enum import Enum

"""元素类型模块。

定义了游戏中所有可用的元素类型。
"""


class ElementType(Enum):
    """
    表示游戏中的元素属性。

    Attributes:
        WATER: 水属性
        FIRE: 火属性
        GRASS: 草属性
        AIR: 空属性
        CREATION: 创属性
        LIGHT: 光属性
        DARK: 暗属性
    """
    WATER = "水"
    FIRE = "火"
    GRASS = "草"
    AIR = "空"
    CREATION = "创"
    LIGHT = "光"
    DARK = "暗"


__all__ = ['ElementType']
