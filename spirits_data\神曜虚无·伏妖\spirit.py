"""
神曜虚无·伏妖 - 主精灵文件

神曜虚无·伏妖 (Divine Void - Demon Subduer)
属性：火，职业：魔法、通灵、神曜

这是伏妖精灵的主要定义文件，整合了所有模块。
"""
from __future__ import annotations
from typing import List

from core.spirit.spirit import Spirit, SpiritMetadata
from core.attribute import Attributes
from core.element import ElementType
from core.profession import ProfessionType
from core.effect.system import IEffect

from .skills import create_fuyao_skills
from .passive_effects import create_bian_shusha_passive_effect


def create_fuyao_spirit() -> Spirit:
    """创建神曜虚无·伏妖精灵"""
    
    # 创建属性 - 火属性魔法神曜通灵型
    attributes = Attributes(
        base_hp=2200,      # 中高生命值
        hp_p=0.0,
        hp_flat=0.0,
        base_attack=9000,   # 高攻击力（魔法输出）
        attack_p=0.0,
        attack_flat=0.0,
        base_pdef=120,     # 中等物理防御
        pdef_p=0.0,
        pdef_flat=0.0,
        base_mdef=160,     # 较高魔法防御
        mdef_p=0.0,
        mdef_flat=0.0,
        base_speed=140,         # 高速度
        base_hit_rate=0.0,      # 命中率加成：0%（标准命中率）
        base_dodge_rate=0.12,   # 闪避率加成：+12%
        base_break_rate=0.25,   # 高破击率
        base_block_rate=0.05,   # 低格挡率
        base_crit_rate=0.25,    # 高暴击率
        base_crit_res_rate=0.10, # 一定暴击抵抗
        # 新增战斗属性
        base_crit_damage=1.6,   # 高暴击伤害倍数
        base_damage_reduction=0.05, # 基础减伤
        base_penetration=0.15   # 穿透率
    )
    
    # 创建元数据
    metadata = SpiritMetadata(
        element=ElementType.FIRE,  # 火属性
        professions={ProfessionType.MAGIC, ProfessionType.NECROMANCER, ProfessionType.SHENYAO},  # 魔法、通灵、神曜
        tags={"VOID_CONTROL", "FLAME_MAGIC", "SHENYAO", "NECROMANCER", "HIGH_DAMAGE"},
        shenge_level=6  # 高级神格
    )
    
    # 创建精灵实例
    spirit = Spirit(
        id="神曜虚无·伏妖",  # 🔧 修复：ID匹配模块名，用于自动发现被动效果
        name="神曜虚无·伏妖",
        attributes=attributes,
        position=(2, 1),  # 中后排位置
        team=0,
        skills=[],
        metadata=metadata
    )
    
    # 创建技能并添加到精灵的技能组件中
    skills = create_fuyao_skills(spirit)
    from core.components import SkillComponent
    skill_component = spirit.components.get_component(SkillComponent)
    if skill_component:
        for skill in skills:
            skill_component.add_skill(skill)
    
    return spirit


def create_fuyao_passive_effects(spirit: Spirit) -> List[IEffect]:
    """创建伏妖所有被动效果列表，供自动注册"""
    return [
        create_bian_shusha_passive_effect(spirit)
    ]


# 通用别名（保持向后兼容）
def create_shen_yao_xu_wu_fu_yao_spirit() -> Spirit:
    """创建神曜虚无·伏妖精灵（向后兼容别名）"""
    return create_fuyao_spirit()


def create_passive_effects(spirit: Spirit) -> List[IEffect]:
    """通用被动效果创建函数（向后兼容别名）"""
    return create_fuyao_passive_effects(spirit)


# 精灵数据配置 - 已迁移到 JSON 配置文件
# 原 SPIRIT_DATA 已被 spirits_json/神曜虚无·伏妖.json 替代
# 如需访问精灵数据，请使用 JSON 配置系统

def get_spirit_data_from_json():
    """从 JSON 配置获取精灵数据（替代硬编码的 SPIRIT_DATA）"""
    from core.spirit.json_loader import get_spirit_json_loader

    loader = get_spirit_json_loader()
    config = loader.load_spirit_config("shenyao_xuwu_fuyao")

    if config is None:
        return None

    return {
        "id": config.id,
        "name": config.name,
        "attributes": config.attributes,
        "metadata": {
            "element": config.element,
            "professions": config.professions,
            "tags": config.tags,
            "shenge_level": config.shenge_level,
            "shenyao_config": getattr(config, 'shenyao_config', None)
        },
        "skills": config.skills
    }


# 精灵信息
FUYAO_INFO = {
    "name": "神曜虚无·伏妖",
    "title": "Divine Void - Demon Subduer",
    "element": "火",
    "professions": ["魔法", "通灵", "神曜"],
    "description": "掌控虚无之力的火属性魔法师，拥有强大的火焰魔法和虚无操控能力，能够在战斗中发挥强大的输出和控制作用。",
    "characteristics": [
        "高攻击力和暴击率",
        "虚无操控机制",
        "强大的火焰魔法",
        "神曜领域控制",
        "通灵能力增强",
        "复合伤害系统"
    ],
    "battle_role": "魔法输出/控制",
    "difficulty": "高"
}


# 导出函数和数据
__all__ = [
    'create_fuyao_spirit',
    'create_fuyao_passive_effects',
    'create_shen_yao_xu_wu_fu_yao_spirit',  # 向后兼容
    'create_passive_effects',  # 向后兼容
    'get_spirit_data_from_json',  # 替代 SPIRIT_DATA
    'FUYAO_INFO'
]
