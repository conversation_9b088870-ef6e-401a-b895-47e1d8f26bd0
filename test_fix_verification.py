#!/usr/bin/env python3
"""
验证修复效果

测试修复导入问题后的战斗是否正常
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fixed_battle():
    """测试修复后的战斗"""
    print("🔧 测试修复后的战斗...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 使用精灵服务创建精灵（和UI完全一样的流程）
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"✅ 可用精灵: {available_spirits}")
        
        # 选择不同的精灵组合来测试
        spirit1_name = "神曜圣谕·女帝"  # 换一个精灵
        spirit2_name = "神曜虚无·伏妖"
        
        print(f"📊 测试精灵组合: {spirit1_name} vs {spirit2_name}")
        
        # 创建阵型和精灵
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
        
        if not spirit1 or not spirit2:
            print("❌ 精灵创建失败")
            return False
        
        print(f"✅ 精灵创建成功")
        print(f"  - 精灵1: {spirit1.name}, HP={spirit1.current_hp}, alive={spirit1.is_alive}")
        print(f"  - 精灵2: {spirit2.name}, HP={spirit2.current_hp}, alive={spirit2.is_alive}")
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎
        from core.battle.engines.factory import create_battle_engine
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=10,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗引擎创建完成")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        print(f"  - 获胜方: {engine.battle_state.winner}")
        
        # 检查初始状态
        print(f"\n📊 初始战斗状态:")
        team0_alive = len(engine.battle_state.get_living_spirits(0))
        team1_alive = len(engine.battle_state.get_living_spirits(1))
        print(f"  - 队伍0存活: {team0_alive}")
        print(f"  - 队伍1存活: {team1_alive}")
        
        # 检查胜负判定
        winner = engine.condition_checker.check_battle_end(engine.battle_state)
        print(f"  - 胜负判定: {winner}")
        
        if winner is not None:
            print("❌ 战斗在创建时就结束了")
            return False
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        print(f"✅ 回合执行完成")
        print(f"  - 结果类型: {result.get('type', 'unknown')}")
        print(f"  - 获胜方: {result.get('winner', 'None')}")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        
        # 检查执行后状态
        print(f"\n📊 回合执行后状态:")
        team0_alive_after = len(engine.battle_state.get_living_spirits(0))
        team1_alive_after = len(engine.battle_state.get_living_spirits(1))
        print(f"  - 队伍0存活: {team0_alive_after}")
        print(f"  - 队伍1存活: {team1_alive_after}")
        
        # 详细检查精灵状态
        all_spirits = engine.battle_state.get_all_spirits()
        for i, spirit in enumerate(all_spirits):
            print(f"    精灵{i+1}: {spirit.name}")
            print(f"      - 队伍: {spirit.team}")
            print(f"      - HP: {spirit.current_hp}/{spirit.max_hp}")
            print(f"      - 存活: {spirit.is_alive}")
        
        # 判断是否修复成功
        if result.get("type") == "battle_end" and engine.battle_state.round_num == 1:
            print("\n⚠️ 战斗仍然在第一回合就结束了")
            if team0_alive_after == 0 and team1_alive_after == 0:
                print("  - 双方都死亡了")
            elif team0_alive_after == 0:
                print("  - 队伍0全灭")
            elif team1_alive_after == 0:
                print("  - 队伍1全灭")
            return False
        else:
            print("\n✅ 战斗正常进行，修复成功！")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_problematic_spirit():
    """测试有问题的精灵"""
    print("\n🔧 测试有问题的精灵...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 测试天恩圣祭·空灵圣龙
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        
        spirit1_name = "天恩圣祭·空灵圣龙"  # 有问题的精灵
        spirit2_name = "神曜圣谕·女帝"
        
        print(f"📊 测试有问题的精灵: {spirit1_name} vs {spirit2_name}")
        
        # 创建阵型和精灵
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
        
        print(f"✅ 精灵创建成功")
        print(f"  - 精灵1: {spirit1.name}, HP={spirit1.current_hp}")
        print(f"  - 精灵2: {spirit2.name}, HP={spirit2.current_hp}")
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎
        from core.battle.engines.factory import create_battle_engine
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=10,
            turn_order_bonus_energy=50
        )
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        print(f"✅ 回合执行完成")
        print(f"  - 结果类型: {result.get('type', 'unknown')}")
        
        # 检查精灵状态
        all_spirits = engine.battle_state.get_all_spirits()
        for spirit in all_spirits:
            print(f"  - {spirit.name}: HP={spirit.current_hp}/{spirit.max_hp}, alive={spirit.is_alive}")
        
        # 检查是否还有问题
        if spirit1.current_hp == 0 and spirit1.max_hp > 0:
            print("❌ 天恩圣祭·空灵圣龙仍然异常死亡")
            return False
        else:
            print("✅ 天恩圣祭·空灵圣龙状态正常")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 修复效果验证")
    print("="*60)
    
    # 测试1：使用其他精灵
    success1 = test_fixed_battle()
    
    # 测试2：测试有问题的精灵
    success2 = test_problematic_spirit()
    
    print("\n" + "="*60)
    print("📊 验证结果")
    print("="*60)
    
    if success1 and success2:
        print("🎉 修复完全成功！")
        print("  ✅ 其他精灵战斗正常")
        print("  ✅ 天恩圣祭·空灵圣龙问题已修复")
    elif success1:
        print("⚠️ 部分修复成功")
        print("  ✅ 其他精灵战斗正常")
        print("  ❌ 天恩圣祭·空灵圣龙仍有问题")
    else:
        print("❌ 修复失败")
        print("  ❌ 战斗系统仍有问题")

if __name__ == "__main__":
    main()
