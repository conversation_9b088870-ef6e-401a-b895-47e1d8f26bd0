#!/usr/bin/env python3
"""
统一效果系统实现

提供规范的、统一的、可扩展的效果管理架构
"""

from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Set, Callable, TYPE_CHECKING
from enum import Enum
from dataclasses import dataclass, field
import time
import uuid

from .triggers import TriggerCondition
from ..logging import get_logger

if TYPE_CHECKING:
    from ..event.unified_manager import UnifiedEventManager

# 获取日志记录器
logger = get_logger("core.effect.system")

class EffectType(Enum):
    """效果类型"""
    PASSIVE = "passive"
    ACTIVE = "active"
    TRIGGERED = "triggered"
    AURA = "aura"
    TEMPORARY = "temporary"
    PERMANENT = "permanent"


class EffectCategory(Enum):
    """效果分类"""
    BUFF = "buff"
    DEBUFF = "debuff"
    CONTROL = "control"
    SHIELD = "shield"
    SPECIAL = "special"
    SYSTEM = "system"


class EffectPriority(Enum):
    """效果优先级"""
    LOWEST = 0
    LOW = 100
    NORMAL = 500
    HIGH = 800
    HIGHEST = 1000


@dataclass
class EffectResult:
    """效果操作结果"""
    is_success: bool
    message: str
    actions: List[Any] = field(default_factory=list)
    data: Dict[str, Any] = field(default_factory=dict)
    exception: Optional[Exception] = None

    @classmethod
    def success(cls, message: str = "操作成功", data: Optional[Dict[str, Any]] = None) -> 'EffectResult':
        return cls(True, message, data=data or {})

    @classmethod
    def success_with_actions(cls, actions: List[Any], message: str = "操作成功") -> 'EffectResult':
        return cls(True, message, actions=actions)
    
    @classmethod
    def success_with_data(cls, data: Dict[str, Any], message: str = "操作成功") -> 'EffectResult':
        return cls(True, message, data=data)

    @classmethod
    def failure(cls, message: str) -> 'EffectResult':
        return cls(False, message)
    
    @classmethod
    def error(cls, message: str, exc: Optional[Exception] = None) -> 'EffectResult':
        return cls(False, message, exception=exc)


class IEffect(ABC):
    """统一效果接口"""
    
    def __init__(self, 
                 effect_id: str,
                 name: str,
                 effect_type: EffectType,
                 category: EffectCategory,
                 priority: EffectPriority = EffectPriority.NORMAL,
                 duration: int = -1,
                 caster=None):
        self.id = effect_id or str(uuid.uuid4())
        self.name = name
        self.type = effect_type
        self.effect_type = effect_type  # 🔧 兼容性：添加effect_type别名
        self.category = category
        self.priority = priority
        self.duration = duration
        self.remaining_duration = duration
        self.caster = caster
        self.owner = None
        self.stackable = False
        self.current_stacks = 1
        self.max_stacks = 1
        self.is_active = True
        self.is_removable = True
        self.is_dispellable = True
        self.source = caster
        self.dependencies: Set[str] = set()
        self.conflicts: Set[str] = set()
        self.data: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        self.created_at = time.time()
        self.last_updated = time.time()

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """[新架构] 返回一个触发此效果的条件列表。"""
        return []

    def on_triggered(self, event_data: Dict[str, Any], battle_state) -> EffectResult:
        """处理事件触发 - 统一接口"""
        return EffectResult.success()

    @abstractmethod
    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        pass
    
    @abstractmethod
    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        pass
    
    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success(f"{self.name} updated.")
    
    def on_stack_change(self, old_stacks: int, new_stacks: int, target, battle_state) -> EffectResult:
        """效果层数变化时触发"""
        return EffectResult.success(f"{self.name} stacks changed.")
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return self.to_dict()

    # --- Data and Property Helpers (Restoring for compatibility) ---
    @property
    def stacks(self) -> int:
        return self.current_stacks
    
    @stacks.setter
    def stacks(self, value: int) -> None:
        self.current_stacks = value

    def get_data(self, key: str, default: Any = None) -> Any:
        return self.data.get(key, default)

    def set_data(self, key: str, value: Any) -> None:
        self.data[key] = value
        self.last_updated = time.time()

    def has_data(self, key: str) -> bool:
        return key in self.data
    
    def update_data(self, data_dict: Dict[str, Any]) -> None:
        self.data.update(data_dict)
        self.last_updated = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type.value,
            "category": self.category.value,
            "priority": self.priority.value,
            "duration": self.duration,
            "remaining_duration": self.remaining_duration,
            "stacks": self.current_stacks,
            "max_stacks": self.max_stacks,
            "is_active": self.is_active,
            "is_removable": self.is_removable,
            "is_dispellable": self.is_dispellable,
            "data": self.data.copy(),
            "metadata": self.metadata.copy(),
        }


class EffectManager:
    """统一效果管理器"""

    def __init__(self, owner, unified_event_manager=None):
        self.owner = owner
        self.effects: Dict[str, IEffect] = {}
        self.effect_groups: Dict[str, Set[str]] = {}
        self.listeners: Dict[str, List[Callable]] = {}

        # 使用统一事件管理器
        if unified_event_manager is not None:
            self.unified_event_manager = unified_event_manager
        else:
            self.unified_event_manager = self._get_global_unified_event_manager()

        # 🔧 优化：基于效果ID的持续时间管理
        self._effect_round_info: Dict[str, Dict[str, int]] = {}  # effect_id -> {"applied_round": int, "last_update_round": int}

        # 性能优化
        self._cache: Dict[str, Any] = {}
        self._dirty_flags: Set[str] = set()
        self._last_cache_update = 0

        # 统计信息
        self.stats = {
            "total_applied": 0,
            "total_removed": 0,
            "total_stacks_changed": 0,
            "total_updates": 0,
            "last_reset": time.time()
        }

    def _get_global_unified_event_manager(self):
        """从统一系统管理器获取全局统一事件管理器"""
        try:
            from ..system_manager import get_system
            event_manager = get_system('event')
            if event_manager is None:
                # 如果系统管理器中没有事件管理器，尝试直接导入
                from ..event.unified_manager import unified_event_manager
                return unified_event_manager
            return event_manager
        except RuntimeError as e:
            if "核心系统尚未初始化" in str(e):
                # 系统初始化期间，直接导入统一事件管理器
                from ..event.unified_manager import unified_event_manager
                return unified_event_manager
            else:
                raise RuntimeError("统一管理器中的事件系统不可用，无法创建效果管理器") from e

    def _extract_event_data(self, event, event_type):
        """提取事件数据（兼容旧接口）"""
        event_data = {
            'event_type': event_type.name if hasattr(event_type, 'name') else str(event_type),
            'event': event
        }

        # 添加事件的所有属性
        if hasattr(event, 'data'):
            event_data.update(event.data)
        else:
            # 手动提取属性
            for attr_name in dir(event):
                if not attr_name.startswith('_') and not callable(getattr(event, attr_name)):
                    event_data[attr_name] = getattr(event, attr_name)

        # 🔧 修复：添加字段映射以确保兼容性
        from ..event.events import BeforeAttackEvent
        if isinstance(event, BeforeAttackEvent):
            # 为BeforeAttackEvent添加字段映射（新版本使用attacker）
            event_data['attacker'] = event.attacker
            event_data['source'] = event.attacker  # 兼容旧代码
            event_data['defender'] = event.target

        return event_data
    
    def add_effect(self, effect: IEffect, battle_state=None) -> EffectResult:
        """添加效果"""
        try:
            # 🔧 优化：添加详细的调试信息
            if not hasattr(self.owner, 'name'):
                return EffectResult.failure(f"效果拥有者缺少name属性")

            # 1. 检查是否可以应用
            if not effect.can_apply_to(self.owner):
                return EffectResult.failure(f"效果 {effect.name} 无法应用到 {self.owner.name}")

            # 2. 检查冲突
            conflicts = self._check_conflicts(effect)
            if conflicts:
                return EffectResult.failure(f"效果冲突: {conflicts}")

            # 3. 处理叠加
            existing_effect = self.effects.get(effect.id)
            if existing_effect:
                return self._handle_stack(existing_effect, effect, battle_state)

            # 4. 设置拥有者并应用效果
            effect.owner = self.owner

            # 🔧 优化：确保on_apply方法存在
            if not hasattr(effect, 'on_apply') or not callable(effect.on_apply):
                return EffectResult.failure(f"效果 {effect.name} 缺少on_apply方法")

            result = effect.on_apply(self.owner, battle_state)

            # 🔧 优化：检查result是否为None
            if result is None:
                result = EffectResult.success(f"效果 {effect.name} 应用成功")

            if result.is_success:
                self.effects[effect.id] = effect

                # 🔧 优化：记录效果应用的回合信息
                current_round = getattr(battle_state, 'round_num', 0) if battle_state else 0
                self._effect_round_info[effect.id] = {
                    "applied_round": current_round,
                    "last_update_round": -1  # -1表示还未更新过
                }

                # 🔧 优化：安全的事件订阅到统一事件管理器
                try:
                    if self.unified_event_manager and hasattr(effect, 'get_trigger_conditions'):
                        conditions = effect.get_trigger_conditions()
                        if conditions:  # 只有有触发条件的效果才订阅
                            # 🔧 修复：在使用时导入EventPriority，避免循环导入
                            from ..event.unified_manager import EventPriority
                            for condition in conditions:
                                # 修复闭包问题：创建一个函数来捕获当前的effect和condition
                                def create_handler(eff, cond):
                                    def handler(event, bs):
                                        try:
                                            if hasattr(eff, 'on_triggered'):
                                                event_data = self._extract_event_data(event, cond.event_type)
                                                result = eff.on_triggered(event_data, bs)
                                                return result.actions if result and hasattr(result, 'actions') else []
                                            return []
                                        except Exception as e:
                                            print(f"⚠️ 效果 {eff.name if hasattr(eff, 'name') else 'unknown'} 处理事件失败: {e}")
                                            return []
                                    return handler

                                listener = self.unified_event_manager.subscribe(
                                    event_type=condition.event_type,
                                    handler=create_handler(effect, condition),
                                    priority=EventPriority.NORMAL,
                                    name=f"effect_{effect.id}_{condition.event_type.name if hasattr(condition.event_type, 'name') else str(condition.event_type)}"
                                )

                                # 记录监听器以便后续取消订阅
                                if not hasattr(effect, '_event_listeners'):
                                    effect._event_listeners = []
                                effect._event_listeners.append((condition.event_type, listener))

                                print(f"✅ 效果 {effect.name} 订阅事件 {condition.event_type.name if hasattr(condition.event_type, 'name') else str(condition.event_type)}")
                except Exception as e:
                    # 订阅失败不应该影响效果添加
                    print(f"⚠️ 效果 {effect.name} 事件订阅失败: {e}")

                self._add_to_group(effect)
                self._invalidate_cache()
                self.stats["total_applied"] += 1

                # 触发监听器
                self._notify_listeners("effect_added", effect)

            return result

        except Exception as e:
            return EffectResult.error(f"添加效果失败: {str(e)}", exc=e)
    
    def remove_effect(self, effect_id: str, battle_state=None) -> EffectResult:
        """移除效果"""
        try:
            effect = self.effects.get(effect_id)
            if not effect:
                return EffectResult.failure(f"效果 {effect_id} 不存在")
            
            if not effect.is_removable:
                return EffectResult.failure(f"效果 {effect.name} 不可移除")
            
            # 触发移除逻辑
            result = effect.on_remove(self.owner, battle_state)
            
            if result.is_success:
                del self.effects[effect_id]

                # 🔧 优化：清理效果的回合信息
                if effect_id in self._effect_round_info:
                    del self._effect_round_info[effect_id]

                # [NEW] Unsubscribe from the unified event manager
                # Note: 在统一事件管理器中，我们需要跟踪监听器来取消订阅
                # 这里暂时跳过，因为效果移除时会自动清理
                self._remove_from_group(effect)
                self._invalidate_cache()
                self.stats["total_removed"] += 1

                # 触发监听器
                self._notify_listeners("effect_removed", effect)
            
            return result
            
        except Exception as e:
            return EffectResult.error(f"移除效果失败: {str(e)}", exc=e)
    
    def update_effects(self, battle_state=None) -> List[EffectResult]:
        """更新所有效果"""
        results = []
        effects_to_remove = []
        
        # 按优先级排序更新
        sorted_effects = sorted(self.effects.values(), 
                              key=lambda e: e.priority.value, 
                              reverse=True)
        
        for effect in sorted_effects:
            try:
                # 🔧 优化：使用基于效果ID的持续时间管理
                current_round = getattr(battle_state, 'round_num', 0) if battle_state else 0

                # 更新持续时间（只有在不同回合时才递减）
                if effect.duration > 0:
                    # 获取或初始化效果的回合信息
                    round_info = self._effect_round_info.get(effect.id)
                    if round_info is None:
                        # 如果没有记录，说明是旧效果，初始化为当前回合
                        round_info = {
                            "applied_round": current_round,
                            "last_update_round": -1
                        }
                        self._effect_round_info[effect.id] = round_info

                    # 只有在不同回合时才递减持续时间
                    if current_round > round_info["last_update_round"] and round_info["last_update_round"] != -1:
                        effect.remaining_duration -= 1
                        if effect.remaining_duration <= 0:
                            effects_to_remove.append(effect.id)
                            continue

                    # 更新上次更新回合
                    round_info["last_update_round"] = current_round
                
                # 触发更新逻辑
                result = effect.on_update(self.owner, battle_state)
                results.append(result)
                self.stats["total_updates"] += 1
                
            except Exception as e:
                results.append(EffectResult.error(f"更新效果 {effect.name} 失败: {str(e)}", exc=e))
        
        # 移除过期效果
        for effect_id in effects_to_remove:
            remove_result = self.remove_effect(effect_id, battle_state)
            results.append(remove_result)
        
        return results
    
    def get_effects_by_type(self, effect_type: EffectType) -> List[IEffect]:
        """按类型获取效果"""
        cache_key = f"type_{effect_type.value}"
        if cache_key not in self._cache or cache_key in self._dirty_flags:
            self._cache[cache_key] = [e for e in self.effects.values() if e.type == effect_type]
            self._dirty_flags.discard(cache_key)
        return self._cache[cache_key].copy()
    
    def get_effects_by_category(self, category: EffectCategory) -> List[IEffect]:
        """按分类获取效果"""
        cache_key = f"category_{category.value}"
        if cache_key not in self._cache or cache_key in self._dirty_flags:
            self._cache[cache_key] = [e for e in self.effects.values() if e.category == category]
            self._dirty_flags.discard(cache_key)
        return self._cache[cache_key].copy()
    
    def get_effects_by_priority(self, min_priority: EffectPriority = EffectPriority.LOWEST) -> List[IEffect]:
        """按优先级获取效果"""
        cache_key = f"priority_{min_priority.value}"
        if cache_key not in self._cache or cache_key in self._dirty_flags:
            self._cache[cache_key] = [e for e in self.effects.values() if e.priority.value >= min_priority.value]
            self._dirty_flags.discard(cache_key)
        return self._cache[cache_key].copy()
    
    def has_effect(self, effect_id: str) -> bool:
        """检查是否有效果"""
        return effect_id in self.effects
    
    def has_effect_type(self, effect_type: EffectType) -> bool:
        """检查是否有某类型效果"""
        return len(self.get_effects_by_type(effect_type)) > 0
    
    def has_effect_category(self, category: EffectCategory) -> bool:
        """检查是否有某分类效果"""
        return len(self.get_effects_by_category(category)) > 0
    
    def get_effect(self, effect_id: str) -> Optional[IEffect]:
        """获取指定效果"""
        return self.effects.get(effect_id)
    
    def get_effect_data(self, effect_id: str, key: str, default: Any = None) -> Any:
        """获取效果数据"""
        effect = self.effects.get(effect_id)
        return effect.get_data(key, default) if effect else default
    
    def set_effect_data(self, effect_id: str, key: str, value: Any) -> bool:
        """设置效果数据"""
        effect = self.effects.get(effect_id)
        if effect:
            effect.set_data(key, value)
            self._invalidate_cache()
            return True
        return False
    
    def get_aggregated_data(self) -> Dict[str, Any]:
        """获取聚合数据"""
        cache_key = "aggregated_data"
        current_time = time.time()
        
        # 检查缓存是否需要更新
        if (cache_key not in self._cache or 
            cache_key in self._dirty_flags or 
            current_time - self._last_cache_update > 1.0):  # 1秒缓存过期
            
            data = self._compute_aggregated_data()
            self._cache[cache_key] = data
            self._dirty_flags.discard(cache_key)
            self._last_cache_update = current_time
        
        return self._cache[cache_key].copy()
    
    def _compute_aggregated_data(self) -> Dict[str, Any]:
        """计算聚合数据"""
        data = {
            "total_effects": len(self.effects),
            "effects_by_type": {},
            "effects_by_category": {},
            "effects_by_priority": {},
            "special_data": {},
            "modifiers": {
                "damage_bonus": 1.0,
                "damage_reduction": 0.0,
                "crit_bonus": 0.0,
                "hit_bonus": 0.0,
                "dodge_bonus": 0.0,
                "speed_bonus": 0.0,
                "energy_bonus": 0.0
            },
            "active_effects": [],
            "effect_stacks": {},
            "stats": self.stats.copy()
        }
        
        # 统计各类型效果
        for effect_type in EffectType:
            effects = [e for e in self.effects.values() if e.type == effect_type]
            data["effects_by_type"][effect_type.value] = len(effects)
        
        # 统计各分类效果
        for category in EffectCategory:
            effects = [e for e in self.effects.values() if e.category == category]
            data["effects_by_category"][category.value] = len(effects)
        
        # 统计各优先级效果
        for priority in EffectPriority:
            effects = [e for e in self.effects.values() if e.priority == priority]
            data["effects_by_priority"][priority.value] = len(effects)
        
        # 收集效果信息和特殊数据
        for effect in self.effects.values():
            # 活跃效果列表
            data["active_effects"].append({
                "id": effect.id,
                "name": effect.name,
                "type": effect.type.value,
                "category": effect.category.value,
                "stacks": effect.current_stacks,
                "duration": effect.remaining_duration if effect.duration > 0 else -1
            })
            
            # 效果层数
            if effect.current_stacks > 1:
                data["effect_stacks"][effect.name] = effect.current_stacks
            
            # 特殊数据收集
            self._collect_special_data(effect, data["special_data"])
            
            # 修正值收集
            self._collect_modifiers(effect, data["modifiers"])
        
        return data
    
    def _collect_special_data(self, effect: IEffect, special_data: Dict[str, Any]) -> None:
        """收集特殊数据"""
        # 通灵进度
        if effect.has_data("tongling_progress"):
            special_data["tongling_progress"] = effect.get_data("tongling_progress")
            special_data["tongling_count"] = effect.get_data("tongling_count", 0)
            special_data["max_tongling"] = effect.get_data("max_tongling", 2)
            special_data["can_tongling"] = (
                effect.get_data("tongling_progress", 0) >= 100 and
                effect.get_data("tongling_count", 0) < effect.get_data("max_tongling", 2)
            )
        
        # 变身状态
        if effect.has_data("is_transformed"):
            special_data["is_transformed"] = effect.get_data("is_transformed")
        
        # 免疫次数
        if effect.has_data("immunity_charges"):
            special_data["immunity_charges"] = effect.get_data("immunity_charges")
        
        # 嘲讽次数
        if effect.has_data("taunt_charges"):
            special_data["taunt_charges"] = effect.get_data("taunt_charges")
        
        # 复活次数
        if effect.has_data("revive_charges"):
            special_data["revive_charges"] = effect.get_data("revive_charges")
        
        # 其他自定义数据
        for key, value in effect.data.items():
            if key not in ["tongling_progress", "tongling_count", "max_tongling", 
                          "is_transformed", "immunity_charges", "taunt_charges", "revive_charges"]:
                if isinstance(value, (int, float, str, bool)):
                    special_data[f"{effect.name}_{key}"] = value
    
    def _collect_modifiers(self, effect: IEffect, modifiers: Dict[str, float]) -> None:
        """收集修正值"""
        # 伤害相关
        if effect.has_data("damage_bonus"):
            modifiers["damage_bonus"] *= (1 + effect.get_data("damage_bonus"))
        
        if effect.has_data("damage_reduction"):
            modifiers["damage_reduction"] += effect.get_data("damage_reduction")
        
        # 暴击相关
        if effect.has_data("crit_bonus"):
            modifiers["crit_bonus"] += effect.get_data("crit_bonus")
        
        # 命中闪避相关
        if effect.has_data("hit_bonus"):
            modifiers["hit_bonus"] += effect.get_data("hit_bonus")
        
        if effect.has_data("dodge_bonus"):
            modifiers["dodge_bonus"] += effect.get_data("dodge_bonus")
        
        # 速度相关
        if effect.has_data("speed_bonus"):
            modifiers["speed_bonus"] += effect.get_data("speed_bonus")
        
        # 能量相关
        if effect.has_data("energy_bonus"):
            modifiers["energy_bonus"] += effect.get_data("energy_bonus")
    
    def _handle_stack(self, existing: IEffect, new: IEffect, battle_state) -> EffectResult:
        """处理效果叠加"""
        if not existing.stackable:
            # 刷新持续时间
            if new.duration > existing.remaining_duration:
                existing.remaining_duration = new.duration
            return EffectResult.success(message=f"刷新效果 {existing.name}")
        
        # 增加层数
        old_stacks = existing.current_stacks
        existing.current_stacks = min(existing.current_stacks + new.current_stacks, existing.max_stacks)
        
        if existing.current_stacks != old_stacks:
            result = existing.on_stack_change(old_stacks, existing.current_stacks, self.owner, battle_state)
            self.stats["total_stacks_changed"] += 1
            self._notify_listeners("effect_stacked", existing)
            self._invalidate_cache()
            return result
        
        return EffectResult.success(message=f"效果 {existing.name} 已达到最大层数")
    
    def _check_conflicts(self, effect: IEffect) -> List[str]:
        """检查效果冲突"""
        conflicts = []
        for existing_effect in self.effects.values():
            if (existing_effect.id in effect.conflicts or 
                effect.id in existing_effect.conflicts or
                (effect.name == existing_effect.name and not effect.stackable)):
                conflicts.append(existing_effect.name)
        return conflicts
    
    def _add_to_group(self, effect: IEffect) -> None:
        """添加到效果组"""
        group_name = f"{effect.type.value}_{effect.category.value}"
        if group_name not in self.effect_groups:
            self.effect_groups[group_name] = set()
        self.effect_groups[group_name].add(effect.id)
    
    def _remove_from_group(self, effect: IEffect) -> None:
        """从效果组移除"""
        group_name = f"{effect.type.value}_{effect.category.value}"
        if group_name in self.effect_groups:
            self.effect_groups[group_name].discard(effect.id)
            if not self.effect_groups[group_name]:
                del self.effect_groups[group_name]
    
    def _invalidate_cache(self) -> None:
        """使缓存失效"""
        self._dirty_flags.update(self._cache.keys())
    
    def _notify_listeners(self, event: str, effect: IEffect) -> None:
        """通知监听器"""
        if event in self.listeners:
            for listener in self.listeners[event]:
                try:
                    listener(effect, self.owner)
                except Exception as e:
                    logger.error(f"监听器错误 ({event}): {e}")
    
    def add_listener(self, event: str, listener: Callable) -> None:
        """添加监听器"""
        if event not in self.listeners:
            self.listeners[event] = []
        self.listeners[event].append(listener)
    
    def remove_listener(self, event: str, listener: Callable) -> None:
        """移除监听器"""
        if event in self.listeners and listener in self.listeners[event]:
            self.listeners[event].remove(listener)
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._dirty_flags.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats["last_reset"]
        
        return {
            **self.stats,
            "uptime_seconds": uptime,
            "effects_per_second": self.stats["total_applied"] / max(uptime, 1),
            "current_effects": len(self.effects),
            "cache_size": len(self._cache),
            "dirty_flags": len(self._dirty_flags)
        }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "total_applied": 0,
            "total_removed": 0,
            "total_stacks_changed": 0,
            "total_updates": 0,
            "last_reset": time.time()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            "owner_id": getattr(self.owner, 'id', 'unknown'),
            "effects": {eid: effect.to_dict() for eid, effect in self.effects.items()},
            "effect_groups": {k: list(v) for k, v in self.effect_groups.items()},
            "stats": self.get_stats(),
            "aggregated_data": self.get_aggregated_data()
        }


__all__ = [
    'EffectType',
    'EffectCategory', 
    'EffectPriority',
    'EffectResult',
    'IEffect',
    'EffectManager'
]