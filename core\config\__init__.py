from __future__ import annotations
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, Union, Type

# 第三方库导入
from pathlib import Path
import json
import os

# 本地导入
from ..logging import get_logger

"""
配置管理模块

提供游戏配置的统一管理，支持从文件、环境变量等多种来源加载配置。
"""



logger = get_logger("core.config")


@dataclass
class GameConfig:
    """游戏配置数据类 - 单例模式"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    # 系统配置
    debug_mode: bool = False
    log_level: str = "INFO"
    performance_monitoring: bool = True
    
    # 战斗配置
    max_battle_rounds: int = 100
    default_energy_per_round: int = 1
    battle_timeout_seconds: int = 300
    
    # 缓存配置
    enable_caching: bool = True
    cache_ttl_seconds: int = 300
    max_cache_size: int = 1000
    
    # 数据库配置
    database_url: str = "sqlite:///aoqiai.db"
    database_pool_size: int = 5
    
    # API配置
    api_rate_limit: int = 100
    api_timeout_seconds: int = 30
    
    # 精灵配置
    max_spirits_per_team: int = 9
    default_spirit_level: int = 1
    max_spirit_level: int = 100
    
    # 技能配置
    max_skills_per_spirit: int = 4
    skill_cooldown_enabled: bool = True
    
    # 效果配置
    max_effects_per_spirit: int = 20
    effect_stack_limit: int = 10
    
    # 其他配置
    custom_settings: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """配置管理器 - 单例模式"""

    _instance = None
    _initialized = False

    def __new__(cls, config_file: Optional[str] = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config_file: Optional[str] = None):
        # 避免重复初始化
        if self._initialized:
            return

        self.config_file = config_file or "config/game_config.json"
        self.config = GameConfig()
        self._env_mapping = {
            'GAME_DEBUG_MODE': ('debug_mode', bool),
            'GAME_LOG_LEVEL': ('log_level', str),
            'GAME_MAX_ROUNDS': ('max_battle_rounds', int),
            'DATABASE_URL': ('database_url', str),
            'API_RATE_LIMIT': ('api_rate_limit', int),
        }
        self.load_config()
        self._initialized = True
    
    def load_config(self) -> None:
        """加载配置"""
        # 1. 加载默认配置（已在GameConfig中定义）
        
        # 2. 从配置文件加载
        self._load_from_file()
        
        # 3. 从环境变量加载（优先级最高）
        self._load_from_env()
        
        logger.info(f"配置加载完成，调试模式: {self.config.debug_mode}")
    
    def _load_from_file(self) -> None:
        """从配置文件加载"""
        config_path = Path(self.config_file)
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_path}")
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 更新配置
            for key, value in file_config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                else:
                    # 未知配置项放入custom_settings
                    self.config.custom_settings[key] = value
            
            logger.info(f"从文件加载配置: {config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def _load_from_env(self) -> None:
        """从环境变量加载"""
        loaded_count = 0
        for env_key, (config_key, config_type) in self._env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                try:
                    # 类型转换
                    if config_type == bool:
                        value = env_value.lower() in ('true', '1', 'yes', 'on')
                    elif config_type == int:
                        value = int(env_value)
                    elif config_type == float:
                        value = float(env_value)
                    else:
                        value = env_value
                    
                    setattr(self.config, config_key, value)
                    loaded_count += 1
                    
                except ValueError as e:
                    logger.error(f"环境变量 {env_key} 类型转换失败: {e}")
        
        if loaded_count > 0:
            logger.info(f"从环境变量加载了 {loaded_count} 个配置项")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        if hasattr(self.config, key):
            return getattr(self.config, key)
        return self.config.custom_settings.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        if hasattr(self.config, key):
            setattr(self.config, key, value)
        else:
            self.config.custom_settings[key] = value
    
    def save_config(self, file_path: Optional[str] = None) -> None:
        """保存配置到文件"""
        save_path = file_path or self.config_file
        config_dict = self.to_dict()
        
        try:
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {save_path}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for field_name in self.config.__dataclass_fields__:
            if field_name != 'custom_settings':
                result[field_name] = getattr(self.config, field_name)
        
        # 添加自定义设置
        result.update(self.config.custom_settings)
        return result
    
    def reload(self) -> None:
        """重新加载配置"""
        self.config = GameConfig()
        self.load_config()
    
    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        return self.config.debug_mode
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.config.log_level


# 全局配置管理器实例
config_manager = ConfigManager()
config = config_manager.config


def get_config(key: str, default: Any = None) -> Any:
    """便捷函数：获取配置值"""
    return config_manager.get(key, default)


def set_config(key: str, value: Any) -> None:
    """便捷函数：设置配置值"""
    config_manager.set(key, value)


def is_debug_mode() -> bool:
    """便捷函数：检查是否为调试模式"""
    return config_manager.is_debug_mode()


__all__ = [
    'GameConfig',
    'ConfigManager',
    'config_manager',
    'config',
    'get_config',
    'set_config',
    'is_debug_mode'
]