{"id": "tian<PERSON>_sheng<PERSON>_kong<PERSON>_sheng<PERSON>", "name": "天恩圣祭·空灵圣龙", "element": "LIGHT", "professions": ["HEALER", "BALANCE"], "tags": ["DRAGON", "HOLY", "HEALING", "SUPPORT"], "shenge_level": 6, "attributes": {"base_hp": 1320000, "hp_p": 0.0, "hp_flat": 0.0, "base_attack": 340000, "attack_p": 0.0, "attack_flat": 0.0, "base_pdef": 180, "pdef_p": 0.0, "pdef_flat": 0.0, "base_mdef": 200, "mdef_p": 0.0, "mdef_flat": 0.0, "base_speed": 110, "base_hit_rate": 0.08, "base_dodge_rate": 0.06, "base_break_rate": 0.15, "base_block_rate": 0.2, "base_crit_rate": 0.12, "base_crit_res_rate": 0.15, "base_crit_damage": 1.4, "base_damage_reduction": 0.12, "base_penetration": 0.05, "base_max_energy": 300}, "position": [2, 1], "skills": [{"skill_id": "qiansi_yinxian", "name": "牵丝引线", "type": "active", "energy_cost": 0, "description": "攻击对手，造成攻击120%的魔法伤害，攻击后获得30点气势"}, {"skill_id": "ji<PERSON><PERSON>_jingfu", "name": "缄言净缚", "type": "ultimate", "energy_threshold": 150, "description": "攻击目标，造成攻击*300%的魔法伤害，攻击后令各横排最靠前的精灵获得免疫(持续1次攻击)，若该次攻击在隐身或神使状态下，则更改本次超杀目标为敌阵各横排最靠前的精灵"}], "passive_effects": ["dragon_aura_effect"], "description": "光属性圣龙，拥有强大的治疗和辅助能力", "rarity": "SSR", "version": "1.0.0"}