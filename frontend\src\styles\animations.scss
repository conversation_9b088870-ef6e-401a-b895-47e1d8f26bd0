// 动画效果

// 淡入淡出
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

// 滑入效果
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 缩放效果
@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// 弹跳效果
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// 脉冲效果
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 摇摆效果
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-4px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(4px);
  }
}

// 旋转效果
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 闪烁效果
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// 战斗特效
@keyframes damageFlash {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(239, 68, 68, 0.5);
  }
  100% {
    background-color: transparent;
  }
}

@keyframes healFlash {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(16, 185, 129, 0.5);
  }
  100% {
    background-color: transparent;
  }
}

@keyframes criticalHit {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.2) rotate(-5deg);
  }
  50% {
    transform: scale(1.1) rotate(5deg);
  }
  75% {
    transform: scale(1.15) rotate(-3deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

// 工具类
.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slideInUp {
  animation: slideInUp 0.4s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-damage {
  animation: damageFlash 0.3s ease-in-out;
}

.animate-heal {
  animation: healFlash 0.3s ease-in-out;
}

.animate-critical {
  animation: criticalHit 0.5s ease-in-out;
}

// 过渡效果
.transition-all {
  transition: all $transition-normal;
}

.transition-fast {
  transition: all $transition-fast;
}

.transition-slow {
  transition: all $transition-slow;
}