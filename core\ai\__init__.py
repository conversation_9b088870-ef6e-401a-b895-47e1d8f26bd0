"""
AI行动生成系统

这个模块提供了完整的精灵行动生成系统，包括：
- 行动能力检查
- 动态条件评估  
- 条件性效果计算
- 智能行动生成

设计原则：
1. 分层架构：每层职责单一，便于维护和扩展
2. 插件化设计：支持动态添加新的条件检查器和效果计算器
3. 事件驱动：完美集成到现有的事件系统中
4. 高性能：通过缓存和批量处理优化性能
"""

from .action_generator import IntelligentActionGenerator
from .capability_checker import ActionCapabilityChecker, ActionCapabilityResult
from .condition_evaluator import DynamicConditionEvaluator, AttackConditionResult
from .effect_calculator import ConditionalEffectCalculator, ConditionalEffectResult
from .extensions import (
    IConditionChecker, IEffectCalculator, IActionStrategy,
    ConditionRegistry, EffectRegistry, StrategyRegistry
)

# 创建全局实例
_global_action_generator = None

def get_action_generator() -> IntelligentActionGenerator:
    """获取全局行动生成器实例"""
    global _global_action_generator
    if _global_action_generator is None:
        _global_action_generator = IntelligentActionGenerator()
    return _global_action_generator

def reset_action_generator():
    """重置全局行动生成器（主要用于测试）"""
    global _global_action_generator
    _global_action_generator = None

__all__ = [
    # 核心类
    'IntelligentActionGenerator',
    'ActionCapabilityChecker', 
    'ActionCapabilityResult',
    'DynamicConditionEvaluator',
    'AttackConditionResult', 
    'ConditionalEffectCalculator',
    'ConditionalEffectResult',
    
    # 扩展接口
    'IConditionChecker',
    'IEffectCalculator', 
    'IActionStrategy',
    'ConditionRegistry',
    'EffectRegistry',
    'StrategyRegistry',
    
    # 全局函数
    'get_action_generator',
    'reset_action_generator'
]
