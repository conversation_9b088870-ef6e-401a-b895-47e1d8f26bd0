"""
战斗系统工具函数

包含各种战斗相关的工具函数：
- 伤害计算: 公式化伤害计算
- 受击加气: 受击时的气势奖励系统
"""

from .formula_damage_calculator import (
    calculate_formula_damage,
    calculate_healing,
)
from .hit_energy_bonus import (
    HitEnergyBonus,
    HitEnergyBonusEventHandler,
    get_global_hit_energy_handler,
    set_global_hit_energy_bonus,
)

__all__ = [
    # 伤害计算
    'calculate_formula_damage',
    'calculate_healing',

    # 受击加气系统
    'HitEnergyBonus',
    'HitEnergyBonusEventHandler',
    'get_global_hit_energy_handler',
    'set_global_hit_energy_bonus',
]
