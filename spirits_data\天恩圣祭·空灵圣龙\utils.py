"""
天恩圣祭·空灵圣龙 - 工具函数

包含隐身检查、目标过滤等通用功能
"""

from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from src.core.interfaces import IBattleEntity


def is_spirit_invisible(spirit) -> bool:
    """
    检查精灵是否处于隐身状态
    
    Args:
        spirit: 要检查的精灵
        
    Returns:
        bool: 是否隐身
    """
    if not spirit:
        return False
    
    # 方法1: 检查效果管理器中的隐身效果
    if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
        has_invisibility_effect = any(
            effect.name == "隐身" 
            for effect in spirit.effect_manager.effects.values()
        )
        if has_invisibility_effect:
            return True
    
    # 方法2: 检查精灵的隐身标记
    if hasattr(spirit, 'is_invisible') and spirit.is_invisible:
        return True
    
    # 方法3: 检查精灵状态字典（如果存在）
    if hasattr(spirit, 'status') and isinstance(spirit.status, dict):
        if spirit.status.get('invisible', False):
            return True
    
    return False


def is_spirit_divine_messenger(spirit) -> bool:
    """
    检查精灵是否处于神使状态
    
    Args:
        spirit: 要检查的精灵
        
    Returns:
        bool: 是否神使状态
    """
    if not spirit:
        return False
    
    # 检查效果管理器中的神使效果
    if hasattr(spirit, 'effect_manager') and spirit.effect_manager:
        has_divine_effect = any(
            "神使" in effect.name or "divine_messenger" in effect.name.lower()
            for effect in spirit.effect_manager.effects.values()
        )
        if has_divine_effect:
            return True
    
    # 检查精灵的神使标记
    if hasattr(spirit, 'is_divine_messenger') and spirit.is_divine_messenger:
        return True
    
    return False


def filter_targetable_enemies(caster, enemies: List) -> List:
    """
    过滤可以被选择为目标的敌方精灵（排除隐身精灵）
    
    Args:
        caster: 施法者
        enemies: 敌方精灵列表
        
    Returns:
        List: 可以被选择的敌方精灵列表
    """
    if not enemies:
        return []
    
    targetable_enemies = []
    
    for enemy in enemies:
        # 检查是否存活
        if not getattr(enemy, 'is_alive', True):
            continue
        
        # 检查是否隐身
        if is_spirit_invisible(enemy):
            # 隐身的精灵不能被敌方选择为目标
            if hasattr(caster, 'team') and hasattr(enemy, 'team'):
                if caster.team != enemy.team:  # 敌对关系
                    continue  # 跳过隐身的敌方精灵
        
        targetable_enemies.append(enemy)
    
    return targetable_enemies


def get_front_spirits_by_row(spirits: List, team: int = None) -> List:
    """
    获取每横排最靠前的精灵
    
    Args:
        spirits: 精灵列表
        team: 队伍编号（可选，用于过滤）
        
    Returns:
        List: 每排最前的精灵列表
    """
    if not spirits:
        return []
    
    # 过滤队伍（如果指定）
    if team is not None:
        spirits = [s for s in spirits if getattr(s, 'team', 0) == team]
    
    # 按横排分组
    row_groups = {}
    for spirit in spirits:
        if not getattr(spirit, 'is_alive', True):
            continue
        
        row = spirit.position[0] if hasattr(spirit, 'position') else 0
        if row not in row_groups:
            row_groups[row] = []
        row_groups[row].append(spirit)
    
    # 选择每排最靠前的精灵（纵排位置最小）
    front_spirits = []
    for row, row_spirits in row_groups.items():
        # 按纵排位置排序
        row_spirits.sort(key=lambda s: s.position[1] if hasattr(s, 'position') else 0)
        front_spirits.append(row_spirits[0])
    
    return front_spirits


# 导出所有工具函数
__all__ = [
    'is_spirit_invisible',
    'is_spirit_divine_messenger', 
    'filter_targetable_enemies',
    'get_front_spirits_by_row'
]
