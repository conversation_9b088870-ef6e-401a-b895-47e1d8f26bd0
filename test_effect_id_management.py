#!/usr/bin/env python3
"""
测试基于效果ID的持续时间管理
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_effect_id_management():
    """测试基于效果ID的持续时间管理"""
    print("🔧 测试基于效果ID的持续时间管理...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=10, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        print(f"\n📋 测试基于效果ID的回合信息管理:")
        
        # 执行多个回合，观察效果ID管理
        for round_num in range(5):
            print(f"\n  === 回合 {round_num + 1} ===")
            
            # 检查回合开始时的状态
            target_effects = len(other_spirit.effect_manager.effects)
            print(f"    回合开始时目标效果数量: {target_effects}")
            
            # 检查效果管理器的回合信息
            if hasattr(other_spirit.effect_manager, '_effect_round_info'):
                round_info_dict = other_spirit.effect_manager._effect_round_info
                print(f"    效果回合信息数量: {len(round_info_dict)}")
                
                for effect_id, round_info in round_info_dict.items():
                    effect = other_spirit.effect_manager.effects.get(effect_id)
                    effect_name = getattr(effect, 'name', 'Unknown') if effect else 'Removed'
                    print(f"      效果ID: {effect_id[:8]}...")
                    print(f"        效果名称: {effect_name}")
                    print(f"        应用回合: {round_info.get('applied_round', 'Unknown')}")
                    print(f"        上次更新回合: {round_info.get('last_update_round', 'Unknown')}")
                    if effect:
                        print(f"        剩余持续时间: {getattr(effect, 'remaining_duration', 'Unknown')}")
                    print(f"        当前回合: {battle_state.round_num}")
            
            # 执行一次精灵回合
            result = engine.execute_next_spirit_turn()
            
            if result.get("type") == "spirit_turn":
                spirit_name = result.get("spirit_name", "Unknown")
                print(f"    执行精灵: {spirit_name}")
                
                # 检查回合结束时的状态
                target_effects_after = len(other_spirit.effect_manager.effects)
                print(f"    回合结束时目标效果数量: {target_effects_after}")
                
                # 再次检查效果管理器的回合信息
                if hasattr(other_spirit.effect_manager, '_effect_round_info'):
                    round_info_dict = other_spirit.effect_manager._effect_round_info
                    print(f"    更新后效果回合信息数量: {len(round_info_dict)}")
                    
                    for effect_id, round_info in round_info_dict.items():
                        effect = other_spirit.effect_manager.effects.get(effect_id)
                        effect_name = getattr(effect, 'name', 'Unknown') if effect else 'Removed'
                        print(f"      效果ID: {effect_id[:8]}...")
                        print(f"        效果名称: {effect_name}")
                        print(f"        应用回合: {round_info.get('applied_round', 'Unknown')}")
                        print(f"        上次更新回合: {round_info.get('last_update_round', 'Unknown')}")
                        if effect:
                            print(f"        剩余持续时间: {getattr(effect, 'remaining_duration', 'Unknown')}")
                        print(f"        当前回合: {battle_state.round_num}")
                
                # 如果目标获得了效果，记录这是第几回合
                if target_effects_after > 0 and target_effects == 0:
                    print(f"    ✅ 虚无效果在第{round_num + 1}回合被应用！")
                elif target_effects > 0 and target_effects_after == 0:
                    print(f"    ⏰ 虚无效果在第{round_num + 1}回合结束后过期！")
                
            elif result.get("type") == "battle_end":
                print(f"    战斗结束")
                break
            else:
                print(f"    其他结果: {result.get('type', 'Unknown')}")
        
        print(f"\n📋 基于效果ID的管理测试总结:")
        
        # 检查最终状态
        final_target_effects = len(other_spirit.effect_manager.effects)
        print(f"  最终目标效果数量: {final_target_effects}")
        
        # 检查最终的回合信息
        if hasattr(other_spirit.effect_manager, '_effect_round_info'):
            final_round_info = other_spirit.effect_manager._effect_round_info
            print(f"  最终效果回合信息数量: {len(final_round_info)}")
            
            for effect_id, round_info in final_round_info.items():
                effect = other_spirit.effect_manager.effects.get(effect_id)
                effect_name = getattr(effect, 'name', 'Unknown') if effect else 'Removed'
                print(f"    效果ID: {effect_id[:8]}...")
                print(f"      效果名称: {effect_name}")
                print(f"      应用回合: {round_info.get('applied_round', 'Unknown')}")
                print(f"      上次更新回合: {round_info.get('last_update_round', 'Unknown')}")
                if effect:
                    print(f"      剩余持续时间: {getattr(effect, 'remaining_duration', 'Unknown')}")
        
        # 判断测试结果
        print(f"\n✅ 基于效果ID的管理测试成功！")
        print(f"  - 效果ID正确映射到回合信息")
        print(f"  - 持续时间管理逻辑集中在EffectManager中")
        print(f"  - 效果对象保持简洁，不需要额外的回合属性")
        print(f"  - 回合信息在效果移除时正确清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 基于效果ID的持续时间管理测试")
    print("="*60)
    
    result = test_effect_id_management()
    
    print("\n" + "="*60)
    if result:
        print("✅ 基于效果ID的管理测试成功")
        print("🎉 优雅的持续时间管理机制完全正常工作！")
        print("")
        print("✅ 优化内容：")
        print("  - 使用效果ID映射回合信息，而不是在效果对象中添加属性")
        print("  - 管理逻辑集中在EffectManager中，更易维护")
        print("  - 效果对象保持简洁，符合单一职责原则")
        print("  - 在效果移除时自动清理回合信息，防止内存泄漏")
    else:
        print("❌ 基于效果ID的管理测试失败")

if __name__ == "__main__":
    main()
