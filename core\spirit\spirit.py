"""
Spirit 类

使用组件系统拆分职责，提高可维护性和可测试性。
"""
from __future__ import annotations
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum, auto

from ..interfaces import IBattleEntity, IBattleState
from ..components import ComponentManager, HealthComponent, EnergyComponent, SkillComponent
from ..effect import EffectManager, EffectType, EffectCategory, EffectResult
from ..attribute import Attributes
from ..element import ElementType
from ..profession import ProfessionType
from ..exceptions import SpiritNotFoundException, InvalidPositionException, InsufficientEnergyException
from ..logging import spirit_logger



class LifeState(Enum):
    """精灵生命状态"""
    ALIVE = auto()
    FATALLY_WOUNDED = auto()
    DEAD = auto()


@dataclass
class SpiritMetadata:
    """精灵元数据"""
    element: Optional[ElementType] = None
    professions: Set[ProfessionType] = field(default_factory=set)
    tags: Set[str] = field(default_factory=set)
    shenge_level: int = 0
    emissary_prototype_name: Optional[str] = None
    shenyao_config: Optional[Dict[str, Any]] = None  # 神曜配置
    
    def __post_init__(self):
        if self.professions is None:
            self.professions = set()
        if self.tags is None:
            self.tags = set()


@dataclass
class SpiritPosition:
    """精灵位置信息"""
    row: int
    col: int
    team: int
    
    @property
    def coordinates(self) -> Tuple[int, int]:
        """获取坐标元组"""
        return (self.row, self.col)
    
    def validate(self) -> None:
        """验证位置有效性"""
        if not (1 <= self.row <= 3 and 1 <= self.col <= 3):
            raise InvalidPositionException(
                (self.row, self.col), 
                "位置必须在 1-3 范围内"
            )


class Spirit(IBattleEntity):
    """
    重构后的精灵类
    
    职责明确：
    - 作为各个组件的协调者
    - 提供统一的接口
    - 管理精灵的基本标识信息
    """
    
    def __init__(
        self,
        id: str,
        name: str,
        attributes: Attributes,
        position: Tuple[int, int],
        team: int,
        *,
        skills: Optional[List[Any]] = None,
        metadata: Optional[SpiritMetadata] = None,
        unified_event_manager=None
    ):
        # 基本信息
        self.id = id
        self.name = name
        self.attributes = attributes
        
        # 位置信息
        self.position_info = SpiritPosition(position[0], position[1], team)
        self.position_info.validate()
        
        # 元数据
        self.metadata = metadata or SpiritMetadata()
        
        # 组件管理器
        self.components = ComponentManager(self)
        
        # 初始化核心组件
        self._initialize_components(skills, unified_event_manager)
        
        # 生命状态
        self.life_state = LifeState.ALIVE
        
        # 额外回合数（用于连击等机制）
        self.extra_turns: int = 0
        
        # 契约相关
        self.contract_ids: Set[str] = set()
        self.emissary_spirit_id: Optional[str] = None

        # 技能缓存（主动/超杀），由 BattleEngine 生成，键: str, 值: Skill
        self._cached_skill_map: Dict[str, Any] = {}  # 用于减少每回合技能遍历
    
    def set_unified_event_manager(self, unified_event_manager):
        """Injects the battle-wide unified event manager into the spirit's effect manager."""
        if self._unified_effect_manager:
            self._unified_effect_manager.unified_event_manager = unified_event_manager

            # 🔧 修复：自动订阅已有的被动效果
            # 当设置统一事件管理器时，需要订阅所有已存在的效果
            from ..battle.models import BattleState

            # 尝试获取当前的战斗状态（如果可用）
            battle_state = None
            if hasattr(self, '_battle_state_ref'):
                battle_state = self._battle_state_ref

            # 如果没有战斗状态引用，创建一个临时的用于订阅
            # 这是安全的，因为订阅只需要事件管理器
            if not battle_state:
                # 创建一个最小的战斗状态用于订阅
                from ..formation import Formation
                temp_formation = Formation()
                battle_state = BattleState(temp_formation, temp_formation, unified_event_manager=unified_event_manager)

            # 订阅所有已有的被动效果
            subscription_count = 0
            for effect_id, effect in self._unified_effect_manager.effects.items():
                if hasattr(effect, 'get_trigger_conditions'):
                    conditions = effect.get_trigger_conditions()
                    if conditions:  # 只订阅有触发条件的效果
                        # 使用统一事件管理器订阅效果
                        # 这里需要实现新的订阅逻辑
                        subscription_count += 1

            # 记录订阅信息（用于调试）
            if subscription_count > 0:
                from ..logging import spirit_logger
                spirit_logger.debug(f"{self.name} 自动订阅了 {subscription_count} 个被动效果")

    def _initialize_components(self, skills: Optional[List[Any]] = None, unified_event_manager=None) -> None:
        """初始化核心组件"""
        # 生命值组件
        health_component = HealthComponent(self, self.attributes.hp)
        self.components.add_component(health_component)
        
        # 能量组件
        energy_component = EnergyComponent(self, max_energy=150)
        self.components.add_component(energy_component)

        # 技能组件
        skill_component = SkillComponent(self, skills or [])
        self.components.add_component(skill_component)

        # 响应式效果系统
        # The unified_event_manager is now set via set_unified_event_manager
        self._unified_effect_manager = EffectManager(self, unified_event_manager=unified_event_manager)

        # 超杀技能管理器
        from ..skill.ultimate_config import UltimateSkillManager
        self.ultimate_manager = UltimateSkillManager(self)
        
        # 添加监听器
        self._unified_effect_manager.add_listener("effect_added", self._on_effect_added)
        self._unified_effect_manager.add_listener("effect_removed", self._on_effect_removed)
        self._unified_effect_manager.add_listener("effect_stacked", self._on_effect_stacked)
        
        # ✅ 自动初始化被动效果
        self._auto_initialize_passive_effects()
        
        spirit_logger.debug(f"为 {self.name} 初始化了所有组件和被动效果")
    
    def initialize_passive_effects(self):
        """初始化被动效果 - 公共接口方法"""
        spirit_logger.debug(f"为 {self.name} 初始化被动效果")
        
        # 如果已经有效果管理器，直接返回
        if self.effect_manager and self.effect_manager.effects:
            spirit_logger.debug(f"{self.name} 已有 {len(self.effect_manager.effects)} 个效果，跳过重复初始化")
            return
        
        # 调用内部的自动初始化方法
        self._auto_initialize_passive_effects()
        
        spirit_logger.debug(f"{self.name} 被动效果初始化完成")

    def _auto_initialize_passive_effects(self) -> None:
        """自动初始化被动效果 - 完全自动化版本"""
        try:
            # 方案1: 通过元数据配置自动初始化
            if self.metadata and hasattr(self.metadata, 'passive_effects'):
                self._initialize_from_metadata()
                return
            
            # 方案2: 通过精灵数据模块自动发现
            self._initialize_from_spirit_module()
            
        except Exception as e:
            spirit_logger.warning(f"{self.name} 被动效果自动初始化失败: {e}")
    
    def _initialize_from_metadata(self) -> None:
        """从元数据配置初始化被动效果"""
        if not self.metadata or not hasattr(self.metadata, 'passive_effects'):
            return
            
        for effect_config in self.metadata.passive_effects:
            try:
                effect = self._create_effect_from_config(effect_config)
                if effect:
                    result = self.apply_effect(effect, battle_state=None)
                    spirit_logger.debug(f"从元数据创建效果 {effect.name}: {result.success if result else False}")
            except Exception as e:
                spirit_logger.warning(f"创建效果失败 {effect_config}: {e}")
    
    def _initialize_from_spirit_module(self) -> None:
        """从精灵数据模块自动发现并初始化被动效果"""
        try:
            # 根据精灵ID自动查找对应的数据模块
            spirit_module = self._get_spirit_module()
            if not spirit_module:
                return
            
            # 查找被动效果创建函数
            create_function = self._find_passive_effects_function(spirit_module)
            if create_function:
                effects = create_function(self)
                for effect in effects:
                    result = self.apply_effect(effect, battle_state=None)
                    spirit_logger.debug(f"自动创建被动效果 {effect.name}: {result.success if result else False}")
                spirit_logger.debug(f"{self.name} 自动初始化了 {len(effects)} 个被动效果")
            else:
                spirit_logger.debug(f"{self.name} 没有找到被动效果创建函数")
                
        except Exception as e:
            spirit_logger.warning(f"自动发现被动效果失败: {e}")
    
    def _get_spirit_module(self):
        """
        获取精灵对应的数据模块。
        
        采用“约定优于配置”的原则，模块路径根据精灵ID自动生成。
        例如，ID为 'my_spirit' 的精灵会加载 'spirits_data.my_spirit' 模块。
        """
        if not hasattr(self, 'id') or not self.id:
            return None
            
        try:
            module_name = f"spirits_data.{self.id}"
            import importlib
            return importlib.import_module(module_name)
        except ImportError:
            # 模块不存在是正常情况，例如该精灵没有被动效果
            spirit_logger.debug(f"未找到精灵 '{self.name}' (ID: {self.id}) 的数据模块: {module_name}")
        except Exception as e:
            spirit_logger.warning(f"加载精灵模块 {module_name} 时发生未知错误: {e}")
        
        return None
    
    def _find_passive_effects_function(self, module):
        """在模块中查找被动效果创建函数"""
        # 标准函数名模式
        function_names = [
            f"create_{self.id}_passive_effects",
            "create_passive_effects",
            f"create_{self.id.split('_')[-1]}_passive_effects",  # 取最后一部分
        ]
        
        for func_name in function_names:
            if hasattr(module, func_name):
                return getattr(module, func_name)
        
        return None
    
    def _create_effect_from_config(self, effect_config):
        """从配置创建效果"""
        try:
            if isinstance(effect_config, str):
                # 简单字符串配置，尝试从效果工厂创建
                from core.effect import create_effect
                return create_effect(effect_config, caster=self)
            elif isinstance(effect_config, dict):
                # 字典配置，包含详细参数
                effect_type = effect_config.get('type')
                if effect_type is None:
                    spirit_logger.warning(f"效果配置缺少'type'字段: {effect_config}")
                    return None
                params = effect_config.get('params', {})
                from src.core.effect import create_effect
                return create_effect(effect_type, caster=self, **params)
            else:
                spirit_logger.warning(f"不支持的效果配置类型: {type(effect_config)}")
                
        except Exception as e:
            spirit_logger.warning(f"创建效果失败: {e}")
        
        return None
    
    # === IBattleEntity 接口实现 ===
    
    @property
    def position(self) -> Tuple[int, int]:
        """获取位置"""
        return self.position_info.coordinates
    
    @position.setter
    def position(self, value: Tuple[int, int]) -> None:
        """设置位置"""
        self.position_info.row, self.position_info.col = value
        self.position_info.validate()
    
    @property
    def team(self) -> int:
        """获取队伍"""
        return self.position_info.team
    
    @team.setter
    def team(self, value: int) -> None:
        """设置队伍"""
        self.position_info.team = value
    
    @property
    def current_hp(self) -> float:
        """当前生命值"""
        health_comp = self.components.get_component(HealthComponent)
        return health_comp.current_hp if health_comp else 0.0
    
    @current_hp.setter
    def current_hp(self, value: float) -> None:
        """设置当前生命值"""
        health_comp = self.components.get_component(HealthComponent)
        if health_comp:
            health_comp.current_hp = value
    
    @property
    def max_hp(self) -> float:
        """最大生命值"""
        health_comp = self.components.get_component(HealthComponent)
        return health_comp.max_hp if health_comp else 0.0
    
    @property
    def energy(self) -> int:
        """当前能量"""
        energy_comp = self.components.get_component(EnergyComponent)
        return energy_comp.current_energy if energy_comp else 0
    
    @energy.setter
    def energy(self, value: int) -> None:
        """设置当前能量"""
        energy_comp = self.components.get_component(EnergyComponent)
        if energy_comp:
            energy_comp.current_energy = value
    
    @property
    def max_energy(self) -> int:
        """最大能量值"""
        energy_comp = self.components.get_component(EnergyComponent)
        return energy_comp.max_energy if energy_comp else 0

    def can_use_ultimate(self, skill_id: Optional[str] = None) -> bool:
        """检查是否可以使用超杀技能"""
        if not hasattr(self, 'ultimate_manager'):
            return False

        if skill_id:
            # 检查特定超杀技能
            return self.ultimate_manager.can_use_ultimate(skill_id, self.energy)
        else:
            # 检查是否可以使用任何超杀技能
            return self.ultimate_manager.can_use_any_ultimate(self.energy)

    def can_use_any_ultimate(self) -> bool:
        """检查是否可以使用任何超杀技能"""
        return self.can_use_ultimate()

    def get_usable_ultimates(self) -> List[Any]:
        """获取当前可以使用的超杀技能"""
        if not hasattr(self, 'ultimate_manager'):
            return []
        return self.ultimate_manager.get_usable_ultimates(self.energy)

    def get_ultimate_threshold(self, skill_id: Optional[str] = None) -> int:
        """获取超杀阈值"""
        if not hasattr(self, 'ultimate_manager'):
            return 300  # 默认阈值

        if skill_id:
            config = self.ultimate_manager.get_ultimate_skill(skill_id)
            return config.energy_threshold if config else 300
        else:
            # 返回最低阈值
            return self.ultimate_manager.get_lowest_threshold()

    def get_ultimate_status(self) -> Dict[str, Any]:
        """获取超杀系统状态"""
        if not hasattr(self, 'ultimate_manager'):
            return {'can_use_any': False, 'total_ultimates': 0}
        return self.ultimate_manager.get_status_info(self.energy)

    # === 技能类型管理方法 ===

    def get_skills_by_type(self, skill_type: str) -> List[Any]:
        """
        按类型获取技能

        Args:
            skill_type: 技能类型

        Returns:
            指定类型的技能列表
        """
        from ..components import SkillComponent
        skill_component = self.components.get_component(SkillComponent)
        if skill_component:
            return skill_component.get_skills_by_type(skill_type)
        return []

    def get_active_skills(self) -> List[Any]:
        """获取主动技能"""
        return self.get_skills_by_type('active')

    def get_passive_skills(self) -> List[Any]:
        """获取被动技能"""
        return self.get_skills_by_type('passive')

    def get_shenyao_skills(self) -> List[Any]:
        """获取神曜技能"""
        return self.get_skills_by_type('shenyao')

    def get_tongling_skills(self) -> List[Any]:
        """获取通灵技能"""
        return self.get_skills_by_type('tongling')

    def can_use_skill(self, skill_name: str) -> bool:
        """
        检查是否可以使用指定技能

        Args:
            skill_name: 技能名称

        Returns:
            是否可以使用
        """
        from ..components import SkillComponent
        skill_component = self.components.get_component(SkillComponent)
        if skill_component:
            return skill_component.can_use_skill(skill_name)
        return False

    def get_skill_info(self) -> Dict[str, Any]:
        """
        获取技能系统信息

        Returns:
            技能系统信息字典
        """
        from ..components import SkillComponent
        skill_component = self.components.get_component(SkillComponent)

        if not skill_component:
            return {
                'total_skills': 0,
                'active_skills': 0,
                'passive_skills': 0,
                'shenyao_skills': 0,
                'tongling_skills': 0,
                'ultimate_skills': 0
            }

        active_skills = self.get_active_skills()
        passive_skills = self.get_passive_skills()
        shenyao_skills = self.get_shenyao_skills()
        tongling_skills = self.get_tongling_skills()

        ultimate_count = 0
        if hasattr(self, 'ultimate_manager'):
            ultimate_count = len(self.ultimate_manager.ultimate_skills)

        return {
            'total_skills': len(skill_component.skills),
            'active_skills': len(active_skills),
            'passive_skills': len(passive_skills),
            'shenyao_skills': len(shenyao_skills),
            'tongling_skills': len(tongling_skills),
            'ultimate_skills': ultimate_count,
            'skill_details': {
                'active': [skill.name for skill in active_skills],
                'passive': [skill.name for skill in passive_skills],
                'shenyao': [skill.name for skill in shenyao_skills],
                'tongling': [skill.name for skill in tongling_skills],
                'ultimate': [config.name for config in self.ultimate_manager.ultimate_skills.values()] if hasattr(self, 'ultimate_manager') else []
            }
        }

    @property
    def is_alive(self) -> bool:
        """是否存活"""
        # 修复：基于HP判断，而不是life_state
        return self.current_hp > 0

    @property
    def is_targetable(self) -> bool:
        """该单位当前是否可被选为目标"""
        return not self.has_effect("Untargetable")

    @property
    def speed(self) -> float:
        """速度"""
        return self.attributes.speed
    
    @property
    def combat_power(self) -> float:
        """战斗力"""
        attrs = self.attributes
        return (attrs.hp / 10) + attrs.attack + attrs.pdef + attrs.mdef + attrs.speed

    # === 技能相关方法 (通过组件代理) ===

    @property
    def skills(self) -> List[Any]:
        """获取技能列表"""
        skill_comp = self.components.get_component(SkillComponent)
        return skill_comp.skills if skill_comp else []

    def get_skill(self, skill_name: str) -> Optional[Any]:
        """按名称获取技能"""
        skill_comp = self.components.get_component(SkillComponent)
        return skill_comp.get_skill(skill_name) if skill_comp else None

    def can_cast_skill(self, skill_name: str, battle_state: IBattleState) -> bool:
        """检查是否可以释放技能"""
        skill_comp = self.components.get_component(SkillComponent)
        return skill_comp.can_cast_skill(skill_name, battle_state) if skill_comp else False

    # === 效果相关方法 (统一效果系统) ===

    @property
    def effect_manager(self):
        """兼容性属性：返回统一效果管理器"""
        return self._unified_effect_manager

    @property
    def effects(self) -> List[Any]:
        """获取所有效果列表"""
        return list(self._unified_effect_manager.effects.values())

    def apply_effect(self, effect, battle_state: Optional[IBattleState] = None) -> Any:
        """应用效果 - 统一版本"""
        return self._unified_effect_manager.add_effect(effect, battle_state)

    def remove_effect(self, effect_id: str, battle_state: Optional[IBattleState] = None) -> Any:
        """移除效果 - 统一版本"""
        return self._unified_effect_manager.remove_effect(effect_id, battle_state)

    def has_effect(self, effect_name: str) -> bool:
        """检查是否有效果 - 统一版本"""
        # 支持按名称或ID查找
        for effect in self._unified_effect_manager.effects.values():
            if effect.name == effect_name or effect.id == effect_name:
                return True
        return False

    def has_effect_type(self, effect_type: EffectType) -> bool:
        """检查是否有某类型效果"""
        return self._unified_effect_manager.has_effect_type(effect_type)

    def has_effect_category(self, category: EffectCategory) -> bool:
        """检查是否有某分类效果"""
        return self._unified_effect_manager.has_effect_category(category)

    def get_effects_by_type(self, effect_type: EffectType) -> List[Any]:
        """按类型获取效果"""
        return self._unified_effect_manager.get_effects_by_type(effect_type)

    def get_effects_by_category(self, category: EffectCategory) -> List[Any]:
        """按分类获取效果"""
        return self._unified_effect_manager.get_effects_by_category(category)

    def update_effects(self, battle_state: Optional[IBattleState] = None) -> List[Any]:
        """更新所有效果"""
        return self._unified_effect_manager.update_effects(battle_state)

    def on_event(self, event_data: Any, battle_state: IBattleState) -> List[Any]:
        """将事件分发给所有组件和效果系统"""
        actions = []
        # 分发给组件
        for component in self.components.components.values():
            if hasattr(component, 'on_event'):
                result = component.on_event(event_data, battle_state)
                if result is not None:
                    if isinstance(result, list):
                        actions.extend(result)
                    else:
                        actions.append(result)
        
        # 分发给效果管理器
        if self.effect_manager:
            effect_actions = self.effect_manager.update_effects(battle_state)
            if effect_actions:
                for res in effect_actions:
                    if res.actions:
                        actions.extend(res.actions)
                
        return actions

    # === 属性相关方法 ===
    
    def set_hp(self, value: float) -> None:
        """直接设置生命值（用于Action）"""
        health_comp = self.components.get_component(HealthComponent)
        if health_comp:
            health_comp.current_hp = value

    def set_energy(self, value: int) -> None:
        """直接设置能量值（用于Action）"""
        energy_comp = self.components.get_component(EnergyComponent)
        if energy_comp:
            energy_comp.current_energy = value

    def get_attribute(self, attr_name: str) -> float:
        """获取属性值"""
        # 效果系统会通过修改器来影响最终属性，这部分逻辑在 Attributes 类中处理
        if hasattr(self.attributes, 'get_value'):
            return self.attributes.get_value(attr_name, spirit=self)
        return getattr(self.attributes, attr_name, 0.0)
    
    def set_attribute(self, attr_name: str, value: float) -> None:
        """设置属性值"""
        # 注意：这会设置基础值，计算后的值会受效果影响
        if hasattr(self.attributes, attr_name):
            setattr(self.attributes, attr_name, value)
    
    # === 生命值相关方法 ===
    
    def take_damage(self, damage: float, is_destruction: bool = False) -> float:
        """承受伤害，返回实际造成的伤害值。

        Args:
            damage: 伤害值
            is_destruction: 是否为毁灭伤害（无视减伤和免疫）
        """
        # 毁灭伤害无视免疫状态
        if not is_destruction and not self.is_targetable:
            spirit_logger.info(f"{self.name} 免疫了本次伤害")
            return 0.0

        health_comp = self.components.get_component(HealthComponent)
        if health_comp:
            return health_comp.take_damage(damage, is_destruction)
        return 0.0
    
    def heal(self, amount: float) -> float:
        """治疗"""
        health_comp = self.components.get_component(HealthComponent)
        if health_comp:
            return health_comp.heal(amount)
        return 0.0
    
    def revive(self, hp_percent: float = 1.0) -> None:
        """复活"""
        if not self.is_alive:
            health_comp = self.components.get_component(HealthComponent)
            if health_comp:
                health_comp.revive(hp_percent)
    
    # === 能量相关方法 ===
    
    def gain_energy(self, amount: int) -> int:
        """获得能量"""
        energy_comp = self.components.get_component(EnergyComponent)
        if energy_comp:
            return energy_comp.gain_energy(amount)
        return 0
    
    def consume_energy(self, amount: int) -> None:
        """
        消耗能量，如果能量不足则抛出 InsufficientEnergyException。
        """
        energy_comp = self.components.get_component(EnergyComponent)
        if not energy_comp or not energy_comp.consume_energy(amount):
            raise InsufficientEnergyException(amount, self.energy)
    
    # === 元数据相关方法 ===
    
    def has_profession(self, profession: ProfessionType) -> bool:
        """检查是否有指定职业"""
        return profession in self.metadata.professions
    
    def add_profession(self, profession: ProfessionType) -> None:
        """添加职业"""
        self.metadata.professions.add(profession)
    
    def has_tag(self, tag: str) -> bool:
        """检查是否有指定标签"""
        return tag in self.metadata.tags
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        self.metadata.tags.add(tag)
    
    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        self.metadata.tags.discard(tag)
    
    # === 序列化与数据访问 ===
    
    def to_dict(self) -> Dict[str, Any]:
        """将精灵对象序列化为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "position": self.position,
            "team": self.team,
            "is_alive": self.is_alive,
            "life_state": self.life_state.name,
            "attributes": {
                "hp": self.current_hp,
                "max_hp": self.max_hp,
                "attack": self.get_attribute("attack"),
                "pdef": self.get_attribute("pdef"),
                "mdef": self.get_attribute("mdef"),
                "speed": self.speed,
                "crit_rate": self.get_attribute("crit_rate"),
                "crit_damage": self.get_attribute("crit_damage"),
            },
            "energy": self.energy,
            "max_energy": self.max_energy,
            "skills": [skill.name for skill in self.skills],
            "effects": [effect.to_dict() for effect in self.effects if hasattr(effect, 'to_dict')],
            "metadata": {
                "element": self.metadata.element.value if self.metadata.element else None,
                "professions": [p.value for p in self.metadata.professions],
                "tags": list(self.metadata.tags),
                "shenge_level": self.metadata.shenge_level ,
            },
            "contract_ids": list(self.contract_ids),
        }
    
    def get_internal_state(self) -> Dict[str, Any]:
        """获取精灵的完整内部状态 - 统一版本"""
        aggregated_data = self._unified_effect_manager.get_aggregated_data()
        
        return {
            "tongling_progress": aggregated_data["special_data"].get("tongling_progress", 0),
            "tongling_count": aggregated_data["special_data"].get("tongling_count", 0),
            "max_tongling": aggregated_data["special_data"].get("max_tongling", 2),
            "special_states": aggregated_data["special_data"],
            "active_effects": [effect["name"] for effect in aggregated_data["active_effects"]],
            "effect_stacks": aggregated_data["effect_stacks"],
            "total_effects": aggregated_data["total_effects"],
            "effects_by_type": aggregated_data["effects_by_type"],
            "effects_by_category": aggregated_data["effects_by_category"]
        }
    
    def get_tongling_info(self) -> Dict[str, Any]:
        """获取通灵相关信息 - 统一版本"""
        aggregated_data = self._unified_effect_manager.get_aggregated_data()
        special_data = aggregated_data["special_data"]
        
        return {
            "progress": special_data.get("tongling_progress", 0),
            "count": special_data.get("tongling_count", 0),
            "max_count": special_data.get("max_tongling", 2),
            "can_tongling": special_data.get("can_tongling", False),
            "effect_name": self._find_tongling_effect_name()
        }
    
    def get_damage_modifiers(self) -> Dict[str, float]:
        """获取伤害修正系数 - 统一版本"""
        aggregated_data = self._unified_effect_manager.get_aggregated_data()
        return aggregated_data["modifiers"]
    
    def get_all_effect_properties(self) -> Dict[str, Any]:
        """获取所有效果的属性 - 统一版本"""
        properties = {}
        
        for effect_id, effect in self._unified_effect_manager.effects.items():
            if hasattr(effect, 'type') and hasattr(effect, 'category') and hasattr(effect, 'priority'):
                properties[effect_id] = {
                    "id": effect.id,
                    "name": effect.name,
                    "type": effect.type.value,
                    "category": effect.category.value,
                    "priority": effect.priority.value,
                    "stacks": effect.stacks,
                    "duration": effect.remaining_duration if effect.duration > 0 else -1,
                    "is_active": effect.is_active,
                    "data": effect.data.copy(),
                    "display_info": effect.get_display_info()
                }
        
        return properties
    
    def get_battle_context(self) -> Dict[str, Any]:
        """获取战斗上下文信息 - 统一版本"""
        base_attrs = self.attributes
        
        def get_attr(name):
            if hasattr(base_attrs, 'get_value'):
                return base_attrs.get_value(name, self)
            return getattr(base_attrs, name, 0)

        return {
            "internal_state": self.get_internal_state(),
            "tongling_info": self.get_tongling_info(),
            "damage_modifiers": self.get_damage_modifiers(),
            "effect_properties": self.get_all_effect_properties(),
            "base_attributes": {
                "hp": self.current_hp,
                "max_hp": self.max_hp,
                "energy": self.energy,
                "max_energy": self.max_energy,
                "attack": get_attr("attack"),
                "pdef": get_attr("pdef"),
                "mdef": get_attr("mdef"),
                "speed": get_attr("speed"),
                "hit_rate": get_attr("hit_rate"),
                "dodge_rate": get_attr("dodge_rate"),
                "crit_rate": get_attr("crit_rate"),
                "block_rate": get_attr("block_rate"),
            },
            "metadata": {
                "element": self.metadata.element.value if self.metadata.element else None,
                "professions": [p.value for p in self.metadata.professions],
                "tags": list(self.metadata.tags),
            },
            "effect_stats": self._unified_effect_manager.get_stats()
        }
        
    def _find_tongling_effect_name(self) -> Optional[str]:
        """查找通灵效果名称"""
        for effect in self._unified_effect_manager.effects.values():
            if "通灵" in effect.name or "星轨" in effect.name:
                return effect.name
        return None
    
    def _on_effect_added(self, effect, owner):
        """效果添加监听器"""
        spirit_logger.debug(f"{owner.name} 获得效果: {effect.name}")

    def _on_effect_removed(self, effect, owner):
        """效果移除监听器"""
        spirit_logger.debug(f"{owner.name} 失去效果: {effect.name}")

    def _on_effect_stacked(self, effect, owner):
        """效果叠加监听器"""
        spirit_logger.debug(f"{owner.name} 的效果 {effect.name} 叠加到 {effect.stacks} 层")

    def get_effect_manager_stats(self) -> Dict[str, Any]:
        """获取效果管理器统计信息"""
        return self._unified_effect_manager.get_stats()

    def clear_effect_cache(self):
        """清空效果缓存"""
        self._unified_effect_manager.clear_cache()

    def reset_effect_stats(self):
        """重置效果统计"""
        self._unified_effect_manager.reset_stats()

    def __repr__(self) -> str:
        """字符串表示"""
        return (f"Spirit({self.name}, HP: {self.current_hp}/{self.max_hp}, "
                f"Energy: {self.energy}, Pos: {self.position})")

    # === AI行动生成系统集成 ===

    def generate_actions(self, battle_state: 'IBattleState') -> List[Any]:
        """
        生成精灵行动 - 集成所有复杂逻辑

        这是战斗引擎期望的核心方法，负责：
        1. 检查精灵是否能够行动（控制效果、资源等）
        2. 选择合适的技能和目标
        3. 评估动态条件（如目标是否无法行动）
        4. 计算条件性效果（如御神英雄技的额外加成）
        5. 生成增强的攻击动作
        6. 发出相应的事件

        Args:
            battle_state: 当前战斗状态

        Returns:
            List[BattleAction]: 精灵要执行的动作列表
        """
        from core.ai import get_action_generator
        from core.action import DispatchEventAction
        from core.event.events import ActionStartEvent, ActionCompleteEvent

        try:
            # 获取全局行动生成器实例
            action_generator = get_action_generator()

            # 发出行动开始事件
            action_start_event = ActionStartEvent(actor=self, turn_number=getattr(battle_state, 'round_num', 1))
            start_actions = battle_state.dispatch_event(action_start_event) or []

            # 生成主要行动
            main_actions = action_generator.generate_actions(self, battle_state)

            # 发出行动完成事件
            action_complete_event = ActionCompleteEvent(actor=self)
            complete_actions = battle_state.dispatch_event(action_complete_event) or []

            # 合并所有行动
            all_actions = []
            all_actions.extend(start_actions)
            all_actions.extend(main_actions or [])
            all_actions.extend(complete_actions)

            return all_actions

        except Exception as e:
            # 错误处理：返回基础日志动作
            from core.action import LogAction
            from core.logging import get_logger

            logger = get_logger("spirit.action")
            logger.error(f"{self.name} 行动生成失败: {e}")

            return [
                LogAction(
                    caster=self,
                    message=f"{self.name} 行动生成失败，跳过回合"
                )
            ]

    def can_act(self, battle_state: 'IBattleState') -> bool:
        """
        检查精灵是否能够行动（简化版本）

        Args:
            battle_state: 当前战斗状态

        Returns:
            bool: 是否能够行动
        """
        try:
            from core.ai import get_action_generator

            action_generator = get_action_generator()
            capability = action_generator.capability_checker.can_act(self, battle_state)

            return capability.can_act

        except Exception:
            # 回退到基础检查
            return self.is_alive

    def get_action_capability_details(self, battle_state: 'IBattleState') -> Dict[str, Any]:
        """
        获取详细的行动能力信息（用于调试和UI显示）

        Args:
            battle_state: 当前战斗状态

        Returns:
            Dict[str, Any]: 详细的行动能力信息
        """
        try:
            from core.ai import get_action_generator

            action_generator = get_action_generator()

            # 获取行动能力检查结果
            capability = action_generator.capability_checker.can_act(self, battle_state)

            # 获取详细状态
            detailed_status = action_generator.capability_checker.get_detailed_status(self, battle_state)

            # 获取可用技能
            skill_result = action_generator.skill_selector.select_skill(self, battle_state)

            return {
                'can_act': capability.can_act,
                'reason': capability.reason,
                'blocked_by': capability.blocked_by,
                'additional_info': capability.additional_info,
                'detailed_status': {name: {
                    'can_act': result.can_act,
                    'reason': result.reason,
                    'blocked_by': result.blocked_by
                } for name, result in detailed_status.items()},
                'selected_skill': {
                    'name': getattr(skill_result.skill.metadata, 'name', 'None') if skill_result.skill else 'None',
                    'reason': skill_result.reason,
                    'priority_score': skill_result.priority_score
                } if skill_result else None
            }

        except Exception as e:
            return {
                'can_act': self.is_alive,
                'reason': f"检查失败: {e}",
                'error': True
            }

    def preview_action(self, battle_state: 'IBattleState') -> Dict[str, Any]:
        """
        预览精灵的行动（不实际执行）

        用于AI决策分析和UI预览

        Args:
            battle_state: 当前战斗状态

        Returns:
            Dict[str, Any]: 行动预览信息
        """
        try:
            from core.ai import get_action_generator

            action_generator = get_action_generator()

            # 检查行动能力
            capability = action_generator.capability_checker.can_act(self, battle_state)
            if not capability.can_act:
                return {
                    'can_act': False,
                    'reason': capability.reason,
                    'actions': []
                }

            # 选择技能
            skill_result = action_generator.skill_selector.select_skill(self, battle_state)
            if not skill_result.skill:
                return {
                    'can_act': True,
                    'reason': skill_result.reason,
                    'actions': []
                }

            # 选择目标
            target_result = action_generator.target_selector.select_targets(
                self, skill_result.skill, battle_state
            )

            # 预览每个目标的效果
            action_previews = []
            for target in target_result.targets:
                # 评估条件
                conditions = action_generator.condition_evaluator.evaluate_attack_conditions(
                    self, target, skill_result.skill, battle_state
                )

                # 计算效果
                effects = action_generator.effect_calculator.calculate_conditional_effects(
                    self, target, skill_result.skill, conditions, battle_state
                )

                action_previews.append({
                    'target_name': getattr(target, 'name', 'Unknown'),
                    'target_id': getattr(target, 'id', 'unknown'),
                    'conditions': conditions.conditions,
                    'effects': effects.effects,
                    'trigger_events': effects.trigger_events
                })

            return {
                'can_act': True,
                'skill_name': getattr(skill_result.skill.metadata, 'name', 'Unknown'),
                'skill_priority': skill_result.priority_score,
                'target_count': len(target_result.targets),
                'action_previews': action_previews
            }

        except Exception as e:
            return {
                'can_act': False,
                'reason': f"预览失败: {e}",
                'error': True
            }