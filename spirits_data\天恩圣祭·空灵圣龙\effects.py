"""
天恩圣祭·空灵圣龙 - 特殊效果

包含：
- InvisibilityEffect: 隐身效果
- ImmunityEffect: 免疫效果
"""

from __future__ import annotations
from typing import Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult


class InvisibilityEffect(IEffect):
    """
    隐身效果
    - 无法被敌方直接选择为攻击目标
    - 持续到条件改变
    """

    def __init__(self, duration: int = -1):
        super().__init__(
            effect_id=f"invisibility_{id(self)}",
            name="隐身",
            effect_type=EffectType.TEMPORARY,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=duration
        )

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        # 设置隐身标记，影响目标选择
        if hasattr(target, 'is_invisible'):
            target.is_invisible = True

        return EffectResult.success_with_data(
            {"applied": True, "invisible": True},
            f"{getattr(target, 'name', '精灵')} 进入隐身状态，无法被敌方选择为目标"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        # 移除隐身标记
        if hasattr(target, 'is_invisible'):
            target.is_invisible = False

        return EffectResult.success_with_data(
            {"removed": True, "invisible": False},
            f"{getattr(target, 'name', '精灵')} 失去隐身状态，可以被敌方选择为目标"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        # 确保隐身标记保持
        if hasattr(target, 'is_invisible'):
            target.is_invisible = True

        return EffectResult.success_with_data(
            {"updated": True, "invisible": True},
            "隐身效果更新"
        )

    def is_targetable_by_enemy(self, target, attacker) -> bool:
        """检查是否可以被敌方选择为目标"""
        # 如果攻击者和目标是敌对关系，且目标隐身，则不可选择
        if hasattr(target, 'team') and hasattr(attacker, 'team'):
            if target.team != attacker.team:  # 敌对关系
                return False  # 隐身状态下不可被敌方选择
        return True  # 友方或其他情况可以选择

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "隐身：无法被敌方直接选择为攻击目标",
            "duration": self.duration,
            "effect_type": "目标选择限制"
        }


class ImmunityEffect(IEffect):
    """
    免疫效果
    - 免疫下一次攻击
    - 持续1次攻击
    """
    
    def __init__(self, duration: int = 1):
        super().__init__(
            effect_id=f"immunity_{id(self)}",
            name="免疫",
            effect_type=EffectType.TEMPORARY,
            category=EffectCategory.SHIELD,
            priority=EffectPriority.HIGHEST,
            duration=duration
        )
        self.attacks_blocked = 0
        self.max_blocks = 1
    
    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True
    
    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        return EffectResult.success_with_data(
            {"applied": True}, 
            f"{getattr(target, 'name', '精灵')} 获得免疫效果"
        )
    
    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{getattr(target, 'name', '精灵')} 失去免疫效果"
        )
    
    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data(
            {"updated": True}, 
            "免疫效果更新"
        )
    
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"免疫：免疫下一次攻击 ({self.max_blocks - self.attacks_blocked}次剩余)",
            "duration": self.duration,
            "attacks_blocked": self.attacks_blocked,
            "max_blocks": self.max_blocks
        }


# 导出所有效果类
__all__ = [
    'InvisibilityEffect',
    'ImmunityEffect'
]
