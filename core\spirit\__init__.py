"""
Spirit 模块 (新架构)

此模块统一导出新一代的、基于组件的 Spirit 系统的核心组件。
"""
from __future__ import annotations

# 本地导入
from .spirit import Spirit, SpiritMetadata
from .spirit_service import SpiritService
from .json_loader import get_spirit_json_loader
from .json_factory import get_json_spirit_factory
from .spirit_service import (
    get_spirit_service,
    create_spirit,
    list_available_spirits,
    spirit_exists,
    get_spirit_info
)

__all__ = [
    'Spirit',
    'SpiritMetadata',
    'SpiritService',
    'get_spirit_json_loader',
    'get_json_spirit_factory',
    'get_spirit_service',
    'create_spirit',
    'list_available_spirits',
    'spirit_exists',
    'get_spirit_info',
]
