<template>
  <div class="analytics-card chart-card">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon><component :is="icon" /></el-icon>
        {{ title }}
      </h3>
    </div>
    <div :ref="refName" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

interface Props {
  title: string
  icon: string
  refName: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  mounted: [refName: string]
}>()

onMounted(() => {
  emit('mounted', props.refName)
})
</script>