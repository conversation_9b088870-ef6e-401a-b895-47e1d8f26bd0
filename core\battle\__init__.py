from __future__ import annotations

# 从新的模块化结构导入
# 注意：coordination 包已被移除
from .engines import RefactoredBattleEngine, BattleConditionChecker
from .utilities import calculate_formula_damage as calculate_damage
from .utilities import calculate_healing
from .models import BattleState

"""战斗系统核心模块 (重构后架构)

该包包含了战斗系统的所有核心组件，包括：
- `battle_engine`: 重构后的战斗引擎，支持新旧系统切换
- `refactored_coordination`: 新的协调器系统
- `deployment`: 部署管理和监控系统
- `model`: 定义了战斗数据的结构，如 `Battle` 和 `BattleState`
- `formula_damage_calculator`: 提供了用于计算伤害和治疗的函数
"""

# 核心战斗引擎

# 注意：重构协调器和部署系统已被移除

# 胜负判定策略
from .conditions import (
    IBattleEndCondition,
    KnockoutCondition,
    CompositeCondition,
    RoundLimitCondition,
)

# 便捷的创建函数
from .engines import create_battle_engine

# 注意：create_battle_coordinator 函数已被移除，因为协调器包已删除
# 工厂

__all__ = [
    # Engine & Core
    'RefactoredBattleEngine',
    'BattleConditionChecker',
    'BattleState',

    # Battle end condition strategies
    'IBattleEndCondition',
    'KnockoutCondition',
    'CompositeCondition',
    'RoundLimitCondition',
    'create_battle_engine',

    # Calculators
    'calculate_damage',
    'calculate_healing',
]
