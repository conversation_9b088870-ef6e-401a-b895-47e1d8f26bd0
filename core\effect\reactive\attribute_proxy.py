"""
属性代理类

自动拦截属性访问和修改，实现透明的属性监控
"""
from __future__ import annotations
import weakref
from typing import Any, Dict, Set, Optional, Callable
from dataclasses import dataclass, field

from .attribute_watcher import AttributeWatcher, ChangeType
from ...logging import get_logger

logger = get_logger("core.effect.reactive.attribute_proxy")


@dataclass
class AttributeConfig:
    """属性配置"""
    name: str
    monitored: bool = True
    readonly: bool = False
    validator: Optional[Callable[[Any], bool]] = None
    transformer: Optional[Callable[[Any], Any]] = None
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    change_threshold: float = 0.0  # 最小变化阈值


class ReactiveAttributeProxy:
    """响应式属性代理
    
    拦截对象属性的访问和修改，自动触发监控和更新机制
    """
    
    def __init__(self, target: Any, attribute_watcher: AttributeWatcher = None):
        # 使用 object.__setattr__ 避免触发代理
        object.__setattr__(self, '_target', weakref.ref(target) if target else None)
        object.__setattr__(self, '_attribute_watcher', attribute_watcher or AttributeWatcher(target))
        object.__setattr__(self, '_attribute_configs', {})
        object.__setattr__(self, '_original_values', {})
        object.__setattr__(self, '_monitoring_enabled', True)
        object.__setattr__(self, '_in_update', False)  # 防止递归更新
        
        # 初始化监控的属性
        self._setup_default_attributes()
    
    def _setup_default_attributes(self):
        """设置默认监控的属性"""
        default_attributes = [
            AttributeConfig("hp", min_value=0),
            AttributeConfig("max_hp", min_value=1),
            AttributeConfig("attack", min_value=0),
            AttributeConfig("defense", min_value=0),
            AttributeConfig("speed", min_value=0),
            AttributeConfig("energy", min_value=0),
            AttributeConfig("level", min_value=1, readonly=True),
        ]
        
        for config in default_attributes:
            self.add_attribute_config(config)
    
    def add_attribute_config(self, config: AttributeConfig):
        """添加属性配置"""
        self._attribute_configs[config.name] = config
        
        # 记录初始值
        target = self._target() if self._target else None
        if target and hasattr(target, config.name):
            self._original_values[config.name] = getattr(target, config.name)
    
    def enable_monitoring(self, enabled: bool = True):
        """启用/禁用监控"""
        object.__setattr__(self, '_monitoring_enabled', enabled)
    
    def __getattribute__(self, name: str) -> Any:
        # 内部属性直接返回
        if name.startswith('_') or name in ['add_attribute_config', 'enable_monitoring']:
            return object.__getattribute__(self, name)
        
        # 获取目标对象
        target = object.__getattribute__(self, '_target')
        target_obj = target() if target else None
        
        if target_obj is None:
            raise AttributeError(f"目标对象已被回收")
        
        # 如果目标对象有该属性，返回目标对象的属性
        if hasattr(target_obj, name):
            value = getattr(target_obj, name)
            
            # 记录属性访问（可选）
            self._on_attribute_accessed(name, value)
            
            return value
        
        # 否则返回代理对象的属性
        return object.__getattribute__(self, name)
    
    def __setattr__(self, name: str, value: Any) -> None:
        # 内部属性直接设置
        if name.startswith('_'):
            object.__setattr__(self, name, value)
            return
        
        # 获取目标对象
        target = self._target() if self._target else None
        if target is None:
            raise AttributeError(f"目标对象已被回收")
        
        # 检查是否在更新中（防止递归）
        if self._in_update:
            setattr(target, name, value)
            return
        
        # 获取属性配置
        config = self._attribute_configs.get(name)
        
        # 检查只读属性
        if config and config.readonly:
            raise AttributeError(f"属性 {name} 是只读的")
        
        # 获取旧值
        old_value = getattr(target, name, None)
        
        # 验证新值
        if config and config.validator:
            if not config.validator(value):
                raise ValueError(f"属性 {name} 的值 {value} 未通过验证")
        
        # 转换新值
        if config and config.transformer:
            value = config.transformer(value)
        
        # 应用范围限制
        if config:
            if config.min_value is not None and isinstance(value, (int, float)):
                value = max(value, config.min_value)
            if config.max_value is not None and isinstance(value, (int, float)):
                value = min(value, config.max_value)
        
        # 检查变化阈值
        if config and config.change_threshold > 0:
            if isinstance(old_value, (int, float)) and isinstance(value, (int, float)):
                if abs(value - old_value) < config.change_threshold:
                    return  # 变化太小，忽略
        
        # 设置新值
        object.__setattr__(self, '_in_update', True)
        try:
            setattr(target, name, value)
            
            # 触发监控
            if self._monitoring_enabled and config and config.monitored:
                self._on_attribute_changed(name, old_value, value)
        
        finally:
            object.__setattr__(self, '_in_update', False)
    
    def _on_attribute_accessed(self, name: str, value: Any):
        """属性访问回调"""
        # 可以在这里记录属性访问日志
        pass
    
    def _on_attribute_changed(self, name: str, old_value: Any, new_value: Any):
        """属性变化回调"""
        if not self._monitoring_enabled:
            return
        
        try:
            # 确定变化类型
            change_type = self._determine_change_type(old_value, new_value)
            
            # 通知属性监控器
            self._attribute_watcher.notify_change(
                name, old_value, new_value, change_type, source=self
            )
            
            logger.debug(f"属性变化通知: {name} = {old_value} -> {new_value} ({change_type.value})")
            
        except Exception as e:
            logger.error(f"处理属性变化失败 {name}: {e}")
    
    def _determine_change_type(self, old_value: Any, new_value: Any) -> ChangeType:
        """确定变化类型"""
        if isinstance(old_value, (int, float)) and isinstance(new_value, (int, float)):
            if new_value > old_value:
                return ChangeType.INCREMENT
            elif new_value < old_value:
                return ChangeType.DECREMENT
        
        return ChangeType.SET
    
    def get_attribute_config(self, name: str) -> Optional[AttributeConfig]:
        """获取属性配置"""
        return self._attribute_configs.get(name)
    
    def get_monitored_attributes(self) -> Set[str]:
        """获取所有监控的属性名"""
        return {name for name, config in self._attribute_configs.items() if config.monitored}
    
    def get_attribute_history(self, name: str, limit: int = 10):
        """获取属性变化历史"""
        return self._attribute_watcher.get_recent_changes(name, limit)
    
    def reset_attribute_history(self):
        """重置属性变化历史"""
        self._attribute_watcher.clear_history()


def make_reactive(obj: Any, attribute_watcher: AttributeWatcher = None) -> ReactiveAttributeProxy:
    """将对象转换为响应式对象
    
    Args:
        obj: 目标对象
        attribute_watcher: 可选的属性监控器
        
    Returns:
        ReactiveAttributeProxy: 响应式代理对象
    """
    return ReactiveAttributeProxy(obj, attribute_watcher)


def create_hp_validator(max_hp_attr: str = "max_hp"):
    """创建HP验证器
    
    Args:
        max_hp_attr: 最大HP属性名
        
    Returns:
        验证器函数
    """
    def validator(value):
        if not isinstance(value, (int, float)):
            return False
        return 0 <= value
    
    return validator


def create_percentage_validator():
    """创建百分比验证器（0-100）"""
    def validator(value):
        if not isinstance(value, (int, float)):
            return False
        return 0 <= value <= 100
    
    return validator


def create_positive_validator():
    """创建正数验证器"""
    def validator(value):
        if not isinstance(value, (int, float)):
            return False
        return value >= 0
    
    return validator
