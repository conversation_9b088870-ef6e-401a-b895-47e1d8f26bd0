"""
超杀技能配置系统

管理超杀技能的气势阈值和相关配置
"""
from __future__ import annotations
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..logging import get_logger

logger = get_logger("core.skill.ultimate_config")

# 系统常量
MAX_ENERGY = 300  # 所有精灵的最大气势


@dataclass
class UltimateSkillConfig:
    """超杀技能配置"""
    skill_id: str
    name: str
    energy_threshold: int
    description: str = ""
    cooldown: int = 0
    max_uses: int = -1  # -1表示无限制


class UltimateSkillManager:
    """超杀技能管理器"""
    
    def __init__(self, owner: Any):
        self.owner = owner
        self.ultimate_skills: Dict[str, UltimateSkillConfig] = {}
        self._default_threshold = MAX_ENERGY  # 默认需要满气势
    
    def add_ultimate_skill(self, config: UltimateSkillConfig) -> None:
        """添加超杀技能配置"""
        self.ultimate_skills[config.skill_id] = config
        logger.debug(f"为 {self.owner.name} 添加超杀技能: {config.name} (阈值: {config.energy_threshold})")
    
    def remove_ultimate_skill(self, skill_id: str) -> bool:
        """移除超杀技能配置"""
        if skill_id in self.ultimate_skills:
            config = self.ultimate_skills.pop(skill_id)
            logger.debug(f"为 {self.owner.name} 移除超杀技能: {config.name}")
            return True
        return False
    
    def get_ultimate_skill(self, skill_id: str) -> Optional[UltimateSkillConfig]:
        """获取超杀技能配置"""
        return self.ultimate_skills.get(skill_id)
    
    def get_all_ultimate_skills(self) -> List[UltimateSkillConfig]:
        """获取所有超杀技能配置"""
        return list(self.ultimate_skills.values())
    
    def can_use_ultimate(self, skill_id: str, current_energy: int) -> bool:
        """检查是否可以使用指定的超杀技能"""
        config = self.get_ultimate_skill(skill_id)
        if config is None:
            return False
        return current_energy >= config.energy_threshold
    
    def can_use_any_ultimate(self, current_energy: int) -> bool:
        """检查是否可以使用任何超杀技能"""
        if not self.ultimate_skills:
            # 如果没有配置超杀技能，使用默认阈值
            return current_energy >= self._default_threshold
        
        # 检查是否有任何超杀技能可以使用
        for config in self.ultimate_skills.values():
            if current_energy >= config.energy_threshold:
                return True
        return False
    
    def get_lowest_threshold(self) -> int:
        """获取最低的超杀阈值"""
        if not self.ultimate_skills:
            return self._default_threshold
        
        return min(config.energy_threshold for config in self.ultimate_skills.values())
    
    def get_highest_threshold(self) -> int:
        """获取最高的超杀阈值"""
        if not self.ultimate_skills:
            return self._default_threshold
        
        return max(config.energy_threshold for config in self.ultimate_skills.values())
    
    def get_usable_ultimates(self, current_energy: int) -> List[UltimateSkillConfig]:
        """获取当前气势下可以使用的超杀技能"""
        usable = []
        for config in self.ultimate_skills.values():
            if current_energy >= config.energy_threshold:
                usable.append(config)
        
        # 按阈值排序，阈值低的优先
        usable.sort(key=lambda x: x.energy_threshold)
        return usable
    
    def get_energy_progress_to_ultimate(self, skill_id: str, current_energy: int) -> float:
        """获取到指定超杀技能的气势进度"""
        config = self.get_ultimate_skill(skill_id)
        if config is None:
            return 0.0
        
        if config.energy_threshold <= 0:
            return 1.0
        
        return min(1.0, current_energy / config.energy_threshold)
    
    def get_next_ultimate_threshold(self, current_energy: int) -> Optional[int]:
        """获取下一个可达到的超杀阈值"""
        available_thresholds = [
            config.energy_threshold 
            for config in self.ultimate_skills.values() 
            if config.energy_threshold > current_energy
        ]
        
        if not available_thresholds:
            return None
        
        return min(available_thresholds)
    
    def load_from_config(self, ultimate_skills_config: List[Dict[str, Any]]) -> None:
        """从配置数据加载超杀技能"""
        for skill_data in ultimate_skills_config:
            config = UltimateSkillConfig(
                skill_id=skill_data.get('skill_id', ''),
                name=skill_data.get('name', ''),
                energy_threshold=skill_data.get('energy_threshold', MAX_ENERGY),
                description=skill_data.get('description', ''),
                cooldown=skill_data.get('cooldown', 0),
                max_uses=skill_data.get('max_uses', -1)
            )
            self.add_ultimate_skill(config)
    
    def to_dict(self) -> Dict[str, Any]:
        """导出为字典格式"""
        return {
            'ultimate_skills': [
                {
                    'skill_id': config.skill_id,
                    'name': config.name,
                    'energy_threshold': config.energy_threshold,
                    'description': config.description,
                    'cooldown': config.cooldown,
                    'max_uses': config.max_uses
                }
                for config in self.ultimate_skills.values()
            ]
        }
    
    def get_status_info(self, current_energy: int) -> Dict[str, Any]:
        """获取超杀系统状态信息"""
        usable_ultimates = self.get_usable_ultimates(current_energy)
        next_threshold = self.get_next_ultimate_threshold(current_energy)
        
        return {
            'current_energy': current_energy,
            'max_energy': MAX_ENERGY,
            'can_use_any': self.can_use_any_ultimate(current_energy),
            'usable_count': len(usable_ultimates),
            'usable_ultimates': [u.name for u in usable_ultimates],
            'next_threshold': next_threshold,
            'total_ultimates': len(self.ultimate_skills)
        }
    
    def __repr__(self) -> str:
        """字符串表示"""
        skill_count = len(self.ultimate_skills)
        if skill_count == 0:
            return f"UltimateSkillManager(无超杀技能)"
        
        thresholds = [str(config.energy_threshold) for config in self.ultimate_skills.values()]
        return f"UltimateSkillManager({skill_count}个超杀技能, 阈值: {', '.join(thresholds)})"


def create_ultimate_skill_config(
    skill_id: str,
    name: str,
    energy_threshold: int,
    description: str = "",
    cooldown: int = 0,
    max_uses: int = -1
) -> UltimateSkillConfig:
    """创建超杀技能配置的便捷函数"""
    return UltimateSkillConfig(
        skill_id=skill_id,
        name=name,
        energy_threshold=energy_threshold,
        description=description,
        cooldown=cooldown,
        max_uses=max_uses
    )


# 预定义的超杀技能配置
PREDEFINED_ULTIMATE_CONFIGS = {
    "chiyaowang_yushen": [
        create_ultimate_skill_config(
            skill_id="demon_king_ultimate",
            name="妖王真身",
            energy_threshold=250,
            description="释放妖王真身，造成巨额伤害"
        )
    ],
    "shenyao_xuwu_fuyao": [
        create_ultimate_skill_config(
            skill_id="void_annihilation",
            name="虚无湮灭",
            energy_threshold=300,
            description="释放虚无之力，湮灭一切"
        )
    ],
    "tianen_shengji_kongling_shenglong": [
        create_ultimate_skill_config(
            skill_id="dragon_blessing_ultimate",
            name="圣龙天恩",
            energy_threshold=200,
            description="释放圣龙之力，治愈并强化全队"
        )
    ],
    "shenyao_shengyu_nudi": [
        create_ultimate_skill_config(
            skill_id="imperial_judgment",
            name="帝王裁决",
            energy_threshold=280,
            description="以帝王之威，裁决敌人"
        )
    ]
}
