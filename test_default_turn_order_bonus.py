#!/usr/bin/env python3
"""
测试默认顺位加气功能

验证顺位加气作为默认逻辑是否正确工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_default_turn_order_bonus():
    """测试默认顺位加气功能"""
    print("🔧 测试默认顺位加气功能...")
    
    try:
        # 初始化核心系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎（默认包含顺位加气）
        from core.battle.engines.factory import create_battle_engine
        from core.formation import Formation
        
        # 创建空阵型
        formation1 = Formation()
        formation2 = Formation()
        
        # 使用默认参数创建引擎
        engine = create_battle_engine(formation1, formation2)
        
        print(f"✅ 战斗引擎创建成功")
        print(f"  - 顺位加气数量: {engine.turn_order_bonus_energy}")
        print(f"  - 策略类型: {engine.turn_order_strategy.__class__.__name__}")
        
        # 验证策略类型
        from core.turn_order import EnhancedTurnOrderStrategy
        if isinstance(engine.turn_order_strategy, EnhancedTurnOrderStrategy):
            print(f"✅ 默认使用增强策略（包含顺位加气）")
            print(f"  - 基础策略: {engine.turn_order_strategy.base_strategy.__class__.__name__}")
            print(f"  - 奖励数量: {engine.turn_order_strategy.bonus_manager.energy_bonus}")
        else:
            print(f"❌ 未使用增强策略: {engine.turn_order_strategy.__class__.__name__}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 默认顺位加气测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_bonus_energy():
    """测试自定义奖励数量"""
    print("\n🔧 测试自定义奖励数量...")
    
    try:
        # 初始化核心系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎（自定义奖励数量）
        from core.battle.engines.factory import create_battle_engine
        from core.formation import Formation
        
        # 创建空阵型
        formation1 = Formation()
        formation2 = Formation()
        
        # 使用自定义奖励数量创建引擎
        custom_energy = 75
        engine = create_battle_engine(
            formation1, 
            formation2,
            turn_order_bonus_energy=custom_energy
        )
        
        print(f"✅ 自定义奖励数量引擎创建成功")
        print(f"  - 顺位加气数量: {engine.turn_order_bonus_energy}")
        print(f"  - 策略类型: {engine.turn_order_strategy.__class__.__name__}")
        
        # 验证奖励数量
        from core.turn_order import EnhancedTurnOrderStrategy
        if isinstance(engine.turn_order_strategy, EnhancedTurnOrderStrategy):
            actual_bonus = engine.turn_order_strategy.bonus_manager.energy_bonus
            if actual_bonus == custom_energy:
                print(f"✅ 奖励数量设置正确: {actual_bonus}")
            else:
                print(f"❌ 奖励数量设置错误: 期望{custom_energy}, 实际{actual_bonus}")
                return False
        else:
            print(f"❌ 未使用增强策略")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义奖励数量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_strategy_with_bonus():
    """测试自定义策略与顺位加气"""
    print("\n🔧 测试自定义策略与顺位加气...")
    
    try:
        # 初始化核心系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建自定义策略
        from core.turn_order import FixedGridTurnOrderStrategy
        custom_strategy = FixedGridTurnOrderStrategy()
        
        # 创建战斗引擎
        from core.battle.engines.factory import create_battle_engine
        from core.formation import Formation
        
        # 创建空阵型
        formation1 = Formation()
        formation2 = Formation()
        
        # 使用自定义策略创建引擎
        engine = create_battle_engine(
            formation1, 
            formation2,
            turn_order_strategy=custom_strategy,
            turn_order_bonus_energy=60
        )
        
        print(f"✅ 自定义策略引擎创建成功")
        print(f"  - 策略类型: {engine.turn_order_strategy.__class__.__name__}")
        
        # 验证策略被正确包装
        from core.turn_order import EnhancedTurnOrderStrategy
        if isinstance(engine.turn_order_strategy, EnhancedTurnOrderStrategy):
            print(f"✅ 自定义策略被正确包装为增强策略")
            print(f"  - 基础策略: {engine.turn_order_strategy.base_strategy.__class__.__name__}")
            print(f"  - 奖励数量: {engine.turn_order_strategy.bonus_manager.energy_bonus}")
            
            # 验证基础策略是我们传入的策略
            if engine.turn_order_strategy.base_strategy is custom_strategy:
                print(f"  ✅ 基础策略是传入的自定义策略")
            else:
                print(f"  ⚠️ 基础策略不是传入的自定义策略")
        else:
            print(f"❌ 自定义策略未被包装")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_engine_creation():
    """测试直接创建引擎"""
    print("\n🔧 测试直接创建引擎...")
    
    try:
        # 初始化核心系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 直接创建引擎
        from core.battle.engines.battle_engine import RefactoredBattleEngine
        from core.formation import Formation
        
        # 创建空阵型
        formation1 = Formation()
        formation2 = Formation()
        
        # 直接创建引擎
        engine = RefactoredBattleEngine(
            formation1,
            formation2,
            turn_order_bonus_energy=80
        )
        
        print(f"✅ 直接创建引擎成功")
        print(f"  - 顺位加气数量: {engine.turn_order_bonus_energy}")
        print(f"  - 策略类型: {engine.turn_order_strategy.__class__.__name__}")
        
        # 验证策略
        from core.turn_order import EnhancedTurnOrderStrategy
        if isinstance(engine.turn_order_strategy, EnhancedTurnOrderStrategy):
            print(f"✅ 默认包装为增强策略")
            print(f"  - 奖励数量: {engine.turn_order_strategy.bonus_manager.energy_bonus}")
        else:
            print(f"❌ 未包装为增强策略")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 直接创建引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 默认顺位加气功能测试")
    print("="*60)
    
    tests = [
        ("默认顺位加气功能", test_default_turn_order_bonus),
        ("自定义奖励数量", test_custom_bonus_energy),
        ("自定义策略与顺位加气", test_custom_strategy_with_bonus),
        ("直接创建引擎", test_direct_engine_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 默认顺位加气功能验证:")
        print("  ✅ 顺位加气是默认逻辑，无需开关")
        print("  ✅ 所有策略自动包装为增强策略")
        print("  ✅ 奖励数量可以自定义配置")
        print("  ✅ 自定义策略正确包装")
        print("  ✅ 工厂函数和直接创建都支持")
        print("\n🚀 顺位加气已成为战斗引擎的默认行为！")
        print("\n💡 使用方法:")
        print("  - 默认创建: create_battle_engine(formation1, formation2)")
        print("  - 自定义奖励: turn_order_bonus_energy=数量")
        print("  - 自定义策略: turn_order_strategy=策略对象")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()
