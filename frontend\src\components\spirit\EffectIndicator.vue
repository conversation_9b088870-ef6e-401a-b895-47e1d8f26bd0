<template>
  <div 
    class="effect-indicator"
    :class="[
      `effect-${effect.category}`,
      `size-${size}`
    ]"
    :title="effectTooltip"
  >
    <!-- 效果图标 -->
    <el-icon class="effect-icon">
      <component :is="getEffectIcon(effect)" />
    </el-icon>

    <!-- 效果名称 -->
    <span class="effect-name" v-if="showName">{{ effect.name }}</span>

    <!-- 持续时间 -->
    <span class="effect-duration" v-if="showDuration && effect.remainingDuration > 0">
      {{ effect.remainingDuration }}
    </span>

    <!-- 叠加层数 -->
    <span class="effect-stacks" v-if="showStacks && effect.stackCount > 1">
      ×{{ effect.stackCount }}
    </span>

    <!-- 进度条 -->
    <div class="effect-progress" v-if="showProgress && effect.duration > 0">
      <div 
        class="progress-fill"
        :style="{ width: `${(effect.remainingDuration / effect.duration) * 100}%` }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Effect } from '@/types/battle'

interface Props {
  effect: Effect
  size?: 'small' | 'medium' | 'large'
  showName?: boolean
  showDuration?: boolean
  showStacks?: boolean
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  showName: true,
  showDuration: true,
  showStacks: true,
  showProgress: false
})

// 计算效果提示信息
const effectTooltip = computed(() => {
  const lines = [
    `${props.effect.name}`,
    `${props.effect.description}`,
    `类型: ${getEffectCategoryName(props.effect.category)}`
  ]

  if (props.effect.remainingDuration > 0) {
    lines.push(`剩余回合: ${props.effect.remainingDuration}`)
  }

  if (props.effect.stackCount > 1) {
    lines.push(`叠加层数: ${props.effect.stackCount}`)
  }

  return lines.join('\n')
})

// 获取效果类型名称
const getEffectCategoryName = (category: string) => {
  const nameMap: Record<string, string> = {
    buff: '增益',
    debuff: '减益',
    neutral: '中性'
  }
  return nameMap[category] || category
}

// 获取效果图标
const getEffectIcon = (effect: Effect) => {
  // 根据效果名称或类型返回对应图标
  const iconMap: Record<string, string> = {
    // 增益效果
    '攻击提升': 'Sword',
    '防御提升': 'Shield',
    '速度提升': 'Lightning',
    '治疗': 'Heart',
    '护盾': 'Protection',
    '免疫': 'CircleCheck',
    '隐身': 'Hide',
    '复活': 'Refresh',
    
    // 减益效果
    '中毒': 'Warning',
    '燃烧': 'Sunny',
    '冰冻': 'Snowflake',
    '眩晕': 'CircleClose',
    '沉默': 'Mute',
    '诅咒': 'MagicStick',
    '虚弱': 'ArrowDown',
    '嘲讽': 'ChatDotSquare',
    
    // 中性效果
    '标记': 'Flag',
    '契约': 'Connection',
    '变身': 'Refresh'
  }

  // 优先根据效果名称匹配
  if (iconMap[effect.name]) {
    return iconMap[effect.name]
  }

  // 根据类型返回默认图标
  switch (effect.category) {
    case 'buff':
      return 'ArrowUp'
    case 'debuff':
      return 'ArrowDown'
    default:
      return 'CircleDot'
  }
}
</script>

<style scoped lang="scss">
.effect-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
  font-weight: 600;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }

  // 尺寸变体
  &.size-small {
    padding: 2px 6px;
    font-size: 10px;
    
    .effect-icon {
      font-size: 12px;
    }
  }

  &.size-medium {
    padding: 4px 8px;
    font-size: 12px;
    
    .effect-icon {
      font-size: 14px;
    }
  }

  &.size-large {
    padding: 6px 12px;
    font-size: 14px;
    
    .effect-icon {
      font-size: 16px;
    }
  }

  // 效果类型样式
  &.effect-buff {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid #10b981;
  }

  &.effect-debuff {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid #ef4444;
  }

  &.effect-neutral {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid #3b82f6;
  }

  .effect-icon {
    flex-shrink: 0;
  }

  .effect-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
  }

  .effect-duration {
    font-size: 0.9em;
    opacity: 0.8;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    padding: 1px 4px;
    min-width: 16px;
    text-align: center;
  }

  .effect-stacks {
    font-size: 0.8em;
    opacity: 0.9;
    font-weight: bold;
  }

  .effect-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 6px 6px;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: currentColor;
      transition: width 0.3s ease;
    }
  }
}
</style>