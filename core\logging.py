"""
统一日志系统

提供项目范围内的标准化日志功能，替换散落在各处的print调试语句。
"""
from __future__ import annotations

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Optional
from pathlib import Path
import contextvars

# 1. 创建一个上下文变量来存储 trace_id
trace_id_var = contextvars.ContextVar('trace_id', default='main')

# 2. 创建一个自定义的日志过滤器来注入 trace_id
class TraceIdFilter(logging.Filter):
    def filter(self, record):
        record.trace_id = trace_id_var.get()
        return True

class GameLogger:
    _loggers: Dict[str, logging.Logger] = {}
    _configured = False
    _log_file: Optional[Path] = None

    @classmethod
    def setup_logging(
        cls,
        level: str = "INFO",
        log_file: Optional[str] = "logs/battle.log",
        max_bytes: int = 10 * 1024 * 1024,
        backup_count: int = 5,
    ):
        if cls._configured:
            return

        log_level = getattr(logging, level.upper(), logging.INFO)
        
        # 3. 更新日志格式以包含 trace_id
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - [%(trace_id)s] - %(levelname)s - %(message)s'
        )

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        # 4. 为处理器添加过滤器
        console_handler.addFilter(TraceIdFilter())

        # 获取根 logger 并应用配置
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        root_logger.handlers.clear()
        root_logger.addHandler(console_handler)

        # 文件处理器
        if log_file:
            cls._log_file = Path(log_file)
            cls._log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = RotatingFileHandler(
                cls._log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding="utf-8",
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(log_level)
            # 4. 为处理器添加过滤器
            file_handler.addFilter(TraceIdFilter())
            root_logger.addHandler(file_handler)

        cls._configured = True
        root_logger.info(f"游戏日志系统已配置。日志级别: {level}, 日志文件: {cls._log_file}")

    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        if not cls._configured:
            cls.setup_logging()

        if name not in cls._loggers:
            logger = logging.getLogger(name)
            # 过滤器在根 logger 上设置，这里不需要重复添加
            cls._loggers[name] = logger
        return cls._loggers[name]

# 便利函数
def get_logger(name: str):
    """获取指定名称的日志记录器"""
    return GameLogger.get_logger(name)

# 预定义的 logger
spirit_logger = GameLogger.get_logger("core.spirit")
battle_logger = GameLogger.get_logger("core.battle")
effect_logger = GameLogger.get_logger("core.effect")
skill_logger = GameLogger.get_logger("core.skill")
event_logger = GameLogger.get_logger("core.event")
attribute_logger = GameLogger.get_logger("core.attribute")


__all__ = [
    'GameLogger', 
    'get_logger',
    'battle_logger',
    'spirit_logger',
    'skill_logger',
    'effect_logger',
    'event_logger',
    'attribute_logger'
]