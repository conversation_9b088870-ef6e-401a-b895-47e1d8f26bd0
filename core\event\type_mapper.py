"""
事件类型映射器

统一 GameEvent 类和 EventType 枚举之间的映射关系
"""
from typing import Dict, Type, Optional
from .events import GameEvent, RoundStartEvent, RoundEndEvent, BattleStartEvent, BattleEndEvent
from ..effect.triggers import EventType

class EventTypeMapper:
    """事件类型映射器，统一事件类和枚举的对应关系"""
    
    # GameEvent 类到 EventType 枚举的映射
    _CLASS_TO_ENUM: Dict[Type[GameEvent], EventType] = {
        RoundStartEvent: EventType.ROUND_START,
        RoundEndEvent: EventType.ROUND_END,
        BattleStartEvent: EventType.BATTLE_START,
        BattleEndEvent: EventType.BATTLE_END,
        # 可以继续添加更多映射
    }
    
    # EventType 枚举到 GameEvent 类的反向映射
    _ENUM_TO_CLASS: Dict[EventType, Type[GameEvent]] = {
        v: k for k, v in _CLASS_TO_ENUM.items()
    }
    
    @classmethod
    def get_event_type(cls, event: GameEvent) -> Optional[EventType]:
        """
        从 GameEvent 实例获取对应的 EventType 枚举
        
        Args:
            event: GameEvent 实例
            
        Returns:
            对应的 EventType 枚举，如果没有映射则返回 None
        """
        event_class = type(event)
        return cls._CLASS_TO_ENUM.get(event_class)
    
    @classmethod
    def get_event_class(cls, event_type: EventType) -> Optional[Type[GameEvent]]:
        """
        从 EventType 枚举获取对应的 GameEvent 类
        
        Args:
            event_type: EventType 枚举
            
        Returns:
            对应的 GameEvent 类，如果没有映射则返回 None
        """
        return cls._ENUM_TO_CLASS.get(event_type)
    
    @classmethod
    def register_mapping(cls, event_class: Type[GameEvent], event_type: EventType):
        """
        注册新的事件类型映射
        
        Args:
            event_class: GameEvent 子类
            event_type: 对应的 EventType 枚举
        """
        cls._CLASS_TO_ENUM[event_class] = event_type
        cls._ENUM_TO_CLASS[event_type] = event_class
    
    @classmethod
    def is_compatible(cls, event: GameEvent, event_type: EventType) -> bool:
        """
        检查事件实例是否与指定的事件类型兼容
        
        Args:
            event: GameEvent 实例
            event_type: EventType 枚举
            
        Returns:
            是否兼容
        """
        mapped_type = cls.get_event_type(event)
        return mapped_type == event_type
    
    @classmethod
    def get_all_mappings(cls) -> Dict[str, str]:
        """
        获取所有映射关系（用于调试）
        
        Returns:
            映射关系字典
        """
        return {
            event_class.__name__: event_type.value
            for event_class, event_type in cls._CLASS_TO_ENUM.items()
        }

# 全局映射器实例
event_type_mapper = EventTypeMapper()

__all__ = ['EventTypeMapper', 'event_type_mapper']
