<template>
  <div class="w-full h-full flex items-center justify-center">
    <el-empty
      description="2D 战斗场景开发中…"
      image-size="120"
      class="opacity-80"
    />
  </div>
</template>

<script setup lang="ts">
/**
 * BattleField2D.vue 占位组件
 */
import { defineEmits } from 'vue'

defineProps<{
  battleState?: any
  selectedSpirit?: any
}>()

const emit = defineEmits<{
  (e: 'spirit-click', spirit: any): void
}>()
</script> 