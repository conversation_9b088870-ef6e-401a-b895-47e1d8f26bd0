"""
统一装饰器注册系统

整合了原有的装饰器式事件监听，提供统一的系统级组件注册机制。
支持处理器注册、组件注册等多种装饰器模式。
"""

from __future__ import annotations
from typing import Dict, List, Callable, Type, Any, Optional, DefaultDict, Union
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
import inspect

from .unified_manager import EventPriority, unified_event_manager


class RegistryType(Enum):
    """注册表类型"""
    HANDLER = "handler"
    COMPONENT = "component"
    EFFECT = "effect"
    SKILL = "skill"


@dataclass
class RegistryEntry:
    """注册表条目"""
    target: Any  # 注册的目标（函数、类等）
    registry_type: RegistryType
    key: Any  # 注册键（事件类型、组件名等）
    priority: int = 0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class UnifiedRegistry:
    """
    统一装饰器注册系统
    
    整合了原有的多种装饰器注册方式，提供统一的系统级组件注册机制。
    
    特性:
    - 类型安全的装饰器注册
    - 支持优先级控制
    - 统一的注册表管理
    - 自动发现和注册
    - 元数据支持
    """
    
    def __init__(self):
        # 分类注册表
        self._registries: Dict[RegistryType, Dict[Any, List[RegistryEntry]]] = {
            RegistryType.HANDLER: defaultdict(list),
            RegistryType.COMPONENT: {},
            RegistryType.EFFECT: {},
            RegistryType.SKILL: {}
        }
        
        # 统计信息
        self._stats = {
            'handlers_registered': 0,
            'components_registered': 0,
            'effects_registered': 0,
            'skills_registered': 0
        }
    
    def register_handler(self, 
                        action_type: Type, 
                        priority: int = 0,
                        event_priority: EventPriority = EventPriority.NORMAL,
                        **metadata):
        """
        注册动作处理器装饰器
        
        Args:
            action_type: 动作类型
            priority: 处理器优先级
            event_priority: 事件优先级
            **metadata: 额外元数据
        """
        def decorator(func: Callable) -> Callable:
            # 创建注册条目
            entry = RegistryEntry(
                target=func,
                registry_type=RegistryType.HANDLER,
                key=action_type,
                priority=priority,
                metadata={
                    'event_priority': event_priority,
                    **metadata
                }
            )
            
            # 添加到注册表
            self._registries[RegistryType.HANDLER][action_type].append(entry)
            
            # 按优先级排序
            self._registries[RegistryType.HANDLER][action_type].sort(
                key=lambda e: e.priority
            )
            
            # 统计
            self._stats['handlers_registered'] += 1
            
            return func
        
        return decorator
    
    def register_component(self, 
                          component_name: str,
                          priority: int = 0,
                          **metadata):
        """
        注册组件装饰器
        
        Args:
            component_name: 组件名称
            priority: 组件优先级
            **metadata: 额外元数据
        """
        def decorator(cls: Type) -> Type:
            # 创建注册条目
            entry = RegistryEntry(
                target=cls,
                registry_type=RegistryType.COMPONENT,
                key=component_name,
                priority=priority,
                metadata=metadata
            )
            
            # 添加到注册表
            self._registries[RegistryType.COMPONENT][component_name] = entry
            
            # 统计
            self._stats['components_registered'] += 1
            
            return cls
        
        return decorator
    
    def register_effect(self, 
                       effect_name: str,
                       priority: int = 0,
                       **metadata):
        """
        注册效果装饰器
        
        Args:
            effect_name: 效果名称
            priority: 效果优先级
            **metadata: 额外元数据
        """
        def decorator(cls: Type) -> Type:
            # 创建注册条目
            entry = RegistryEntry(
                target=cls,
                registry_type=RegistryType.EFFECT,
                key=effect_name,
                priority=priority,
                metadata=metadata
            )
            
            # 添加到注册表
            self._registries[RegistryType.EFFECT][effect_name] = entry
            
            # 统计
            self._stats['effects_registered'] += 1
            
            return cls
        
        return decorator
    
    def register_skill(self, 
                      skill_name: str,
                      priority: int = 0,
                      **metadata):
        """
        注册技能装饰器
        
        Args:
            skill_name: 技能名称
            priority: 技能优先级
            **metadata: 额外元数据
        """
        def decorator(cls: Type) -> Type:
            # 创建注册条目
            entry = RegistryEntry(
                target=cls,
                registry_type=RegistryType.SKILL,
                key=skill_name,
                priority=priority,
                metadata=metadata
            )
            
            # 添加到注册表
            self._registries[RegistryType.SKILL][skill_name] = entry
            
            # 统计
            self._stats['skills_registered'] += 1
            
            return cls
        
        return decorator
    
    def get_handlers(self, action_type: Type) -> List[RegistryEntry]:
        """获取指定动作类型的处理器"""
        return self._registries[RegistryType.HANDLER].get(action_type, [])
    
    def get_component(self, component_name: str) -> Optional[RegistryEntry]:
        """获取指定名称的组件"""
        return self._registries[RegistryType.COMPONENT].get(component_name)
    
    def get_effect(self, effect_name: str) -> Optional[RegistryEntry]:
        """获取指定名称的效果"""
        return self._registries[RegistryType.EFFECT].get(effect_name)
    
    def get_skill(self, skill_name: str) -> Optional[RegistryEntry]:
        """获取指定名称的技能"""
        return self._registries[RegistryType.SKILL].get(skill_name)
    
    def get_all_entries(self, registry_type: RegistryType) -> Dict[Any, Any]:
        """获取指定类型的所有注册条目"""
        return dict(self._registries[registry_type])
    
    def clear_registry(self, registry_type: Optional[RegistryType] = None):
        """清理注册表"""
        if registry_type is None:
            # 清理所有注册表
            for reg_type in RegistryType:
                if reg_type == RegistryType.HANDLER:
                    self._registries[reg_type] = defaultdict(list)
                else:
                    self._registries[reg_type] = {}
            
            # 重置统计
            self._stats = {key: 0 for key in self._stats}
        else:
            # 清理指定类型的注册表
            if registry_type == RegistryType.HANDLER:
                self._registries[registry_type] = defaultdict(list)
            else:
                self._registries[registry_type] = {}
            
            # 更新统计
            stat_key = f"{registry_type.value}s_registered"
            if stat_key in self._stats:
                self._stats[stat_key] = 0
    
    def get_stats(self) -> Dict[str, int]:
        """获取注册统计信息"""
        return self._stats.copy()
    
    def auto_discover_and_register(self, module_path: str):
        """
        自动发现和注册模块中的组件
        
        Args:
            module_path: 模块路径
        """
        import importlib
        
        try:
            module = importlib.import_module(module_path)
            
            # 扫描模块中的类和函数
            for name in dir(module):
                obj = getattr(module, name)
                
                # 检查是否有注册标记
                if hasattr(obj, '_registry_info'):
                    registry_info = obj._registry_info
                    
                    # 根据类型进行注册
                    if registry_info['type'] == 'handler':
                        self._registries[RegistryType.HANDLER][registry_info['key']].append(
                            RegistryEntry(
                                target=obj,
                                registry_type=RegistryType.HANDLER,
                                key=registry_info['key'],
                                priority=registry_info.get('priority', 0),
                                metadata=registry_info.get('metadata', {})
                            )
                        )
                    # 其他类型的注册...
                    
        except ImportError as e:
            print(f"⚠️ 自动发现模块 {module_path} 失败: {e}")


# 全局统一注册表实例
unified_registry = UnifiedRegistry()

# 便捷装饰器别名
handler = unified_registry.register_handler
component = unified_registry.register_component
effect = unified_registry.register_effect
skill = unified_registry.register_skill
