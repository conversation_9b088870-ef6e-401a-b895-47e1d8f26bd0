from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Set, List, Optional, TYPE_CHECKING

# 本地导入
if TYPE_CHECKING:
    from ..interfaces import IBattleEntity, IBattleState

"""
状态系统模块

定义游戏中的各种状态类型和状态检查器
"""

class ActionState(Enum):
    """行动状态枚举"""
    NORMAL = auto()          # 正常状态，可以行动
    STUNNED = auto()         # 眩晕，无法行动
    FROZEN = auto()          # 冰冻，无法行动
    PARALYZED = auto()       # 麻痹，无法行动
    SLEEPING = auto()        # 睡眠，无法行动
    CHARMED = auto()         # 魅惑，无法行动
    FEARED = auto()          # 恐惧，无法行动
    SILENCED = auto()        # 沉默，无法使用技能
    DISABLED = auto()        # 禁用，无法行动
    DEAD = auto()            # 死亡，无法行动

class StatusType(Enum):
    """状态类型枚举"""
    CONTROL = "control"      # 控制类状态
    DEBUFF = "debuff"        # 减益状态
    BUFF = "buff"           # 增益状态
    SPECIAL = "special"      # 特殊状态

# 定义无法行动的状态集合
UNABLE_TO_ACT_STATES: Set[ActionState] = {
    ActionState.STUNNED,
    ActionState.FROZEN,
    ActionState.PARALYZED,
    ActionState.SLEEPING,
    ActionState.CHARMED,
    ActionState.FEARED,
    ActionState.DISABLED,
    ActionState.DEAD
}

# 定义无法使用技能的状态集合
UNABLE_TO_CAST_STATES: Set[ActionState] = {
    ActionState.SILENCED,
    ActionState.STUNNED,
    ActionState.FROZEN,
    ActionState.PARALYZED,
    ActionState.SLEEPING,
    ActionState.CHARMED,
    ActionState.FEARED,
    ActionState.DISABLED,
    ActionState.DEAD
}

class IStatusChecker(ABC):
    """状态检查器接口"""
    
    @abstractmethod
    def can_act(self, entity: "IBattleEntity") -> bool:
        """检查实体是否可以行动"""
        pass
    
    @abstractmethod
    def can_cast_skill(self, entity: "IBattleEntity") -> bool:
        """检查实体是否可以使用技能"""
        pass
    
    @abstractmethod
    def get_action_state(self, entity: "IBattleEntity") -> ActionState:
        """获取实体的行动状态"""
        pass
    
    @abstractmethod
    def is_unable_to_act(self, entity: "IBattleEntity") -> bool:
        """检查实体是否无法行动"""
        pass

class StatusChecker(IStatusChecker):
    """默认状态检查器实现"""
    
    def can_act(self, entity: "IBattleEntity") -> bool:
        """检查实体是否可以行动"""
        if not entity.is_alive:
            return False
        
        action_state = self.get_action_state(entity)
        return action_state not in UNABLE_TO_ACT_STATES
    
    def can_cast_skill(self, entity: "IBattleEntity") -> bool:
        """检查实体是否可以使用技能"""
        if not entity.is_alive:
            return False
        
        action_state = self.get_action_state(entity)
        return action_state not in UNABLE_TO_CAST_STATES
    
    def get_action_state(self, entity: "IBattleEntity") -> ActionState:
        """获取实体的行动状态"""
        if not entity.is_alive:
            return ActionState.DEAD
        
        # 检查各种状态效果
        if hasattr(entity, 'effects'):
            for effect in entity.effects:
                if hasattr(effect, 'name'):
                    effect_name = effect.name.lower()
                    if '眩晕' in effect_name or 'stun' in effect_name:
                        return ActionState.STUNNED
                    elif '冰冻' in effect_name or 'frozen' in effect_name:
                        return ActionState.FROZEN
                    elif '麻痹' in effect_name or 'paralyz' in effect_name:
                        return ActionState.PARALYZED
                    elif '睡眠' in effect_name or 'sleep' in effect_name:
                        return ActionState.SLEEPING
                    elif '魅惑' in effect_name or 'charm' in effect_name:
                        return ActionState.CHARMED
                    elif '恐惧' in effect_name or 'fear' in effect_name:
                        return ActionState.FEARED
                    elif '沉默' in effect_name or 'silence' in effect_name:
                        return ActionState.SILENCED
                    elif '禁用' in effect_name or 'disable' in effect_name:
                        return ActionState.DISABLED
        
        return ActionState.NORMAL
    
    def is_unable_to_act(self, entity: "IBattleEntity") -> bool:
        """检查实体是否无法行动"""
        return not self.can_act(entity)

class BattleStatusChecker:
    """战斗状态检查器 - 提供战斗相关的状态检查功能"""
    
    def __init__(self, status_checker: Optional[IStatusChecker] = None):
        self.status_checker = status_checker or StatusChecker()
    
    def count_unable_to_act_spirits(self, battle_state: "IBattleState", team: int) -> int:
        """统计指定队伍中无法行动的精灵数量"""
        count = 0
        spirits = battle_state.get_living_spirits(team)
        
        for spirit in spirits:
            if self.status_checker.is_unable_to_act(spirit):
                count += 1
        
        return count
    
    def get_unable_to_act_spirits(self, battle_state: "IBattleState", team: int) -> List["IBattleEntity"]:
        """获取指定队伍中无法行动的精灵列表"""
        unable_spirits = []
        spirits = battle_state.get_living_spirits(team)
        
        for spirit in spirits:
            if self.status_checker.is_unable_to_act(spirit):
                unable_spirits.append(spirit)
        
        return unable_spirits
    
    def get_able_to_act_spirits(self, battle_state: "IBattleState", team: int) -> List["IBattleEntity"]:
        """获取指定队伍中可以行动的精灵列表"""
        able_spirits = []
        spirits = battle_state.get_living_spirits(team)
        
        for spirit in spirits:
            if self.status_checker.can_act(spirit):
                able_spirits.append(spirit)
        
        return able_spirits
    
    def get_spirits_by_profession(self, battle_state: "IBattleState", team: int, profession: str) -> List["IBattleEntity"]:
        """获取指定队伍中指定职业的精灵列表"""
        profession_spirits = []
        spirits = battle_state.get_living_spirits(team)
        
        for spirit in spirits:
            if hasattr(spirit, 'metadata') and hasattr(spirit.metadata, 'professions'):
                # 检查精灵是否有指定职业
                for prof in spirit.metadata.professions:
                    if prof.value == profession or prof.name == profession:
                        profession_spirits.append(spirit)
                        break
        
        return profession_spirits
    
    def get_first_acting_hero_spirit(self, battle_state: "IBattleState", team: int) -> Optional["IBattleEntity"]:
        """获取指定队伍中最先出手的英雄职业精灵"""
        hero_spirits = self.get_spirits_by_profession(battle_state, team, "英雄")
        
        if not hero_spirits:
            return None
        
        # 按速度排序，返回最快的
        hero_spirits.sort(key=lambda s: s.attributes.speed, reverse=True)
        return hero_spirits[0]
    
    def get_highest_energy_spirit(self, battle_state: "IBattleState", team: int) -> Optional["IBattleEntity"]:
        """获取指定队伍中气势最高的精灵"""
        spirits = battle_state.get_living_spirits(team)
        
        if not spirits:
            return None
        
        # 按当前气势排序，返回最高的
        spirits.sort(key=lambda s: getattr(s, 'energy', 0), reverse=True)
        return spirits[0]

# 创建全局状态检查器实例
status_checker = StatusChecker()
battle_status_checker = BattleStatusChecker(status_checker)

__all__ = [
    'ActionState',
    'StatusType', 
    'UNABLE_TO_ACT_STATES',
    'UNABLE_TO_CAST_STATES',
    'IStatusChecker',
    'StatusChecker',
    'BattleStatusChecker',
    'status_checker',
    'battle_status_checker'
]
