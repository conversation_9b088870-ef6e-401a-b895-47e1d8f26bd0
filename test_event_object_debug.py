#!/usr/bin/env python3
"""
调试事件对象的传递
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_event_object_debug():
    """调试事件对象的传递"""
    print("🔧 调试事件对象的传递...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=10, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 等待虚无效果被应用
        print(f"\n📋 等待虚无效果被应用...")
        
        # 执行第一回合，让伏妖攻击并应用虚无效果
        result = engine.execute_next_spirit_turn()
        print(f"第一回合结果: {result.get('type', 'Unknown')}")
        
        # 检查虚无效果是否被应用
        target_effects = len(other_spirit.effect_manager.effects)
        print(f"目标效果数量: {target_effects}")
        
        xuwu_effect = None
        if target_effects > 0:
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"  - {effect_name}")
                if effect_name == "虚无状态":
                    xuwu_effect = effect
                    print(f"    ✅ 找到虚无效果: {effect_id}")
        
        if not xuwu_effect:
            print(f"❌ 虚无效果没有被应用，无法继续测试")
            return False
        
        # 手动测试事件对象的传递
        print(f"\n📋 手动测试事件对象的传递:")
        
        # 创建一个真实的BeforeAttackEvent
        from core.event.events import BeforeAttackEvent
        
        real_attack_event = BeforeAttackEvent(
            attacker=other_spirit,  # 虚无状态精灵
            target=fuyao_spirit,
            skill_name="测试攻击"
        )
        
        print(f"  创建真实的BeforeAttackEvent:")
        print(f"    事件类型: {type(real_attack_event).__name__}")
        print(f"    攻击者: {getattr(real_attack_event.attacker, 'name', 'Unknown')}")
        print(f"    目标: {getattr(real_attack_event.target, 'name', 'Unknown')}")
        print(f"    初始 attack_blocked: {real_attack_event.attack_blocked}")
        
        # 使用效果管理器的_extract_event_data方法
        from core.effect.triggers import EventType
        event_data = other_spirit.effect_manager._extract_event_data(real_attack_event, EventType.BEFORE_ATTACK)
        
        print(f"\n  提取的事件数据:")
        print(f"    事件数据键: {list(event_data.keys())}")
        
        # 检查原始事件对象
        original_event = event_data.get("event")
        if original_event:
            print(f"    原始事件对象: {type(original_event).__name__}")
            print(f"    原始事件 attack_blocked: {getattr(original_event, 'attack_blocked', 'Not found')}")
            print(f"    原始事件是否是同一个对象: {original_event is real_attack_event}")
        else:
            print(f"    ❌ 没有找到原始事件对象")
        
        # 调用虚无效果的处理方法
        print(f"\n  调用虚无效果的处理方法:")
        try:
            result = xuwu_effect.on_triggered(event_data, battle_state)
            print(f"    处理结果: {result.success if hasattr(result, 'success') else 'Unknown'}")
            print(f"    处理消息: {result.message if hasattr(result, 'message') else 'No message'}")
            
            # 检查原始事件对象是否被修改
            if original_event:
                print(f"    处理后原始事件 attack_blocked: {getattr(original_event, 'attack_blocked', 'Not found')}")
                print(f"    处理后真实事件 attack_blocked: {real_attack_event.attack_blocked}")
                
                if real_attack_event.attack_blocked:
                    print(f"    ✅ 原始事件对象被正确修改！")
                else:
                    print(f"    ❌ 原始事件对象没有被修改")
            
        except Exception as e:
            print(f"    ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试实际的事件分发
        print(f"\n📋 测试实际的事件分发:")
        
        # 重置事件状态
        real_attack_event.attack_blocked = False
        real_attack_event.source_effect = None
        
        print(f"  重置后事件 attack_blocked: {real_attack_event.attack_blocked}")
        
        # 通过统一事件管理器分发事件
        if hasattr(battle_state, 'unified_event_manager'):
            event_manager = battle_state.unified_event_manager
            print(f"  通过统一事件管理器分发事件...")
            
            try:
                actions = event_manager.dispatch(real_attack_event, battle_state)
                print(f"    分发成功，生成 {len(actions)} 个动作")
                
                # 检查事件是否被修改
                print(f"    分发后事件 attack_blocked: {real_attack_event.attack_blocked}")
                
                if real_attack_event.attack_blocked:
                    print(f"    ✅ 实际事件分发成功修改了事件对象！")
                    return True
                else:
                    print(f"    ❌ 实际事件分发没有修改事件对象")
                    return False
                    
            except Exception as e:
                print(f"    ❌ 事件分发失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"  ❌ 没有统一事件管理器")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 事件对象传递调试")
    print("="*60)
    
    result = test_event_object_debug()
    
    print("\n" + "="*60)
    if result:
        print("✅ 事件对象传递调试成功")
        print("虚无效果可以正确修改原始事件对象")
    else:
        print("❌ 事件对象传递调试失败")

if __name__ == "__main__":
    main()
