# 统一异常定义模块 - 核心功能已完善
"""
统一异常定义模块

定义了项目中使用的所有自定义异常，提供结构化的错误信息。
"""
from __future__ import annotations
from typing import Optional, Any, Dict
from enum import Enum


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = 1000
    INVALID_PARAMETER = 1001
    RESOURCE_NOT_FOUND = 1002
    PERMISSION_DENIED = 1003
    
    # 战斗系统错误 (2000-2999)
    BATTLE_NOT_INITIALIZED = 2000
    BATTLE_ALREADY_ENDED = 2001
    INVALID_BATTLE_STATE = 2002
    SPIRIT_NOT_FOUND = 2003
    INVALID_POSITION = 2004
    FORMATION_FULL = 2005
    ATTACK_IMMUNE = 2006
    
    # 技能系统错误 (3000-3999)
    SKILL_NOT_FOUND = 3000
    SKILL_ON_COOLDOWN = 3001
    INSUFFICIENT_ENERGY = 3002
    INVALID_TARGET = 3003
    SKILL_DISABLED = 3004
    
    # 效果系统错误 (4000-4999)
    EFFECT_NOT_FOUND = 4000
    EFFECT_STACK_LIMIT = 4001
    EFFECT_IMMUNE = 4002
    INVALID_EFFECT_DURATION = 4003
    
    # 数据访问错误 (5000-5999)
    DATABASE_ERROR = 5000
    DATA_CORRUPTION = 5001
    SERIALIZATION_ERROR = 5002
    DESERIALIZATION_ERROR = 5003


class GameException(Exception):
    """游戏异常基类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}
        self.message = message
    
    def __str__(self) -> str:
        context_str = f" (上下文: {self.context})" if self.context else ""
        return f"[{self.error_code.name}] {self.message}{context_str}"


class BattleException(GameException):
    """战斗系统异常"""
    pass


class SpiritNotFoundException(BattleException):
    """精灵未找到异常"""
    
    def __init__(self, spirit_id: str):
        super().__init__(
            f"精灵未找到: {spirit_id}",
            ErrorCode.SPIRIT_NOT_FOUND,
            {"spirit_id": spirit_id}
        )


class InvalidPositionException(BattleException):
    """无效位置异常"""
    
    def __init__(self, position: tuple, reason: str = ""):
        super().__init__(
            f"无效位置 {position}: {reason}",
            ErrorCode.INVALID_POSITION,
            {"position": position, "reason": reason}
        )


class FormationFullException(BattleException):
    """阵型已满异常"""
    
    def __init__(self, team_id: int):
        super().__init__(
            f"队伍 {team_id} 的阵型已满",
            ErrorCode.FORMATION_FULL,
            {"team_id": team_id}
        )


class AttackImmuneException(BattleException):
    """攻击免疫异常
    
    表示完全的攻击免疫，不仅阻止伤害，还阻止攻击的所有效果。
    """
    
    def __init__(self, target_name: str, source_name: str, reason: str = "Unknown"):
        super().__init__(
            f"'{target_name}'完全免疫了来自'{source_name}'的攻击 (原因: {reason})",
            ErrorCode.ATTACK_IMMUNE,
            {"target_name": target_name, "source_name": source_name, "reason": reason}
        )


class SkillException(GameException):
    """技能系统异常"""
    pass


class SkillNotFoundException(SkillException):
    """技能未找到异常"""
    
    def __init__(self, skill_name: str):
        super().__init__(
            f"技能未找到: {skill_name}",
            ErrorCode.SKILL_NOT_FOUND,
            {"skill_name": skill_name}
        )


class InsufficientEnergyException(SkillException):
    """能量不足异常"""
    
    def __init__(self, required: int, current: int):
        super().__init__(
            f"能量不足: 需要 {required}, 当前 {current}",
            ErrorCode.INSUFFICIENT_ENERGY,
            {"required": required, "current": current}
        )


class InvalidTargetException(SkillException):
    """无效目标异常"""
    
    def __init__(self, reason: str):
        super().__init__(
            f"无效目标: {reason}",
            ErrorCode.INVALID_TARGET,
            {"reason": reason}
        )


class EffectException(GameException):
    """效果系统异常基类"""
    pass


class EffectNotFoundException(EffectException):
    """效果未找到异常"""
    
    def __init__(self, effect_id: str):
        super().__init__(
            f"效果未找到: {effect_id}",
            ErrorCode.EFFECT_NOT_FOUND,
            {"effect_id": effect_id}
        )


class EffectStackLimitException(EffectException):
    """效果堆叠上限异常"""
    
    def __init__(self, effect_name: str, limit: int):
        super().__init__(
            f"效果 {effect_name} 已达到堆叠上限 {limit}",
            ErrorCode.EFFECT_STACK_LIMIT,
            {"effect_name": effect_name, "limit": limit}
        )


class EffectImmuneException(EffectException):
    """效果免疫异常"""
    
    def __init__(self, target_name: str, effect_name: str):
        super().__init__(
            f"{target_name} 对效果 {effect_name} 免疫",
            ErrorCode.EFFECT_IMMUNE,
            {"target_name": target_name, "effect_name": effect_name}
        )


class DataException(GameException):
    """数据访问异常"""
    pass


class DataCorruptionException(DataException):
    """数据损坏异常"""
    
    def __init__(self, data_type: str, details: str = ""):
        super().__init__(
            f"数据损坏: {data_type} - {details}",
            ErrorCode.DATA_CORRUPTION,
            {"data_type": data_type, "details": details}
        )


class SerializationException(DataException):
    """序列化异常"""
    
    def __init__(self, obj_type: str, reason: str):
        super().__init__(
            f"序列化失败: {obj_type} - {reason}",
            ErrorCode.SERIALIZATION_ERROR,
            {"obj_type": obj_type, "reason": reason}
        )


# 异常处理装饰器
def handle_game_exceptions(default_return=None, log_errors=True):
    """
    游戏异常处理装饰器
    
    Args:
        default_return: 发生异常时的默认返回值
        log_errors: 是否记录错误日志
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except GameException as e:
                if log_errors:
                    # 这里可以集成日志系统
                    logger.error(f"游戏异常: {e}")
                return default_return
            except Exception as e:
                if log_errors:
                    logger.error(f"未知异常: {e}")
                # 将未知异常包装为游戏异常
                raise GameException(f"未知错误: {str(e)}", ErrorCode.UNKNOWN_ERROR)
        return wrapper
    return decorator


__all__ = [
    'ErrorCode',
    'GameException',
    'BattleException',
    'SpiritNotFoundException',
    'InvalidPositionException', 
    'FormationFullException',
    'SkillException',
    'SkillNotFoundException',
    'InsufficientEnergyException',
    'InvalidTargetException',
    'AttackImmuneException',
    'EffectException',
    'EffectNotFoundException',
    'EffectStackLimitException',
    'EffectImmuneException',
    'DataException',
    'DataCorruptionException',
    'SerializationException',
    'handle_game_exceptions'
]