from __future__ import annotations
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Type, Union

# 第三方库导入
from unittest.mock import Mock, MagicMock
import copy
import random

# 本地导入
        from ..spirit import SpiritMetadata
from ..attribute import Attributes
from ..battle.model import BattleState
from ..components import SkillComponent
from ..effect import IEffect
from ..effect.system import IEffect as Effect  # type: ignore
from ..element import ElementType
from ..formation import Formation
from ..interfaces import IBattleEntity, IBattleState
from ..profession import ProfessionType
from ..skill.skills import Skill as Skill, SkillMetadata, SingleEnemySelector, DamageComponent
from ..skill.skills import SkillFactory as CoreSkillFactory
from ..spirit.spirit import Spirit as Spirit, LifeState

"""
测试支持框架

提供游戏系统测试所需的工具、模拟器和断言。
"""


# ------------------------------------------------------------
# Compatibility alias: external tests expect `Effect` base class
# ------------------------------------------------------------


@dataclass
class TestSpiritTemplate:
    """测试精灵模板"""
    name: str = "测试精灵"
    hp: float = 1000
    attack: float = 100
    pdef: float = 50
    mdef: float = 50
    speed: float = 100
    element: Optional[ElementType] = None
    profession: Optional[ProfessionType] = None
    position: tuple[int, int] = (1, 1)
    team: int = 0
    skills: List[str] = field(default_factory=list)
    tags: set[str] = field(default_factory=set)


class SpiritFactory:
    """精灵工厂，用于创建测试精灵"""
    
    @staticmethod
    def create_spirit(template: TestSpiritTemplate) -> Spirit:
        """
        根据模板创建精灵

        参数:
            template (TestSpiritTemplate): 精灵属性模板

        返回:
            Spirit: 创建的精灵对象

        说明:
            该方法根据传入的TestSpiritTemplate实例，构造Attributes对象，并创建Spirit实例。
            注意：Attributes的构造需要补全所有必需参数，防止遗漏。
        """
        # crossref: Attributes类定义（src/core/attribute.py），Spirit类定义（src/core/spirit.py）
        attributes = Attributes(
            base_hp=template.hp,
            base_attack=template.attack,
            base_pdef=template.pdef,
            base_mdef=template.mdef,
            base_speed=template.speed,
            # 以下为Attributes类的其他必需参数，若有默认值可省略，否则需补全
            hp_p=0,
            hp_flat=0,
            attack_p=0,
            attack_flat=0,
            pdef_p=0,
            pdef_flat=0,
            mdef_p=0,
            mdef_flat=0,
            base_hit_rate=0,
            base_dodge_rate=0,
            base_break_rate=0,
            base_block_rate=0,
            base_crit_rate=0,
            base_crit_res_rate=0
        )

        metadata = SpiritMetadata(
            element=template.element,
            professions=set([template.profession]) if template.profession else set(),
            tags=template.tags
        )
        spirit = Spirit(
            id=f"test_{template.name}_{random.randint(1000, 9999)}",
            name=template.name,
            attributes=attributes,
            position=template.position,
            team=template.team,
            metadata=metadata
        )
        
        return spirit
    
    @staticmethod
    def create_tank_spirit(team: int = 0, position: tuple[int, int] = (1, 1)) -> Spirit:
        """创建坦克型精灵"""
        template = TestSpiritTemplate(
            name="坦克精灵",
            hp=1500,  # 降低生命值
            attack=80,
            pdef=150,
            mdef=100,
            speed=50,
            profession=ProfessionType.TANK,
            position=position,
            team=team
        )
        spirit = SpiritFactory.create_spirit(template)
        # 为测试精灵添加基础攻击技能
        skill_comp = spirit.components.get_component(SkillComponent)
        if skill_comp:
            skill_comp.skills.append(CoreSkillFactory.create_basic_attack())
        return spirit
    
    @staticmethod
    def create_dps_spirit(team: int = 0, position: tuple[int, int] = (2, 1)) -> Spirit:
        """创建输出型精灵"""
        template = TestSpiritTemplate(
            name="输出精灵",
            hp=800,
            attack=300,  # 提高攻击力
            pdef=60,
            mdef=60,
            speed=120,
            profession=ProfessionType.MAGIC,
            position=position,
            team=team
        )
        spirit = SpiritFactory.create_spirit(template)
        # 为测试精灵添加基础攻击技能
        skill_comp = spirit.components.get_component(SkillComponent)
        if skill_comp:
            skill_comp.skills.append(CoreSkillFactory.create_basic_attack())
        return spirit
    
    @staticmethod
    def create_healer_spirit(team: int = 0, position: tuple[int, int] = (3, 1)) -> Spirit:
        """创建治疗型精灵"""
        template = TestSpiritTemplate(
            name="治疗精灵",
            hp=1200,
            attack=60,
            pdef=80,
            mdef=120,
            speed=90,
            profession=ProfessionType.HEALER,
            position=position,
            team=team
        )
        return SpiritFactory.create_spirit(template)


class FormationBuilder:
    """阵型构建器"""
    
    def __init__(self):
        self.formation = Formation()
    
    def add_spirit(self, spirit: Spirit, row: int, col: int) -> 'FormationBuilder':
        """添加精灵到指定位置"""
        spirit.position = (row, col)
        self.formation.add_spirit(spirit, row, col)
        return self
    
    def add_tank_front(self, team: int = 0) -> 'FormationBuilder':
        """在前排添加坦克"""
        tank = SpiritFactory.create_tank_spirit(team, (1, 2))
        self.formation.add_spirit(tank, 1, 2)
        return self
    
    def add_dps_mid(self, team: int = 0) -> 'FormationBuilder':
        """在中排添加输出"""
        dps = SpiritFactory.create_dps_spirit(team, (2, 2))
        self.formation.add_spirit(dps, 2, 2)
        return self
    
    def add_healer_back(self, team: int = 0) -> 'FormationBuilder':
        """在后排添加治疗"""
        healer = SpiritFactory.create_healer_spirit(team, (3, 2))
        self.formation.add_spirit(healer, 3, 2)
        return self
    
    def fill_random(self, team: int = 0, count: int = 9) -> 'FormationBuilder':
        """随机填充阵型"""
        positions = [(r, c) for r in range(1, 4) for c in range(1, 4)]
        random.shuffle(positions)
        
        for i in range(min(count, len(positions))):
            row, col = positions[i]
            if self.formation.get_spirit_at(row, col) is None:
                spirit_type = random.choice(['tank', 'dps', 'healer'])
                if spirit_type == 'tank':
                    spirit = SpiritFactory.create_tank_spirit(team, (row, col))
                elif spirit_type == 'dps':
                    spirit = SpiritFactory.create_dps_spirit(team, (row, col))
                else:
                    spirit = SpiritFactory.create_healer_spirit(team, (row, col))
                
                self.formation.add_spirit(spirit, row, col)
        
        return self
    
    def build(self) -> Formation:
        """构建阵型"""
        return self.formation


class MockSkill(Skill):
    """模拟技能"""
    
    def __init__(
        self,
        name: str = "测试技能",
        energy_cost: int = 20,
        damage: float = 100,
        effect_func: Optional[Callable] = None
    ):
        metadata = SkillMetadata(name=name, energy_cost=energy_cost)
        super().__init__(
            metadata=metadata,
            target_selector=SingleEnemySelector(),  # 假设导入 SingleEnemySelector
            components=[DamageComponent(base_damage=damage)]
        )
        self.effect_func = effect_func or self._default_effect
    
    def _default_effect(self, battle_state: IBattleState, targets: List[IBattleEntity]) -> List[Any]:
        """默认效果：造成伤害（通过组件处理）"""
        return []  # 假设默认通过组件处理
    
    def cast(self, battle_state: IBattleState, targets: List[IBattleEntity]) -> List[Any]:
        """释放技能"""
        return self.effect_func(battle_state, targets)


class MockEffect(Effect):
    """模拟效果"""
    
    def __init__(
        self,
        name: str = "测试效果",
        duration: int = 3,
        apply_func: Optional[Callable] = None,
        remove_func: Optional[Callable] = None,
        round_end_func: Optional[Callable] = None
    ):
        super().__init__(name=name, category="BUFF", duration=duration)  # 使用字符串代替 EffectCategory
        self.apply_func = apply_func or self._default_apply
        self.remove_func = remove_func or self._default_remove
        self.round_end_func = round_end_func or self._default_round_end
    
    def _default_apply(self, target: IBattleEntity, battle_state: IBattleState) -> List[Any]:
        """默认应用效果"""
        return []
    
    def _default_remove(self, target: IBattleEntity, battle_state: IBattleState) -> List[Any]:
        """默认移除效果"""
        return []
    
    def _default_round_end(self, target: IBattleEntity, battle_state: IBattleState) -> List[Any]:
        """默认回合结束效果"""
        return []
    
    def on_apply(self, target: IBattleEntity, battle_state: IBattleState) -> List[Any]:
        """应用效果时触发"""
        return self.apply_func(target, battle_state)
    
    def on_remove(self, target: IBattleEntity, battle_state: IBattleState) -> List[Any]:
        """移除效果时触发"""
        return self.remove_func(target, battle_state)
    
    def on_round_end(self, target: IBattleEntity, battle_state: IBattleState) -> List[Any]:
        """回合结束时触发"""
        return self.round_end_func(target, battle_state)


class BattleSimulator:
    """战斗模拟器"""
    
    def __init__(self):
        self.battle_state: Optional[BattleState] = None
        self.action_history: List[Any] = []
        self.round_history: List[Dict[str, Any]] = []
    
    def setup_battle(self, formation1: Formation, formation2: Formation) -> BattleState:
        """设置战斗"""
        self.battle_state = BattleState(formation1, formation2)
        self.action_history.clear()
        self.round_history.clear()
        return self.battle_state
    
    def simulate_round(self) -> Dict[str, Any]:
        """模拟一个回合"""
        if not self.battle_state:
            raise ValueError("战斗未初始化")
        
        round_info = {
            'round_num': self.battle_state.round_num + 1,
            'actions': [],
            'spirit_states': {}
        }
        
        # 记录回合开始时的精灵状态
        for spirit_id, spirit in self.battle_state.spirits.items():
            round_info['spirit_states'][spirit_id] = {
                'hp': spirit.current_hp,
                'energy': spirit.energy,
                'is_alive': spirit.is_alive
            }
        
        self.battle_state.round_num += 1
        self.round_history.append(round_info)
        
        return round_info
    
    def apply_damage(self, target_id: str, damage: float) -> None:
        """对目标造成伤害"""
        if not self.battle_state:
            raise ValueError("战斗未初始化")
        
        target = self.battle_state.get_spirit_by_id(target_id)
        if isinstance(target, Spirit):  # 确保 target 是 Spirit 实例
            target.current_hp = max(0, target.current_hp - damage)
            if target.current_hp <= 0:
                target.life_state = LifeState.DEAD
            
            self.action_history.append({
                'type': 'damage',
                'target': target_id,
                'damage': damage,
                'remaining_hp': target.current_hp
            })
    
    def apply_healing(self, target_id: str, healing: float) -> None:
        """对目标进行治疗"""
        if not self.battle_state:
            raise ValueError("战斗未初始化")
        
        target = self.battle_state.get_spirit_by_id(target_id)
        if target:
            old_hp = target.current_hp
            target.current_hp = min(target.max_hp, target.current_hp + healing)
            actual_healing = target.current_hp - old_hp
            
            self.action_history.append({
                'type': 'healing',
                'target': target_id,
                'healing': actual_healing,
                'current_hp': target.current_hp
            })
    
    def get_battle_summary(self) -> Dict[str, Any]:
        """获取战斗摘要"""
        if not self.battle_state:
            return {}
        
        living_team0 = len([s for s in self.battle_state.get_living_spirits(0)])
        living_team1 = len([s for s in self.battle_state.get_living_spirits(1)])
        
        return {
            'rounds': self.battle_state.round_num,
            'team0_living': living_team0,
            'team1_living': living_team1,
            'winner': 0 if living_team1 == 0 else (1 if living_team0 == 0 else None),
            'total_actions': len(self.action_history)
        }


class TestAssertions:
    """测试断言工具"""
    
    @staticmethod
    def assert_spirit_alive(spirit: Spirit, message: str = "") -> None:
        """断言精灵存活"""
        # TestAssertions.assert_spirit_alive(spirit)  # 注释掉未知属性
        # TestAssertions.assert_spirit_dead(spirit)
        assert spirit.is_alive, f"精灵 {spirit.name} 应该存活 {message}"
    
    @staticmethod
    def assert_spirit_dead(spirit: Spirit, message: str = "") -> None:
        """断言精灵死亡"""
        # TestAssertions.assert_spirit_alive(spirit)  # 注释掉未知属性
        # TestAssertions.assert_spirit_dead(spirit)
        assert not spirit.is_alive, f"精灵 {spirit.name} 应该死亡 {message}"
    
    @staticmethod
    def assert_hp_equals(spirit: Spirit, expected_hp: float, tolerance: float = 0.1, message: str = "") -> None:
        """断言生命值相等"""
        assert abs(spirit.current_hp - expected_hp) <= tolerance, \
            f"精灵 {spirit.name} 的生命值应该是 {expected_hp}，实际是 {spirit.current_hp} {message}"
    
    @staticmethod
    def assert_hp_greater_than(spirit: Spirit, min_hp: float, message: str = "") -> None:
        """断言生命值大于某值"""
        assert spirit.current_hp > min_hp, \
            f"精灵 {spirit.name} 的生命值 {spirit.current_hp} 应该大于 {min_hp} {message}"
    
    @staticmethod
    def assert_has_effect(spirit: Spirit, effect_name: str, message: str = "") -> None:
        """断言精灵有某个效果"""
        has_effect = any(effect.name == effect_name for effect in spirit.effects)
        assert has_effect, f"精灵 {spirit.name} 应该有效果 {effect_name} {message}"
    
    @staticmethod
    def assert_no_effect(spirit: Spirit, effect_name: str, message: str = "") -> None:
        """断言精灵没有某个效果"""
        has_effect = any(effect.name == effect_name for effect in spirit.effects)
        assert not has_effect, f"精灵 {spirit.name} 不应该有效果 {effect_name} {message}"
    
    @staticmethod
    def assert_energy_equals(spirit: Spirit, expected_energy: int, message: str = "") -> None:
        """断言能量值相等"""
        assert spirit.energy == expected_energy, \
            f"精灵 {spirit.name} 的能量应该是 {expected_energy}，实际是 {spirit.energy} {message}"


# 便捷函数
def create_test_battle() -> tuple[BattleState, BattleSimulator]:
    """创建测试战斗"""
    formation1 = (FormationBuilder()
                  .add_tank_front(0)
                  .add_dps_mid(0)
                  .add_healer_back(0)
                  .build())
    
    formation2 = (FormationBuilder()
                  .add_tank_front(1)
                  .add_dps_mid(1)
                  .add_healer_back(1)
                  .build())
    
    simulator = BattleSimulator()
    battle_state = simulator.setup_battle(formation1, formation2)
    
    return battle_state, simulator


def create_mock_battle_state() -> Mock:
    """创建模拟战斗状态"""
    mock_state = Mock(spec=IBattleState)
    mock_state.round_num = 1
    mock_state.winner = None
    mock_state.spirits = {}
    mock_state.get_spirit_by_id = Mock(return_value=None)
    mock_state.get_living_spirits = Mock(return_value=[])
    
    return mock_state


__all__ = [
    'TestSpiritTemplate',
    'SpiritFactory',
    'FormationBuilder',
    'MockSkill',
    'MockEffect',
    'BattleSimulator',
    'TestAssertions',
    'create_test_battle',
    'create_mock_battle_state'
]