#!/usr/bin/env python3
"""
测试JSON优化：超杀技能只需要energy_threshold，不需要energy_cost
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_json_optimization():
    """测试JSON优化效果"""
    print("🔧 测试JSON优化：超杀技能配置...")
    
    try:
        # 检查所有精灵的JSON文件
        spirits_json_dir = "spirits_json"
        json_files = [
            "天恩圣祭·空灵圣龙.json",
            "赤妖王·御神.json", 
            "神曜圣谕·女帝.json",
            "神曜虚无·伏妖.json"
        ]
        
        optimization_results = []
        
        for json_file in json_files:
            file_path = os.path.join(spirits_json_dir, json_file)
            if not os.path.exists(file_path):
                print(f"⚠️ 文件不存在: {file_path}")
                continue
                
            print(f"\n📋 检查文件: {json_file}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查技能配置
            skills = data.get("skills", [])
            ultimate_skills = [skill for skill in skills if skill.get("type") in ["ultimate", "tongling_ultimate"]]
            
            print(f"  找到 {len(ultimate_skills)} 个超杀技能")
            
            for skill in ultimate_skills:
                skill_name = skill.get("name", "Unknown")
                skill_type = skill.get("type", "unknown")
                has_energy_cost = "energy_cost" in skill
                has_energy_threshold = "energy_threshold" in skill
                energy_cost = skill.get("energy_cost", "N/A")
                energy_threshold = skill.get("energy_threshold", "N/A")
                
                print(f"    技能: {skill_name} ({skill_type})")
                print(f"      energy_cost: {energy_cost} (存在: {has_energy_cost})")
                print(f"      energy_threshold: {energy_threshold} (存在: {has_energy_threshold})")
                
                # 检查优化状态
                is_optimized = True
                issues = []
                
                if has_energy_cost and energy_cost != 0:
                    is_optimized = False
                    issues.append(f"仍有energy_cost={energy_cost}")
                
                if not has_energy_threshold:
                    is_optimized = False
                    issues.append("缺少energy_threshold")
                
                if is_optimized:
                    print(f"      ✅ 已优化")
                else:
                    print(f"      ❌ 需要优化: {', '.join(issues)}")
                
                optimization_results.append({
                    "file": json_file,
                    "skill": skill_name,
                    "optimized": is_optimized,
                    "issues": issues
                })
        
        # 测试超杀技能的实际工作情况
        print(f"\n🎯 测试超杀技能实际工作情况...")
        
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 设置超杀气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"🔥 为 {spirit1.name} 设置超杀气势: 300")
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=2,
            turn_order_bonus_energy=50
        )
        
        # 测试第一只精灵的回合
        result = engine.execute_next_spirit_turn()
        
        if result.get("type") == "spirit_turn":
            spirit_name = result.get("spirit_name", "Unknown")
            actions_count = result.get("actions_generated", 0)
            
            print(f"  精灵: {spirit_name}")
            print(f"  生成动作: {actions_count}")
            
            # 检查是否触发超杀
            if actions_count >= 3 and spirit_name == "天恩圣祭·空灵圣龙":
                print(f"  ✅ 超杀技能正常工作！{spirit_name} 触发了超杀技能")
                functional_test_passed = True
            elif spirit_name == "天恩圣祭·空灵圣龙":
                print(f"  ❌ 超杀技能异常：{spirit_name} 应该触发超杀但只生成了 {actions_count} 个动作")
                functional_test_passed = False
            else:
                print(f"  ℹ️ 其他精灵 {spirit_name} 正常行动")
                functional_test_passed = True
        else:
            print(f"  ❌ 精灵回合执行异常: {result}")
            functional_test_passed = False
        
        # 总结结果
        print(f"\n📊 优化结果总结:")
        optimized_count = sum(1 for r in optimization_results if r["optimized"])
        total_count = len(optimization_results)
        
        print(f"  已优化的超杀技能: {optimized_count}/{total_count}")
        
        if optimized_count < total_count:
            print(f"  ❌ 仍需优化的技能:")
            for result in optimization_results:
                if not result["optimized"]:
                    print(f"    {result['file']} - {result['skill']}: {', '.join(result['issues'])}")
        
        overall_success = (optimized_count == total_count) and functional_test_passed
        
        if overall_success:
            print(f"\n✅ JSON优化完成！")
            print(f"  📋 优化内容:")
            print(f"    1. 移除了超杀技能中多余的energy_cost字段")
            print(f"    2. 保留了energy_threshold用于阈值判断")
            print(f"    3. 超杀技能消耗全部气势的逻辑正常工作")
            print(f"  🎯 设计逻辑:")
            print(f"    - 超杀技能只需要energy_threshold判断是否可以释放")
            print(f"    - 释放时消耗所有当前气势，而不是固定的energy_cost")
            print(f"    - 这样避免了设计矛盾，逻辑更清晰")
        else:
            print(f"\n❌ JSON优化未完成或功能测试失败")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 JSON优化测试：超杀技能配置")
    print("="*60)
    
    result = test_json_optimization()
    
    print("\n" + "="*60)
    if result:
        print("✅ JSON优化验证成功")
        print("\n🎉 超杀技能配置已优化完成！")
        print("现在超杀技能的逻辑更加清晰：")
        print("  - 只需要energy_threshold判断是否可以释放")
        print("  - 释放时消耗所有当前气势")
        print("  - 没有设计矛盾")
    else:
        print("❌ JSON优化验证失败")

if __name__ == "__main__":
    main()
