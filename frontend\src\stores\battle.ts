import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Spirit, BattleState, BattleAction, BattleOutput, LogEntry } from '../types/battle'
import { battleAPI, spiritAPI } from '../api/battle' // Assuming spiritAPI is added to battle.ts or a new file

// 辅助函数，将后端的 Spirit 数据转换为前端需要的格式
// 注意：这只是一个示例，具体实现需要根据后端 Spirit 结构进行调整
const transformSpiritData = (backendSpirit: any, teamId: 1 | 2): Spirit => {
  return {
    id: backendSpirit.id,
    name: backendSpirit.name,
    // 后端坐标 1-3 转换回前端 0-2
    position: [backendSpirit.position[0] - 1, backendSpirit.position[1] - 1],
    team: teamId,
    isAlive: backendSpirit.life_state === 'ALIVE',
    attributes: {
      hp: backendSpirit.current_hp,
      maxHp: backendSpirit.max_hp,
      attack: backendSpirit.attributes.attack, // 假设嵌套
      defense: backendSpirit.attributes.pdef, // 假设嵌套
      speed: backendSpirit.attributes.speed,
      energy: backendSpirit.energy,
      maxEnergy: backendSpirit.max_energy,
    },
    skills: backendSpirit.skills,
    effects: backendSpirit.effects,
    level: backendSpirit.level || 1, // 假设
    element: backendSpirit.element,
    professions: backendSpirit.professions,
    tags: backendSpirit.tags,
    shengeLevel: backendSpirit.shenge_level,
    contractIds: backendSpirit.contract_ids || [],
  };
};

// 辅助函数，将后端的 formations 状态转换为前端的 team 数组
const transformFormationsToTeams = (finalState: any): { team1: Spirit[], team2: Spirit[] } => {
  const team1: Spirit[] = [];
  const team2: Spirit[] = [];

  if (finalState && finalState.formations) {
    // 假设队伍0是team1，队伍1是team2
    const formation0 = finalState.formations['0']?.grid;
    const formation1 = finalState.formations['1']?.grid;

    if (formation0) {
      for (const pos in formation0) {
        if (formation0[pos]) {
          team1.push(transformSpiritData(formation0[pos], 1));
        }
      }
    }
    if (formation1) {
      for (const pos in formation1) {
        if (formation1[pos]) {
          team2.push(transformSpiritData(formation1[pos], 2));
        }
      }
    }
  }

  return { team1, team2 };
};


export const useBattleStore = defineStore('battle', () => {
  // 战斗状态
  const isInBattle = ref(false)
  const battleWinner = ref<number | null>(null)
  const battleLog = ref<LogEntry[]>([])

  // 队伍配置
  const team1 = ref<Spirit[]>([])
  const team2 = ref<Spirit[]>([])
  
  // 计算属性
  const team1Alive = computed(() => team1.value.filter(s => s.isAlive))
  const team2Alive = computed(() => team2.value.filter(s => s.isAlive))

  // 方法
  const runBattle = async () => {
    if (team1.value.length === 0 || team2.value.length === 0) {
      throw new Error('双方队伍必须至少有一名精灵。')
    }
    
    isInBattle.value = true
    battleWinner.value = null
    battleLog.value = []

    try {
      // 1. 构建API输入
      const battleInput = {
        // 前端使用 0-2 坐标，后端要求 1-3，因此 +1
        team0: { spirits: team1.value.map(s => ({ spirit_name: s.name, position: [s.position[0] + 1, s.position[1] + 1] as [number, number] })) },
        team1: { spirits: team2.value.map(s => ({ spirit_name: s.name, position: [s.position[0] + 1, s.position[1] + 1] as [number, number] })) }
      }
      
      // 2. 调用API
      const result = await battleAPI.runBattle(battleInput)
      
      // 3. 更新Store状态
      battleWinner.value = result.winner
      battleLog.value = result.log
      
      // 使用最终状态更新精灵信息
      const { team1: finalTeam1, team2: finalTeam2 } = transformFormationsToTeams(result.final_state)
      team1.value = finalTeam1
      team2.value = finalTeam2

    } catch (error) {
      console.error('运行战斗失败:', error)
      throw error
    } finally {
      isInBattle.value = false // 战斗运行完毕
    }
  }
  
  const resetBattle = () => {
    isInBattle.value = false
    battleWinner.value = null
    battleLog.value = []
    // 保留队伍配置以便重新开始
  }

  // 队伍管理方法 (保持不变)
  const addSpiritToTeam = (spirit: Spirit, team: 1 | 2, position: [number, number]) => {
    const targetTeam = team === 1 ? team1.value : team2.value
    const spiritWithPosition = { ...spirit, position, team, id: spirit.name } // 使用名字作为临时ID
    
    const existingIndex = targetTeam.findIndex(s => 
      s.position[0] === position[0] && s.position[1] === position[1]
    )
    
    if (existingIndex >= 0) {
      targetTeam[existingIndex] = spiritWithPosition
    } else {
      targetTeam.push(spiritWithPosition)
    }
  }

  const removeSpiritFromTeam = (spiritId: string, team: 1 | 2) => {
    const targetTeam = team === 1 ? team1.value : team2.value
    // 因为我们可能使用名字作为ID，所以这里按名字查找
    const index = targetTeam.findIndex(s => s.id === spiritId || s.name === spiritId)
    if (index >= 0) {
      targetTeam.splice(index, 1)
    }
  }

  const moveSpiritToPosition = (spirit: Spirit, newPosition: [number, number]) => {
    const spiritTeam = spirit.team === 1 ? team1.value : team2.value
    const targetSpirit = spiritTeam.find(s => s.id === spirit.id || s.name === spirit.name)
    if (targetSpirit) {
      targetSpirit.position = newPosition
    }
  }

  return {
    isInBattle,
    battleWinner,
    battleLog,
    team1,
    team2,
    team1Alive,
    team2Alive,
    runBattle,
    resetBattle,
    addSpiritToTeam,
    removeSpiritFromTeam,
    moveSpiritToPosition,
  }
})