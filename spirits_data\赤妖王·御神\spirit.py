"""
赤妖王·御神 - 主精灵文件

赤妖王·御神 (Crimson Demon King - Yu Shen)
属性：火，职业：英雄、平衡

这是御神精灵的主要定义文件，整合了所有模块。
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.spirit.spirit import Spirit, SpiritMetadata
from core.attribute import Attributes
from core.element import ElementType
from core.profession import ProfessionType
from core.components import SkillComponent
from core.effect.system import IEffect

from .skills import create_yushen_skills
from .passive_effects import create_fox_spirit_power_effect


def create_yushen_spirit() -> Spirit:
    """创建赤妖王·御神精灵 - 优化版本，完全基于 JSON 配置"""
    from core.spirit.json_factory import SpiritJsonFactory

    # 使用统一的精灵工厂创建精灵
    factory = SpiritJsonFactory()
    spirit = factory.create_spirit("chiyaowang_yushen")

    if spirit is None:
        raise ValueError("无法创建赤妖王·御神精灵，请检查 JSON 配置")

    # 创建技能并添加到精灵的技能组件中
    skills = create_yushen_skills(spirit)
    skill_component = spirit.components.get_component(SkillComponent)
    if skill_component:
        for skill in skills:
            skill_component.add_skill(skill)

    return spirit


def create_yushen_spirit_legacy() -> Spirit:
    """创建赤妖王·御神精灵 - 旧版本（保留用于兼容性）"""

    # 从 JSON 文件加载属性配置
    from core.spirit.json_loader import load_spirit_attributes_from_json, load_spirit_metadata_from_json

    spirit_id = "chiyaowang_yushen"

    # 加载属性
    attributes = load_spirit_attributes_from_json(spirit_id)
    if attributes is None:
        # 如果 JSON 加载失败，使用默认属性
        attributes = Attributes(
            base_hp=2400, hp_p=0.0, hp_flat=0.0,
            base_attack=180, attack_p=0.0, attack_flat=0.0,
            base_pdef=160, pdef_p=0.0, pdef_flat=0.0,
            base_mdef=140, mdef_p=0.0, mdef_flat=0.0,
            base_speed=130, base_hit_rate=-0.05, base_dodge_rate=0.10,
            base_break_rate=0.20, base_block_rate=0.08, base_crit_rate=0.20,
            base_crit_res_rate=0.15, base_crit_damage=1.5,
            base_damage_reduction=0.10, base_penetration=0.0
        )

    # 加载元数据
    metadata_dict = load_spirit_metadata_from_json(spirit_id)
    if metadata_dict:
        element = metadata_dict['element']
        professions = metadata_dict['professions']
        tags = metadata_dict['tags']
        shenge_level = metadata_dict['shenge_level']
        position = metadata_dict['position']
    else:
        # 默认元数据
        element = ElementType.FIRE
        professions = {ProfessionType.HERO, ProfessionType.BALANCE}
        tags = {"DEMON_KING", "SURVIVAL", "CONTROL", "SUPPORT"}
        shenge_level = 6
        position = (2, 2)

    # 创建元数据
    metadata = SpiritMetadata(
        element=element,
        professions=professions,
        tags=tags,
        shenge_level=shenge_level
    )

    # 创建精灵实例
    spirit = Spirit(
        id=spirit_id,
        name=metadata_dict.get('name', "赤妖王·御神") if metadata_dict else "赤妖王·御神",
        attributes=attributes,
        position=position,
        team=0,
        skills=[],
        metadata=metadata
    )

    # 创建技能并添加到精灵的技能组件中
    skills = create_yushen_skills(spirit)
    skill_component = spirit.components.get_component(SkillComponent)
    if skill_component:
        for skill in skills:
            skill_component.add_skill(skill)

    return spirit


def create_yushen_passive_effects(spirit: Spirit) -> List[IEffect]:
    """创建御神所有被动效果列表，供自动注册"""
    return [
        create_fox_spirit_power_effect(spirit)
    ]


# 通用别名（保持向后兼容）
def create_chiyaowang_yushen_spirit() -> Spirit:
    """创建赤妖王·御神精灵（向后兼容别名）"""
    return create_yushen_spirit()


def create_passive_effects(spirit: Spirit) -> List[IEffect]:
    """通用被动效果创建函数（向后兼容别名）"""
    return create_yushen_passive_effects(spirit)


# 精灵数据配置 - 已迁移到 JSON 配置文件
# 原 SPIRIT_DATA 已被 spirits_json/赤妖王·御神.json 替代
# 如需访问精灵数据，请使用 JSON 配置系统：
# from core.spirit.json_loader import get_spirit_json_loader
# loader = get_spirit_json_loader()
# config = loader.load_spirit_config("chiyaowang_yushen")

def get_spirit_data_from_json():
    """从 JSON 配置获取精灵数据（替代硬编码的 SPIRIT_DATA）"""
    from core.spirit.json_loader import get_spirit_json_loader

    loader = get_spirit_json_loader()
    config = loader.load_spirit_config("chiyaowang_yushen")

    if config is None:
        return None

    return {
        "id": config.id,
        "name": config.name,
        "attributes": config.attributes,
        "metadata": {
            "element": config.element,
            "professions": config.professions,
            "tags": config.tags,
            "shenge_level": config.shenge_level
        },
        "skills": config.skills
    }


# 精灵信息
YUSHEN_INFO = {
    "name": "赤妖王·御神",
    "title": "Crimson Demon King - Yu Shen",
    "element": "火",
    "professions": ["英雄", "平衡"],
    "description": "拥有强大生存能力和控制辅助技能的火属性精灵，能够在战斗中发挥关键作用。",
    "characteristics": [
        "高攻击力和生命值",
        "复杂的条件生存机制",
        "强大的团队增益能力",
        "智能的目标切换逻辑",
        "基于敌方状态的动态减伤"
    ],
    "battle_role": "生存型控制辅助",
    "difficulty": "高"
}


# 导出函数和数据
__all__ = [
    'create_yushen_spirit',
    'create_yushen_passive_effects',
    'create_chiyaowang_yushen_spirit',  # 向后兼容
    'create_passive_effects',  # 向后兼容
    'get_spirit_data_from_json',  # 替代 SPIRIT_DATA
    'YUSHEN_INFO'
]
