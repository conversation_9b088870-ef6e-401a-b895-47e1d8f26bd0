"""
神曜虚无·伏妖 - 技能组件模块

包含伏妖技能使用的特殊组件：
- BurnTargetComponent: 烧伤目标组件
- XuWuStateComponent: 虚无状态施加组件
- HighestEnergyTargetComponent: 气势最高目标选择组件
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING, Any

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import SkillContext

from .effects import create_burn_effect, create_xuwu_state_effect


class BurnTargetComponent:
    """烧伤目标组件 - 对目标和气势最高的精灵造成烧伤"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: SkillContext
    ) -> List[Any]:
        from core.action import LogAction, ApplyEffectAction
        
        actions = []
        
        # 获取烧伤倍率
        burn_multiplier = context.additional_data.get("burn_multiplier", 0.8)
        additional_targets = context.additional_data.get("additional_targets", 1)  # 额外目标数量
        
        # 对主要目标造成烧伤
        for target in targets:
            if getattr(target, 'is_alive', False):
                burn_effect = create_burn_effect(caster, target, burn_multiplier)
                actions.append(ApplyEffectAction(
                    caster=caster,
                    target=target,
                    effect=burn_effect
                ))
        
        # 对气势最高的精灵造成烧伤
        highest_energy_targets = self._get_highest_energy_targets(
            battle_state, caster, additional_targets
        )
        
        for target in highest_energy_targets:
            if target not in targets:  # 避免重复烧伤
                burn_effect = create_burn_effect(caster, target, burn_multiplier)
                actions.append(ApplyEffectAction(
                    caster=caster,
                    target=target,
                    effect=burn_effect
                ))
        
        # 添加日志
        all_burned_targets = targets + [t for t in highest_energy_targets if t not in targets]
        if all_burned_targets:
            target_names = [getattr(t, 'name', '目标') for t in all_burned_targets]
            actions.append(LogAction(
                caster=caster,
                message=f"🔥 烧伤目标：{', '.join(target_names)}"
            ))
        
        return actions
    
    def _get_highest_energy_targets(self, battle_state: IBattleState, caster: IBattleEntity, count: int) -> List[IBattleEntity]:
        """获取气势最高的敌方精灵"""
        try:
            enemy_team = 1 if getattr(caster, 'team', 0) == 0 else 0
            
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                
                # 筛选存活的精灵
                alive_enemies = [s for s in enemy_spirits if getattr(s, 'is_alive', False)]
                
                # 按气势排序
                alive_enemies.sort(key=lambda s: getattr(s, 'energy', 0), reverse=True)
                
                return alive_enemies[:count]
        except:
            pass
        
        return []


class XuWuStateComponent:
    """虚无状态施加组件 - 令目标进入虚无状态"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: SkillContext
    ) -> List[Any]:
        from core.action import LogAction, ApplyEffectAction
        
        actions = []
        
        duration = context.additional_data.get("xuwu_duration", 2)
        
        for target in targets:
            if getattr(target, 'is_alive', False):
                xuwu_effect = create_xuwu_state_effect(caster, target, duration)
                actions.append(ApplyEffectAction(
                    caster=caster,
                    target=target,
                    effect=xuwu_effect
                ))
                
                actions.append(LogAction(
                    caster=caster,
                    message=f"🌀 {getattr(target, 'name', '目标')} 进入虚无状态！"
                ))
        
        return actions


class HighestEnergyTargetComponent:
    """气势最高目标选择组件 - 自动选择气势最高的目标"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: SkillContext
    ) -> List[Any]:
        # 这个组件主要用于目标选择，不产生直接的Action
        # 实际的目标选择逻辑应该在技能的目标选择器中实现
        return []
    
    def select_targets(self, battle_state: IBattleState, caster: IBattleEntity, count: int = 1) -> List[IBattleEntity]:
        """选择气势最高的目标"""
        try:
            enemy_team = 1 if getattr(caster, 'team', 0) == 0 else 0
            
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                
                # 筛选存活的精灵
                alive_enemies = [s for s in enemy_spirits if getattr(s, 'is_alive', False)]
                
                # 按气势排序
                alive_enemies.sort(key=lambda s: getattr(s, 'energy', 0), reverse=True)
                
                return alive_enemies[:count]
        except:
            pass
        
        return []


class TongLingProgressComponent:
    """通灵进度组件 - 为通灵技能增加进度"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: SkillContext
    ) -> List[Any]:
        from core.action import LogAction
        
        actions = []
        
        # 检查是否有唤灵协诛效果
        tongling_effect = self._get_tongling_effect(caster)
        if not tongling_effect:
            return actions
        
        progress_amount = context.additional_data.get("progress_amount", 5)
        reason = context.additional_data.get("progress_reason", "火系精灵出手")
        
        # 添加通灵进度
        triggered = tongling_effect.add_progress(progress_amount, reason)
        
        if triggered:
            actions.append(LogAction(
                caster=caster,
                message=f"🌟 唤灵协诛通灵触发！"
            ))
        else:
            current_progress = getattr(tongling_effect, 'tongling_progress', 0)
            max_progress = getattr(tongling_effect, 'max_progress', 100)
            actions.append(LogAction(
                caster=caster,
                message=f"⚡ 通灵进度 +{progress_amount} ({current_progress}/{max_progress})"
            ))
        
        return actions
    
    def _get_tongling_effect(self, caster: IBattleEntity):
        """获取通灵效果"""
        try:
            if hasattr(caster, 'get_effect'):
                return caster.get_effect("唤灵协诛")
            return None
        except:
            return None


class ConditionalXuWuComponent:
    """条件虚无组件 - 根据条件施加虚无状态"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: SkillContext
    ) -> List[Any]:
        from core.action import LogAction, ApplyEffectAction
        
        actions = []
        
        # 检查条件：目标是否无法行动
        for target in targets:
            if getattr(target, 'is_alive', False) and self._is_unable_to_act(target):
                xuwu_effect = create_xuwu_state_effect(caster, target, duration=2)
                actions.append(ApplyEffectAction(
                    caster=caster,
                    target=target,
                    effect=xuwu_effect
                ))
                
                actions.append(LogAction(
                    caster=caster,
                    message=f"🌀 {getattr(target, 'name', '目标')} 无法行动，额外进入虚无状态！"
                ))
        
        return actions
    
    def _is_unable_to_act(self, target: IBattleEntity) -> bool:
        """检查目标是否无法行动"""
        try:
            if hasattr(target, 'is_unable_to_act'):
                return target.is_unable_to_act()
            return False
        except:
            return False


class EnergyGainComponent:
    """气势获得组件 - 为精灵增加气势"""
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: SkillContext
    ) -> List[Any]:
        from core.action import LogAction
        
        actions = []
        
        energy_amount = context.additional_data.get("energy_amount", 150)
        
        # 为施法者增加气势 - 使用组件系统
        if hasattr(caster, 'gain_energy'):
            actual_gain = caster.gain_energy(energy_amount)

            actions.append(LogAction(
                caster=caster,
                message=f"⚡ {getattr(caster, 'name', '精灵')} 获得{actual_gain}点气势！（{caster.energy}/{caster.max_energy}）"
            ))
        
        return actions


# 导出所有组件类
__all__ = [
    'BurnTargetComponent',
    'XuWuStateComponent',
    'HighestEnergyTargetComponent',
    'TongLingProgressComponent',
    'ConditionalXuWuComponent',
    'EnergyGainComponent'
]
