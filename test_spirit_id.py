#!/usr/bin/env python3
"""
检查精灵ID
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_spirit_id():
    """检查精灵ID"""
    print("🔧 检查精灵ID...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        fuyao_spirit = None
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            print("❌ 未找到伏妖精灵")
            return False
        
        # 检查精灵ID和模块路径
        print(f"\n📋 精灵信息:")
        print(f"  精灵名称: {fuyao_spirit.name}")
        print(f"  精灵ID: {fuyao_spirit.id}")
        
        # 检查自动发现机制
        print(f"\n📋 自动发现机制:")
        
        # 模拟自动发现过程
        module_name = f"spirits_data.{fuyao_spirit.id}"
        print(f"  尝试加载模块: {module_name}")
        
        try:
            import importlib
            module = importlib.import_module(module_name)
            print(f"  ✅ 模块加载成功: {module}")
            
            # 查找被动效果创建函数
            function_names = [
                f"create_{fuyao_spirit.id}_passive_effects",
                "create_passive_effects",
                f"create_{fuyao_spirit.id.split('_')[-1]}_passive_effects",
            ]
            
            print(f"  查找被动效果创建函数:")
            for func_name in function_names:
                print(f"    尝试函数名: {func_name}")
                if hasattr(module, func_name):
                    func = getattr(module, func_name)
                    print(f"      ✅ 找到函数: {func}")
                    
                    # 尝试调用函数
                    try:
                        effects = func(fuyao_spirit)
                        print(f"      函数调用成功，返回 {len(effects)} 个效果:")
                        for effect in effects:
                            effect_name = getattr(effect, 'name', 'Unknown')
                            print(f"        - {effect_name}")
                        return True
                    except Exception as e:
                        print(f"      ❌ 函数调用失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"      ❌ 函数不存在")
            
            print(f"  ❌ 没有找到被动效果创建函数")
            return False
            
        except ImportError as e:
            print(f"  ❌ 模块加载失败: {e}")
            
            # 尝试其他可能的模块名
            print(f"\n  尝试其他可能的模块名:")
            possible_modules = [
                "spirits_data.神曜虚无·伏妖",
                "spirits_data.shen_yao_xu_wu_fu_yao",
            ]
            
            for module_name in possible_modules:
                print(f"    尝试模块: {module_name}")
                try:
                    module = importlib.import_module(module_name)
                    print(f"      ✅ 模块加载成功: {module}")
                    
                    # 查找被动效果创建函数
                    if hasattr(module, 'create_passive_effects'):
                        func = getattr(module, 'create_passive_effects')
                        print(f"      ✅ 找到create_passive_effects函数")
                        
                        try:
                            effects = func(fuyao_spirit)
                            print(f"      函数调用成功，返回 {len(effects)} 个效果:")
                            for effect in effects:
                                effect_name = getattr(effect, 'name', 'Unknown')
                                print(f"        - {effect_name}")
                            return True
                        except Exception as e:
                            print(f"      ❌ 函数调用失败: {e}")
                            import traceback
                            traceback.print_exc()
                    else:
                        print(f"      ❌ 没有create_passive_effects函数")
                        
                except ImportError:
                    print(f"      ❌ 模块不存在")
            
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 精灵ID检查")
    print("="*50)
    
    result = test_spirit_id()
    
    print("\n" + "="*50)
    if result:
        print("✅ 精灵ID检查成功")
        print("找到了被动效果创建函数")
    else:
        print("❌ 精灵ID检查失败")

if __name__ == "__main__":
    main()
