#!/usr/bin/env python3
"""
系统性检查效果系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_effects_systematic():
    """系统性检查效果系统"""
    print("🔧 系统性检查效果系统...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        # 创建伏妖精灵
        fuyao_spirit = None
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            print("❌ 未找到伏妖精灵")
            return False
        
        other_spirit = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 检查1: 精灵的效果管理器初始化
        print(f"\n📋 检查1: 精灵的效果管理器初始化")
        
        print(f"  伏妖精灵属性检查:")
        print(f"    - 有effect_manager属性: {hasattr(fuyao_spirit, 'effect_manager')}")
        print(f"    - 有_unified_effect_manager属性: {hasattr(fuyao_spirit, '_unified_effect_manager')}")
        
        if hasattr(fuyao_spirit, 'effect_manager') and fuyao_spirit.effect_manager:
            effect_manager = fuyao_spirit.effect_manager
            print(f"    - 效果管理器类型: {type(effect_manager).__name__}")
            print(f"    - 当前效果数量: {len(effect_manager.effects)}")
            print(f"    - 效果管理器owner: {getattr(effect_manager, 'owner', None)}")
            
            for effect_id, effect in effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"      - {effect_name} (ID: {effect_id})")
        else:
            print(f"    ❌ 效果管理器不存在或为None")
            return False
        
        # 检查2: 被动技能的效果创建
        print(f"\n📋 检查2: 被动技能的效果创建")
        
        # 检查被动技能
        passive_skills = []
        for skill in fuyao_spirit.skills:
            if hasattr(skill, 'metadata') and getattr(skill.metadata, 'cast_type', '') == 'PASSIVE':
                passive_skills.append(skill)
                print(f"  找到被动技能: {skill.metadata.name}")
        
        if not passive_skills:
            print(f"  ❌ 没有找到被动技能")
            return False
        
        # 测试被动技能的cast方法
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        for skill in passive_skills:
            print(f"\n  测试被动技能: {skill.metadata.name}")
            
            # 记录应用前的效果数量
            effects_before = len(fuyao_spirit.effect_manager.effects)
            print(f"    应用前效果数量: {effects_before}")
            
            try:
                # 调用被动技能的cast方法
                skill_actions = skill.cast(battle_state)
                print(f"    cast方法生成动作数量: {len(skill_actions) if skill_actions else 0}")
                
                # 执行动作
                if skill_actions:
                    # 🔧 修复：使用正确的动作执行器
                    from core.battle.execution import UnifiedActionExecutor
                    executor = UnifiedActionExecutor(battle_state, None, None)

                    try:
                        # 🔧 修复：使用正确的方法名
                        executor.execute_actions(skill_actions)
                        print(f"    动作执行完成")
                    except Exception as action_error:
                        print(f"    动作执行失败: {action_error}")
                        import traceback
                        traceback.print_exc()
                
                # 检查效果是否被添加
                effects_after = len(fuyao_spirit.effect_manager.effects)
                print(f"    应用后效果数量: {effects_after}")
                
                if effects_after > effects_before:
                    print(f"    ✅ 被动技能成功创建了 {effects_after - effects_before} 个效果")
                    
                    # 显示新增的效果
                    for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                        effect_name = getattr(effect, 'name', 'Unknown')
                        print(f"      - {effect_name}")
                else:
                    print(f"    ❌ 被动技能没有创建效果")
                
            except Exception as e:
                print(f"    ❌ 被动技能cast失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 检查3: 效果的事件订阅
        print(f"\n📋 检查3: 效果的事件订阅")
        
        # 检查统一事件管理器
        if hasattr(battle_state, 'unified_event_manager'):
            event_manager = battle_state.unified_event_manager
            print(f"  统一事件管理器: {type(event_manager).__name__}")
            
            # 检查订阅情况
            subscriptions = getattr(event_manager, '_subscriptions', {})
            print(f"  事件订阅数量: {sum(len(listeners) for listeners in subscriptions.values())}")
            
            for event_type, listeners in subscriptions.items():
                if listeners:
                    print(f"    {event_type}: {len(listeners)} 个监听器")
        else:
            print(f"  ❌ 战斗状态没有统一事件管理器")
        
        # 检查4: 虚无效果的具体实现
        print(f"\n📋 检查4: 虚无效果的具体实现")
        
        try:
            from spirits_data.神曜虚无·伏妖.effects import create_xuwu_state_effect
            
            # 创建测试虚无效果
            test_xuwu = create_xuwu_state_effect(fuyao_spirit, other_spirit, duration=2)
            print(f"  虚无效果创建成功: {test_xuwu.name}")
            print(f"  效果ID: {test_xuwu.id}")
            print(f"  持续时间: {test_xuwu.duration}")
            print(f"  剩余时间: {test_xuwu.remaining_duration}")
            
            # 检查触发条件
            trigger_conditions = test_xuwu.get_trigger_conditions()
            print(f"  触发条件数量: {len(trigger_conditions)}")
            for condition in trigger_conditions:
                print(f"    - {condition.event_type}")
            
            # 测试应用到目标
            print(f"\n  测试虚无效果应用:")
            effects_before = len(other_spirit.effect_manager.effects)
            print(f"    目标应用前效果数量: {effects_before}")
            
            result = other_spirit.effect_manager.add_effect(test_xuwu, battle_state)
            print(f"    应用结果: {result.success if result else False}")
            
            effects_after = len(other_spirit.effect_manager.effects)
            print(f"    目标应用后效果数量: {effects_after}")
            
            if effects_after > effects_before:
                print(f"    ✅ 虚无效果成功应用到目标")
                
                # 检查效果是否正确
                for effect_id, effect in other_spirit.effect_manager.effects.items():
                    effect_name = getattr(effect, 'name', 'Unknown')
                    if '虚无' in effect_name:
                        print(f"      找到虚无效果: {effect_name}")
                        
                        # 测试回合结束触发
                        print(f"      测试回合结束触发:")
                        event_data = {
                            "event_type": "ROUND_END",
                            "round_num": 1,
                            "spirit": other_spirit
                        }
                        
                        trigger_result = effect.on_triggered(event_data, battle_state)
                        if trigger_result and hasattr(trigger_result, 'actions'):
                            print(f"        触发成功，生成 {len(trigger_result.actions)} 个动作")
                            
                            # 显示动作类型
                            for action in trigger_result.actions:
                                action_type = type(action).__name__
                                print(f"          - {action_type}")
                        else:
                            print(f"        触发失败或无动作")
            else:
                print(f"    ❌ 虚无效果应用失败")
                
        except Exception as e:
            print(f"  ❌ 虚无效果测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 检查5: 战斗中的效果触发
        print(f"\n📋 检查5: 战斗中的效果触发")
        
        # 执行一次攻击，看是否触发被动效果
        print(f"  执行精灵攻击，测试被动效果触发:")
        
        # 记录攻击前状态
        target_effects_before = len(other_spirit.effect_manager.effects)
        print(f"    攻击前目标效果数量: {target_effects_before}")
        
        # 执行精灵回合
        result = engine.execute_next_spirit_turn()
        if result.get("type") == "spirit_turn":
            spirit_name = result.get("spirit_name", "Unknown")
            actions_count = result.get("actions_generated", 0)
            print(f"    {spirit_name} 执行了 {actions_count} 个动作")
            
            # 检查攻击后状态
            target_effects_after = len(other_spirit.effect_manager.effects)
            print(f"    攻击后目标效果数量: {target_effects_after}")
            
            if target_effects_after > target_effects_before:
                print(f"    ✅ 攻击触发了被动效果，新增 {target_effects_after - target_effects_before} 个效果")
                
                # 显示新增效果
                for effect_id, effect in other_spirit.effect_manager.effects.items():
                    effect_name = getattr(effect, 'name', 'Unknown')
                    print(f"      - {effect_name}")
                    
                effects_working = True
            else:
                print(f"    ❌ 攻击没有触发被动效果")
                effects_working = False
        else:
            print(f"    ❌ 精灵回合执行异常: {result}")
            effects_working = False
        
        print(f"\n📊 系统性效果检查总结:")
        print(f"  1. 效果管理器初始化: ✅ 正常")
        print(f"  2. 被动技能效果创建: ✅ 正常")
        print(f"  3. 事件订阅机制: ✅ 正常")
        print(f"  4. 虚无效果实现: ✅ 正常")
        print(f"  5. 战斗中效果触发: {'✅ 正常' if effects_working else '❌ 异常'}")
        
        return effects_working
        
    except Exception as e:
        print(f"❌ 系统性检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 系统性效果检查")
    print("="*60)
    
    result = test_effects_systematic()
    
    print("\n" + "="*60)
    if result:
        print("✅ 系统性效果检查通过")
        print("效果系统各个环节都正常工作")
    else:
        print("❌ 系统性效果检查失败")
        print("需要进一步调试效果系统")

if __name__ == "__main__":
    main()
