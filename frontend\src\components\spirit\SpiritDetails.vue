<template>
  <div class="spirit-details">
    <!-- 精灵头像和基本信息 -->
    <div class="spirit-header bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
      <div class="flex items-center">
        <div class="spirit-avatar w-20 h-20 rounded-full bg-white/20 flex items-center justify-center text-2xl font-bold mr-4">
          {{ spirit.name.charAt(0) }}
        </div>
        <div>
          <h2 class="text-2xl font-bold">{{ spirit.name }}</h2>
          <p class="text-white/80">等级 {{ spirit.level }}</p>
          <div class="flex items-center mt-2">
            <el-tag v-if="spirit.element" size="small" :type="getElementTagType(spirit.element)" class="mr-2">
              {{ getElementLabel(spirit.element) }}
            </el-tag>
            <el-tag v-for="profession in spirit.professions" :key="profession" size="small" type="info" class="mr-1">
              {{ getProfession<PERSON>abel(profession) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 属性面板 -->
    <div class="spirit-attributes p-6">
      <h3 class="text-lg font-semibold text-white mb-4">
        <el-icon class="mr-2"><DataAnalysis /></el-icon>
        属性数据
      </h3>
      
      <div class="grid grid-cols-2 gap-4">
        <!-- 生命值 -->
        <div class="attribute-item">
          <div class="flex justify-between items-center mb-2">
            <span class="text-slate-400">生命值</span>
            <span class="text-green-400 font-bold">{{ spirit.attributes.hp }}/{{ spirit.attributes.maxHp }}</span>
          </div>
          <el-progress 
            :percentage="(spirit.attributes.hp / spirit.attributes.maxHp) * 100" 
            color="#10b981"
            :show-text="false"
          />
        </div>

        <!-- 能量值 -->
        <div class="attribute-item">
          <div class="flex justify-between items-center mb-2">
            <span class="text-slate-400">能量值</span>
            <span class="text-blue-400 font-bold">{{ spirit.attributes.energy }}/{{ spirit.attributes.maxEnergy }}</span>
          </div>
          <el-progress 
            :percentage="(spirit.attributes.energy / spirit.attributes.maxEnergy) * 100" 
            color="#3b82f6"
            :show-text="false"
          />
        </div>

        <!-- 攻击力 -->
        <div class="attribute-item">
          <div class="flex justify-between items-center">
            <span class="text-slate-400">攻击力</span>
            <span class="text-red-400 font-bold">{{ spirit.attributes.attack }}</span>
          </div>
        </div>

        <!-- 防御力 -->
        <div class="attribute-item">
          <div class="flex justify-between items-center">
            <span class="text-slate-400">防御力</span>
            <span class="text-blue-400 font-bold">{{ spirit.attributes.defense }}</span>
          </div>
        </div>

        <!-- 速度 -->
        <div class="attribute-item">
          <div class="flex justify-between items-center">
            <span class="text-slate-400">速度</span>
            <span class="text-yellow-400 font-bold">{{ spirit.attributes.speed }}</span>
          </div>
        </div>

        <!-- 神格等级 -->
        <div class="attribute-item" v-if="spirit.shengeLevel > 0">
          <div class="flex justify-between items-center">
            <span class="text-slate-400">神格等级</span>
            <span class="text-purple-400 font-bold">{{ spirit.shengeLevel }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 技能列表 -->
    <div class="spirit-skills p-6 border-t border-slate-600/30">
      <h3 class="text-lg font-semibold text-white mb-4">
        <el-icon class="mr-2"><Magic /></el-icon>
        技能列表
      </h3>
      
      <div v-if="spirit.skills && spirit.skills.length > 0" class="space-y-3">
        <div
          v-for="skill in spirit.skills"
          :key="skill.id"
          class="skill-item bg-slate-700/50 rounded-lg p-4 border border-slate-600/30"
        >
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-white font-medium">{{ skill.name }}</h4>
            <div class="flex items-center space-x-2">
              <el-tag size="small" :type="getSkillCategoryType(skill.category)">
                {{ getSkillCategoryLabel(skill.category) }}
              </el-tag>
              <el-tag v-if="skill.isPassive" size="small" type="info">被动</el-tag>
            </div>
          </div>
          
          <p class="text-slate-400 text-sm mb-3">{{ skill.description }}</p>
          
          <div class="skill-stats flex items-center space-x-4 text-sm">
            <span class="text-slate-400">
              冷却: <span class="text-blue-400">{{ skill.cooldown }}回合</span>
            </span>
            <span class="text-slate-400">
              消耗: <span class="text-purple-400">{{ skill.energyCost }}能量</span>
            </span>
            <span v-if="skill.currentCooldown > 0" class="text-orange-400">
              剩余冷却: {{ skill.currentCooldown }}回合
            </span>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-8">
        <el-icon class="text-4xl text-slate-500 mb-2"><Magic /></el-icon>
        <p class="text-slate-400">暂无技能</p>
      </div>
    </div>

    <!-- 效果列表 -->
    <div class="spirit-effects p-6 border-t border-slate-600/30">
      <h3 class="text-lg font-semibold text-white mb-4">
        <el-icon class="mr-2"><Star /></el-icon>
        当前效果
      </h3>
      
      <div v-if="spirit.effects && spirit.effects.length > 0" class="space-y-3">
        <div
          v-for="effect in spirit.effects"
          :key="effect.id"
          class="effect-item bg-slate-700/50 rounded-lg p-4 border border-slate-600/30"
        >
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-white font-medium">{{ effect.name }}</h4>
            <div class="flex items-center space-x-2">
              <el-tag size="small" :type="getEffectCategoryType(effect.category)">
                {{ getEffectCategoryLabel(effect.category) }}
              </el-tag>
              <span v-if="effect.stackCount > 1" class="text-yellow-400 text-sm">
                x{{ effect.stackCount }}
              </span>
            </div>
          </div>
          
          <p class="text-slate-400 text-sm mb-3">{{ effect.description }}</p>
          
          <div class="effect-stats flex items-center space-x-4 text-sm">
            <span class="text-slate-400">
              剩余时间: 
              <span class="text-green-400">{{ effect.remainingDuration }}回合</span>
            </span>
            <span v-if="effect.caster" class="text-slate-400">
              施法者: <span class="text-purple-400">{{ effect.caster }}</span>
            </span>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-8">
        <el-icon class="text-4xl text-slate-500 mb-2"><Star /></el-icon>
        <p class="text-slate-400">暂无效果</p>
      </div>
    </div>

    <!-- 契约信息 -->
    <div v-if="spirit.contractIds && spirit.contractIds.length > 0" class="spirit-contracts p-6 border-t border-slate-600/30">
      <h3 class="text-lg font-semibold text-white mb-4">
        <el-icon class="mr-2"><Connection /></el-icon>
        契约关系
      </h3>
      
      <div class="space-y-2">
        <div v-for="contractId in spirit.contractIds" :key="contractId" class="contract-item">
          <el-tag type="warning">契约 {{ contractId }}</el-tag>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="spirit-actions p-6 border-t border-slate-600/30">
      <div class="flex space-x-3">
        <el-button type="primary" @click="$emit('edit', spirit)">
          <el-icon class="mr-2"><Edit /></el-icon>
          编辑
        </el-button>
        <el-button @click="$emit('duplicate', spirit)">
          <el-icon class="mr-2"><CopyDocument /></el-icon>
          复制
        </el-button>
        <el-button @click="$emit('export', spirit)">
          <el-icon class="mr-2"><Download /></el-icon>
          导出
        </el-button>
        <el-button type="danger" @click="$emit('delete', spirit)">
          <el-icon class="mr-2"><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Spirit, ElementType, ProfessionType, SkillCategory, EffectCategory } from '@/types/battle'

interface Props {
  spirit: Spirit
}

defineProps<Props>()

defineEmits<{
  edit: [spirit: Spirit]
  duplicate: [spirit: Spirit]
  export: [spirit: Spirit]
  delete: [spirit: Spirit]
}>()

// 辅助函数
const getElementLabel = (element: ElementType) => {
  const elementMap = {
    fire: '火',
    water: '水',
    earth: '土',
    air: '风',
    light: '光',
    dark: '暗',
    neutral: '无'
  }
  return elementMap[element] || element
}

const getProfessionLabel = (profession: ProfessionType) => {
  const professionMap = {
    warrior: '战士',
    mage: '法师',
    archer: '射手',
    healer: '治疗',
    assassin: '刺客',
    tank: '坦克'
  }
  return professionMap[profession] || profession
}

const getElementTagType = (element: ElementType) => {
  const typeMap = {
    fire: 'danger',
    water: 'primary',
    earth: 'warning',
    air: 'success',
    light: 'info',
    dark: 'warning',
    neutral: 'info'
  }
  return typeMap[element] || 'info'
}

const getSkillCategoryLabel = (category: SkillCategory) => {
  const categoryMap = {
    attack: '攻击',
    defense: '防御',
    support: '支援',
    special: '特殊'
  }
  return categoryMap[category] || category
}

const getSkillCategoryType = (category: SkillCategory) => {
  const typeMap = {
    attack: 'danger',
    defense: 'primary',
    support: 'success',
    special: 'warning'
  }
  return typeMap[category] || ''
}

const getEffectCategoryLabel = (category: EffectCategory) => {
  const categoryMap = {
    buff: '增益',
    debuff: '减益',
    neutral: '中性'
  }
  return categoryMap[category] || category
}

const getEffectCategoryType = (category: EffectCategory) => {
  const typeMap = {
    buff: 'success',
    debuff: 'danger',
    neutral: 'info'
  }
  return typeMap[category] || ''
}
</script>

<style scoped lang="scss">
.spirit-details {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  min-height: 100%;
}

.attribute-item {
  padding: 12px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.skill-item, .effect-item {
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(51, 65, 85, 0.7);
    border-color: rgba(139, 92, 246, 0.3);
  }
}

:deep(.el-progress-bar__outer) {
  background-color: rgba(148, 163, 184, 0.2);
}
</style>