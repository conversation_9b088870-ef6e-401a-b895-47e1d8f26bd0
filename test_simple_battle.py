#!/usr/bin/env python3
"""
测试简单战斗

使用最简单的精灵配置来测试战斗系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_battle():
    """测试简单战斗"""
    print("🔧 测试简单战斗...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建简单精灵（不使用复杂的被动效果）
        from core.spirit.spirit import Spirit
        from core.attribute import Attributes
        from core.formation import Formation
        
        # 创建简单属性
        attributes1 = Attributes(
            base_hp=1000,
            hp_p=0.0,
            hp_flat=0.0,
            base_attack=100,
            attack_p=0.0,
            attack_flat=0.0,
            base_pdef=50,
            pdef_p=0.0,
            pdef_flat=0.0,
            base_mdef=50,
            mdef_p=0.0,
            mdef_flat=0.0,
            base_speed=100,
            base_hit_rate=0.0,
            base_dodge_rate=0.0,
            base_break_rate=0.0,
            base_block_rate=0.0,
            base_crit_rate=0.0,
            base_crit_res_rate=0.0,
            base_crit_damage=1.5,
            base_damage_reduction=0.0,
            base_penetration=0.0,

        )

        attributes2 = Attributes(
            base_hp=1000,
            hp_p=0.0,
            hp_flat=0.0,
            base_attack=100,
            attack_p=0.0,
            attack_flat=0.0,
            base_pdef=50,
            pdef_p=0.0,
            pdef_flat=0.0,
            base_mdef=50,
            mdef_p=0.0,
            mdef_flat=0.0,
            base_speed=100,
            base_hit_rate=0.0,
            base_dodge_rate=0.0,
            base_break_rate=0.0,
            base_block_rate=0.0,
            base_crit_rate=0.0,
            base_crit_res_rate=0.0,
            base_crit_damage=1.5,
            base_damage_reduction=0.0,
            base_penetration=0.0,

        )
        
        # 创建简单精灵
        spirit1 = Spirit(
            id="test_spirit_1",
            name="测试精灵1",
            attributes=attributes1,
            position=(1, 1),
            team=0
        )
        
        spirit2 = Spirit(
            id="test_spirit_2",
            name="测试精灵2",
            attributes=attributes2,
            position=(3, 1),
            team=1
        )
        
        print(f"✅ 简单精灵创建成功")
        print(f"  - 精灵1: {spirit1.name}, HP={spirit1.current_hp}, alive={spirit1.is_alive}")
        print(f"  - 精灵2: {spirit2.name}, HP={spirit2.current_hp}, alive={spirit2.is_alive}")
        
        # 创建阵型
        formation1 = Formation()
        formation2 = Formation()
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"✅ 阵型创建完成")
        print(f"  - 阵型1存活精灵: {len(formation1.get_living_spirits())}")
        print(f"  - 阵型2存活精灵: {len(formation2.get_living_spirits())}")
        
        # 创建战斗引擎
        from core.battle.engines.factory import create_battle_engine
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        print(f"✅ 战斗引擎创建完成")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        print(f"  - 获胜方: {engine.battle_state.winner}")
        
        # 检查初始状态
        print(f"\n📊 初始战斗状态:")
        print(f"  - 队伍0存活: {len(engine.battle_state.get_living_spirits(0))}")
        print(f"  - 队伍1存活: {len(engine.battle_state.get_living_spirits(1))}")
        
        # 检查胜负判定
        winner = engine.condition_checker.check_battle_end(engine.battle_state)
        print(f"  - 胜负判定: {winner}")
        
        if winner is not None:
            print("❌ 简单精灵战斗也立即结束了！")
            return False
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        print(f"✅ 回合执行完成")
        print(f"  - 结果类型: {result.get('type', 'unknown')}")
        print(f"  - 获胜方: {result.get('winner', 'None')}")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        
        # 检查执行后状态
        print(f"\n📊 回合执行后状态:")
        print(f"  - 队伍0存活: {len(engine.battle_state.get_living_spirits(0))}")
        print(f"  - 队伍1存活: {len(engine.battle_state.get_living_spirits(1))}")
        
        all_spirits = engine.battle_state.get_all_spirits()
        for i, spirit in enumerate(all_spirits):
            print(f"    精灵{i+1}: {spirit.name}")
            print(f"      - 队伍: {spirit.team}")
            print(f"      - HP: {spirit.current_hp}/{spirit.max_hp}")
            print(f"      - 存活: {spirit.is_alive}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单战斗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 简单战斗测试")
    print("="*60)
    
    success = test_simple_battle()
    
    print("\n" + "="*60)
    print("📊 测试结果")
    print("="*60)
    
    if success:
        print("✅ 简单战斗测试成功")
        print("问题可能出在复杂精灵的被动效果或技能上")
    else:
        print("❌ 简单战斗测试失败")
        print("问题可能出在战斗引擎的核心逻辑上")

if __name__ == "__main__":
    main()
