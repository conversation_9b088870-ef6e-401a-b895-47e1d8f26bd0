#!/usr/bin/env python3
"""
调试AI技能选择问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_ai_skill_selection():
    """调试AI技能选择"""
    print("🔧 调试AI技能选择...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 设置高气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"  设置 {spirit.name} 气势为: {energy_component._current_energy}")
        
        print(f"📊 测试精灵: {spirit.name}")
        print(f"  当前气势: {spirit.energy}")
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 获取AI生成器
        from core.ai import get_action_generator
        ai_generator = get_action_generator()
        
        print(f"  AI生成器类型: {type(ai_generator).__name__}")
        print(f"  技能选择器类型: {type(ai_generator.skill_selector).__name__}")
        
        # 手动调用技能选择器
        print(f"\n🎯 手动调用技能选择器:")
        skill_result = ai_generator.skill_selector.select_skill(spirit, battle_state)
        
        print(f"  选择结果:")
        print(f"    技能: {skill_result.skill.metadata.name if skill_result.skill else None}")
        print(f"    原因: {skill_result.reason}")
        print(f"    优先级: {skill_result.priority_score}")
        print(f"    元数据: {skill_result.metadata}")
        
        if skill_result.skill:
            skill = skill_result.skill
            cast_type = getattr(skill.metadata, 'cast_type', 'Unknown')
            energy_cost = getattr(skill.metadata, 'energy_cost', 0)
            
            print(f"    技能类型: {cast_type}")
            print(f"    气势消耗: {energy_cost}")
            
            if cast_type == 'ULTIMATE':
                print(f"    ✅ 选择了超杀技能！")
                return True
            else:
                print(f"    ❌ 没有选择超杀技能")
        
        # 检查技能选择器的内部逻辑
        print(f"\n🔍 检查技能选择器内部逻辑:")
        
        # 检查是否是SimpleSkillSelector
        if hasattr(ai_generator.skill_selector, '_get_ultimate_threshold'):
            print(f"  使用SimpleSkillSelector")
            
            # 获取超杀阈值
            threshold = ai_generator.skill_selector._get_ultimate_threshold(spirit)
            print(f"  超杀阈值: {threshold}")
            
            current_energy = spirit.energy
            print(f"  当前气势: {current_energy}")
            
            if current_energy >= threshold:
                print(f"  ✅ 气势足够使用超杀 ({current_energy} >= {threshold})")
                
                # 检查是否能获取超杀技能
                skill_component = ai_generator.skill_selector._get_skill_component(spirit)
                if skill_component:
                    print(f"  技能组件存在: ✅")
                    
                    ultimate_skill = ai_generator.skill_selector._get_ultimate_skill(skill_component)
                    if ultimate_skill:
                        skill_name = getattr(ultimate_skill.metadata, 'name', 'Unknown')
                        print(f"  找到超杀技能: {skill_name} ✅")
                    else:
                        print(f"  没有找到超杀技能: ❌")
                        
                        # 手动检查get_skills_by_type
                        ultimate_skills = skill_component.get_skills_by_type('ULTIMATE')
                        print(f"  get_skills_by_type('ULTIMATE'): {len(ultimate_skills)} 个技能")
                        
                        for skill in ultimate_skills:
                            skill_name = getattr(skill, 'name', 'Unknown')
                            print(f"    - {skill_name}")
                else:
                    print(f"  技能组件不存在: ❌")
            else:
                print(f"  ❌ 气势不足使用超杀 ({current_energy} < {threshold})")
        else:
            print(f"  使用其他技能选择器")
        
        return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_simple_skill_selector():
    """直接测试SimpleSkillSelector"""
    print("\n🔧 直接测试SimpleSkillSelector...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 设置高气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        battle_state = BattleState(formation1, formation2)
        
        # 直接创建SimpleSkillSelector
        from core.ai.action_generator import SimpleSkillSelector
        
        selector = SimpleSkillSelector()
        
        print(f"📊 直接测试SimpleSkillSelector:")
        print(f"  精灵: {spirit.name}")
        print(f"  气势: {spirit.energy}")
        
        # 调用选择方法
        result = selector.select_skill(spirit, battle_state)
        
        print(f"  选择结果:")
        print(f"    技能: {result.skill.metadata.name if result.skill else None}")
        print(f"    原因: {result.reason}")
        print(f"    优先级: {result.priority_score}")
        print(f"    元数据: {result.metadata}")
        
        if result.skill:
            cast_type = getattr(result.skill.metadata, 'cast_type', 'Unknown')
            if cast_type == 'ULTIMATE':
                print(f"    ✅ 成功选择超杀技能！")
                return True
            else:
                print(f"    ❌ 选择了非超杀技能")
        
        return False
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 AI技能选择问题调试")
    print("="*60)
    
    tests = [
        ("AI技能选择调试", debug_ai_skill_selection),
        ("SimpleSkillSelector直接测试", debug_simple_skill_selector),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
