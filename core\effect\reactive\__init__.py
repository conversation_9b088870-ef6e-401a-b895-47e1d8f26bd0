"""
响应式效果管理系统

提供全监控的动态更新机制，确保属性和效果的及时更新
"""

from .attribute_watcher import (
    AttributeWatcher,
    AttributeChange,
    AttributeCondition,
    ChangeType,
    hp_below_threshold,
    hp_above_threshold,
    attribute_increased,
    attribute_decreased
)

from .lifecycle_manager import (
    EffectLifecycleManager,
    EffectLifecycleEvent,
    EffectLifecycleRecord,
    EffectDependency
)

from .update_scheduler import (
    DynamicUpdateScheduler,
    UpdateTask,
    UpdatePriority,
    UpdateType
)

from .reactive_manager import ReactiveEffectManager

__all__ = [
    # 属性监控
    "AttributeWatcher",
    "AttributeChange", 
    "AttributeCondition",
    "ChangeType",
    "hp_below_threshold",
    "hp_above_threshold",
    "attribute_increased",
    "attribute_decreased",
    
    # 生命周期管理
    "EffectLifecycleManager",
    "EffectLifecycleEvent",
    "EffectLifecycleRecord",
    "EffectDependency",
    
    # 更新调度
    "DynamicUpdateScheduler",
    "UpdateTask",
    "UpdatePriority", 
    "UpdateType",
    
    # 响应式管理器
    "ReactiveEffectManager"
]


def create_reactive_effect_manager(owner, scheduler=None):
    """创建响应式效果管理器的便捷函数
    
    Args:
        owner: 拥有者对象
        scheduler: 可选的调度器实例
        
    Returns:
        ReactiveEffectManager: 响应式效果管理器实例
    """
    return ReactiveEffectManager(owner, scheduler)


def create_global_scheduler(max_workers=4):
    """创建全局调度器的便捷函数
    
    Args:
        max_workers: 最大工作线程数
        
    Returns:
        DynamicUpdateScheduler: 调度器实例
    """
    scheduler = DynamicUpdateScheduler(max_workers)
    scheduler.start()
    return scheduler


# 全局调度器实例（可选）
_global_scheduler = None

def get_global_scheduler():
    """获取全局调度器实例"""
    global _global_scheduler
    if _global_scheduler is None:
        _global_scheduler = create_global_scheduler()
    return _global_scheduler


def shutdown_global_scheduler():
    """关闭全局调度器"""
    global _global_scheduler
    if _global_scheduler is not None:
        _global_scheduler.stop()
        _global_scheduler = None
