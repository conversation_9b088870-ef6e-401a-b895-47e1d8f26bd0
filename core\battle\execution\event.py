"""Event dispatch handler registered for DispatchEventAction."""
from __future__ import annotations

from typing import List, Optional, cast

from core.battle.executor.executor import handler, UnifiedActionExecutor
from ...action import DispatchEventAction, BattleAction


@handler(DispatchEventAction)
def _handle_dispatch_event(
    self: UnifiedActionExecutor, action: BattleAction
) -> Optional[List[BattleAction]]:
    """
    Dispatches the event using the unified event manager.
    """
    # Cast the action to the correct type to access its specific attributes
    dispatch_action = cast(DispatchEventAction, action)

    # 🔧 修复：在事件分发时调用统计系统
    if hasattr(self, 'stats_tracker') and self.stats_tracker:
        # 将事件传递给统计系统处理
        self.stats_tracker.on_event(dispatch_action.event, self.battle_state)

    # 使用统一的事件分发方法
    new_actions = self.battle_state.dispatch_event(dispatch_action.event, self.battle_state)
    return new_actions if new_actions else None


def _convert_event_to_dict(event) -> dict:
        """🔥 将事件对象转换为效果期望的字典格式"""
        from ...event.events import BattleStartEvent, RoundStartEvent, RoundEndEvent, BeforeAttackEvent
        
        # 根据事件类型转换
        if isinstance(event, BeforeAttackEvent):
            return {
                "event_type": "BEFORE_ATTACK",
                "attacker": event.attacker,  # 修复：使用正确的字段名
                "source": event.attacker,    # 兼容：保留旧字段名以兼容现有代码
                "target": event.target,
                "skill_name": event.skill_name,
                "attack_blocked": event.attack_blocked,
                "immunity_triggered": False,  # 初始值
                "immunity_source": None,
                "immunity_effect_id": None
                # 移除：damage, damage_type, is_critical, is_ultimate 这些在攻击前不应该有
            }
        elif isinstance(event, BattleStartEvent):
            return {
                "event_type": "BATTLE_START",
                "battle_state": event.battle_state
            }
        elif isinstance(event, RoundStartEvent):
            return {
                "event_type": "ROUND_START", 
                "round_num": getattr(event, "round_num", 0)
            }
        elif isinstance(event, RoundEndEvent):
            return {
                "event_type": "ROUND_END",
                "round_num": getattr(event, "round_num", 0)
            }
        else:
            # 对于未知事件类型，尝试通用转换
            event_dict = {"event_type": type(event).__name__.replace("Event", "").upper()}
            
            # 复制事件对象的属性
            if hasattr(event, "__dict__"):
                event_dict.update(event.__dict__)
            
            return event_dict


def _sync_event_data_back(event, event_data: dict) -> None:
    """将事件数据字典的修改同步回原始事件对象"""
    from ...event.events import BeforeAttackEvent
    
    if isinstance(event, BeforeAttackEvent):
        # 同步攻击阻止状态和免疫信息
        event.attack_blocked = event_data.get("attack_blocked", event.attack_blocked)
        # 可以根据需要同步其他字段
        if "immunity_triggered" in event_data:
            # 为了保持兼容性，可以添加动态属性
            setattr(event, "immunity_triggered", event_data["immunity_triggered"])
        if "immunity_source" in event_data:
            setattr(event, "immunity_source", event_data["immunity_source"])
        if "immunity_effect_id" in event_data:
            setattr(event, "immunity_effect_id", event_data["immunity_effect_id"]) 