"""
Core module for the battle system

This module provides the main components for the battle system,
including spirits, skills, formations, and the battle engine.
"""
from __future__ import annotations

# Core battle components
from .battle import RefactoredBattleEngine
from .battle.models import BattleState
from .spirit import Spirit
from .formation import Formation
from .skill.skills import Skill

# Interfaces
from .interfaces import IBattleEntity, IBattleState

# Utilities
from .element import ElementType
from .profession import ProfessionType
from .attribute import Attributes

# Turn order and targeting
from .turn_order import FixedGridTurnOrderStrategy, TurnOrderStrategy
from .targeting import TargetingStrategy

# System components
from .logging import GameLogger, get_logger
from .system_manager import system_manager

__all__ = [
    # Battle system
    'RefactoredBattleEngine',
    'BattleState',
    'Spirit',
    'Formation',
    'Skill',

    # Interfaces
    'IBattleEntity',
    'IBattleState',

    # Utilities
    'ElementType',
    'ProfessionType',
    'Attributes',

    # Strategies
    'FixedGridTurnOrderStrategy',
    'TurnOrderStrategy',
    'TargetingStrategy',

    # System
    'GameLogger',
    'get_logger',
    'system_manager',
]