<template>
  <el-dialog v-model="visible" title="战斗设置" width="40%">
    <div class="settings-container">
      <div class="setting-item">
        <div class="setting-label">
          <el-icon><VideoCameraFilled /></el-icon>
          <span>播放速度</span>
        </div>
        <div class="setting-control">
          <el-slider
            v-model="playbackSpeed"
            :min="0.5"
            :max="3"
            :step="0.5"
            :format-tooltip="formatSpeed"
            class="w-full"
          />
          <span class="setting-value">{{ playbackSpeed }}x</span>
        </div>
      </div>
      
      <div class="setting-item">
        <div class="setting-label">
          <el-icon><ZoomIn /></el-icon>
          <span>缩放级别</span>
        </div>
        <div class="setting-control">
          <el-slider
            v-model="zoomLevel"
            :min="50"
            :max="150"
            :step="10"
            :format-tooltip="formatZoom"
            class="w-full"
          />
          <span class="setting-value">{{ zoomLevel }}%</span>
        </div>
      </div>
      
      <div class="setting-item">
        <div class="setting-label">
          <el-icon><Notebook /></el-icon>
          <span>日志高度</span>
        </div>
        <div class="setting-control">
          <el-slider
            v-model="logHeight"
            :min="100"
            :max="300"
            :step="25"
            :format-tooltip="formatHeight"
            class="w-full"
          />
          <span class="setting-value">{{ logHeight }}px</span>
        </div>
      </div>
      
      <div class="setting-item">
        <div class="setting-label">
          <el-icon><Picture /></el-icon>
          <span>特效强度</span>
        </div>
        <div class="setting-control">
          <el-slider
            v-model="effectsIntensity"
            :min="0"
            :max="100"
            :step="10"
            :format-tooltip="formatPercentage"
            class="w-full"
          />
          <span class="setting-value">{{ effectsIntensity }}%</span>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetSettings">重置默认</el-button>
        <el-button type="primary" @click="saveAndClose">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElDialog, ElButton, ElSlider, ElIcon } from 'element-plus'
import { 
  VideoCameraFilled, 
  ZoomIn,
  Notebook,
  Picture
} from '@element-plus/icons-vue'
import { useBattleStore } from '../../stores/battle'

/**
 * BattleSettings.vue
 * 战斗配置对话框，提供各种战斗相关的设置选项
 */

const props = defineProps<{ modelValue: boolean }>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'settings-changed', settings: any): void
}>()

const battleStore = useBattleStore()
const visible = ref(false)

// 设置值
const playbackSpeed = ref(battleStore.playbackSpeed)
const zoomLevel = ref(100)
const logHeight = ref(200)
const effectsIntensity = ref(80)

// 格式化提示
const formatSpeed = (val: number) => `${val}x`
const formatZoom = (val: number) => `${val}%`
const formatHeight = (val: number) => `${val}px`
const formatPercentage = (val: number) => `${val}%`

// 监听弹窗状态变化
watch(
  () => visible.value,
  (val) => emit('update:modelValue', val)
)

watch(
  () => props.modelValue,
  (val) => (visible.value = val),
  { immediate: true }
)

// 重置为默认设置
function resetSettings() {
  playbackSpeed.value = 1
  zoomLevel.value = 100
  logHeight.value = 200
  effectsIntensity.value = 80
}

// 保存并关闭
function saveAndClose() {
  // 更新全局状态
  battleStore.playbackSpeed = playbackSpeed.value
  
  // 发出事件通知父组件
  emit('settings-changed', {
    playbackSpeed: playbackSpeed.value,
    zoomLevel: zoomLevel.value,
    logHeight: logHeight.value,
    effectsIntensity: effectsIntensity.value
  })
  
  visible.value = false
}

function close() {
  visible.value = false
}
</script>

<style scoped>
.settings-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #606266;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.setting-value {
  min-width: 50px;
  text-align: right;
  color: #409EFF;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 10px;
}
</style> 