# 本地导入 - 保持所有处理器模块的导入以确保注册
from . import attribute
from . import basic
from . import clone
from . import damage
from . import effect
from . import event
from . import logging
from . import revive_die
from . import skill
from . import enhanced_actions  # 导入增强动作处理器
from ...logging import battle_logger

# 导入统一执行器（移除旧的执行器）
from core.battle.executor.executor import UnifiedActionExecutor, handler, _handler_registry, ExecutionPhase, ActionClassifier

# 为了向后兼容，提供别名
ActionExecutor = UnifiedActionExecutor
PhasedActionExecutor = UnifiedActionExecutor

"""Executor package: houses ActionExecutor core and handler modules.

This package automatically imports all handler modules to ensure they are
registered with the ActionExecutor registry.
"""

# First import core module

# Then immediately import all handler modules to ensure registration

# Debug: verify handler registration
battle_logger.debug(f"Executor package loaded with {len(_handler_registry)} handlers")

__all__ = [
    'UnifiedActionExecutor',
    'ActionExecutor',  # 向后兼容别名
    'PhasedActionExecutor',  # 向后兼容别名
    'ExecutionPhase',
    'ActionClassifier',
    'handler',
    '_handler_registry',
    'damage',
    'effect',
    'basic',
    'skill',
    'attribute',
    'revive_die',
    'clone',
    'event',
    'logging',
    'enhanced_actions',  # 增强动作处理器
]