#!/usr/bin/env python3
"""
测试战斗中的事件流
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_battle_event_flow():
    """测试战斗中的事件流"""
    print("🔧 测试战斗中的事件流...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 检查初始状态
        print(f"\n📋 检查初始状态:")
        print(f"  伏妖效果数量: {len(fuyao_spirit.effect_manager.effects)}")
        print(f"  目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 检查被动效果的首次攻击状态
        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
            if hasattr(effect, 'first_attack_used'):
                print(f"  {effect.name} 首次攻击已使用: {effect.first_attack_used}")
        
        # 创建一个自定义的事件管理器来监控事件分发
        original_dispatch = battle_state.unified_event_manager.dispatch
        
        dispatched_events = []
        
        def monitored_dispatch(event, battle_state_param):
            """监控事件分发"""
            event_type = type(event).__name__
            dispatched_events.append({
                'event_type': event_type,
                'event': event
            })
            print(f"    🎯 分发事件: {event_type}")
            
            if hasattr(event, 'attacker'):
                attacker_name = getattr(event.attacker, 'name', 'Unknown')
                print(f"      攻击者: {attacker_name}")
            
            if hasattr(event, 'target'):
                target_name = getattr(event.target, 'name', 'Unknown')
                print(f"      目标: {target_name}")
            
            # 调用原始的分发方法
            result = original_dispatch(event, battle_state_param)
            
            if result:
                print(f"      生成动作数量: {len(result)}")
                for i, action in enumerate(result):
                    action_type = type(action).__name__
                    print(f"        动作{i+1}: {action_type}")
            else:
                print(f"      生成动作数量: 0")
            
            return result
        
        # 替换事件分发方法
        battle_state.unified_event_manager.dispatch = monitored_dispatch
        
        # 执行一次精灵回合
        print(f"\n📋 执行精灵回合:")
        print(f"  执行前目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        result = engine.execute_next_spirit_turn()
        
        print(f"  回合结果: {result.get('type', 'Unknown')}")
        if result.get("type") == "spirit_turn":
            spirit_name = result.get("spirit_name", "Unknown")
            print(f"  执行精灵: {spirit_name}")
        
        print(f"  执行后目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 显示目标的效果
        if len(other_spirit.effect_manager.effects) > 0:
            print(f"  目标效果:")
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"    - {effect_name}")
        
        # 检查被动效果的状态变化
        print(f"\n📋 检查被动效果状态变化:")
        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
            if hasattr(effect, 'first_attack_used'):
                print(f"  {effect.name} 首次攻击已使用: {effect.first_attack_used}")
        
        # 显示分发的事件
        print(f"\n📋 分发的事件总结:")
        print(f"  总事件数量: {len(dispatched_events)}")
        
        attack_events = [e for e in dispatched_events if 'Attack' in e['event_type']]
        print(f"  攻击相关事件数量: {len(attack_events)}")
        
        for event_info in attack_events:
            event_type = event_info['event_type']
            event = event_info['event']
            print(f"    - {event_type}")
            
            if hasattr(event, 'attacker') and hasattr(event, 'target'):
                attacker_name = getattr(event.attacker, 'name', 'Unknown')
                target_name = getattr(event.target, 'name', 'Unknown')
                print(f"      {attacker_name} -> {target_name}")
        
        # 判断测试结果
        target_effects_after = len(other_spirit.effect_manager.effects)
        attack_events_count = len(attack_events)
        
        if target_effects_after > 0 and attack_events_count > 0:
            print(f"\n✅ 战斗事件流测试成功！")
            print(f"  - 攻击事件正确分发: {attack_events_count} 个")
            print(f"  - 被动效果成功触发: 目标获得了效果")
            return True
        elif attack_events_count > 0:
            print(f"\n⚠️ 战斗事件流部分成功")
            print(f"  - 攻击事件正确分发: {attack_events_count} 个")
            print(f"  - 但被动效果没有触发: 目标没有获得效果")
            return False
        else:
            print(f"\n❌ 战斗事件流测试失败")
            print(f"  - 没有分发攻击事件")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 战斗事件流测试")
    print("="*60)
    
    result = test_battle_event_flow()
    
    print("\n" + "="*60)
    if result:
        print("✅ 战斗事件流测试成功")
        print("攻击事件正确分发并触发被动效果")
    else:
        print("❌ 战斗事件流测试失败")

if __name__ == "__main__":
    main()
