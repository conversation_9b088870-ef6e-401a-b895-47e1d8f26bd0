/* 战斗结果和日志样式 */

/* ==================== 战斗控制面板 ==================== */

/* 战斗按钮 */
.battle-button {
  background: var(--gradient-battle);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.battle-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.battle-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.battle-button:hover::before {
  left: 100%;
}

.battle-button:active {
  transform: translateY(-1px);
}

.battle-button:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.battle-button:disabled::before {
  display: none;
}

/* 战斗状态指示器 */
.battle-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.battle-status.ready {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-green);
  border: 2px solid rgba(16, 185, 129, 0.3);
}

.battle-status.fighting {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-orange);
  border: 2px solid rgba(245, 158, 11, 0.3);
  animation: pulse 2s infinite;
}

.battle-status.finished {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
  border: 2px solid rgba(59, 130, 246, 0.3);
}

.battle-status.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-red);
  border: 2px solid rgba(239, 68, 68, 0.3);
  animation: shake 0.5s ease-in-out;
}

/* 状态图标 */
.status-icon {
  font-size: 1.25rem;
  animation: fadeIn 0.5s ease-out;
}

/* ==================== 战斗结果展示 ==================== */

/* 结果容器 */
.battle-results {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-top: 24px;
  animation: slideIn 0.5s ease-out;
}

/* 结果头部 */
.results-header {
  background: var(--gradient-primary);
  color: white;
  padding: 20px 24px;
  text-align: center;
  position: relative;
}

.results-title {
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.results-subtitle {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-top: 4px;
}

/* 获胜者展示 */
.winner-display {
  padding: 24px;
  text-align: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid var(--gray-200);
}

.winner-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 50px;
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 16px;
  box-shadow: var(--shadow-md);
  animation: fadeIn 1s ease-out;
}

.winner-badge.team-1 {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.winner-badge.team-2 {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.winner-badge.draw {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.winner-icon {
  font-size: 1.5rem;
}

/* 战斗统计 */
.battle-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 20px 24px;
  background: var(--gray-50);
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 800;
  color: var(--primary-blue);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--gray-600);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ==================== 战斗日志 ==================== */

/* 日志容器 */
.battle-log-container {
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-top: 20px;
}

.log-header {
  background: var(--gray-800);
  color: white;
  padding: 12px 16px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-header::before {
  content: '📜';
  font-size: 1rem;
}

/* 日志内容 */
.battle-log {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  background: #1a1a1a;
  color: #e5e5e5;
}

/* 日志滚动条 */
.battle-log::-webkit-scrollbar {
  width: 8px;
}

.battle-log::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.battle-log::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 4px;
}

.battle-log::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

/* 日志行样式 */
.log-line {
  margin-bottom: 4px;
  padding: 2px 0;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.log-line:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* 日志类型样式 */
.log-damage {
  color: #ff6b6b;
}

.log-heal {
  color: #51cf66;
}

.log-buff {
  color: #74c0fc;
}

.log-debuff {
  color: #ffa8a8;
}

.log-action {
  color: #ffd43b;
}

.log-system {
  color: #9775fa;
}

.log-round {
  color: #20c997;
  font-weight: bold;
  border-bottom: 1px solid #2a2a2a;
  padding-bottom: 4px;
  margin-bottom: 8px;
}

/* 日志时间戳 */
.log-timestamp {
  color: #6c757d;
  font-size: 0.75rem;
  margin-right: 8px;
}

/* 空日志状态 */
.log-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--gray-500);
  font-style: italic;
}

.log-empty::before {
  content: '⚔️';
  display: block;
  font-size: 2rem;
  margin-bottom: 12px;
  opacity: 0.5;
}

/* ==================== 加载状态 ==================== */

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: var(--gray-600);
  font-weight: 500;
}

/* 加载骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-line {
  height: 16px;
  margin-bottom: 8px;
}

.skeleton-line.short {
  width: 60%;
}

.skeleton-line.medium {
  width: 80%;
}

.skeleton-line.long {
  width: 100%;
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 768px) {
  .battle-button {
    padding: 12px 24px;
    font-size: 1rem;
  }
  
  .battle-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }
  
  .winner-display {
    padding: 16px;
  }
  
  .winner-badge {
    font-size: 1rem;
    padding: 10px 20px;
  }
  
  .battle-log {
    font-size: 0.8125rem;
    padding: 12px;
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .battle-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-item {
    padding: 8px;
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
  
  .results-title {
    font-size: 1.25rem;
  }
  
  .battle-log {
    font-size: 0.75rem;
    max-height: 250px;
  }
}

/* Battle Results Styling */
.battle-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.stat-card {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-title {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
}

.stat-chart {
  flex: 1;
  min-height: 120px;
}

/* Custom scrollbar for battle results */
.battle-stats::-webkit-scrollbar {
  width: 8px;
}

.battle-stats::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.battle-stats::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

.battle-stats::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}