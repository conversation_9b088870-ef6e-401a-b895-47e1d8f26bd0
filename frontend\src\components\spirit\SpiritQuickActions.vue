<template>
  <div class="spirit-quick-actions">
    <!-- 快速操作按钮组 -->
    <div class="quick-actions-bar bg-slate-800/50 rounded-lg p-3 mb-4 border border-slate-600/30">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <el-button-group>
            <el-button 
              size="small" 
              @click="autoFillTeam(1)"
              :disabled="!canAutoFill"
            >
              <el-icon class="mr-1"><Magic /></el-icon>
              智能配队1
            </el-button>
            <el-button 
              size="small" 
              @click="autoFillTeam(2)"
              :disabled="!canAutoFill"
            >
              <el-icon class="mr-1"><Magic /></el-icon>
              智能配队2
            </el-button>
          </el-button-group>
          
          <el-button 
            size="small" 
            @click="balanceTeams"
            :disabled="!canBalance"
          >
            <el-icon class="mr-1"><Scale /></el-icon>
            平衡队伍
          </el-button>
          
          <el-button 
            size="small" 
            @click="randomizeTeams"
          >
            <el-icon class="mr-1"><Refresh /></el-icon>
            随机配队
          </el-button>
        </div>
        
        <div class="team-analysis text-sm text-slate-400">
          <span>队伍1: {{ getTeamScore(team1) }}分</span>
          <span class="mx-2">|</span>
          <span>队伍2: {{ getTeamScore(team2) }}分</span>
        </div>
      </div>
    </div>

    <!-- 队伍分析面板 -->
    <div v-if="showAnalysis" class="team-analysis-panel bg-slate-800/30 rounded-lg p-4 mb-4 border border-slate-600/20">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 队伍1分析 -->
        <div class="team-analysis-card">
          <h4 class="text-white font-bold mb-3 flex items-center">
            <el-icon class="mr-2 text-blue-400"><User /></el-icon>
            队伍1分析
          </h4>
          <!-- <TeamAnalysisChart :team="team1" /> -->
        </div>
        
        <!-- 队伍2分析 -->
        <div class="team-analysis-card">
          <h4 class="text-white font-bold mb-3 flex items-center">
            <el-icon class="mr-2 text-red-400"><User /></el-icon>
            队伍2分析
          </h4>
          <!-- <TeamAnalysisChart :team="team2" /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { Spirit } from '../../types/battle'
import type { SpiritPrototype } from '../../types/spirit';
import { getSpirits } from '../../api/spirits';
import { Star as Magic, Setting as Scale, Refresh, User } from '@element-plus/icons-vue'
import { useSpiritRecommendation } from '../../composables/useSpiritRecommendation'
// import TeamAnalysisChart from './TeamAnalysisChart.vue'

const props = defineProps<{
  team1: Spirit[]
  team2: Spirit[]
}>()

const emit = defineEmits<{
  (e: 'team-updated', payload: { team: 1 | 2; spirits: Spirit[] }): void
  (e: 'spirit-added', payload: { team: 1 | 2; spiritPrototype: SpiritPrototype; position: [number, number] }): void
}>()

const availableSpirits = ref<SpiritPrototype[]>([]);
const showAnalysis = ref(false)
const { analyzeTeamComposition, getRecommendedSpirits } = useSpiritRecommendation()

onMounted(async () => {
  try {
    availableSpirits.value = await getSpirits();
  } catch (error) {
    console.error("Failed to load spirits for quick actions:", error);
  }
});


// 计算属性
const canAutoFill = computed(() => availableSpirits.value.length >= 6)
const canBalance = computed(() => props.team1.length > 0 && props.team2.length > 0)

// 获取队伍评分
const getTeamScore = (team: Spirit[]) => {
  if (team.length === 0) return 0;
  return team.reduce((acc, s) => acc + (s.level || 0), 0);
}

// 智能配队
const autoFillTeam = (teamNumber: 1 | 2) => {
  const currentTeam = teamNumber === 1 ? props.team1 : props.team2
  const enemyTeam = teamNumber === 1 ? props.team2 : props.team1
  
  // This function expects `Spirit` type, might need adjustment
  const recommendations = getRecommendedSpirits(
    availableSpirits.value,
    currentTeam,
    enemyTeam,
    9 - currentTeam.length // 填满剩余位置
  )
  
  // 自动放置精灵到空位置
  const newSpirits = [...currentTeam]
  const occupiedPositions = new Set(currentTeam.map(s => `${s.position[0]},${s.position[1]}`))
  
  let positionIndex = 0
  for (const rec of recommendations) {
    if (newSpirits.length >= 9) break
    
    // 找到下一个空位置
    while (positionIndex < 9) {
      const row = Math.floor(positionIndex / 3)
      const col = positionIndex % 3
      const posKey = `${row},${col}`
      
      if (!occupiedPositions.has(posKey)) {
        emit('spirit-added', { 
            team: teamNumber, 
            spiritPrototype: rec.spirit, 
            position: [row, col] 
        });
        occupiedPositions.add(posKey) // Manually update for this loop
        newSpirits.push({} as any) // Placeholder to count
        positionIndex++
        break
      }
      positionIndex++
    }
  }
}

// 平衡队伍
const balanceTeams = () => {
  const allSpirits = [...props.team1, ...props.team2]
  
  const newTeam1: Spirit[] = []
  const newTeam2: Spirit[] = []
  
  allSpirits.forEach((spirit, index) => {
    const targetTeam = index % 2 === 0 ? newTeam1 : newTeam2
    const teamNumber = index % 2 === 0 ? 1 : 2
    const position = [Math.floor(targetTeam.length / 3), targetTeam.length % 3] as [number, number]
    
    targetTeam.push({
      ...spirit,
      team: teamNumber as 1 | 2,
      position
    })
  })
  
  emit('team-updated', { team: 1, spirits: newTeam1 })
  emit('team-updated', { team: 2, spirits: newTeam2 })
}

// 随机配队
const randomizeTeams = () => {
  const shuffled = [...availableSpirits.value].sort(() => Math.random() - 0.5)
  const team1Count = Math.min(6, Math.floor(shuffled.length / 2))
  const team2Count = Math.min(6, shuffled.length - team1Count)
  
  const convertPrototypeToSpirit = (proto: SpiritPrototype, team: 1 | 2, position: [number, number]): Spirit => ({
      id: proto.name_prefix,
      name: proto.name_prefix,
      isAlive: true,
      attributes: {
        hp: proto.attributes.base_hp,
        maxHp: proto.attributes.base_hp,
        attack: proto.attributes.base_attack,
        defense: proto.attributes.base_pdef,
        speed: proto.attributes.speed,
        energy: 0,
        maxEnergy: 150,
      },
      skills: proto.skills.map(s => s.name),
      effects: [],
      level: proto.shenge_level,
      element: proto.element,
      professions: proto.professions,
      tags: proto.tags || [],
      shengeLevel: proto.shenge_level,
      contractIds: [],
      team: team,
      position: position,
  });

  const newTeam1 = shuffled.slice(0, team1Count).map((spirit, index) => 
    convertPrototypeToSpirit(spirit, 1, [Math.floor(index / 3), index % 3])
  )
  
  const newTeam2 = shuffled.slice(team1Count, team1Count + team2Count).map((spirit, index) => 
    convertPrototypeToSpirit(spirit, 2, [Math.floor(index / 3), index % 3])
  )
  
  emit('team-updated', { team: 1, spirits: newTeam1 })
  emit('team-updated', { team: 2, spirits: newTeam2 })
}
</script>

<style scoped>
.team-analysis-card {
  @apply bg-slate-700/30 rounded-lg p-3 border border-slate-600/20;
}

.quick-actions-bar {
  backdrop-filter: blur(8px);
}
</style>