"""
动态死亡系统

提供自动化的死亡检测和处理机制，当精灵HP变化时自动触发死亡判定，
无需手动检查死亡状态。

主要组件：
- DynamicDeathDetector: 核心死亡检测器
- DynamicDeathManager: 死亡管理器
- DeathTriggerMode: 触发模式枚举

使用示例：
```python
from core.death import set_global_death_trigger_mode, DeathTriggerMode

# 配置触发模式
set_global_death_trigger_mode(DeathTriggerMode.IMMEDIATE)

# 正常使用，系统会自动检测死亡
spirit.take_damage(100)

# 获取生成的死亡动作
from core.death import get_pending_death_actions
death_actions = get_pending_death_actions()
```
"""

from .detector import DynamicDeathDetector
from .manager import DynamicDeathManager
from .types import DeathTriggerMode
from .global_manager import (
    get_global_death_manager,
    set_global_death_trigger_mode,
    monitor_spirit_hp_change,
    get_pending_death_actions,
    process_all_deferred_deaths,
    register_global_death_callback,
    get_global_death_statistics,
    clear_global_death_state,
    force_global_entity_death,
    reset_global_death_manager
)

__all__ = [
    # 核心类
    'DynamicDeathDetector',
    'DynamicDeathManager',
    'DeathTriggerMode',

    # 全局管理函数
    'get_global_death_manager',
    'set_global_death_trigger_mode',
    'monitor_spirit_hp_change',
    'get_pending_death_actions',
    'process_all_deferred_deaths',
    'register_global_death_callback',
    'get_global_death_statistics',
    'clear_global_death_state',
    'force_global_entity_death',
    'reset_global_death_manager',
]
