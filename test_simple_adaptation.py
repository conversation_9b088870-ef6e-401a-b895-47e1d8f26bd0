#!/usr/bin/env python3
"""
简化的组件适配测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_adaptation():
    """简化的组件适配测试"""
    print("🔧 测试组件适配：超杀技能energy_threshold支持...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        
        # 设置超杀气势
        if hasattr(spirit, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"🔥 为 {spirit.name} 设置超杀气势: 300")
        
        print(f"✅ 精灵创建成功: {spirit.name}")
        
        # 测试SkillComponent的超杀技能检查
        print(f"\n📋 测试SkillComponent的超杀技能检查")
        
        if hasattr(spirit, 'components'):
            from core.components import SkillComponent
            skill_component = spirit.components.get_component(SkillComponent)
            
            if skill_component:
                print(f"  技能组件存在: ✅")
                
                # 获取超杀技能
                ultimate_skills = skill_component.get_skills_by_type('ULTIMATE')
                print(f"  找到超杀技能: {len(ultimate_skills)} 个")
                
                for skill in ultimate_skills:
                    skill_name = getattr(skill, 'name', 'Unknown')
                    can_use = skill_component.can_use_skill(skill_name)
                    print(f"    {skill_name}: 可以使用 = {can_use}")
                    
                    if can_use:
                        print(f"      ✅ 超杀技能检查通过（使用energy_threshold）")
                        return True
                    else:
                        print(f"      ❌ 超杀技能检查失败")
                        return False
            else:
                print(f"  ❌ 技能组件不存在")
                return False
        else:
            print(f"  ❌ 精灵没有组件系统")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 简化组件适配测试")
    print("="*50)
    
    result = test_simple_adaptation()
    
    print("\n" + "="*50)
    if result:
        print("✅ 组件适配验证成功")
        print("\n🎉 关键组件已适配超杀技能的新逻辑！")
        print("SkillComponent能正确处理超杀技能：")
        print("  - 使用energy_threshold而不是energy_cost")
        print("  - 超杀技能检查逻辑正确")
    else:
        print("❌ 组件适配验证失败")

if __name__ == "__main__":
    main()
