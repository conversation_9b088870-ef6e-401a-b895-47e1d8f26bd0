<template>
  <div class="formation-editor h-full flex flex-col">
    <!-- 工具栏 -->
    <div class="toolbar bg-slate-700/50 rounded-lg p-4 mb-4 border border-slate-600/30">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h3 class="text-white font-medium">阵容配置</h3>
          <el-button-group size="small">
            <el-button :type="editMode === 'place' ? 'primary' : ''" @click="editMode = 'place'">
              <el-icon><Plus /></el-icon>
              放置
            </el-button>
            <el-button :type="editMode === 'move' ? 'primary' : ''" @click="editMode = 'move'">
              <el-icon><Rank /></el-icon>
              移动
            </el-button>
            <el-button :type="editMode === 'remove' ? 'primary' : ''" @click="editMode = 'remove'">
              <el-icon><Delete /></el-icon>
              移除
            </el-button>
          </el-button-group>
        </div>
        
        <div class="flex items-center space-x-3">
          <el-button size="small" @click="clearFormation">
            <el-icon class="mr-1"><Clear /></el-icon>
            清空
          </el-button>
          <el-button size="small" @click="randomFormation">
            <el-icon class="mr-1"><Refresh /></el-icon>
            随机
          </el-button>
          <el-button size="small" @click="saveAsTemplate">
            <el-icon class="mr-1"><Star /></el-icon>
            保存模板
          </el-button>
        </div>
      </div>
    </div>

    <!-- 阵容区域 -->
    <div class="formation-area flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 队伍1 -->
      <div class="team-section">
        <div class="team-header bg-blue-600/20 rounded-t-lg p-4 border border-blue-500/30">
          <div class="flex items-center justify-between">
            <h4 class="text-white font-medium">
              <el-icon class="mr-2"><User /></el-icon>
              队伍 1 ({{ formation.team1.length }}/6)
            </h4>
            <div class="team-stats text-sm text-blue-300">
              总战力: {{ calculateTeamPower(formation.team1) }}
            </div>
          </div>
        </div>
        
        <div class="battle-grid bg-slate-800/30 rounded-b-lg p-4 border-l border-r border-b border-blue-500/30">
          <div class="grid grid-cols-3 gap-2 mb-4">
            <div
              v-for="(position, index) in gridPositions"
              :key="`team1-${index}`"
              class="grid-cell"
              :class="getCellClass(position, 1)"
              @click="onCellClick(position, 1)"
              @dragover.prevent
              @drop="onDrop($event, position, 1)"
            >
              <SpiritCard
                v-if="getSpiritAt(position, 1)"
                :spirit="getSpiritAt(position, 1)"
                :compact="true"
                @remove="removeSpiritAt(position, 1)"
                draggable="true"
                @dragstart="onDragStart($event, getSpiritAt(position, 1))"
              />
              <div v-else class="empty-cell">
                <el-icon class="text-slate-500"><Plus /></el-icon>
              </div>
            </div>
          </div>
          
          <!-- 队伍属性统计 -->
          <div class="team-attributes grid grid-cols-3 gap-2 text-xs">
            <div class="attr-item text-center">
              <div class="text-red-400 font-bold">{{ getTeamAvgAttribute(formation.team1, 'attack') }}</div>
              <div class="text-slate-400">平均攻击</div>
            </div>
            <div class="attr-item text-center">
              <div class="text-blue-400 font-bold">{{ getTeamAvgAttribute(formation.team1, 'defense') }}</div>
              <div class="text-slate-400">平均防御</div>
            </div>
            <div class="attr-item text-center">
              <div class="text-green-400 font-bold">{{ getTeamAvgAttribute(formation.team1, 'hp') }}</div>
              <div class="text-slate-400">平均生命</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 队伍2 -->
      <div class="team-section">
        <div class="team-header bg-red-600/20 rounded-t-lg p-4 border border-red-500/30">
          <div class="flex items-center justify-between">
            <h4 class="text-white font-medium">
              <el-icon class="mr-2"><User /></el-icon>
              队伍 2 ({{ formation.team2.length }}/6)
            </h4>
            <div class="team-stats text-sm text-red-300">
              总战力: {{ calculateTeamPower(formation.team2) }}
            </div>
          </div>
        </div>
        
        <div class="battle-grid bg-slate-800/30 rounded-b-lg p-4 border-l border-r border-b border-red-500/30">
          <div class="grid grid-cols-3 gap-2 mb-4">
            <div
              v-for="(position, index) in gridPositions"
              :key="`team2-${index}`"
              class="grid-cell"
              :class="getCellClass(position, 2)"
              @click="onCellClick(position, 2)"
              @dragover.prevent
              @drop="onDrop($event, position, 2)"
            >
              <SpiritCard
                v-if="getSpiritAt(position, 2)"
                :spirit="getSpiritAt(position, 2)"
                :compact="true"
                @remove="removeSpiritAt(position, 2)"
                draggable="true"
                @dragstart="onDragStart($event, getSpiritAt(position, 2))"
              />
              <div v-else class="empty-cell">
                <el-icon class="text-slate-500"><Plus /></el-icon>
              </div>
            </div>
          </div>
          
          <!-- 队伍属性统计 -->
          <div class="team-attributes grid grid-cols-3 gap-2 text-xs">
            <div class="attr-item text-center">
              <div class="text-red-400 font-bold">{{ getTeamAvgAttribute(formation.team2, 'attack') }}</div>
              <div class="text-slate-400">平均攻击</div>
            </div>
            <div class="attr-item text-center">
              <div class="text-blue-400 font-bold">{{ getTeamAvgAttribute(formation.team2, 'defense') }}</div>
              <div class="text-slate-400">平均防御</div>
            </div>
            <div class="attr-item text-center">
              <div class="text-green-400 font-bold">{{ getTeamAvgAttribute(formation.team2, 'hp') }}</div>
              <div class="text-slate-400">平均生命</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 精灵库 -->
    <div class="spirit-library mt-6 bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-white font-medium">精灵库</h3>
        <div class="flex items-center space-x-3">
          <el-input
            v-model="spiritSearchQuery"
            placeholder="搜索精灵..."
            size="small"
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="spiritFilter" placeholder="筛选" size="small" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="攻击型" value="attack" />
            <el-option label="防御型" value="defense" />
            <el-option label="支援型" value="support" />
          </el-select>
        </div>
      </div>
      
      <div class="spirits-grid grid grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-3 max-h-48 overflow-auto">
        <div
          v-for="spirit in filteredSpirits"
          :key="spirit.id"
          class="spirit-item"
          draggable="true"
          @dragstart="onDragStart($event, spirit)"
          @click="selectSpiritForPlacement(spirit)"
        >
          <SpiritCard :spirit="spirit" :compact="true" />
        </div>
      </div>
    </div>

    <!-- 精灵选择对话框 -->
    <el-dialog
      v-model="showSpiritSelector"
      title="选择精灵"
      width="600px"
    >
      <SpiritSelector
        :available-spirits="availableSpirits"
        @spirit-selected="onSpiritSelected"
        @cancel="showSpiritSelector = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { Spirit } from '@/types/battle'
import SpiritCard from '@/components/spirit/SpiritCard.vue'
import SpiritSelector from '@/components/spirit/SpiritSelector.vue'

interface Formation {
  team1: Spirit[]
  team2: Spirit[]
}

interface Props {
  modelValue: Formation
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: Formation]
  'formation-change': [value: Formation]
}>()

// 响应式数据
const formation = ref<Formation>({ ...props.modelValue })
const editMode = ref<'place' | 'move' | 'remove'>('place')
const spiritSearchQuery = ref('')
const spiritFilter = ref('')
const showSpiritSelector = ref(false)
const selectedPosition = ref<[number, number] | null>(null)
const selectedTeam = ref<1 | 2>(1)
const draggedSpirit = ref<Spirit | null>(null)

// 网格位置
const gridPositions = [
  [0, 0], [0, 1], [0, 2],
  [1, 0], [1, 1], [1, 2],
  [2, 0], [2, 1], [2, 2]
]

// 可用精灵（模拟数据）
const availableSpirits = ref<Spirit[]>([
  {
    id: 'spirit_1',
    name: '神曜王者·极',
    level: 50,
    position: [0, 0],
    team: 1,
    isAlive: true,
    attributes: {
      hp: 8000,
      maxHp: 8000,
      attack: 1200,
      defense: 800,
      speed: 600,
      energy: 100,
      maxEnergy: 100
    },
    element: 'light',
    professions: ['warrior'],
    tags: ['神曜', '王者'],
    skills: [],
    effects: [],
    shengeLevel: 3,
    contractIds: []
  }
  // 更多精灵数据...
])

// 计算属性
const filteredSpirits = computed(() => {
  let result = availableSpirits.value

  if (spiritSearchQuery.value) {
    result = result.filter(spirit =>
      spirit.name.toLowerCase().includes(spiritSearchQuery.value.toLowerCase())
    )
  }

  if (spiritFilter.value) {
    // 根据精灵类型筛选
    result = result.filter(spirit => {
      // 这里可以根据精灵的职业或技能类型进行筛选
      return true
    })
  }

  return result
})

// 监听器
watch(formation, (newValue) => {
  emit('update:modelValue', newValue)
  emit('formation-change', newValue)
}, { deep: true })

// 方法
const getCellClass = (position: number[], team: number) => {
  const classes = ['w-16', 'h-16', 'border-2', 'border-dashed', 'rounded-lg', 'flex', 'items-center', 'justify-center', 'cursor-pointer', 'transition-all']
  
  if (team === 1) {
    classes.push('border-blue-500/30', 'hover:border-blue-500/60', 'hover:bg-blue-500/10')
  } else {
    classes.push('border-red-500/30', 'hover:border-red-500/60', 'hover:bg-red-500/10')
  }
  
  return classes.join(' ')
}

const getSpiritAt = (position: number[], team: number) => {
  const teamSpirits = team === 1 ? formation.value.team1 : formation.value.team2
  return teamSpirits.find(spirit => 
    spirit.position[0] === position[0] && spirit.position[1] === position[1]
  )
}

const onCellClick = (position: number[], team: number) => {
  if (editMode.value === 'place') {
    selectedPosition.value = position
    selectedTeam.value = team
    showSpiritSelector.value = true
  } else if (editMode.value === 'remove') {
    removeSpiritAt(position, team)
  }
}

const onSpiritSelected = (spirit: Spirit) => {
  if (selectedPosition.value && selectedTeam.value) {
    placeSpiritAt(spirit, selectedPosition.value, selectedTeam.value)
  }
  showSpiritSelector.value = false
  selectedPosition.value = null
}

const placeSpiritAt = (spirit: Spirit, position: number[], team: number) => {
  const teamKey = team === 1 ? 'team1' : 'team2'
  const teamSpirits = formation.value[teamKey]
  
  // 检查位置是否已被占用
  const existingSpirit = getSpiritAt(position, team)
  if (existingSpirit) {
    ElMessage.warning('该位置已有精灵')
    return
  }
  
  // 检查队伍是否已满
  if (teamSpirits.length >= 6) {
    ElMessage.warning('队伍已满，最多6个精灵')
    return
  }
  
  // 检查精灵是否已在队伍中
  const spiritExists = teamSpirits.some(s => s.id === spirit.id)
  if (spiritExists) {
    ElMessage.warning('该精灵已在队伍中')
    return
  }
  
  // 添加精灵到队伍
  const newSpirit = {
    ...spirit,
    position: position,
    team: team
  }
  teamSpirits.push(newSpirit)
  
  ElMessage.success(`${spirit.name} 已加入队伍 ${team}`)
}

const removeSpiritAt = (position: number[], team: number) => {
  const teamKey = team === 1 ? 'team1' : 'team2'
  const teamSpirits = formation.value[teamKey]
  
  const index = teamSpirits.findIndex(spirit =>
    spirit.position[0] === position[0] && spirit.position[1] === position[1]
  )
  
  if (index > -1) {
    const removedSpirit = teamSpirits.splice(index, 1)[0]
    ElMessage.info(`${removedSpirit.name} 已从队伍中移除`)
  }
}

const selectSpiritForPlacement = (spirit: Spirit) => {
  if (editMode.value === 'place') {
    // 自动寻找空位置
    const team = selectedTeam.value || 1
    const emptyPosition = findEmptyPosition(team)
    
    if (emptyPosition) {
      placeSpiritAt(spirit, emptyPosition, team)
    } else {
      ElMessage.warning(`队伍 ${team} 没有空位置`)
    }
  }
}

const findEmptyPosition = (team: number) => {
  for (const position of gridPositions) {
    if (!getSpiritAt(position, team)) {
      return position
    }
  }
  return null
}

const onDragStart = (event: DragEvent, spirit: Spirit) => {
  draggedSpirit.value = spirit
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', spirit.id)
  }
}

const onDrop = (event: DragEvent, position: number[], team: number) => {
  event.preventDefault()
  
  if (draggedSpirit.value) {
    // 如果是从队伍中拖拽，先移除原位置
    const originalTeam = draggedSpirit.value.team
    if (originalTeam) {
      removeSpiritAt(draggedSpirit.value.position, originalTeam)
    }
    
    // 放置到新位置
    placeSpiritAt(draggedSpirit.value, position, team)
    draggedSpirit.value = null
  }
}

const clearFormation = () => {
  formation.value.team1 = []
  formation.value.team2 = []
  ElMessage.info('阵容已清空')
}

const randomFormation = () => {
  // 随机生成阵容
  clearFormation()
  
  // 为每个队伍随机选择3-6个精灵
  const team1Count = Math.floor(Math.random() * 4) + 3
  const team2Count = Math.floor(Math.random() * 4) + 3
  
  const shuffledSpirits = [...availableSpirits.value].sort(() => Math.random() - 0.5)
  
  // 队伍1
  for (let i = 0; i < team1Count && i < shuffledSpirits.length; i++) {
    const position = gridPositions[i]
    placeSpiritAt(shuffledSpirits[i], position, 1)
  }
  
  // 队伍2
  for (let i = 0; i < team2Count && i + team1Count < shuffledSpirits.length; i++) {
    const position = gridPositions[i]
    placeSpiritAt(shuffledSpirits[i + team1Count], position, 2)
  }
  
  ElMessage.success('随机阵容生成完成')
}

const saveAsTemplate = () => {
  ElMessage.info('保存模板功能开发中...')
}

const calculateTeamPower = (team: Spirit[]) => {
  return team.reduce((total, spirit) => {
    return total + spirit.attributes.attack + spirit.attributes.defense + spirit.attributes.maxHp / 10
  }, 0).toFixed(0)
}

const getTeamAvgAttribute = (team: Spirit[], attribute: string) => {
  if (team.length === 0) return 0
  
  const total = team.reduce((sum, spirit) => {
    switch (attribute) {
      case 'attack':
        return sum + spirit.attributes.attack
      case 'defense':
        return sum + spirit.attributes.defense
      case 'hp':
        return sum + spirit.attributes.maxHp
      default:
        return sum
    }
  }, 0)
  
  return Math.round(total / team.length)
}
</script>

<style scoped lang="scss">
.formation-editor {
  .grid-cell {
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .empty-cell {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }
  
  .spirit-item {
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  .spirits-grid {
    &::-webkit-scrollbar {
      height: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(148, 163, 184, 0.1);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(139, 92, 246, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(139, 92, 246, 0.5);
      }
    }
  }
}
</style>