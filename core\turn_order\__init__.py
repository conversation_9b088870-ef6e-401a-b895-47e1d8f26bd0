from __future__ import annotations
from abc import ABC, abstractmethod
from typing import List, TYPE_CHECKING

# 第三方库导入
import random

# 本地导入
from ..battle.models import BattleState
from ..spirit import Spirit
from ..interfaces import IBattleEntity

"""行动顺序策略模块。

本模块负责『谁先动、谁后动』的规则实现。

- `TurnOrderStrategy` 抽象基类：定义策略接口。
- `FixedGridTurnOrderStrategy`：默认实现，步骤如下 ↴
  1. 计算双方【存活精灵速度总和】；速度高的一方获得先手。
  2. 按固定九宫格 `GRID_ORDER` 遍历位置：每个位置依次插入先手方精灵、再插入后手方精灵，实现 **1:1 交替**。
  3. 若双方总速度相同，则随机决定哪一方先手，确保公平性。
"""

if TYPE_CHECKING:
    pass


class TurnOrderStrategy(ABC):
    """行动顺序策略的抽象基类。"""

    @abstractmethod
    def create_action_queue(self, state: BattleState) -> List[IBattleEntity]:
        """
        根据当前战斗状态，创建一个回合的行动队列。

        Args:
            state: 当前的战斗状态。

        Returns:
            一个按行动顺序排序的、包含本回合所有存活精灵的列表。
        """
        pass


# 固定九宫格站位顺序 (前排->中排->后排, 同排中间->左->右)
GRID_ORDER = [
    (1, 2), (1, 1), (1, 3),
    (2, 2), (2, 1), (2, 3),
    (3, 2), (3, 1), (3, 3),
]

class FixedGridTurnOrderStrategy(TurnOrderStrategy):
    """默认行动顺序策略。

    • 计算双方速度总和，决定哪一方获得全局先手。先手方在每个网格位置都会优先行动。
    • 在遍历 `GRID_ORDER` 时，依次插入 `先手→后手`，形成交替序列：
      `A1, B1, A2, B2, ...`。
    • 如果总速度平局，则随机决定哪一方先手，确保公平性。
    """
    def _team_total_speed(self, state: BattleState, team_id: int) -> float:
        """计算指定队伍所有存活精灵速度之和。"""
        living = state.get_living_spirits(team_id)
        return sum(getattr(sp, "speed", 0.0) for sp in living)

    def _collect_team_queue(self, state: BattleState, team_id: int) -> List[IBattleEntity]:
        """按 GRID_ORDER 收集指定队伍存活精灵列表。"""
        queue: List[IBattleEntity] = []
        for pos in GRID_ORDER:
            spirit = state.get_spirit_at_position(pos, team_id)
            if spirit and spirit.is_alive:
                queue.append(spirit)
        return queue

    def create_action_queue(self, state: BattleState) -> List[IBattleEntity]:
        """
        根据两个阵型总速度的高低，决定哪一方先整体行动。随后各自内部仍按固定
        九宫格顺序排序。
        """
        team0_speed = self._team_total_speed(state, 0)
        team1_speed = self._team_total_speed(state, 1)

        # 先收集各自队列
        team0_queue = self._collect_team_queue(state, 0)
        team1_queue = self._collect_team_queue(state, 1)

        if team0_speed == team1_speed:
            # 平局：随机决定先手方
            leading_team = random.choice([0, 1])
            other_team = 1 - leading_team
            
            action_queue: List[IBattleEntity] = []
            for pos in GRID_ORDER:
                first = state.get_spirit_at_position(pos, leading_team)
                second = state.get_spirit_at_position(pos, other_team)
                if first and first.is_alive:
                    action_queue.append(first)
                if second and second.is_alive:
                    action_queue.append(second)
            return action_queue

        leading_team = 0 if team0_speed > team1_speed else 1
        other_team = 1 - leading_team

        action_queue: List[IBattleEntity] = []
        for pos in GRID_ORDER:
            first = state.get_spirit_at_position(pos, leading_team)
            second = state.get_spirit_at_position(pos, other_team)
            if first and first.is_alive:
                action_queue.append(first)
            if second and second.is_alive:
                action_queue.append(second)
        return action_queue


# 导入奖励系统
from .turn_bonus import TurnOrderBonus, EnhancedTurnOrderStrategy, create_enhanced_turn_order_strategy

__all__ = [
    'TurnOrderStrategy',
    'FixedGridTurnOrderStrategy',
    'GRID_ORDER',
    'TurnOrderBonus',
    'EnhancedTurnOrderStrategy',
    'create_enhanced_turn_order_strategy'
]
