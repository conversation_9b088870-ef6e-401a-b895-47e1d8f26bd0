"""
批量Action集成模块

提供批量Action功能的集成和启用机制。
"""
from __future__ import annotations

from typing import List, Dict, Any
from core.battle.executor.executor import UnifiedActionExecutor
from .batch_actions import (
    BatchApplyEffectAction, BatchDamageAction, BatchModifyAttributeAction,
    ConditionalAction, BatchActionProcessor
)


def create_batch_enabled_executor(battle_state, condition_checker, battle_log, **kwargs):
    """创建启用批量处理的统一执行器"""

    # 创建统一执行器
    executor = UnifiedActionExecutor(battle_state, condition_checker, battle_log)
    
    # 添加批量处理器
    if not hasattr(executor, '_batch_processor'):
        executor._batch_processor = BatchActionProcessor(executor)  # type: ignore[attr-defined]
    
    return executor


def register_batch_handlers():
    """注册批量Action处理器"""
    # 这个函数会在模块导入时自动调用
    # 批量处理器已经通过装饰器自动注册
    pass


# 批量Action工厂函数
def create_batch_apply_effect(caster, targets: List, effect, from_attack: bool = False) -> BatchApplyEffectAction:
    """创建批量效果应用Action"""
    return BatchApplyEffectAction(
        caster=caster,
        targets=targets,
        effect=effect,
        from_attack=from_attack
    )


def create_batch_damage(caster, damage_actions: List) -> BatchDamageAction:
    """创建批量伤害Action"""
    return BatchDamageAction(
        caster=caster,
        damage_actions=damage_actions
    )


def create_conditional_action(caster, condition: str, true_action, false_action=None) -> ConditionalAction:
    """创建条件Action"""
    return ConditionalAction(
        caster=caster,
        condition=condition,
        true_action=true_action,
        false_action=false_action
    )


# 批量处理工具函数
def batch_apply_effects_to_team(caster, team_spirits: List, effect):
    """为整个队伍批量应用效果"""
    if not team_spirits:
        return None
    
    return create_batch_apply_effect(
        caster=caster,
        targets=[spirit for spirit in team_spirits if spirit.is_alive],
        effect=effect
    )


def batch_damage_to_enemies(caster, enemy_spirits: List, damage_per_target: int):
    """对敌方队伍造成批量伤害"""
    if not enemy_spirits:
        return None
    
    from ...action import DamageAction
    
    damage_actions = []
    for spirit in enemy_spirits:
        if spirit.is_alive:
            damage_actions.append(DamageAction(
                caster=caster,
                target=spirit,
                damage_value=damage_per_target
            ))
    
    if damage_actions:
        return create_batch_damage(caster, damage_actions)
    
    return None


def create_aoe_skill_actions(caster, targets: List, skill_effects: Dict[str, Any]):
    """创建AOE技能的批量Action"""
    actions = []
    
    # 批量伤害
    if 'damage' in skill_effects:
        damage_actions = []
        for target in targets:
            if target.is_alive:
                from ...action import DamageAction
                damage_actions.append(DamageAction(
                    caster=caster,
                    target=target,
                    damage_value=skill_effects['damage'],
                    skill_name=skill_effects.get('skill_name', 'AOE攻击')
                ))
        
        if damage_actions:
            actions.append(create_batch_damage(caster, damage_actions))
    
    # 批量效果
    if 'effects' in skill_effects:
        for effect in skill_effects['effects']:
            batch_effect = create_batch_apply_effect(
                caster=caster,
                targets=[t for t in targets if t.is_alive],
                effect=effect,
                from_attack=True
            )
            actions.append(batch_effect)
    
    return actions


# 性能优化工具
class BatchOptimizer:
    """批量处理优化器"""
    
    def __init__(self):
        self.optimization_stats = {
            'actions_batched': 0,
            'time_saved': 0.0,
            'optimizations_applied': 0
        }
    
    def optimize_action_list(self, actions: List) -> List:
        """优化Action列表，合并可批量处理的Action"""
        if not actions:
            return actions
        
        optimized_actions = []
        
        # 按类型分组Action
        action_groups = self._group_actions_by_type(actions)
        
        for action_type, action_list in action_groups.items():
            if len(action_list) > 1:
                # 尝试批量化
                batched_action = self._try_batch_actions(action_type, action_list)
                if batched_action:
                    optimized_actions.append(batched_action)
                    self.optimization_stats['actions_batched'] += len(action_list)
                    self.optimization_stats['optimizations_applied'] += 1
                else:
                    optimized_actions.extend(action_list)
            else:
                optimized_actions.extend(action_list)
        
        return optimized_actions
    
    def _group_actions_by_type(self, actions: List) -> Dict[str, List]:
        """按类型分组Action"""
        groups = {}
        
        for action in actions:
            action_type = type(action).__name__
            if action_type not in groups:
                groups[action_type] = []
            groups[action_type].append(action)
        
        return groups
    
    def _try_batch_actions(self, action_type: str, actions: List):
        """尝试将相同类型的Action批量化"""
        if not actions:
            return None
        
        # 检查是否可以批量化
        if action_type == 'ApplyEffectAction':
            return self._batch_apply_effect_actions(actions)
        elif action_type == 'DamageAction':
            return self._batch_damage_actions(actions)
        elif action_type == 'ModifyAttributeAction':
            return self._batch_modify_attribute_actions(actions)
        
        return None
    
    def _batch_apply_effect_actions(self, actions: List):
        """批量化效果应用Action"""
        # 按效果类型分组
        effect_groups = {}
        
        for action in actions:
            effect_name = getattr(action.effect, 'name', 'unknown')
            if effect_name not in effect_groups:
                effect_groups[effect_name] = {
                    'effect': action.effect,
                    'targets': [],
                    'caster': action.caster,
                    'from_attack': getattr(action, 'from_attack', False)
                }
            effect_groups[effect_name]['targets'].append(action.target)
        
        # 为每个效果类型创建批量Action
        batch_actions = []
        for effect_name, group_data in effect_groups.items():
            if len(group_data['targets']) > 1:
                batch_action = create_batch_apply_effect(
                    caster=group_data['caster'],
                    targets=group_data['targets'],
                    effect=group_data['effect'],
                    from_attack=group_data['from_attack']
                )
                batch_actions.append(batch_action)
        
        return batch_actions[0] if len(batch_actions) == 1 else batch_actions
    
    def _batch_damage_actions(self, actions: List):
        """批量化伤害Action"""
        # 检查是否有相同的施法者
        casters = set(action.caster for action in actions)
        if len(casters) == 1:
            return create_batch_damage(list(casters)[0], actions)
        
        return None
    
    def _batch_modify_attribute_actions(self, actions: List):
        """批量化属性修改Action"""
        # 检查是否有相同的施法者
        casters = set(action.caster for action in actions)
        if len(casters) == 1:
            return BatchModifyAttributeAction(
                caster=list(casters)[0],
                modifications=actions
            )
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取优化统计"""
        return self.optimization_stats.copy()


# 全局批量优化器实例
batch_optimizer = BatchOptimizer()


# 自动注册批量处理器
register_batch_handlers()


# 导出主要接口
__all__ = [
    'create_batch_enabled_executor',
    'create_batch_apply_effect',
    'create_batch_damage', 
    'create_conditional_action',
    'batch_apply_effects_to_team',
    'batch_damage_to_enemies',
    'create_aoe_skill_actions',
    'BatchOptimizer',
    'batch_optimizer'
]
