"""
神曜虚无·伏妖 - 神曜技能和通灵技能效果

包含伏妖的神曜技能和通灵技能：
- WuNianChanShenShenYaoEffect: 无念缠身神曜技效果
- HuanLingXieZhuTongLingEffect: 唤灵协诛通灵技效果
"""
from __future__ import annotations
from typing import List, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition, RoundStartCondition
from .effects import create_xuwu_state_effect, create_tongling_transform_effect


class WuNianChanShenShenYaoEffect(IEffect):
    """
    无念缠身神曜技效果
    
    每个大回合开始时，对敌阵随机一位处于无法行动但不处于虚无状态的目标额外施加虚无状态
    3级神格：成功触发后，令自身攻击永久提高15%（最多叠加三次）
    6级神格：除首次触发外，触发时若没有无法行动且不处于虚无状态的目标则令敌阵生命值最低的可行动目标进入虚无状态
    10级神格：通灵时额外触发一次
    """

    def __init__(self, owner_spirit, shenge_level: int = 6):
        super().__init__(
            effect_id=f"wunian_chanshen_{owner_spirit.id}",
            name="无念缠身",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=-1  # 永久效果
        )

        self.owner_spirit = owner_spirit
        self.shenge_level = shenge_level
        self.attack_boost_stacks = 0  # 攻击提升层数
        self.max_attack_stacks = 3   # 最大攻击提升层数
        self.first_trigger = True    # 是否为首次触发

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        return [
            RoundStartCondition()  # 大回合开始时触发
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: IBattleState) -> EffectResult:
        """处理神曜技触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "ROUND_START":
                return self._handle_round_start(battle_state)

            return EffectResult.success()

        except Exception as e:
            from core.logging import spirit_logger
            spirit_logger.error(f"无念缠身神曜技触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)

    def _handle_round_start(self, battle_state: IBattleState) -> EffectResult:
        """处理大回合开始时的神曜技触发"""
        from core.action import LogAction, ApplyEffectAction
        import random
        
        actions = []
        
        # 获取敌方队伍
        enemy_team = 1 if getattr(self.owner_spirit, 'team', 0) == 0 else 0
        
        # 寻找无法行动但不处于虚无状态的目标
        unable_but_not_xuwu_targets = self._find_unable_but_not_xuwu_targets(battle_state, enemy_team)
        
        target_found = False
        
        if unable_but_not_xuwu_targets:
            # 随机选择一个目标施加虚无状态
            target = random.choice(unable_but_not_xuwu_targets)
            xuwu_effect = create_xuwu_state_effect(self.owner_spirit, target, duration=2)
            
            actions.append(ApplyEffectAction(
                caster=self.owner_spirit,
                target=target,
                effect=xuwu_effect
            ))
            
            actions.append(LogAction(
                caster=self.owner_spirit,
                message=f"🌀 [无念缠身] {getattr(target, 'name', '目标')} 进入虚无状态！"
            ))
            
            target_found = True
            
            # 3级神格：成功触发后，攻击永久提高15%
            if self.shenge_level >= 3 and self.attack_boost_stacks < self.max_attack_stacks:
                self.attack_boost_stacks += 1
                # 这里需要实际修改攻击力属性
                self._boost_attack()
                
                actions.append(LogAction(
                    caster=self.owner_spirit,
                    message=f"⚡ [无念缠身] 攻击力永久提升15%！（{self.attack_boost_stacks}/{self.max_attack_stacks}）"
                ))
        
        # 6级神格：除首次触发外，若没有目标则攻击生命值最低的可行动目标
        elif self.shenge_level >= 6 and not self.first_trigger:
            lowest_hp_target = self._find_lowest_hp_actionable_target(battle_state, enemy_team)
            if lowest_hp_target:
                xuwu_effect = create_xuwu_state_effect(self.owner_spirit, lowest_hp_target, duration=2)
                
                actions.append(ApplyEffectAction(
                    caster=self.owner_spirit,
                    target=lowest_hp_target,
                    effect=xuwu_effect
                ))
                
                actions.append(LogAction(
                    caster=self.owner_spirit,
                    message=f"🌀 [无念缠身·6级神格] 生命值最低的 {getattr(lowest_hp_target, 'name', '目标')} 进入虚无状态！"
                ))
                
                target_found = True
        
        # 10级神格：通灵时额外触发一次
        if self.shenge_level >= 10 and self._is_in_tongling_state():
            # 递归调用一次（但要避免无限递归）
            if not getattr(self, '_extra_trigger_used', False):
                self._extra_trigger_used = True
                extra_result = self._handle_round_start(battle_state)
                if extra_result.actions:
                    actions.extend(extra_result.actions)
                    actions.append(LogAction(
                        caster=self.owner_spirit,
                        message=f"✨ [无念缠身·10级神格] 通灵状态额外触发！"
                    ))
                self._extra_trigger_used = False
        
        self.first_trigger = False
        
        if not target_found:
            actions.append(LogAction(
                caster=self.owner_spirit,
                message=f"🌀 [无念缠身] 未找到合适的目标"
            ))
        
        return EffectResult.success_with_actions(actions, "无念缠身神曜技触发")

    def _find_unable_but_not_xuwu_targets(self, battle_state: IBattleState, enemy_team: int) -> List:
        """寻找无法行动但不处于虚无状态的目标"""
        targets = []
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                
                for spirit in enemy_spirits:
                    if (getattr(spirit, 'is_alive', False) and 
                        self._is_unable_to_act(spirit) and
                        not self._is_in_xuwu_state(spirit)):
                        targets.append(spirit)
        except:
            pass
        
        return targets

    def _find_lowest_hp_actionable_target(self, battle_state: IBattleState, enemy_team: int):
        """寻找生命值最低的可行动目标"""
        try:
            if hasattr(battle_state, 'get_team_spirits'):
                enemy_spirits = battle_state.get_team_spirits(enemy_team)
                
                actionable_spirits = []
                for spirit in enemy_spirits:
                    if (getattr(spirit, 'is_alive', False) and 
                        not self._is_unable_to_act(spirit) and
                        not self._is_in_xuwu_state(spirit)):
                        actionable_spirits.append(spirit)
                
                if actionable_spirits:
                    return min(actionable_spirits, key=lambda s: getattr(s, 'current_hp', 0))
        except:
            pass
        
        return None

    def _boost_attack(self):
        """提升攻击力"""
        try:
            if hasattr(self.owner_spirit, 'attributes') and hasattr(self.owner_spirit.attributes, 'base_attack'):
                boost_amount = self.owner_spirit.attributes.base_attack * 0.15
                self.owner_spirit.attributes.base_attack += boost_amount
        except:
            pass

    def _is_unable_to_act(self, spirit) -> bool:
        """检查精灵是否无法行动"""
        try:
            if hasattr(spirit, 'is_unable_to_act'):
                return spirit.is_unable_to_act()
            return False
        except:
            return False

    def _is_in_xuwu_state(self, spirit) -> bool:
        """检查精灵是否处于虚无状态"""
        try:
            if hasattr(spirit, 'has_effect'):
                return spirit.has_effect("虚无状态")
            return False
        except:
            return False

    def _is_in_tongling_state(self) -> bool:
        """检查是否处于通灵状态"""
        try:
            if hasattr(self.owner_spirit, 'has_effect'):
                return self.owner_spirit.has_effect("终昼神御变身")
            return False
        except:
            return False

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"无念缠身：每回合开始施加虚无状态，攻击提升{self.attack_boost_stacks}/{self.max_attack_stacks}层",
            "duration": -1,
            "shenge_level": self.shenge_level,
            "attack_boost_stacks": self.attack_boost_stacks
        }


class HuanLingXieZhuTongLingEffect(IEffect):
    """
    唤灵协诛通灵技效果
    
    通灵条件：集齐100点进度后，触发通灵，最多通灵两次
    进度获得：
    1. 火系精灵每次出手获得5点通灵进度
    2. 敌阵精灵每被施加一次无法行动效果，则己方获得20点通灵进度
    """

    def __init__(self, owner_spirit):
        super().__init__(
            effect_id=f"huanling_xiezhu_{owner_spirit.id}",
            name="唤灵协诛",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,
            priority=EffectPriority.HIGH,
            duration=-1  # 永久效果
        )

        self.owner_spirit = owner_spirit
        self.tongling_progress = 0    # 通灵进度
        self.max_progress = 100       # 最大进度
        self.tongling_count = 0       # 已通灵次数
        self.max_tongling = 2         # 最大通灵次数

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def add_progress(self, amount: int, reason: str = "") -> bool:
        """添加通灵进度，返回是否触发通灵"""
        if self.tongling_count >= self.max_tongling:
            return False
        
        old_progress = self.tongling_progress
        self.tongling_progress = min(self.tongling_progress + amount, self.max_progress)
        
        from core.action import LogAction
        from core.logging import spirit_logger
        
        spirit_logger.info(f"唤灵协诛进度：{old_progress} -> {self.tongling_progress} (+{amount}) {reason}")
        
        # 检查是否达到通灵条件
        if self.tongling_progress >= self.max_progress:
            return self._trigger_tongling()
        
        return False

    def _trigger_tongling(self) -> bool:
        """触发通灵"""
        if self.tongling_count >= self.max_tongling:
            return False
        
        from core.action import LogAction, ApplyEffectAction
        
        # 重置进度
        self.tongling_progress = 0
        self.tongling_count += 1
        
        # 应用通灵变身效果
        transform_effect = create_tongling_transform_effect(self.owner_spirit)
        
        # 这里需要通过战斗系统来应用效果
        # 暂时直接调用效果的on_apply方法
        try:
            transform_effect.on_apply(self.owner_spirit, None)
        except:
            pass
        
        # 获得150点气势
        if hasattr(self.owner_spirit, 'energy'):
            max_energy = getattr(self.owner_spirit, 'max_energy', 300)
            current_energy = getattr(self.owner_spirit, 'energy', 0)
            new_energy = min(current_energy + 150, max_energy)
            setattr(self.owner_spirit, 'energy', new_energy)
        
        # 二次通灵时额外获得一次立即出手
        extra_action = ""
        if self.tongling_count == 2:
            extra_action = "，并获得立即出手"
            # TODO: 实现立即出手逻辑
        
        from core.logging import spirit_logger
        spirit_logger.info(f"🌟 唤灵协诛通灵触发！第{self.tongling_count}次通灵{extra_action}")
        
        return True

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"唤灵协诛：通灵进度{self.tongling_progress}/{self.max_progress}，已通灵{self.tongling_count}/{self.max_tongling}次",
            "duration": -1,
            "progress": self.tongling_progress,
            "tongling_count": self.tongling_count
        }


# 神曜和通灵效果创建函数
def create_wunian_chanshen_effect(owner_spirit, shenge_level: int = 6) -> WuNianChanShenShenYaoEffect:
    """创建无念缠身神曜技效果"""
    return WuNianChanShenShenYaoEffect(owner_spirit, shenge_level)


def create_huanling_xiezhu_effect(owner_spirit) -> HuanLingXieZhuTongLingEffect:
    """创建唤灵协诛通灵技效果"""
    return HuanLingXieZhuTongLingEffect(owner_spirit)


# 导出所有效果类和创建函数
__all__ = [
    'WuNianChanShenShenYaoEffect',
    'HuanLingXieZhuTongLingEffect',
    'create_wunian_chanshen_effect',
    'create_huanling_xiezhu_effect'
]
