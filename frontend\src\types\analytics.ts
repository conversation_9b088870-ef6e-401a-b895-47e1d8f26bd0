
export interface StatCardData {
  key: string;
  title: string;
  value: number | string;
  change: number;
  changeLabel: string;
  icon: any; 
  suffix?: string;
  gradient: string;
}

export interface ChartData {
  winLossTrend: {
    dates: string[];
    wins: number[];
    losses: number[];
  };
  spiritUsage: { name: string; value: number }[];
  skillUsage: { name: string; value: number }[];
  battleOutcomes: { name: string; value: number }[];
}

export interface SpiritStat {
  id: string;
  name: string;
  battles: number;
  wins: number;
  winRate: number;
  avgDamage: number;
  avgHealing: number;
  deathRate: number;
}

export interface SkillStat {
  id: string;
  name:string;
  category: 'attack' | 'support' | 'special' | 'passive';
  usageCount: number;
  successRate: number;
  avgDamage: number;
  avgHealing: number;
  critRate: number;
}

export interface BattleHistoryEntry {
  id: string;
  date: string;
  result: 'win' | 'lose';
  duration: number;
  rounds: number;
  team1Score: number;
  team2Score: number;
}

export interface DashboardAnalytics {
  statsCards: StatCardData[];
  chartData: ChartData;
}

export interface SpiritAnalytics {
  spiritStats: SpiritStat[];
}

export interface SkillAnalytics {
  skillStats: SkillStat[];
}

export interface BattleHistory {
  battles: BattleHistoryEntry[];
} 