#!/usr/bin/env python3
"""
AoQiAI 对战平台服务器

专门为Vue前端提供对战平台服务的后端服务器
"""
import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import random

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel


@dataclass
class SimpleSpirit:
    """简化精灵类"""
    id: str
    name: str
    hp: int
    max_hp: int
    attack: int
    defense: int
    speed: int
    position: tuple
    team: int
    is_alive: bool = True
    
    def take_damage(self, damage: int) -> int:
        """承受伤害"""
        actual_damage = min(damage, self.hp)
        self.hp -= actual_damage
        if self.hp <= 0:
            self.hp = 0
            self.is_alive = False
        return actual_damage
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "hp": self.hp,
            "max_hp": self.max_hp,
            "attack": self.attack,
            "defense": self.defense,
            "speed": self.speed,
            "position": self.position,
            "team": self.team,
            "is_alive": self.is_alive
        }


class SimpleBattleEngine:
    """简化战斗引擎"""
    
    def __init__(self, team1_spirits: List[SimpleSpirit], team2_spirits: List[SimpleSpirit]):
        self.team1_spirits = team1_spirits
        self.team2_spirits = team2_spirits
        self.all_spirits = team1_spirits + team2_spirits
        self.round_num = 0
        self.max_rounds = 50
        
    def run_battle(self):
        """运行战斗"""
        yield {
            "type": "battle_start",
            "data": {
                "team1": [s.to_dict() for s in self.team1_spirits],
                "team2": [s.to_dict() for s in self.team2_spirits]
            }
        }
        
        while self.round_num < self.max_rounds:
            self.round_num += 1
            
            # 检查胜负
            team1_alive = any(s.is_alive for s in self.team1_spirits)
            team2_alive = any(s.is_alive for s in self.team2_spirits)
            
            if not team1_alive:
                yield {"type": "battle_end", "winner": 1, "round_num": self.round_num}
                return
            elif not team2_alive:
                yield {"type": "battle_end", "winner": 0, "round_num": self.round_num}
                return
            
            # 执行回合
            yield from self.execute_round()
        
        # 超时平局
        yield {"type": "battle_end", "winner": None, "round_num": self.round_num}
    
    def execute_round(self):
        """执行单回合"""
        yield {
            "type": "round_start",
            "round_num": self.round_num,
            "data": {}
        }
        
        # 按速度排序决定行动顺序
        alive_spirits = [s for s in self.all_spirits if s.is_alive]
        alive_spirits.sort(key=lambda x: x.speed, reverse=True)
        
        actions = []
        
        for spirit in alive_spirits:
            if not spirit.is_alive:
                continue
                
            # 选择目标
            enemy_team = 1 - spirit.team
            targets = [s for s in self.all_spirits if s.team == enemy_team and s.is_alive]
            
            if not targets:
                continue
                
            target = random.choice(targets)
            
            # 计算伤害
            base_damage = spirit.attack
            damage_variance = random.uniform(0.8, 1.2)
            final_damage = int(base_damage * damage_variance)
            
            # 应用防御
            reduced_damage = max(1, final_damage - target.defense // 2)
            
            # 造成伤害
            actual_damage = target.take_damage(reduced_damage)
            
            action = {
                "type": "attack",
                "actor": spirit.name,
                "target": target.name,
                "damage": actual_damage,
                "target_hp": target.hp,
                "target_alive": target.is_alive
            }
            actions.append(action)
        
        yield {
            "type": "round_actions",
            "round_num": self.round_num,
            "actions": actions
        }


# 预设精灵数据
PRESET_SPIRITS = {
    "神曜圣谕·女帝": {
        "name": "神曜圣谕·女帝",
        "hp": 1200,
        "attack": 180,
        "defense": 160,
        "speed": 120
    },
    "神曜创世·以撒": {
        "name": "神曜创世·以撒",
        "hp": 1000,
        "attack": 200,
        "defense": 140,
        "speed": 140
    },
    "乐律之神·音织": {
        "name": "乐律之神·音织",
        "hp": 900,
        "attack": 160,
        "defense": 120,
        "speed": 160
    },
    "天恩圣祭·空灵圣龙": {
        "name": "天恩圣祭·空灵圣龙",
        "hp": 1100,
        "attack": 170,
        "defense": 150,
        "speed": 130
    },
    "赤妖王·御神": {
        "name": "赤妖王·御神",
        "hp": 1300,
        "attack": 190,
        "defense": 170,
        "speed": 110
    },
    "神曜虚无·伏妖": {
        "name": "神曜虚无·伏妖",
        "hp": 950,
        "attack": 210,
        "defense": 130,
        "speed": 150
    }
}


def create_spirit(spirit_name: str, team: int, position: tuple) -> SimpleSpirit:
    """创建精灵"""
    if spirit_name not in PRESET_SPIRITS:
        # 默认精灵
        data = {
            "name": spirit_name,
            "hp": 1000,
            "attack": 150,
            "defense": 100,
            "speed": 100
        }
    else:
        data = PRESET_SPIRITS[spirit_name].copy()
    
    spirit_id = f"{spirit_name}_{team}_{position[0]}_{position[1]}"
    
    return SimpleSpirit(
        id=spirit_id,
        name=data["name"],
        hp=data["hp"],
        max_hp=data["hp"],
        attack=data["attack"],
        defense=data["defense"],
        speed=data["speed"],
        position=position,
        team=team
    )


@dataclass
class Player:
    """玩家信息"""
    id: str
    name: str
    websocket: Optional[WebSocket] = None
    rating: int = 1000
    wins: int = 0
    losses: int = 0
    current_room: Optional[str] = None
    is_online: bool = False


@dataclass
class BattleRoom:
    """对战房间"""
    id: str
    name: str
    mode: str
    max_players: int
    players: List[Player]
    status: str
    battle_engine: Optional[SimpleBattleEngine] = None
    battle_history: List[Dict] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.battle_history is None:
            self.battle_history = []
        if self.created_at is None:
            self.created_at = datetime.now()


class PlatformManager:
    """平台管理器"""
    
    def __init__(self):
        self.players: Dict[str, Player] = {}
        self.rooms: Dict[str, BattleRoom] = {}
        self.active_connections: Dict[str, WebSocket] = {}
        self.battle_history: List[Dict] = []
        self.leaderboard: List[Dict] = []
        
    async def register_player(self, player_id: str, player_name: str, websocket: WebSocket) -> Player:
        """注册玩家"""
        if player_id in self.players:
            player = self.players[player_id]
            player.websocket = websocket
            player.is_online = True
        else:
            player = Player(
                id=player_id,
                name=player_name,
                websocket=websocket,
                is_online=True
            )
            self.players[player_id] = player
        
        self.active_connections[player_id] = websocket
        await self.broadcast_player_list()
        return player
    
    async def disconnect_player(self, player_id: str):
        """断开玩家连接"""
        if player_id in self.players:
            self.players[player_id].is_online = False
            self.players[player_id].websocket = None
        
        if player_id in self.active_connections:
            del self.active_connections[player_id]
        
        await self.broadcast_player_list()
    
    async def create_room(self, room_name: str, mode: str, creator_id: str) -> BattleRoom:
        """创建对战房间"""
        room_id = str(uuid.uuid4())[:8]
        max_players = 2 if mode == "1v1" else 4
        
        room = BattleRoom(
            id=room_id,
            name=room_name,
            mode=mode,
            max_players=max_players,
            players=[],
            status="waiting"
        )
        
        self.rooms[room_id] = room
        await self.join_room(room_id, creator_id)
        await self.broadcast_room_list()
        return room
    
    async def join_room(self, room_id: str, player_id: str) -> bool:
        """加入房间"""
        if room_id not in self.rooms or player_id not in self.players:
            return False
        
        room = self.rooms[room_id]
        player = self.players[player_id]
        
        if len(room.players) >= room.max_players:
            return False
        
        if player not in room.players:
            room.players.append(player)
            player.current_room = room_id
        
        if len(room.players) == room.max_players:
            room.status = "ready"
        
        await self.broadcast_room_update(room_id)
        return True
    
    async def leave_room(self, room_id: str, player_id: str):
        """离开房间"""
        if room_id not in self.rooms or player_id not in self.players:
            return
        
        room = self.rooms[room_id]
        player = self.players[player_id]
        
        if player in room.players:
            room.players.remove(player)
            player.current_room = None
        
        if not room.players:
            del self.rooms[room_id]
        else:
            room.status = "waiting"
        
        await self.broadcast_room_update(room_id)
        await self.broadcast_room_list()
    
    async def start_battle(self, room_id: str, formations: Dict[str, Any]) -> bool:
        """开始战斗"""
        if room_id not in self.rooms:
            return False
        
        room = self.rooms[room_id]
        if room.status != "ready" or len(room.players) < 2:
            return False
        
        try:
            # 创建精灵队伍
            team1_spirits = []
            team2_spirits = []
            
            player1_formation = formations.get(room.players[0].id, {})
            player2_formation = formations.get(room.players[1].id, {})
            
            # 创建队伍1的精灵
            for spirit_data in player1_formation.get("spirits", []):
                spirit = create_spirit(
                    spirit_data["spirit_name"],
                    team=0,
                    position=tuple(spirit_data["position"])
                )
                team1_spirits.append(spirit)
            
            # 创建队伍2的精灵
            for spirit_data in player2_formation.get("spirits", []):
                spirit = create_spirit(
                    spirit_data["spirit_name"],
                    team=1,
                    position=tuple(spirit_data["position"])
                )
                team2_spirits.append(spirit)
            
            # 如果没有精灵，创建默认精灵
            if not team1_spirits:
                team1_spirits.append(create_spirit("默认精灵1", 0, (1, 1)))
            if not team2_spirits:
                team2_spirits.append(create_spirit("默认精灵2", 1, (1, 1)))
            
            # 创建战斗引擎
            room.battle_engine = SimpleBattleEngine(team1_spirits, team2_spirits)
            room.status = "battling"
            
            # 开始战斗
            await self.run_battle(room_id)
            return True
            
        except Exception as e:
            print(f"开始战斗失败: {e}")
            return False
    
    async def run_battle(self, room_id: str):
        """运行战斗"""
        room = self.rooms[room_id]
        engine = room.battle_engine
        
        if not engine:
            return
        
        try:
            # 广播战斗开始
            await self.broadcast_to_room(room_id, {
                "type": "battle_start",
                "data": {
                    "room_id": room_id,
                    "players": [{"id": p.id, "name": p.name} for p in room.players]
                }
            })
            
            # 执行战斗
            for round_result in engine.run_battle():
                # 广播回合结果
                await self.broadcast_to_room(room_id, {
                    "type": "battle_round",
                    "data": round_result
                })
                
                # 添加到历史记录
                room.battle_history.append(round_result)
                
                # 检查战斗是否结束
                if round_result.get("type") == "battle_end":
                    winner = round_result.get("winner")
                    await self.finish_battle(room_id, winner)
                    break
                
                # 添加延迟以便观看
                await asyncio.sleep(1)
                
        except Exception as e:
            print(f"战斗执行失败: {e}")
            await self.broadcast_to_room(room_id, {
                "type": "battle_error",
                "data": {"error": str(e)}
            })
    
    async def finish_battle(self, room_id: str, winner: Optional[int]):
        """结束战斗"""
        room = self.rooms[room_id]
        room.status = "finished"
        
        # 更新玩家战绩
        if winner is not None and len(room.players) >= 2:
            winner_player = room.players[winner]
            loser_player = room.players[1 - winner]
            
            winner_player.wins += 1
            winner_player.rating += 25
            
            loser_player.losses += 1
            loser_player.rating = max(0, loser_player.rating - 25)
        
        # 保存战斗历史
        battle_record = {
            "id": str(uuid.uuid4()),
            "room_id": room_id,
            "players": [{"id": p.id, "name": p.name} for p in room.players],
            "winner": winner,
            "history": room.battle_history,
            "timestamp": datetime.now().isoformat()
        }
        self.battle_history.append(battle_record)
        
        # 更新排行榜
        await self.update_leaderboard()
        
        # 广播战斗结束
        await self.broadcast_to_room(room_id, {
            "type": "battle_end",
            "data": {
                "winner": winner,
                "battle_record": battle_record
            }
        })
    
    async def update_leaderboard(self):
        """更新排行榜"""
        online_players = [p for p in self.players.values() if p.is_online]
        self.leaderboard = sorted(
            [{"id": p.id, "name": p.name, "rating": p.rating, "wins": p.wins, "losses": p.losses}
             for p in online_players],
            key=lambda x: x["rating"],
            reverse=True
        )[:10]
        
        await self.broadcast_leaderboard()
    
    async def broadcast_to_room(self, room_id: str, message: Dict):
        """向房间内所有玩家广播消息"""
        if room_id not in self.rooms:
            return
        
        room = self.rooms[room_id]
        for player in room.players:
            if player.websocket:
                try:
                    await player.websocket.send_text(json.dumps(message))
                except:
                    pass
    
    async def broadcast_player_list(self):
        """广播在线玩家列表"""
        online_players = [
            {"id": p.id, "name": p.name, "rating": p.rating, "current_room": p.current_room}
            for p in self.players.values() if p.is_online
        ]
        
        message = {
            "type": "player_list",
            "data": {"players": online_players}
        }
        
        await self.broadcast_all(message)
    
    async def broadcast_room_list(self):
        """广播房间列表"""
        rooms_data = []
        for room in self.rooms.values():
            rooms_data.append({
                "id": room.id,
                "name": room.name,
                "mode": room.mode,
                "players": len(room.players),
                "max_players": room.max_players,
                "status": room.status
            })
        
        message = {
            "type": "room_list",
            "data": {"rooms": rooms_data}
        }
        
        await self.broadcast_all(message)
    
    async def broadcast_room_update(self, room_id: str):
        """广播房间更新"""
        if room_id not in self.rooms:
            return
        
        room = self.rooms[room_id]
        message = {
            "type": "room_update",
            "data": {
                "room_id": room_id,
                "players": [{"id": p.id, "name": p.name} for p in room.players],
                "status": room.status
            }
        }
        
        await self.broadcast_to_room(room_id, message)
    
    async def broadcast_leaderboard(self):
        """广播排行榜"""
        message = {
            "type": "leaderboard",
            "data": {"leaderboard": self.leaderboard}
        }
        
        await self.broadcast_all(message)
    
    async def broadcast_all(self, message: Dict):
        """向所有在线玩家广播消息"""
        for websocket in self.active_connections.values():
            try:
                await websocket.send_text(json.dumps(message))
            except:
                pass


# 创建平台管理器实例
platform = PlatformManager()

# 创建FastAPI应用
app = FastAPI(
    title="AoQiAI 对战平台服务器",
    description="为Vue前端提供对战平台服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# API模型
class SpiritInput(BaseModel):
    spirit_name: str
    position: List[int]


class FormationInput(BaseModel):
    spirits: List[SpiritInput]


# WebSocket连接处理
@app.websocket("/ws/{player_id}/{player_name}")
async def websocket_endpoint(websocket: WebSocket, player_id: str, player_name: str):
    await websocket.accept()
    
    try:
        # 注册玩家
        player = await platform.register_player(player_id, player_name, websocket)
        
        # 发送欢迎消息
        await websocket.send_text(json.dumps({
            "type": "welcome",
            "data": {
                "player_id": player_id,
                "player_name": player_name,
                "rating": player.rating
            }
        }))
        
        # 发送当前状态
        await platform.broadcast_player_list()
        await platform.broadcast_room_list()
        await platform.broadcast_leaderboard()
        
        # 保持连接
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理客户端消息
            await handle_client_message(player_id, message)
            
    except WebSocketDisconnect:
        await platform.disconnect_player(player_id)
    except Exception as e:
        print(f"WebSocket错误: {e}")
        await platform.disconnect_player(player_id)


async def handle_client_message(player_id: str, message: Dict):
    """处理客户端消息"""
    msg_type = message.get("type")
    data = message.get("data", {})
    
    if msg_type == "create_room":
        await platform.create_room(
            room_name=data.get("name", "新房间"),
            mode=data.get("mode", "1v1"),
            creator_id=player_id
        )
        
    elif msg_type == "join_room":
        await platform.join_room(data.get("room_id"), player_id)
        
    elif msg_type == "leave_room":
        await platform.leave_room(data.get("room_id"), player_id)
        
    elif msg_type == "start_battle":
        formations = data.get("formations", {})
        await platform.start_battle(data.get("room_id"), formations)


# REST API端点
@app.get("/api/spirits")
async def get_available_spirits():
    """获取可用精灵列表"""
    spirits_info = {}
    
    for spirit_name, spirit_data in PRESET_SPIRITS.items():
        spirits_info[spirit_name] = {
            "name": spirit_data["name"],
            "id": spirit_name,
            "attributes": {
                "hp": spirit_data["hp"],
                "attack": spirit_data["attack"],
                "defense": spirit_data["defense"],
                "speed": spirit_data["speed"]
            },
            "element": None,
            "professions": [],
            "skills": ["基础攻击"]
        }
    
    return {
        "total_spirits": len(spirits_info),
        "spirits": spirits_info
    }


@app.get("/api/leaderboard")
async def get_leaderboard():
    """获取排行榜"""
    return {"leaderboard": platform.leaderboard}


@app.get("/api/battle_history")
async def get_battle_history(limit: int = 10):
    """获取战斗历史"""
    return {
        "battles": platform.battle_history[-limit:],
        "total": len(platform.battle_history)
    }


@app.get("/api/rooms")
async def get_rooms():
    """获取房间列表"""
    rooms_data = []
    for room in platform.rooms.values():
        rooms_data.append({
            "id": room.id,
            "name": room.name,
            "mode": room.mode,
            "players": len(room.players),
            "max_players": room.max_players,
            "status": room.status,
            "created_at": room.created_at.isoformat()
        })
    
    return {"rooms": rooms_data}


if __name__ == "__main__":
    print("🚀 启动 AoQiAI 对战平台服务器...")
    print("📱 前端地址: http://localhost:5173")
    print("🔌 WebSocket: ws://localhost:8081/ws/{player_id}/{player_name}")
    print("📊 API文档: http://localhost:8081/docs")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8081,
        log_level="info"
    )
