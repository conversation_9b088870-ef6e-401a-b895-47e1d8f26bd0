#!/usr/bin/env python3
"""
调试UI中的战斗问题

模拟UI中的完整战斗流程
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simulate_ui_battle():
    """模拟UI中的战斗流程"""
    print("🔧 模拟UI战斗流程...")
    
    try:
        # 1. 初始化系统（模拟UI的initialize_system）
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 2. 获取精灵服务（模拟UI的spirit_service）
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"✅ 可用精灵: {available_spirits}")
        
        # 3. 模拟UI的create_battle方法
        spirit1_name = available_spirits[0]  # 天恩圣祭·空灵圣龙
        spirit2_name = available_spirits[1]  # 神曜圣谕·女帝
        
        print(f"📊 选择精灵: {spirit1_name} vs {spirit2_name}")
        
        # 4. 创建阵型和精灵（完全按照UI的逻辑）
        from core.formation import Formation
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(spirit1_name, team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(spirit2_name, team=1, position=(3, 1))
        
        if not spirit1 or not spirit2:
            print("❌ 精灵创建失败")
            return None
        
        print(f"✅ 精灵创建成功")
        print(f"  - 精灵1: {spirit1.name}, HP={spirit1.current_hp}, alive={spirit1.is_alive}")
        print(f"  - 精灵2: {spirit2.name}, HP={spirit2.current_hp}, alive={spirit2.is_alive}")
        
        # 5. 添加到阵型（完全按照UI的逻辑）
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"✅ 阵型创建完成")
        print(f"  - 阵型1存活精灵: {len(formation1.get_living_spirits())}")
        print(f"  - 阵型2存活精灵: {len(formation2.get_living_spirits())}")
        
        # 6. 创建战斗引擎（完全按照UI的逻辑）
        from core.battle.engines.factory import create_battle_engine
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=10,  # UI默认值
            turn_order_bonus_energy=50  # UI默认值
        )
        
        print(f"✅ 战斗引擎创建完成")
        print(f"  - 引擎类型: {engine.__class__.__name__}")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        print(f"  - 获胜方: {engine.battle_state.winner}")
        
        # 7. 检查初始战斗状态（模拟UI的update_all_displays）
        print(f"\n📊 初始战斗状态:")
        print(f"  - 队伍0存活: {len(engine.battle_state.get_living_spirits(0))}")
        print(f"  - 队伍1存活: {len(engine.battle_state.get_living_spirits(1))}")
        
        # 详细检查每个精灵
        all_spirits = engine.battle_state.get_all_spirits()
        print(f"  - 总精灵数: {len(all_spirits)}")
        
        for i, spirit in enumerate(all_spirits):
            print(f"    精灵{i+1}: {spirit.name}")
            print(f"      - 队伍: {spirit.team}")
            print(f"      - HP: {spirit.current_hp}/{spirit.max_hp}")
            print(f"      - 存活: {spirit.is_alive}")
        
        # 8. 模拟UI的execute_round方法
        print(f"\n🎯 执行第一回合...")
        
        # 检查执行前状态
        winner_before = engine.condition_checker.check_battle_end(engine.battle_state)
        print(f"  - 执行前胜负判定: {winner_before}")
        
        if winner_before is not None:
            print("❌ 回合执行前战斗就结束了！")
            return engine
        
        # 执行回合
        result = engine.execute_round()
        
        print(f"✅ 回合执行完成")
        print(f"  - 结果类型: {result.get('type', 'unknown')}")
        print(f"  - 获胜方: {result.get('winner', 'None')}")
        print(f"  - 当前回合: {engine.battle_state.round_num}")
        
        # 9. 检查执行后状态
        print(f"\n📊 回合执行后状态:")
        print(f"  - 队伍0存活: {len(engine.battle_state.get_living_spirits(0))}")
        print(f"  - 队伍1存活: {len(engine.battle_state.get_living_spirits(1))}")
        print(f"  - 战斗状态获胜方: {engine.battle_state.winner}")
        
        # 详细检查精灵状态变化
        for i, spirit in enumerate(all_spirits):
            print(f"    精灵{i+1}: {spirit.name}")
            print(f"      - 队伍: {spirit.team}")
            print(f"      - HP: {spirit.current_hp}/{spirit.max_hp}")
            print(f"      - 存活: {spirit.is_alive}")
        
        return engine
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_battle_result(engine):
    """分析战斗结果"""
    if not engine:
        return
    
    print(f"\n🔍 战斗结果分析:")
    
    # 检查胜负判定逻辑
    team0_alive = len(engine.battle_state.get_living_spirits(0))
    team1_alive = len(engine.battle_state.get_living_spirits(1))
    
    print(f"  - 队伍0存活精灵数: {team0_alive}")
    print(f"  - 队伍1存活精灵数: {team1_alive}")
    
    # 手动执行胜负判定
    winner = engine.condition_checker.check_battle_end(engine.battle_state)
    print(f"  - 胜负判定结果: {winner}")
    
    # 分析胜负判定逻辑
    if team0_alive == 0 and team1_alive == 0:
        expected_winner = -1  # 平局
    elif team0_alive == 0:
        expected_winner = 1  # 队伍1获胜
    elif team1_alive == 0:
        expected_winner = 0  # 队伍0获胜
    else:
        expected_winner = None  # 战斗继续
    
    print(f"  - 预期胜负判定: {expected_winner}")
    
    if winner != expected_winner:
        print(f"❌ 胜负判定逻辑错误！")
        print(f"   实际: {winner}, 预期: {expected_winner}")
    else:
        print(f"✅ 胜负判定逻辑正确")
    
    # 检查UI显示的矛盾
    if winner == 1 and team0_alive > team1_alive:
        print(f"❌ 发现矛盾：队伍1获胜但队伍0存活精灵更多")
    elif winner == 0 and team1_alive > team0_alive:
        print(f"❌ 发现矛盾：队伍0获胜但队伍1存活精灵更多")

def main():
    """主函数"""
    print("="*60)
    print("🔧 UI战斗问题调试")
    print("="*60)
    
    # 模拟完整的UI战斗流程
    engine = simulate_ui_battle()
    
    # 分析结果
    analyze_battle_result(engine)
    
    print("\n" + "="*60)
    print("📊 调试结论")
    print("="*60)
    
    if engine:
        print(f"战斗引擎状态: 回合{engine.battle_state.round_num}, 获胜方:{engine.battle_state.winner}")
        
        # 检查是否是一回合结束
        if engine.battle_state.round_num == 1 and engine.battle_state.winner is not None:
            print("🔍 战斗在第一回合就结束了")
            print("可能的原因:")
            print("1. 精灵在回合执行过程中死亡")
            print("2. 特殊技能或效果导致即死")
            print("3. 伤害计算错误导致过高伤害")
            print("4. 被动技能触发导致死亡")
    else:
        print("❌ 无法获取引擎状态")

if __name__ == "__main__":
    main()
