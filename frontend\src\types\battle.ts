// 精灵相关类型
export interface Spirit {
  id: string
  name: string
  position: [number, number]
  team: 1 | 2
  isAlive: boolean
  attributes: {
    hp: number
    maxHp: number
    attack: number
    defense: number
    speed: number
    energy: number
    maxEnergy: number
  }
  skills: any[] // 适当定义技能类型
  effects: any[] // 适当定义效果类型
  level: number
  element: string
  professions: string[]
  tags: string[]
  shengeLevel: number
  contractIds: string[]
}

export interface BattleAction {
  type: string
  casterId: string
  targetId: string
  value: number
  message: string
}

export interface BattleState {
  id: string
  turn: number
  spirits: Spirit[]
}

// 新增类型以匹配后端API

export interface LogEntry {
  round: number;
  message: string;
  caster_id?: string;
  target_id?: string;
  type: string;
}

export interface BattleOutput {
  winner: number | null;
  log: LogEntry[];
  initial_state: any; // 或者更具体的类型
  final_state: any;   // 或者更具体的类型
}

// 枚举类型
export enum ElementType {
  FIRE = 'fire',
  WATER = 'water',
  EARTH = 'earth',
  AIR = 'air',
  LIGHT = 'light',
  DARK = 'dark',
  NEUTRAL = 'neutral'
}

export enum ProfessionType {
  WARRIOR = 'warrior',
  MAGE = 'mage',
  ARCHER = 'archer',
  HEALER = 'healer',
  ASSASSIN = 'assassin',
  TANK = 'tank'
}

export enum SkillCategory {
  ATTACK = 'attack',
  DEFENSE = 'defense',
  SUPPORT = 'support',
  SPECIAL = 'special'
}

export enum CastType {
  ACTIVE = 'active',
  PASSIVE = 'passive',
  TRIGGER = 'trigger'
}

export enum TargetType {
  SELF = 'self',
  ALLY = 'ally',
  ENEMY = 'enemy',
  ALL_ALLIES = 'all_allies',
  ALL_ENEMIES = 'all_enemies',
  ALL = 'all',
  AREA = 'area'
}

export enum EffectCategory {
  BUFF = 'buff',
  DEBUFF = 'debuff',
  NEUTRAL = 'neutral'
}

export enum ActionType {
  DAMAGE = 'damage',
  HEAL = 'heal',
  APPLY_EFFECT = 'apply_effect',
  REMOVE_EFFECT = 'remove_effect',
  MODIFY_ATTRIBUTE = 'modify_attribute',
  SKILL_CAST = 'skill_cast',
  MOVE = 'move',
  DIE = 'die',
  REVIVE = 'revive',
  LOG = 'log'
}

export enum TerrainType {
  NORMAL = 'normal',
  FOREST = 'forest',
  DESERT = 'desert',
  MOUNTAIN = 'mountain',
  SWAMP = 'swamp'
}

// API 请求/响应类型
export interface StartBattleRequest {
  team1: Spirit[]
  team2: Spirit[]
  battleConfig?: {
    terrain?: TerrainType
    timeLimit?: number
    rules?: string[]
  }
}

export interface StartBattleResponse {
  battleId: string
  battleState: BattleState
}

export interface ExecuteTurnResponse {
  actions: BattleAction[]
  newState: BattleState
  isFinished: boolean
  winner?: 1 | 2 | 'draw'
}