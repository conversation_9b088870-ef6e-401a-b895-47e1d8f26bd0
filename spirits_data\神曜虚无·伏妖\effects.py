"""
神曜虚无·伏妖 - 基础效果类

包含伏妖使用的基础效果：
- XuWuStateEffect: 虚无状态效果
- BurnEffect: 烧伤效果
- TongLingTransformEffect: 通灵变身效果
"""
from __future__ import annotations
from typing import List, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState

from core.effect.system import IEffect, EffectType, EffectCategory, EffectPriority, EffectResult
from core.effect.triggers import TriggerCondition, RoundEndCondition
from core.action import create_indirect_damage_action, DamageType


class XuWuStateEffect(IEffect):
    """虚无状态效果 - 无法攻击与被攻击，结束时受到施加者攻击200%的伤害"""

    def __init__(self, caster: IBattleEntity, target: IBattleEntity, duration: int = 2):
        super().__init__(
            effect_id=f"xuwu_state_{target.id}_{caster.id}",
            name="虚无状态",
            effect_type=EffectType.TRIGGERED,
            category=EffectCategory.SPECIAL,  # 非负面状态
            priority=EffectPriority.HIGHEST,
            duration=duration
        )
        
        self.caster = caster  # 施加者
        self.target = target  # 目标
        self.damage_multiplier = 2.0  # 200%伤害

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def get_trigger_conditions(self) -> List[TriggerCondition]:
        """定义触发条件"""
        from core.effect.triggers import BeforeAttackCondition
        return [
            RoundEndCondition(),  # 效果结束时触发
            BeforeAttackCondition()  # 攻击前触发，用于阻止攻击和被攻击
        ]

    def on_triggered(self, event_data: Dict[str, Any], battle_state: IBattleState) -> EffectResult:
        """处理虚无状态的事件触发"""
        try:
            event_type = event_data.get("event_type")

            if event_type == "ROUND_END" and self.remaining_duration <= 0:
                return self._handle_xuwu_end_damage(battle_state)
            elif event_type == "BEFORE_ATTACK":
                return self._handle_before_attack(event_data, battle_state)

            return EffectResult.success()

        except Exception as e:
            from core.logging import spirit_logger
            spirit_logger.error(f"虚无状态效果触发失败: {e}")
            return EffectResult.error(f"触发失败: {e}", e)

    def _handle_xuwu_end_damage(self, battle_state: IBattleState) -> EffectResult:
        """处理虚无状态结束时的伤害"""
        from core.action import LogAction
        
        actions = []
        
        # 检查施加者是否还存活
        if not getattr(self.caster, 'is_alive', False):
            return EffectResult.success("施加者已不存活")
        
        # 检查目标是否还存活
        if not getattr(self.target, 'is_alive', False):
            return EffectResult.success("目标已不存活")
        
        # 创建虚无结束伤害
        damage_action = create_indirect_damage_action(
            caster=self.caster,
            target=self.target,
            damage_type=DamageType.PSYCHIC,  # 精神伤害
            power_multiplier=self.damage_multiplier,
            indirect_source="虚无状态结束",
            label="虚无伤害"
        )
        actions.append(damage_action)
        
        actions.append(LogAction(
            caster=self.caster,
            message=f"🌀 {getattr(self.target, 'name', '目标')} 的虚无状态结束，受到{self.damage_multiplier*100:.0f}%攻击力的精神伤害！"
        ))
        
        return EffectResult.success_with_actions(actions, "虚无状态结束伤害")

    def _handle_before_attack(self, event_data: Dict[str, Any], battle_state: IBattleState) -> EffectResult:
        """处理攻击前事件 - 阻止攻击和被攻击"""
        from core.action import LogAction

        attacker = event_data.get("attacker")
        target = event_data.get("target")

        # 检查是否是虚无状态精灵发起攻击
        if attacker == self.owner:
            # 虚无状态精灵无法攻击
            event_data["attack_blocked"] = True
            event_data["source_effect"] = self

            # 🔧 修复：修改原始事件对象的属性
            # 在效果管理器中，原始事件对象通过 'event' 键传递
            original_event = event_data.get("event")
            if original_event and hasattr(original_event, 'attack_blocked'):
                original_event.attack_blocked = True
                original_event.source_effect = self
                print(f"🌀 虚无效果阻止攻击：设置 attack_blocked = True")

            return EffectResult.success_with_actions([
                LogAction(caster=attacker, message=f"🌀 {getattr(attacker, 'name', '精灵')} 处于虚无状态，无法发起攻击")
            ], "虚无状态阻止攻击")

        # 检查是否是攻击虚无状态精灵
        if target == self.owner:
            # 虚无状态精灵无法被攻击
            event_data["attack_blocked"] = True
            event_data["source_effect"] = self

            # 🔧 修复：修改原始事件对象的属性
            # 在效果管理器中，原始事件对象通过 'event' 键传递
            original_event = event_data.get("event")
            if original_event and hasattr(original_event, 'attack_blocked'):
                original_event.attack_blocked = True
                original_event.source_effect = self
                print(f"🌀 虚无效果阻止被攻击：设置 attack_blocked = True")

            return EffectResult.success_with_actions([
                LogAction(caster=target, message=f"🌀 {getattr(target, 'name', '精灵')} 处于虚无状态，无法被攻击")
            ], "虚无状态阻止被攻击")

        return EffectResult.success()

    def blocks_attack(self) -> bool:
        """虚无状态阻止攻击"""
        return True

    def blocks_being_attacked(self) -> bool:
        """虚无状态阻止被攻击"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发"""
        return EffectResult.success_with_data(
            {"applied": True, "caster": getattr(self.caster, 'name', '未知')}, 
            f"{getattr(target, 'name', '精灵')} 进入虚无状态"
        )

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True}, 
            f"{getattr(target, 'name', '精灵')} 脱离虚无状态"
        )

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"虚无状态：无法攻击与被攻击，结束时受到{self.damage_multiplier*100:.0f}%攻击力伤害",
            "duration": self.remaining_duration if self.duration > 0 else -1,
            "caster": getattr(self.caster, 'name', '未知')
        }


class BurnEffect(IEffect):
    """烧伤效果 - 造成伏妖攻击力的火焰伤害"""

    def __init__(self, caster: IBattleEntity, target: IBattleEntity, power_multiplier: float = 0.8):
        super().__init__(
            effect_id=f"burn_{target.id}_{caster.id}",
            name="烧伤",
            effect_type=EffectType.TRIGGERED,  # 触发型效果
            category=EffectCategory.DEBUFF,
            priority=EffectPriority.NORMAL,
            duration=0  # 立即执行
        )
        
        self.caster = caster
        self.target = target
        self.power_multiplier = power_multiplier

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时立即执行烧伤"""
        from core.action import LogAction
        
        actions = []
        
        # 创建烧伤伤害
        burn_damage = create_indirect_damage_action(
            caster=self.caster,
            target=self.target,
            damage_type=DamageType.BURN,
            power_multiplier=self.power_multiplier,
            indirect_source="烧伤",
            label="烧伤伤害"
        )
        actions.append(burn_damage)
        
        # 检查目标是否无法行动，如果是则额外施加虚无状态
        if self._is_unable_to_act(self.target):
            xuwu_effect = XuWuStateEffect(self.caster, self.target, duration=2)
            from core.action import ApplyEffectAction
            actions.append(ApplyEffectAction(
                caster=self.caster,
                target=self.target,
                effect=xuwu_effect
            ))
            
            actions.append(LogAction(
                caster=self.caster,
                message=f"🔥 {getattr(self.target, 'name', '目标')} 烧伤！由于无法行动，额外进入虚无状态！"
            ))
        else:
            actions.append(LogAction(
                caster=self.caster,
                message=f"🔥 {getattr(self.target, 'name', '目标')} 受到烧伤伤害！"
            ))
        
        return EffectResult.success_with_actions(actions, "烧伤效果执行")

    def _is_unable_to_act(self, target: IBattleEntity) -> bool:
        """检查目标是否无法行动"""
        # 这里需要根据实际的状态检查系统来实现
        # 暂时返回False，后续需要完善
        try:
            if hasattr(target, 'is_unable_to_act'):
                return target.is_unable_to_act()
            # 检查是否有眩晕、冰冻等无法行动的状态
            return False
        except:
            return False

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True},
            f"{getattr(target, 'name', '精灵')} 烧伤效果结束"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data(
            {"updated": True},
            "烧伤效果更新"
        )

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": f"烧伤：造成{self.power_multiplier*100:.0f}%攻击力的火焰伤害",
            "power_multiplier": self.power_multiplier
        }


class TongLingTransformEffect(IEffect):
    """通灵变身效果 - 变身为终昼神御"""

    def __init__(self, target: IBattleEntity):
        super().__init__(
            effect_id=f"tongling_transform_{target.id}",
            name="终昼神御变身",
            effect_type=EffectType.PERMANENT,
            category=EffectCategory.BUFF,
            priority=EffectPriority.HIGHEST,
            duration=-1  # 永久效果
        )
        
        self.target = target
        self.original_name = getattr(target, 'name', '神曜虚无·伏妖')

    def can_apply_to(self, target) -> bool:
        """检查是否可以应用到目标"""
        return True

    def on_apply(self, target, battle_state) -> EffectResult:
        """效果应用时触发变身"""
        from core.action import LogAction, SetHPAction, ApplyEffectAction
        from core.effect.effects import ShieldEffect
        
        actions = []
        
        # 复活并获得100%生命上限的护盾
        max_hp = getattr(target.attributes, 'base_hp', 2200)
        
        # 复活到满血
        revive_action = SetHPAction(
            caster=target,
            target=target,
            hp=max_hp
        )
        actions.append(revive_action)
        
        # 添加护盾
        shield_effect = ShieldEffect(
            shield_amount=max_hp,  # 100%生命上限的护盾
            duration=-1  # 永久护盾
        )
        shield_action = ApplyEffectAction(
            caster=target,
            target=target,
            effect=shield_effect
        )
        actions.append(shield_action)
        
        # 变更名称
        if hasattr(target, 'name'):
            target.name = "终昼神御·伏妖"
        
        # 清除所有负面效果（这里需要根据实际的效果系统来实现）
        # TODO: 实现清除负面效果的逻辑
        
        actions.append(LogAction(
            caster=target,
            message=f"✨ {self.original_name} 通灵变身为终昼神御·伏妖！获得护盾并清除所有负面效果！"
        ))
        
        return EffectResult.success_with_actions(actions, "通灵变身完成")

    def on_remove(self, target, battle_state) -> EffectResult:
        """效果移除时触发"""
        return EffectResult.success_with_data(
            {"removed": True},
            f"{getattr(target, 'name', '精灵')} 失去通灵变身效果"
        )

    def on_update(self, target, battle_state) -> EffectResult:
        """效果更新时触发（每回合）"""
        return EffectResult.success_with_data(
            {"updated": True},
            "通灵变身效果更新"
        )

    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "name": self.name,
            "description": "通灵变身：变身为终昼神御，获得护盾并清除负面效果",
            "duration": -1,
            "original_name": self.original_name
        }


# 效果创建函数
def create_xuwu_state_effect(caster: IBattleEntity, target: IBattleEntity, duration: int = 2) -> XuWuStateEffect:
    """创建虚无状态效果"""
    return XuWuStateEffect(caster, target, duration)


def create_burn_effect(caster: IBattleEntity, target: IBattleEntity, power_multiplier: float = 0.8) -> BurnEffect:
    """创建烧伤效果"""
    return BurnEffect(caster, target, power_multiplier)


def create_tongling_transform_effect(target: IBattleEntity) -> TongLingTransformEffect:
    """创建通灵变身效果"""
    return TongLingTransformEffect(target)


# 导出所有效果类和创建函数
__all__ = [
    'XuWuStateEffect',
    'BurnEffect', 
    'TongLingTransformEffect',
    'create_xuwu_state_effect',
    'create_burn_effect',
    'create_tongling_transform_effect'
]
