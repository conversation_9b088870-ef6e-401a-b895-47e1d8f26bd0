#!/usr/bin/env python3
"""
测试伤害修复效果

验证EnhancedAttackAction是否正确使用技能参数
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_attack_damage():
    """测试增强攻击伤害"""
    print("🔧 测试增强攻击伤害...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"📊 战斗前状态:")
        print(f"  {spirit1.name}: {spirit1.current_hp}/{spirit1.max_hp} HP")
        print(f"  {spirit2.name}: {spirit2.current_hp}/{spirit2.max_hp} HP")
        
        # 检查技能参数
        skill = spirit1.skills[0]
        print(f"\n🎯 技能信息: {skill.metadata.name}")
        for component in skill.components:
            if hasattr(component, 'power_multiplier'):
                print(f"  倍率: {component.power_multiplier}")
            if hasattr(component, 'damage_type'):
                print(f"  类型: {component.damage_type}")
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        # 记录战斗前的HP
        before_hp = {}
        all_spirits = engine.battle_state.get_all_spirits()
        for spirit in all_spirits:
            before_hp[spirit.name] = spirit.current_hp
        
        # 执行一回合
        print(f"\n🎯 执行第一回合...")
        result = engine.execute_round()
        
        # 记录战斗后的HP
        after_hp = {}
        for spirit in all_spirits:
            after_hp[spirit.name] = spirit.current_hp
        
        print(f"\n📈 战斗后状态:")
        for spirit in all_spirits:
            before = before_hp[spirit.name]
            after = after_hp[spirit.name]
            damage = before - after
            print(f"  {spirit.name}: {before} -> {after} (伤害: {damage})")
        
        # 检查伤害是否合理
        total_damage = sum(before_hp[name] - after_hp[name] for name in before_hp.keys())
        print(f"\n📊 伤害分析:")
        print(f"  总伤害: {total_damage}")
        
        if total_damage > 1000:  # 期望伤害应该大于1000
            print(f"  ✅ 伤害正常 (>{1000})")
            return True
        else:
            print(f"  ❌ 伤害过低 (<={1000})")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_enhanced_attack():
    """直接测试EnhancedAttackAction"""
    print("\n🔧 直接测试EnhancedAttackAction...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 获取技能
        skill = spirit1.skills[0]
        
        print(f"📊 技能参数:")
        print(f"  名称: {skill.metadata.name}")
        for component in skill.components:
            if hasattr(component, 'power_multiplier'):
                print(f"  倍率: {component.power_multiplier}")
            if hasattr(component, 'damage_type'):
                print(f"  类型: {component.damage_type}")
        
        # 创建EnhancedAttackAction
        from core.action import EnhancedAttackAction
        enhanced_action = EnhancedAttackAction(
            caster=spirit1,
            target=spirit2,
            skill=skill,
            conditional_effects={'damage_multiplier': 1.0}  # 无额外倍率
        )
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        battle_state = BattleState(formation1, formation2)
        
        # 创建执行器
        from core.battle.executor.executor import UnifiedActionExecutor
        executor = UnifiedActionExecutor(battle_state)
        
        # 记录目标初始HP
        initial_hp = spirit2.current_hp
        print(f"\n🎯 执行增强攻击:")
        print(f"  目标初始HP: {initial_hp}")
        
        # 执行动作
        result_actions = executor.execute_action(enhanced_action)
        
        final_hp = spirit2.current_hp
        damage = initial_hp - final_hp
        
        print(f"  目标最终HP: {final_hp}")
        print(f"  造成伤害: {damage}")
        print(f"  生成动作数: {len(result_actions) if result_actions else 0}")
        
        # 检查结果
        if damage > 10000:  # 期望伤害应该很高
            print(f"  ✅ 伤害正常 (>{10000})")
            return True
        else:
            print(f"  ❌ 伤害过低 (<={10000})")
            
            # 显示生成的动作
            if result_actions:
                print(f"  生成的动作:")
                for i, action in enumerate(result_actions):
                    print(f"    动作 {i}: {type(action).__name__}")
                    if hasattr(action, 'damage_value'):
                        print(f"      伤害值: {action.damage_value}")
                    if hasattr(action, 'power_multiplier'):
                        print(f"      倍率: {action.power_multiplier}")
                    if hasattr(action, 'damage_type'):
                        print(f"      类型: {action.damage_type}")
            
            return False
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_damage_calculation_comparison():
    """对比伤害计算"""
    print("\n🔧 对比伤害计算...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        battle_state = BattleState(formation1, formation2)
        
        # 获取技能参数
        skill = spirit1.skills[0]
        skill_multiplier = 1.0
        skill_damage_type = "PHYSICAL"
        
        for component in skill.components:
            if hasattr(component, 'power_multiplier'):
                skill_multiplier = component.power_multiplier
            if hasattr(component, 'damage_type'):
                skill_damage_type = component.damage_type
        
        print(f"📊 技能参数:")
        print(f"  倍率: {skill_multiplier}")
        print(f"  类型: {skill_damage_type}")
        
        # 1. 直接伤害计算
        from core.action import DamageAction, DamageType
        from core.battle.utilities.formula_damage_calculator import calculate_formula_damage
        
        damage_action = DamageAction(
            caster=spirit1,
            target=spirit2,
            power_multiplier=skill_multiplier,
            damage_type=DamageType.MAGIC if skill_damage_type.upper() == "MAGIC" else DamageType.PHYSICAL
        )
        
        direct_damage, breakdown = calculate_formula_damage(
            caster=spirit1,
            target=spirit2,
            action=damage_action,
            battle_state=battle_state
        )
        
        print(f"\n📈 直接计算结果:")
        print(f"  伤害: {direct_damage}")
        
        # 2. EnhancedAttackAction计算
        from core.action import EnhancedAttackAction
        enhanced_action = EnhancedAttackAction(
            caster=spirit1,
            target=spirit2,
            skill=skill,
            conditional_effects={'damage_multiplier': 1.0}
        )
        
        # 模拟修复后的处理逻辑
        final_power_multiplier = skill_multiplier * 1.0  # conditional_multiplier = 1.0
        
        enhanced_damage_action = DamageAction(
            caster=spirit1,
            target=spirit2,
            power_multiplier=final_power_multiplier,
            damage_type=DamageType.MAGIC if skill_damage_type.upper() == "MAGIC" else DamageType.PHYSICAL
        )
        
        enhanced_damage, enhanced_breakdown = calculate_formula_damage(
            caster=spirit1,
            target=spirit2,
            action=enhanced_damage_action,
            battle_state=battle_state
        )
        
        print(f"\n📈 增强攻击计算结果:")
        print(f"  伤害: {enhanced_damage}")
        
        # 比较结果
        print(f"\n📊 结果比较:")
        print(f"  直接计算: {direct_damage}")
        print(f"  增强攻击: {enhanced_damage}")
        print(f"  差异: {abs(direct_damage - enhanced_damage)}")
        
        if abs(direct_damage - enhanced_damage) < 1:
            print(f"  ✅ 结果一致")
            return True
        else:
            print(f"  ❌ 结果不一致")
            return False
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 伤害修复效果测试")
    print("="*60)
    
    tests = [
        ("伤害计算对比", test_damage_calculation_comparison),
        ("直接增强攻击测试", test_direct_enhanced_attack),
        ("战斗伤害测试", test_enhanced_attack_damage),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！伤害修复成功")
        print("\n📋 修复内容:")
        print("  ✅ EnhancedAttackAction正确使用技能倍率")
        print("  ✅ EnhancedAttackAction正确使用技能伤害类型")
        print("  ✅ 伤害计算交给伤害计算器处理")
        print("  ✅ 连击效果也使用正确参数")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
