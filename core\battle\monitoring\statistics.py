"""
Battle Statistics Tracker
-------------------------

收集并汇总战斗过程中的关键统计数据，例如：
    • 出手次数 (actions)
    • 暴击次数 (crit)
    • 闪避次数 (dodge)
    • 死亡次数 (death)
    • 复活次数 (revive)
    • 【新增】格挡次数 (block)
    • 【新增】免疫次数 (immune)
    • 【新增】暴击分布 (crit_against)：谁暴击了谁
    • 【新增】闪避分布 (dodge_from)：谁闪避了谁
    • 【新增】格挡分布 (block_from)：谁格挡了谁
    • 【新增】击杀分布 (kill_against)：谁击杀了谁
    • 【新增】免疫分布 (immune_from)：谁免疫了谁
    • 【升级】攻击免疫次数 (damage_immune) - 统一使用 AttackImmunityEffect
    • 【新增】总造成伤害 (total_damage_dealt)
    • 【新增】总承受伤害 (total_damage_taken)
    • 【新增】伤害来源分布 (damage_from)：谁对谁造成了伤害
    • 【新增】承伤来源分布 (damage_taken_from)：谁从谁那里承受了伤害

使用方式 (由统一管理器提供)：
    from src.core.system_manager import get_system
    tracker = get_system('statistics')
    tracker.on_action(action, action_result)

最终结果可通过 ``tracker.data`` 获取，结构示例::

    {
        "spirit_id_1": {
            "actions": 10, 
            "crit": 2, 
            "dodge": 1, 
            "death": 0, 
            "revive": 0,
            "block": 3,
            "immune": 1,
            "crit_against": {
                "spirit_id_2": 1,
                "spirit_id_3": 1
            },
            "kill_against": {
                "spirit_id_3": 1
            }
        },
        "spirit_id_2": {...},
    }
"""
from __future__ import annotations

from collections import defaultdict
from typing import Any, Dict, Optional, DefaultDict

from ...action import (
    BattleAction,
    DamageAction,
    DieAction,
    ReviveAction,
    ApplyEffectAction,
    LogAction,
    DispatchEventAction,
)
from ...event.events import ImmunityEvent, ActionCompleteEvent  # 只使用新事件系统
from ...interfaces import IBattleEntity
# 移除 AttackImmuneException，统一使用 AttackImmunityEffect

__all__ = ["BattleStatsTracker"]

# 跟踪的一级统计指标
METRICS = [
    "actions",         # 出手次数
    "crit",            # 暴击次数
    "dodge",           # 闪避次数
    "block",           # 格挡次数
    "death",           # 死亡次数
    "revive",          # 复活次数
    "immune",          # 伤害免疫次数（主要免疫指标）
    "effect_immune",   # 效果免疫次数（控制效果等）
    "damage_immune",   # 伤害免疫次数（备用指标）
    "total_damage_dealt", # 总造成伤害
    "total_damage_taken", # 总承受伤害
]

class BattleStatsTracker:  # noqa: D401 – simple data holder
    """在执行动作过程中累计统计数据。"""

    def __init__(self) -> None:  # noqa: D401
        # data[spirit_id][metric] = int
        self.data: DefaultDict[str, Dict[str, Any]] = defaultdict(lambda: defaultdict(int))

        # 初始化"对谁"的统计（二级嵌套）
        # 由于defaultdict不支持多级嵌套，手动初始化
        self._initialize_target_tracking()

        # 击杀者跟踪（临时，仅在死亡时使用）
        self._last_damage_source: Dict[str, str] = {}

        # 🔧 使用统一管理器：尝试从系统管理器获取组件
        self._system_manager_available = self._check_system_manager_availability()
        
    def _check_system_manager_availability(self) -> bool:
        """检查统一系统管理器是否可用"""
        try:
            from ...system_manager import get_system
            return get_system is not None
        except Exception:
            return False

    # ---------------------------------------------------------------
    # Public API
    # ---------------------------------------------------------------

    def on_event(self, event, battle_state=None) -> None:
        """
        处理战斗事件，用于更精确的统计。
        这个方法可以被事件管理器调用。
        """
        try:
            # 处理免疫事件
            if isinstance(event, ImmunityEvent):
                target_id = self._get_entity_id(event.target)
                if target_id:
                    self._ensure_target_tracking(target_id)
                    self.data[target_id]["immune"] += 1

                    # 根据免疫类型分类统计
                    immunity_type = getattr(event, 'immunity_type', 'attack')
                    if immunity_type == 'attack':
                        self.data[target_id]["damage_immune"] += 1
                    elif immunity_type == 'effect':
                        self.data[target_id]["effect_immune"] += 1

            # 处理行动完成事件
            elif isinstance(event, ActionCompleteEvent):
                spirit_id = self._get_entity_id(getattr(event, 'actor', None))
                if spirit_id:
                    self._ensure_target_tracking(spirit_id)
                    self.data[spirit_id]["actions"] += 1

        except Exception as e:
            # 统计错误不应该影响战斗进行
            import logging
            logging.warning(f"统计事件处理失败: {e}")

    def get_summary(self) -> Dict[str, Any]:
        """获取战斗统计摘要"""
        summary = {
            "total_spirits": len(self.data),
            "total_actions": sum(spirit_data.get("actions", 0) for spirit_data in self.data.values()),
            "total_damage": sum(spirit_data.get("total_damage_dealt", 0) for spirit_data in self.data.values()),
            "total_immunities": sum(spirit_data.get("immune", 0) for spirit_data in self.data.values()),
            "total_deaths": sum(spirit_data.get("death", 0) for spirit_data in self.data.values()),
            "total_revives": sum(spirit_data.get("revive", 0) for spirit_data in self.data.values()),
        }
        return summary

    def get_spirit_stats(self, spirit_id: str) -> Dict[str, Any]:
        """获取指定精灵的统计数据"""
        return dict(self.data.get(spirit_id, {}))

    def reset(self) -> None:
        """重置所有统计数据"""
        self.data.clear()
        self._last_damage_source.clear()
        self._initialize_target_tracking()
    def on_action(self, action: BattleAction, result: Optional[Dict[str, Any]] = None) -> None:  # noqa: D401
        """在执行器完成动作后调用，以累加统计。"""
        # 1. 出手次数 – 统计施放者
        caster_id = self._get_entity_id(getattr(action, "caster", None))
        
        # 🔥 修复：只有在行动完成时才算一次出手
        # 根据8阶段行动系统，一次完整行动才算一次出手
        is_action_complete = self._is_action_complete(action)
        
        # 只有在行动完成时才增加出手次数
        if caster_id and is_action_complete:
            self.data[caster_id]["actions"] += 1

        # 2. 根据动作类型计数
        if isinstance(action, DamageAction):
            # --- 伤害动作特殊处理 ---
            target_id = self._get_entity_id(action.target)
            if not target_id:
                return
                
            # 记录最后一次伤害来源，用于后续死亡统计
            if caster_id and target_id:
                self._last_damage_source[target_id] = caster_id
            
            # 🔥 统一免疫统计（只使用 AttackImmunityEffect）
            is_immune, is_attack_immune, is_effect_immune = self._is_immune_action(action, result)

            # 统计免疫次数
            if is_immune:
                self._ensure_target_tracking(target_id)  # 🔥 确保字典已初始化
                self.data[target_id]["immune"] += 1
                if caster_id:
                    self.data[target_id]["immune_from"][caster_id] += 1

                # 攻击免疫统计（通过 AttackImmunityEffect 触发）
                if is_attack_immune:
                    self.data[target_id]["damage_immune"] += 1
                    if caster_id:
                        self.data[target_id]["damage_immune_from"][caster_id] += 1

                # 效果免疫统计
                if is_effect_immune:
                    self.data[target_id]["effect_immune"] += 1
                    if caster_id:
                        self.data[target_id]["effect_immune_from"][caster_id] += 1
            
            # 2.1 暴击：记录总数+分布
            if caster_id and getattr(action, "is_critical", False):
                self._ensure_target_tracking(caster_id)  # 确保字典已初始化
                self.data[caster_id]["crit"] += 1
                if target_id:
                    self.data[caster_id]["crit_against"][target_id] += 1
            
            # 2.2 闪避：根据结果判断
            if result and result.get("is_dodged"):
                self._ensure_target_tracking(target_id)
                self.data[target_id]["dodge"] += 1
                if caster_id:
                    self.data[target_id]["dodge_from"][caster_id] += 1

            # 2.3 格挡：根据结果判断
            if result and result.get("is_blocked"):
                self._ensure_target_tracking(target_id)
                self.data[target_id]["block"] += 1
                if caster_id:
                    self.data[target_id]["block_from"][caster_id] += 1
            
            # 2.4 伤害统计：记录造成和承受的伤害
            if not is_immune:  # 只有在没有免疫的情况下才统计伤害
                damage_dealt = getattr(action, 'damage_value', 0)
                if result:
                    # 从结果中获取实际伤害
                    actual_damage = abs(result.get("hp_change", 0))
                    damage_dealt = max(damage_dealt, actual_damage)

                if damage_dealt > 0:
                    # 统计造成伤害
                    if caster_id:
                        self._ensure_target_tracking(caster_id)
                        self.data[caster_id]["total_damage_dealt"] += damage_dealt
                        if target_id:
                            self.data[caster_id]["damage_to"][target_id] += damage_dealt

                    # 统计承受伤害
                    if target_id:
                        self._ensure_target_tracking(target_id)
                        self.data[target_id]["total_damage_taken"] += damage_dealt
                        if caster_id:
                            self.data[target_id]["damage_taken_from"][caster_id] += damage_dealt
                    
        elif isinstance(action, DieAction):
            # --- 死亡动作处理 ---
            target_id = self._get_entity_id(getattr(action, "target", None))
            if target_id:
                self.data[target_id]["death"] += 1
                
                # 追踪击杀者
                # killer_id = self._last_damage_source.get(target_id)
                killer_id = None
                if action.damage_action and action.damage_action.caster:
                    killer_id = self._get_entity_id(action.damage_action.caster)
                
                if killer_id:
                    self._ensure_target_tracking(killer_id)
                    self.data[killer_id]["kill_against"][target_id] += 1
                    
        elif isinstance(action, ReviveAction):
            # --- 复活动作处理 ---
            target_id = self._get_entity_id(getattr(action, "target", None))
            if target_id:
                self.data[target_id]["revive"] += 1
                
        elif isinstance(action, ApplyEffectAction):
            # --- 效果应用动作处理 ---
            # 🔥 统一免疫统计：效果免疫也通过 AttackImmunityEffect 处理
            is_immune, is_attack_immune, is_effect_immune = self._is_immune_action(action, result)

            if is_effect_immune:
                target_id = self._get_entity_id(getattr(action, "target", None))
                if target_id:
                    self._ensure_target_tracking(target_id)
                    self.data[target_id]["effect_immune"] += 1
                    if caster_id:
                        self.data[target_id]["effect_immune_from"][caster_id] += 1
        
        # 其他动作类型可以在此扩展...

    def _is_immune_action(self, action: BattleAction, result: Optional[Dict[str, Any]] = None) -> tuple[bool, bool, bool]:
        """
        检查动作是否触发免疫机制（统一使用 AttackImmunityEffect）。
        
        返回值: 
            is_immune: 是否触发免疫
            is_attack_immune: 是否是攻击免疫（通过 AttackImmunityEffect）
            is_effect_immune: 是否是效果免疫
        """
        is_immune = False
        is_attack_immune = False
        is_effect_immune = False

        # [新增] 检查新的免疫事件系统
        if isinstance(action, DispatchEventAction) and hasattr(action, 'event'):
            event = action.event

            # 检查新的ImmunityEvent
            if isinstance(event, ImmunityEvent):
                is_immune = True
                immunity_type = getattr(event, 'immunity_type', 'unknown')
                if immunity_type == 'attack':
                    is_attack_immune = True
                elif immunity_type == 'effect':
                    is_effect_immune = True
                else:
                    # 默认按攻击免疫处理
                    is_attack_immune = True
                return is_immune, is_attack_immune, is_effect_immune



        if result:
            # 统一免疫标记检查
            is_immune = result.get("is_immune", False)
            is_attack_immune = result.get("is_damage_immune", False) or result.get("immunity_triggered", False)
            is_effect_immune = result.get("is_effect_immune", False)

            # 如果没有明确标记，尝试通过其他方式判断
            if not (is_immune or is_attack_immune or is_effect_immune):
                if isinstance(action, DamageAction):
                    # 检查是否是攻击免疫：原始伤害>0但实际伤害=0
                    original_damage = result.get("original_damage", 0)
                    actual_damage = abs(result.get("hp_change", 0))
                    if original_damage > 0 and actual_damage == 0:
                        is_immune = is_attack_immune = True
                elif isinstance(action, ApplyEffectAction):
                    # 效果应用被阻止
                    is_immune = is_effect_immune = True
            
            # 确保效果免疫时总免疫标记也为True
            if is_effect_immune and not is_immune:
                is_immune = True

        # 检查 action 本身的免疫标记（AttackImmunityEffect 设置）
        if not is_immune and hasattr(action, 'immunity_triggered'):
            is_immune = getattr(action, 'immunity_triggered', False)
            if isinstance(action, DamageAction):
                is_attack_immune = True
            elif isinstance(action, ApplyEffectAction):
                is_effect_immune = True

        # 检查结果中的免疫来源标记
        if result and result.get("immunity_source"):
            immunity_source = result.get("immunity_source", "")
            if "AttackImmunity" in immunity_source or "攻击免疫" in immunity_source:
                is_immune = is_attack_immune = True

        return is_immune, is_attack_immune, is_effect_immune

    def _is_action_complete(self, action: BattleAction) -> bool:
        """
        检查动作是否表示一次完整的行动完成。

        根据8阶段行动系统，只有在第8阶段（行动完成）时才算一次出手。
        """
        # 1. 检查是否是DispatchEventAction且包含ActionCompleteEvent
        if isinstance(action, DispatchEventAction) and hasattr(action, 'event') and action.event:
            event = action.event
            # 直接检查事件类型
            if isinstance(event, ActionCompleteEvent):
                return True
            # 兼容性检查：通过事件名称
            event_name = getattr(event, 'name', '') or type(event).__name__
            if event_name == 'ActionCompleteEvent':
                return True

        # 2. 检查是否是LogAction且包含行动完成的标记
        elif isinstance(action, LogAction):
            message = getattr(action, 'message', '')
            # 检查中文和英文的行动完成标记
            complete_markers = [
                '行动完成', 'action complete', '完成行动',
                '行动结束', 'action finished', '✅',
                '第8阶段', 'stage 8', 'action_complete'
            ]
            for marker in complete_markers:
                if marker in message.lower():
                    return True

        # 3. 检查动作是否有行动完成标记
        elif hasattr(action, 'is_action_complete') and getattr(action, 'is_action_complete', False):
            return True

        # 4. 检查动作是否来自ActionCompleteEvent
        elif hasattr(action, 'source_event'):
            source_event = getattr(action, 'source_event', None)
            if source_event:
                if isinstance(source_event, ActionCompleteEvent):
                    return True
                elif type(source_event).__name__ == 'ActionCompleteEvent':
                    return True

        # 5. 检查动作的阶段标记（新增）
        elif hasattr(action, 'action_stage'):
            stage = getattr(action, 'action_stage', None)
            if stage == 'ACTION_COMPLETE' or stage == 8:
                return True

        return False

    # ---------------------------------------------------------------
    # Helpers
    # ---------------------------------------------------------------
    def _initialize_target_tracking(self) -> None:
        """初始化二级统计字典。"""
        # 需要跟踪目标的统计维度
        target_dimensions = [
            "crit_against",    # 暴击了谁（施放者->目标）
            "dodge_from",      # 闪避了谁（目标->施放者）
            "block_from",      # 格挡了谁（目标->施放者）
            "kill_against",    # 击杀了谁（施放者->目标）
            "immune_from",     # 攻击免疫了谁（目标->施放者）
            "effect_immune_from", # 效果免疫来源
            "damage_immune_from", # 攻击免疫来源（备用，保持兼容性）
            "damage_to",       # 对谁造成了伤害
            "damage_taken_from" # 从谁处承受了伤害
        ]
        
        # 为每个精灵初始化目标跟踪维度
        for spirit_id in self.data:
            for dimension in target_dimensions:
                if dimension not in self.data[spirit_id]:
                    self.data[spirit_id][dimension] = defaultdict(int)
    
    def _ensure_target_tracking(self, spirit_id: str) -> None:
        """确保指定精灵的目标跟踪字典已初始化"""
        target_dimensions = [
            "crit_against", "dodge_from", "block_from", "kill_against", 
            "immune_from", "effect_immune_from", "damage_immune_from",
            "damage_to", "damage_taken_from"
        ]
        
        for dimension in target_dimensions:
            if dimension not in self.data[spirit_id] or not isinstance(self.data[spirit_id][dimension], defaultdict):
                self.data[spirit_id][dimension] = defaultdict(int)
                    
    @staticmethod
    def _get_entity_id(entity: Optional[IBattleEntity]) -> Optional[str]:  # noqa: D401
        return getattr(entity, "id", None) if entity else None