"""游戏核心事件定义。

本模块定义了在游戏逻辑中使用的所有具体事件。
这些事件类作为数据载体，在不同系统之间通过 `EventManager` 传递。
所有事件都应继承自 `GameEvent`。
"""
from __future__ import annotations
from typing import TYPE_CHECKING, Dict, Any, Optional, List
from dataclasses import dataclass, fields, field

if TYPE_CHECKING:
    from ..spirit.spirit import Spirit
    from ..battle.models import BattleState
    from ..action import DamageAction
    from ..interfaces import IBattleEntity, IBattleState
    from ..effect.system import IEffect


@dataclass(slots=True)
class DamageModificationContext:
    """
    一个可变的上下文对象，用于在伤害事件中允许监听器修改伤害值或取消伤害。
    由于事件本身是不可变的，这个对象作为修改伤害的媒介。

    Attributes:
        damage (float): 当前计算出的伤害值，可以被监听器修改。
        is_cancelled (bool): 标记伤害是否已被某个效果或技能取消。
    """
    damage: float
    is_cancelled: bool = False


@dataclass
class GameEvent:
    """所有游戏事件的基类。"""
    @property
    def name(self) -> str:
        """事件的名称，用于在 EventManager 中注册和分发。"""
        return self.__class__.__name__
        
    @property
    def data(self) -> Dict[str, Any]:
        """将事件数据转换为字典，用于向后兼容或序列化。"""
        return {f.name: getattr(self, f.name) for f in fields(self)}


@dataclass(slots=True)
class BattleStartEvent(GameEvent):
    """
    战斗开始时触发。
    
    Attributes:
        battle_state (BattleState): 当前的战斗状态。
    """
    battle_state: BattleState


@dataclass(slots=True)
class BattleEndEvent(GameEvent):
    """
    战斗结束时触发。
    
    Attributes:
        winner (int): 获胜方的队伍ID (0或1)，或-1表示平局。
    """
    winner: int


@dataclass(slots=True)
class RoundStartEvent(GameEvent):
    """
    新回合开始时触发。
    
    Attributes:
        round_num (int): 新的回合数。
    """
    round_num: int


@dataclass(slots=True)
class RoundEndEvent(GameEvent):
    """
    回合结束时触发。
    
    Attributes:
        round_num (int): 刚刚结束的回合数。
    """
    round_num: int


@dataclass(slots=True)
class SpiritFaintEvent(GameEvent):
    """
    当一个精灵单位的生命值降至0或以下，进入濒死状态时触发。

    这是处理所有免死、复活等效果的主要入口。
    
    Attributes:
        spirit (Spirit): 濒死的精灵单位。
    """
    spirit: Spirit


@dataclass(slots=True)
class SpiritReviveEvent(GameEvent):
    """
    当一个精灵单位被复活时触发。
    
    Attributes:
        spirit (Spirit): 被复活的精灵单位。
        health_percent (float): 复活后恢复的生命值百分比 (0.0 到 1.0)。
    """
    spirit: Spirit
    health_percent: float


@dataclass(slots=True)
class DeclareAttackEvent(GameEvent):
    """
    攻击声明事件 - 在攻击执行前声明攻击意图。
    
    此事件在攻击流程的最开始触发，允许先制攻击、反制等机制响应。
    可以在此阶段取消或修改攻击。
    
    Attributes:
        attacker (Spirit): 发起攻击的精灵
        target (Spirit): 攻击目标精灵
        skill_name (str): 使用的技能名称
        attack_cancelled (bool): 攻击是否被取消
        can_be_countered (bool): 是否可以被反制
    """
    attacker: "Spirit"
    target: "Spirit"
    skill_name: Optional[str] = None
    attack_cancelled: bool = False
    can_be_countered: bool = True


@dataclass(slots=True)
class BeforeAttackEvent(GameEvent):
    """
    在攻击执行前触发，允许免疫效果完全阻止攻击及其所有效果。

    此事件在攻击声明后、伤害计算前触发，主要用于：
    - 免疫效果阻止攻击
    - 攻击前的状态检查
    - 攻击拦截机制

    注意：此时伤害、暴击等信息尚未计算，不应包含这些参数。

    Attributes:
        attacker: 攻击来源精灵（与其他攻击事件保持一致）
        target: 攻击目标精灵
        skill_name: 技能名称
        attack_blocked: 攻击是否被阻止（由免疫效果设置）
        source_effect: 记录是哪个效果阻止了攻击
    """
    attacker: "Spirit"  # 修复：与其他攻击事件保持一致
    target: "Spirit"  # 修复：与其他攻击事件保持一致
    skill_name: Optional[str] = None
    attack_blocked: bool = False
    source_effect: Optional[IEffect] = None  # 记录是哪个效果阻止了攻击


@dataclass(slots=True)
class ModifyAttackEvent(GameEvent):
    """
    攻击属性修改事件 - 在伤害计算前修改攻击属性。
    
    此事件在攻击前检查完成后、伤害计算前触发，允许效果修改本次攻击的属性。
    如暴击率提升、伤害加成、攻击类型改变等。
    
    Attributes:
        attacker (Spirit): 攻击来源精灵
        target (Spirit): 攻击目标精灵
        skill_name (str): 技能名称
        crit_rate_bonus (float): 暴击率加成 (0.0-1.0)
        damage_multiplier (float): 伤害倍率 (默认1.0)
        hit_rate_bonus (float): 命中率加成
        damage_type_override (str): 伤害类型覆盖
        is_guaranteed_crit (bool): 是否必定暴击
        is_guaranteed_hit (bool): 是否必定命中
        additional_effects (List): 本次攻击额外附加的效果
    """
    attacker: "Spirit"
    target: "Spirit"
    skill_name: Optional[str] = None
    crit_rate_bonus: float = 0.0
    damage_multiplier: float = 1.0
    hit_rate_bonus: float = 0.0
    damage_type_override: Optional[str] = None
    is_guaranteed_crit: bool = False
    is_guaranteed_hit: bool = False
    additional_effects: List[Any] = field(default_factory=list)
    
    def __post_init__(self):
        if self.additional_effects is None:
            self.additional_effects = []


@dataclass(slots=True)
class ActionStartEvent(GameEvent):
    """
    行动开始事件 - 精灵开始行动时触发。
    
    此事件在精灵轮到行动时的最开始触发，用于处理行动开始相关的效果。
    
    Attributes:
        actor (Spirit): 行动的精灵
        turn_number (int): 当前回合数
        action_cancelled (bool): 行动是否被取消
        extra_turn (bool): 是否为额外回合
    """
    actor: "IBattleEntity"
    turn_number: int
    action_cancelled: bool = False
    extra_turn: bool = False


@dataclass(slots=True)
class BeforeActionEvent(GameEvent):
    """
    行动前检查事件 - 检查精灵是否能够行动。
    
    此事件用于处理控制效果，如眩晕、沉默、冰冻等。
    
    Attributes:
        actor (Spirit): 行动的精灵
        action_blocked (bool): 行动是否被阻止
        block_reason (str): 阻止原因
        can_use_skills (bool): 是否可以使用技能
        can_move (bool): 是否可以移动
    """
    actor: "IBattleEntity"
    action_blocked: bool = False
    block_reason: Optional[str] = None
    can_use_skills: bool = True
    can_move: bool = True


@dataclass(slots=True)
class ActionDecisionEvent(GameEvent):
    """
    行动决策事件 - 进行AI决策和策略选择。
    
    此事件用于AI决策，选择行动策略和优先级。
    
    Attributes:
        actor (Spirit): 行动的精灵
        available_actions (List[str]): 可用行动类型
        decision_override (str): 决策覆盖
        priority_targets (List): 优先目标列表
        strategy (str): 行动策略
    """
    actor: "IBattleEntity"
    available_actions: List[str]
    decision_override: Optional[str] = None
    priority_targets: List["Spirit"] = field(default_factory=list)
    strategy: str = "default"
    
    def __post_init__(self):
        if self.priority_targets is None:
            self.priority_targets = []


@dataclass(slots=True)
class ModifyActionEvent(GameEvent):
    """
    行动属性修改事件 - 修改行动的属性。
    
    此事件用于修改行动速度、技能替换等属性。
    
    Attributes:
        actor (Spirit): 行动的精灵
        speed_multiplier (float): 行动速度倍率
        skill_replacement (str): 技能替换
        extra_actions (int): 额外行动次数
        action_priority (int): 行动优先级修正
        forced_skill (str): 强制使用的技能
    """
    actor: "IBattleEntity"
    speed_multiplier: float = 1.0
    skill_replacement: Optional[str] = None
    extra_actions: int = 0
    action_priority: int = 0
    forced_skill: Optional[str] = None
    immediate_action: bool = False      # 是否立即行动
    queue_extra_turn: bool = False      # 是否排队额外回合


@dataclass(slots=True)
class ActionCompleteEvent(GameEvent):
    """
    行动完成事件 - 行动完成后触发。
    
    此事件在精灵完成行动后触发，用于处理行动后效果。
    
    Attributes:
        actor (Spirit): 行动的精灵
        skill_used (str): 使用的技能名称
        targets_hit (List): 命中的目标列表
        action_successful (bool): 行动是否成功
        damage_dealt (float): 造成的总伤害
        effects_applied (List[str]): 应用的效果列表
    """
    actor: "IBattleEntity"
    skill_used: Optional[str] = None
    targets_hit: List["Spirit"] = field(default_factory=list)
    action_successful: bool = False
    damage_dealt: float = 0.0
    effects_applied: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if self.targets_hit is None:
            self.targets_hit = []
        if self.effects_applied is None:
            self.effects_applied = []


@dataclass(slots=True)
class AfterDamageEvent(GameEvent):
    """
    在伤害被实际应用到目标之后触发。

    此事件在目标生命值减少之后触发，用于处理反击、治疗触发等机制。

    Attributes:
        attacker (Spirit): 伤害的来源精灵。
        target (Spirit): 承受伤害的目标精灵。
        damage_dealt (float): 实际造成的伤害值。
        original_damage (float): 原始计算的伤害值。
        is_critical (bool): 是否暴击。
        is_ultimate (bool): 是否超杀。
        skill_name (str): 技能名称。
        target_died (bool): 目标是否因此次伤害死亡。
        damage_action: 造成此次伤害的动作（用于判断伤害类型等）。
    """
    attacker: "Spirit"
    target: "Spirit"
    damage_dealt: float
    original_damage: float = 0.0
    is_critical: bool = False
    is_ultimate: bool = False
    skill_name: Optional[str] = None
    target_died: bool = False
    damage_action: Optional[Any] = None


@dataclass(slots=True)
class ApplyAttackEffectsEvent(GameEvent):
    """
    攻击效果应用事件 - 在伤害后应用攻击附带的效果。
    
    此事件用于处理攻击技能附带的状态效果，如中毒、眩晕等。
    
    Attributes:
        attacker (Spirit): 攻击来源精灵
        target (Spirit): 攻击目标精灵
        effects_to_apply (List): 待应用的效果列表
        effects_blocked (bool): 效果是否被阻止
        skill_name (str): 技能名称
    """
    attacker: "Spirit"
    target: "Spirit"
    effects_to_apply: List[Any]
    effects_blocked: bool = False
    skill_name: Optional[str] = None


@dataclass(slots=True)
class AfterAttackEvent(GameEvent):
    """
    攻击后事件 - 在整个攻击流程完成后触发。
    
    此事件在攻击的所有阶段完成后触发，用于处理连击、追击等机制。
    
    Attributes:
        attacker (Spirit): 攻击来源精灵
        target (Spirit): 攻击目标精灵
        attack_successful (bool): 攻击是否成功
        damage_dealt (float): 实际造成的伤害
        effects_applied (List[str]): 成功应用的效果名称列表
        target_died (bool): 目标是否死亡
        skill_name (str): 技能名称
    """
    attacker: "Spirit"
    target: "Spirit"
    attack_successful: bool
    damage_dealt: float
    effects_applied: List[str]
    target_died: bool = False
    skill_name: Optional[str] = None


@dataclass(slots=True)
class BeforeDamageAppliedEvent(GameEvent):
    """
    在伤害被实际应用到目标之前触发。

    此事件在伤害数值计算之后，但在目标生命值减少之前触发。
    它允许监听器通过修改上下文来改变或取消最终的伤害。

    Attributes:
        caster (Spirit): 伤害的来源精灵。
        target (Spirit): 承受伤害的目标精灵。
        action (DamageAction): 导致伤害的原始动作。
        context (DamageModificationContext): 一个可变对象，用于在监听器之间传递伤害修改结果。
    """
    caster: Spirit
    target: Spirit
    action: "DamageAction"
    context: "DamageModificationContext" 


@dataclass(slots=True)
class AfterActionEndEvent(GameEvent):
    """
    在一个单位完成其行动后触发。
    
    Attributes:
        spirit (Spirit): 完成行动的精灵。
    """
    spirit: Spirit


@dataclass(slots=True)
class EffectAppliedEvent(GameEvent):
    """一个效果被施加到单位身上后触发。"""
    effect: "IEffect"
    target: "IBattleEntity"
    battle_state: "IBattleState"



@dataclass(slots=True)
class SpiritSummonedEvent(GameEvent):
    """A spirit has been summoned to the battlefield."""
    spirit: "Spirit"
    battle_state: "BattleState"


@dataclass(slots=True)
class CommuningTriggeredEvent(GameEvent):
    """
    当一个单位成功触发通灵时触发。

    Attributes:
        spirit (Spirit): 触发通灵的精灵。
        communing_count (int): 这是该精灵第几次通灵。
    """
    spirit: Spirit
    communing_count: int


@dataclass(slots=True)
class ImmunityEvent(GameEvent):
    """
    当免疫效果成功阻止攻击或效果时触发。

    Attributes:
        target: 受到保护的目标精灵
        source_effect_id: 提供免疫的效果ID
        immunity_type: 免疫类型（attack, effect, damage等）
        blocked_action: 被阻止的动作类型
    """
    target: "IBattleEntity"
    source_effect_id: str
    immunity_type: str = "attack"
    blocked_action: Optional[str] = None