#!/usr/bin/env python3
"""
测试递归执行问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_recursive_execution():
    """测试递归执行问题"""
    print("🔧 测试递归执行问题...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 获取战斗引擎的动作执行器
        executor = engine.action_executor
        
        # 监控所有动作执行
        original_execute_single = executor._execute_single_action
        original_execute_actions = executor.execute_actions
        
        executed_actions = []
        execution_stack = []
        
        def monitored_execute_single(action):
            """监控单个动作执行"""
            action_type = type(action).__name__
            execution_stack.append(action_type)
            
            executed_actions.append({
                'action_type': action_type,
                'action': action,
                'stack_depth': len(execution_stack)
            })
            
            indent = "  " * len(execution_stack)
            print(f"{indent}🎯 执行动作: {action_type} (深度: {len(execution_stack)})")
            
            # 调用原始方法
            try:
                result = original_execute_single(action)
                
                if result:
                    print(f"{indent}  生成新动作数量: {len(result)}")
                    for i, new_action in enumerate(result):
                        new_action_type = type(new_action).__name__
                        print(f"{indent}    新动作{i+1}: {new_action_type}")
                else:
                    print(f"{indent}  生成新动作数量: 0")
                
                execution_stack.pop()
                return result
                
            except Exception as e:
                execution_stack.pop()
                print(f"{indent}  ❌ 执行失败: {e}")
                raise
        
        def monitored_execute_actions(actions):
            """监控动作列表执行"""
            if not actions:
                return
            
            indent = "  " * len(execution_stack)
            print(f"{indent}📋 执行动作列表: {len(actions)} 个动作")
            
            for i, action in enumerate(actions):
                action_type = type(action).__name__
                print(f"{indent}  动作{i+1}: {action_type}")
            
            # 调用原始方法
            return original_execute_actions(actions)
        
        # 替换执行方法
        executor._execute_single_action = monitored_execute_single
        executor.execute_actions = monitored_execute_actions
        
        # 手动创建并执行DamageAction
        print(f"\n📋 手动测试DamageAction:")
        
        from core.action import DamageAction
        
        # 创建伤害动作
        damage_action = DamageAction(
            caster=fuyao_spirit,
            target=other_spirit,
            damage_value=1000,
            skill_name="咒缚锁妖"
        )
        
        print(f"  创建DamageAction: {type(damage_action).__name__}")
        print(f"  执行前目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 执行动作
        print(f"  执行DamageAction...")
        executor.execute_actions([damage_action])
        
        print(f"  执行后目标效果数量: {len(other_spirit.effect_manager.effects)}")
        
        # 显示目标的效果
        if len(other_spirit.effect_manager.effects) > 0:
            print(f"  目标效果:")
            for effect_id, effect in other_spirit.effect_manager.effects.items():
                effect_name = getattr(effect, 'name', 'Unknown')
                print(f"    - {effect_name}")
        
        # 显示执行的动作
        print(f"\n📋 执行的动作总结:")
        print(f"  总动作数量: {len(executed_actions)}")
        
        max_depth = max(action['stack_depth'] for action in executed_actions) if executed_actions else 0
        print(f"  最大递归深度: {max_depth}")
        
        # 按深度分组显示
        for depth in range(1, max_depth + 1):
            depth_actions = [a for a in executed_actions if a['stack_depth'] == depth]
            print(f"  深度{depth}: {len(depth_actions)} 个动作")
            for action_info in depth_actions:
                action_type = action_info['action_type']
                print(f"    - {action_type}")
        
        # 检查是否有ApplyEffectAction被执行
        apply_effect_actions = [a for a in executed_actions if a['action_type'] == 'ApplyEffectAction']
        print(f"  ApplyEffectAction执行数量: {len(apply_effect_actions)}")
        
        # 判断测试结果
        target_effects_after = len(other_spirit.effect_manager.effects)
        apply_effect_count = len(apply_effect_actions)
        
        if target_effects_after > 0 and apply_effect_count > 0:
            print(f"\n✅ 递归执行测试成功！")
            print(f"  - ApplyEffectAction正确执行: {apply_effect_count} 个")
            print(f"  - 效果成功应用: 目标获得了效果")
            print(f"  - 最大递归深度: {max_depth}")
            return True
        elif apply_effect_count > 0:
            print(f"\n⚠️ 递归执行部分成功")
            print(f"  - ApplyEffectAction已执行: {apply_effect_count} 个")
            print(f"  - 但效果没有应用: 目标没有获得效果")
            print(f"  - 最大递归深度: {max_depth}")
            return False
        else:
            print(f"\n❌ 递归执行测试失败")
            print(f"  - ApplyEffectAction没有被执行")
            print(f"  - 最大递归深度: {max_depth}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 递归执行测试")
    print("="*60)
    
    result = test_recursive_execution()
    
    print("\n" + "="*60)
    if result:
        print("✅ 递归执行测试成功")
        print("DamageAction中的递归调用正常工作")
    else:
        print("❌ 递归执行测试失败")

if __name__ == "__main__":
    main()
