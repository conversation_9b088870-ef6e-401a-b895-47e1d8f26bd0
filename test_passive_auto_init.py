#!/usr/bin/env python3
"""
测试被动效果自动初始化
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_passive_auto_init():
    """测试被动效果自动初始化"""
    print("🔧 测试被动效果自动初始化...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建伏妖精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        fuyao_spirit = None
        for spirit_id in available_spirits:
            if "伏妖" in spirit_id:
                fuyao_spirit = spirit_service.create_spirit(spirit_id, team=0, position=(1, 1))
                print(f"✅ 找到伏妖精灵: {fuyao_spirit.name}")
                break
        
        if not fuyao_spirit:
            print("❌ 未找到伏妖精灵")
            return False
        
        # 检查精灵创建后的被动效果
        print(f"\n📋 检查精灵创建后的被动效果:")
        
        if hasattr(fuyao_spirit, 'effect_manager') and fuyao_spirit.effect_manager:
            effects_count = len(fuyao_spirit.effect_manager.effects)
            print(f"  效果管理器存在: ✅")
            print(f"  当前效果数量: {effects_count}")
            
            if effects_count > 0:
                print(f"  ✅ 精灵创建时已自动添加被动效果！")
                
                # 显示所有效果
                for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                    effect_name = getattr(effect, 'name', 'Unknown')
                    effect_type = type(effect).__name__
                    print(f"    - {effect_name} ({effect_type})")
                
                return True
            else:
                print(f"  ❌ 精灵创建时没有自动添加被动效果")
                
                # 尝试手动调用初始化
                print(f"\n  尝试手动调用被动效果初始化:")
                try:
                    fuyao_spirit.initialize_passive_effects()
                    
                    effects_count_after = len(fuyao_spirit.effect_manager.effects)
                    print(f"    手动初始化后效果数量: {effects_count_after}")
                    
                    if effects_count_after > 0:
                        print(f"    ✅ 手动初始化成功！")
                        
                        # 显示所有效果
                        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
                            effect_name = getattr(effect, 'name', 'Unknown')
                            effect_type = type(effect).__name__
                            print(f"      - {effect_name} ({effect_type})")
                        
                        return True
                    else:
                        print(f"    ❌ 手动初始化也失败")
                        return False
                        
                except Exception as e:
                    print(f"    ❌ 手动初始化失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
        else:
            print(f"  ❌ 效果管理器不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 被动效果自动初始化测试")
    print("="*50)
    
    result = test_passive_auto_init()
    
    print("\n" + "="*50)
    if result:
        print("✅ 被动效果自动初始化测试成功")
        print("精灵创建时正确添加了被动效果")
    else:
        print("❌ 被动效果自动初始化测试失败")

if __name__ == "__main__":
    main()
