# 灵活的技能系统 - 核心模块已完善
"""
灵活的技能系统

使用策略模式和组合模式构建可扩展的技能系统。
"""
from __future__ import annotations
from typing import List, Dict, Any, Optional, Callable, Protocol, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
import uuid

from ..interfaces import IBattleEntity, IBattleState, IAction
from ..exceptions import SkillException, InsufficientEnergyException, InvalidTargetException
from ..performance import monitor_skill_performance
from ..cache import cache_skill_result
from ..logging import skill_logger


# === 技能组件接口 ===

class ISkillComponent(Protocol):
    """技能组件接口"""
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:  
        """执行技能组件"""
        ...


class ISkillCondition(Protocol):
    """技能条件接口"""
    
    def check(
        self, 
        caster: IBattleEntity, 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> bool:
        """检查条件是否满足"""
        ...


class ITargetSelector(Protocol):
    """目标选择器接口"""
    
    def select_targets(
        self, 
        caster: IBattleEntity, 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[IBattleEntity]:
        """选择目标"""
        ...


# === 技能元数据 ===

class CastType(Enum):
    """技能施放类型枚举。"""
    ACTIVE = "ACTIVE"  # 主动
    PASSIVE = "PASSIVE"  # 被动
    REACTION = "REACTION"  # 触发/反应


@dataclass
class SkillMetadata:
    """技能元数据"""
    name: str
    description: str = ""
    category: str = "ATTACK"
    cast_type: str = "ACTIVE"
    energy_cost: int = 0
    cooldown: int = 0
    max_level: int = 1
    tags: List[str] = field(default_factory=list)
    listened_events: List[str] = field(default_factory=list) # 被动技能监听的事件


@dataclass
class SkillContext:
    """技能执行上下文"""
    skill_level: int = 1
    power_multiplier: float = 1.0
    is_critical: bool = False
    is_ultimate: bool = False # 新增：标记是否为超杀
    is_counter_attack: bool = False
    additional_data: Dict[str, Any] = field(default_factory=dict)


# === 技能条件实现 ===

class EnergyCondition:
    """能量条件"""
    
    def __init__(self, required_energy: int):
        self.required_energy = required_energy
    
    def check(
        self, 
        caster: IBattleEntity, 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> bool:
        return caster.energy >= self.required_energy


class HealthPercentCondition:
    """血量百分比条件"""
    
    def __init__(self, threshold: float, above: bool = True):
        self.threshold = threshold
        self.above = above
    
    def check(
        self, 
        caster: IBattleEntity, 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> bool:
        hp_percent = caster.current_hp / caster.max_hp
        if self.above:
            return hp_percent >= self.threshold
        else:
            return hp_percent <= self.threshold


class CooldownCondition:
    """冷却时间条件"""
    
    def __init__(self):
        self.last_used_round: Dict[str, int] = {}
    
    def check(
        self, 
        caster: IBattleEntity, 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> bool:
        skill_id = context.additional_data.get('skill_id', '')
        cooldown = context.additional_data.get('cooldown', 0)
        current_round = battle_state.round_num
        
        last_used = self.last_used_round.get(f"{caster.id}_{skill_id}", -999)
        return current_round - last_used >= cooldown


# === 目标选择器实现 ===
# 🔧 重构完成：目标选择器已迁移到统一模块，这里直接导入

# 从统一模块导入所有选择器
from ..targeting.selectors import (
    SingleEnemySelector,
    AllEnemiesSelector,
    AllAlliesSelector,
    SelfSelector,
    LowestHpAllySelector
)


# === 技能组件实现 ===

class DamageComponent:
    """伤害组件"""
    
    def __init__(
        self, 
        power_multiplier: float = 1.0,
        damage_type: str = "PHYSICAL",
    ):
        self.power_multiplier = power_multiplier
        self.damage_type = damage_type
    
    def execute(
        self,
        caster: IBattleEntity,
        targets: List[IBattleEntity],
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from ..action import DamageAction, DamageType, ConsumeEnergyAction, LogAction

        actions = []

        # 如果是超杀技能，先处理气势消耗
        if context.is_ultimate:
            # 获取超杀阈值
            ultimate_threshold = 150  # 默认阈值，可以从精灵获取
            try:
                if hasattr(caster, 'get_ultimate_threshold'):
                    ultimate_threshold = caster.get_ultimate_threshold()
            except:
                pass

            # 获取当前气势
            current_energy = getattr(caster, 'energy', 0)



            # 检查是否可以释放
            if current_energy >= ultimate_threshold:
                # 计算溢出气势
                overflow_energy = max(0, current_energy - ultimate_threshold)

                # 创建气势消耗动作
                consume_action = ConsumeEnergyAction(
                    caster=caster,
                    target=caster,
                    amount=current_energy  # 消耗所有气势
                )
                actions.append(consume_action)

                # 添加超杀释放日志
                skill_name = context.additional_data.get('skill_name', '超杀技能')
                log_message = (
                    f"💥 [{skill_name}] {getattr(caster, 'name', 'Unknown')} 消耗所有气势({current_energy})释放超杀技能！"
                    f"溢出气势: {overflow_energy}"
                )
                log_action = LogAction(caster=caster, message=log_message)
                actions.append(log_action)

                # 对每个目标创建增强的伤害动作
                for target in targets:
                    if target.is_alive:
                        damage_action = DamageAction(
                            caster=caster,
                            target=target,
                            power_multiplier=self.power_multiplier,
                            damage_value=None,
                            damage_type=DamageType.PHYSICAL if self.damage_type.upper() == "PHYSICAL" else DamageType.MAGIC,
                            is_critical=context.is_critical,
                            is_ultimate=context.is_ultimate,
                            skill_name=context.additional_data.get('skill_name'),
                            overflow_energy=overflow_energy,
                            ultimate_threshold=ultimate_threshold
                        )
                        actions.append(damage_action)
            else:
                # 气势不足，不能释放超杀技能
                return []
        else:
            # 普通技能，正常处理
            for target in targets:
                if target.is_alive:
                    damage_action = DamageAction(
                        caster=caster,
                        target=target,
                        power_multiplier=self.power_multiplier,
                        damage_value=None,
                        damage_type=DamageType.PHYSICAL if self.damage_type.upper() == "PHYSICAL" else DamageType.MAGIC,
                        is_critical=context.is_critical,
                        is_ultimate=context.is_ultimate,
                        skill_name=context.additional_data.get('skill_name')
                    )
                    actions.append(damage_action)

        return actions
    
    


class HealingComponent:
    """治疗组件"""
    
    def __init__(self, base_healing: float, scale_with_attack: bool = False):
        self.base_healing = base_healing
        self.scale_with_attack = scale_with_attack
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from ..action import SetHPAction, LogAction
        
        actions = []
        
        # 计算治疗量
        healing = self.base_healing
        if self.scale_with_attack:
            healing += caster.get_attribute('attack') * 0.3
        
        # 应用技能等级
        skill_context = context
        healing *= (1 + (skill_context.skill_level - 1) * 0.15)
        
        # 对每个目标进行治疗
        for target in targets:
            if target.is_alive:
                new_hp = min(target.max_hp, target.current_hp + healing)
                actual_healing = new_hp - target.current_hp
                
                if actual_healing > 0:
                    actions.append(SetHPAction(
                        caster=caster,
                        target=target,
                        hp=int(new_hp)
                    ))
                    actions.append(LogAction(
                        caster=caster,
                        message=f"{target.name} 恢复 {int(actual_healing)} 点生命值"
                    ))
        
        return actions


class SkillEffectComponent:
    """技能效果组件"""
    
    def __init__(self, effect_factory: Callable[[], Any]):
        self.effect_factory = effect_factory
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from ..action import ApplyEffectAction
        
        actions = []
        
        for target in targets:
            if target.is_alive:
                effect = self.effect_factory()
                effect.caster = caster
                
                actions.append(ApplyEffectAction(
                    caster=caster,
                    target=target,
                    effect=effect
                ))
        
        return actions


class SkillEnergyComponent:
    """技能能量操作组件"""
    
    def __init__(self, energy_change: int):
        self.energy_change = energy_change
    
    def execute(
        self, 
        caster: IBattleEntity, 
        targets: List[IBattleEntity], 
        battle_state: IBattleState,
        context: "SkillContext"
    ) -> List[Any]:
        from ..action import ConsumeEnergyAction, LogAction
        
        actions = []
        
        if self.energy_change < 0:
            # 消耗能量
            actions.append(ConsumeEnergyAction(
                caster=caster,
                target=caster,
                amount=abs(self.energy_change)
            ))
        else:
            # 获得能量 - 使用组件系统
            for target in targets:
                if hasattr(target, 'gain_energy'):
                    actual_gain = target.gain_energy(self.energy_change)
                    actions.append(LogAction(
                        caster=caster,
                        message=f"{target.name} 获得 {actual_gain} 点能量"
                    ))
        
        return actions


# === 灵活技能类 ===

class Skill:
    """
    灵活的技能实现
    
    通过组合不同的组件和策略来构建技能。
    """
    
    def __init__(
        self,
        metadata: SkillMetadata,
        target_selector: ITargetSelector,
        components: List[ISkillComponent],
        conditions: Optional[List[ISkillCondition]] = None,
        owner: Optional[IBattleEntity] = None
    ):
        self.id = str(uuid.uuid4())
        self.metadata = metadata
        self.target_selector = target_selector
        self.components = components
        self.conditions = conditions or []
        self.owner = owner
        self.current_cooldown = 0
        self.current_level = 1
        self.last_used_round = -999
    
    @property
    def name(self) -> str:
        return self.metadata.name
    
    @property
    def energy_cost(self) -> int:
        return self.metadata.energy_cost
    
    def can_cast(self, battle_state: IBattleState) -> bool:
        """检查是否可以释放技能"""
        if not self.owner:
            return False

        # 为条件检查创建上下文
        context = SkillContext(
            skill_level=self.current_level,
            additional_data={
                'skill_id': self.id,
                'cooldown': self.metadata.cooldown
            }
        )

        # 检查所有条件
        for condition in self.conditions:
            if not condition.check(self.owner, battle_state, context):
                return False
        return True

    def _get_context_for_check(self) -> Dict[str, Any]:
        return {
            'skill_id': self.id,
            'cooldown': self.metadata.cooldown
        }

    @monitor_skill_performance
    @cache_skill_result
    def cast(self, battle_state: IBattleState, forced_targets: Optional[List[IBattleEntity]] = None) -> List[Any]:
        """
        释放技能

        1. 检查条件
        2. 选择目标
        3. 执行所有组件
        4. 返回动作列表
        """
        if not self.can_cast(battle_state) or not self.owner:
            return []

        # 选择目标
        context = SkillContext(
            skill_level=self.current_level,
            additional_data={'skill_name': self.metadata.name}
        )
        targets = forced_targets if forced_targets is not None else self.target_selector.select_targets(self.owner, battle_state, context)

        if not targets:
            return [] # or raise InvalidTargetException("No valid targets found")
            
        all_actions = []
        # 创建技能执行上下文，包含暴击和超杀信息
        context = SkillContext(
            skill_level=self.current_level,
            is_critical=self._check_critical(),
            is_ultimate=self.metadata.cast_type == "ULTIMATE",
            additional_data={'skill_name': self.metadata.name}
        )

        # 执行所有组件
        for component in self.components:
            # 将完整的 context 传递给组件
            component_actions = component.execute(self.owner, targets, battle_state, context)
            all_actions.extend(component_actions)

        # 应用冷却
        self.current_cooldown = self.metadata.cooldown

        return all_actions

    def _execute_components(self, targets: List[IBattleEntity], battle_state: IBattleState) -> List[Any]:
        """执行所有技能组件。"""
        actions = []
        context = SkillContext(
            skill_level=self.current_level,
            is_critical=self._check_critical(),
            is_ultimate=self.metadata.cast_type == "ULTIMATE",
            additional_data={'skill_name': self.metadata.name}
        )
        assert self.owner is not None
        for component in self.components:
            actions.extend(component.execute(self.owner, targets, battle_state, context))
        return actions

    def on_event(self, event_name: str, event_data: Dict[str, Any], battle_state: IBattleState) -> List[Any]:
        """
        被动技能的事件处理入口 - 已废弃
        
        事件监听已移至Effect内部，此方法不再使用。
        """
        # 被动技能不再在Skill级别处理事件
        # 所有事件处理已移至Effect内部的on_event方法
        return []

    def on_round_end(self) -> None:
        """在回合结束时调用，用于减少冷却时间。"""
        if self.current_cooldown > 0:
            self.current_cooldown -= 1

    def _check_critical(self) -> bool:
        """检查是否暴击"""
        if self.owner:
            crit_rate = self.owner.get_attribute('crit_rate')
            import random
            return random.random() < crit_rate
        return False
    
    def level_up(self) -> bool:
        """升级技能"""
        if self.current_level < self.metadata.max_level:
            self.current_level += 1
            return True
        return False


# === 技能工厂 ===

class SkillFactory:
    """技能工厂，用于创建常用的技能组合"""
    @staticmethod
    def create_basic_attack(base_damage: float = 100) -> "Skill":  # noqa: D401
        """Create a simple single-target basic attack used by tests."""
        metadata = SkillMetadata(name="Basic Attack", energy_cost=0)
        return Skill(
            metadata=metadata,
            target_selector=SingleEnemySelector(),
            components=[DamageComponent(power_multiplier=1.0)], # 基础攻击威力为100%
        )


# Provide backward compatibility alias expected in tests
class FlexibleSkillFactory(SkillFactory):
    """Backward-compat factory alias for tests."""


__all__ = [
    'ISkillComponent',
    'ISkillCondition',
    'ITargetSelector',
    'SkillMetadata',
    'SkillContext',
    'EnergyCondition',
    'HealthPercentCondition',
    'CooldownCondition',
    'SingleEnemySelector',
    'AllEnemiesSelector',
    'AllAlliesSelector',
    'SelfSelector',
    'LowestHpAllySelector',
    'DamageComponent',
    'HealingComponent',
    'SkillEffectComponent',
    'SkillEnergyComponent',
    'Skill',
    'SkillFactory'
]