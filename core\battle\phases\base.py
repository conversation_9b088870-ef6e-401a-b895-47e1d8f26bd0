"""Base definitions for battle phases."""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import List, Any

from ..models import BattleState  # type: ignore


class IBattlePhase(ABC):
    """战斗阶段接口 (Interface for a single battle phase)."""

    @abstractmethod
    def execute(self, battle_state: BattleState) -> List[Any]:
        """Execute phase and return a list of actions to be processed."""
        raise NotImplementedError 