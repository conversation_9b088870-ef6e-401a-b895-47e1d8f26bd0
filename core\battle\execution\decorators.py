"""
Action处理器装饰器

提供统一的类型检查、错误处理、性能监控等装饰器功能。
这些装饰器可以应用到现有的handler函数上，提供增强功能。
"""
from __future__ import annotations

import time
import traceback
from typing import Callable, Optional, List
from functools import wraps

from ...action import BattleAction
from ...spirit.spirit import Spirit
from ...logging import battle_logger


def validate_target(alive_required: bool = True, spirit_type_required: bool = True):
    """
    目标验证装饰器
    
    Args:
        alive_required: 是否要求目标存活
        spirit_type_required: 是否要求目标是RefactoredSpirit类型
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            # 检查是否有target属性
            if not hasattr(action, 'target'):
                return func(self, action)
            
            target = getattr(action, 'target')
            if target is None:
                return func(self, action)
            
            # 类型检查
            if spirit_type_required and not isinstance(target, Spirit):
                battle_logger.warning(f"Action target is not Spirit: {type(target)}")
                return None
            
            # 存活状态检查
            if alive_required and hasattr(target, 'is_alive') and not target.is_alive:
                battle_logger.debug(f"Action target is not alive: {getattr(target, 'name', 'Unknown')}")
                return None
            
            return func(self, action)
        return wrapper
    return decorator


def validate_caster(alive_required: bool = False, spirit_type_required: bool = True):
    """
    施法者验证装饰器
    
    Args:
        alive_required: 是否要求施法者存活
        spirit_type_required: 是否要求施法者是RefactoredSpirit类型
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            caster = getattr(action, 'caster', None)
            if caster is None:
                return func(self, action)
            
            # 类型检查
            if spirit_type_required and not isinstance(caster, Spirit):
                battle_logger.warning(f"Action caster is not Spirit: {type(caster)}")
                return None
            
            # 存活状态检查
            if alive_required and hasattr(caster, 'is_alive') and not caster.is_alive:
                battle_logger.debug(f"Action caster is not alive: {getattr(caster, 'name', 'Unknown')}")
                return None
            
            return func(self, action)
        return wrapper
    return decorator


def safe_execute(log_errors: bool = True, return_on_error: Optional[List[BattleAction]] = None):
    """
    安全执行装饰器
    
    Args:
        log_errors: 是否记录错误
        return_on_error: 发生错误时的返回值
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            try:
                return func(self, action)
            except Exception as e:
                action_type = type(action).__name__
                func_name = func.__name__
                
                if log_errors:
                    error_msg = f"Error in {func_name} handling {action_type}: {str(e)}"
                    battle_logger.error(error_msg)
                    
                    # 添加到战斗日志
                    if hasattr(self, 'battle_log'):
                        self.battle_log.append({
                            "level": "ERROR",
                            "message": error_msg,
                            "action_type": action_type,
                            "handler": func_name,
                            "round": getattr(self.battle_state, 'round_num', 0),
                            "traceback": traceback.format_exc()
                        })
                
                return return_on_error
        return wrapper
    return decorator


def monitor_performance(log_slow_actions: bool = True, slow_threshold: float = 0.1):
    """
    性能监控装饰器
    
    Args:
        log_slow_actions: 是否记录慢速Action
        slow_threshold: 慢速Action的阈值（秒）
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            start_time = time.time()
            
            try:
                result = func(self, action)
                execution_time = time.time() - start_time
                
                # 记录性能统计
                if hasattr(self, 'execution_stats'):
                    action_type = type(action).__name__
                    if 'action_performance' not in self.execution_stats:
                        self.execution_stats['action_performance'] = {}
                    
                    if action_type not in self.execution_stats['action_performance']:
                        self.execution_stats['action_performance'][action_type] = {
                            'count': 0,
                            'total_time': 0.0,
                            'avg_time': 0.0,
                            'max_time': 0.0,
                            'min_time': float('inf')
                        }
                    
                    stats = self.execution_stats['action_performance'][action_type]
                    stats['count'] += 1
                    stats['total_time'] += execution_time
                    stats['avg_time'] = stats['total_time'] / stats['count']
                    stats['max_time'] = max(stats['max_time'], execution_time)
                    stats['min_time'] = min(stats['min_time'], execution_time)
                
                # 记录慢速Action
                if log_slow_actions and execution_time > slow_threshold:
                    battle_logger.warning(
                        f"Slow action detected: {type(action).__name__} "
                        f"took {execution_time:.4f}s (threshold: {slow_threshold}s)"
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                battle_logger.error(f"Action failed after {execution_time:.4f}s: {str(e)}")
                raise
                
        return wrapper
    return decorator


def validate_required_attributes(*required_attrs: str):
    """
    必需属性验证装饰器
    
    Args:
        *required_attrs: 必需的属性名列表
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            action_type = type(action).__name__
            
            for attr_name in required_attrs:
                if not hasattr(action, attr_name):
                    battle_logger.error(f"{action_type} missing required attribute: {attr_name}")
                    return None
                
                attr_value = getattr(action, attr_name)
                if attr_value is None:
                    battle_logger.error(f"{action_type} has None value for required attribute: {attr_name}")
                    return None
            
            return func(self, action)
        return wrapper
    return decorator


def log_action_execution(log_level: str = "DEBUG", include_result: bool = False):
    """
    Action执行日志装饰器
    
    Args:
        log_level: 日志级别
        include_result: 是否包含执行结果
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            action_type = type(action).__name__
            func_name = func.__name__
            
            # 执行前日志
            battle_logger.log(
                getattr(battle_logger, log_level.lower(), battle_logger.debug),
                f"Executing {func_name} for {action_type}"
            )
            
            result = func(self, action)
            
            # 执行后日志
            if include_result:
                result_info = f"returned {len(result) if result else 0} actions" if result else "returned None"
                battle_logger.log(
                    getattr(battle_logger, log_level.lower(), battle_logger.debug),
                    f"Completed {func_name} for {action_type}, {result_info}"
                )
            
            return result
        return wrapper
    return decorator


def batch_validate(*validators: Callable[[BattleAction], bool]):
    """
    批量验证装饰器
    
    Args:
        *validators: 验证函数列表，每个函数接受BattleAction并返回bool
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: ActionExecutor, action: BattleAction) -> Optional[List[BattleAction]]:
            action_type = type(action).__name__
            
            for i, validator in enumerate(validators):
                try:
                    if not validator(action):
                        battle_logger.warning(f"{action_type} failed validation #{i+1}")
                        return None
                except Exception as e:
                    battle_logger.error(f"{action_type} validation #{i+1} raised exception: {str(e)}")
                    return None
            
            return func(self, action)
        return wrapper
    return decorator


# 预定义的验证函数
def validate_battle_not_over(action: BattleAction) -> bool:
    """验证战斗未结束"""
    # 这里需要访问battle_state，但装饰器中无法直接访问
    # 实际使用时需要在handler内部进行此检查
    return True


def validate_action_not_none(action: BattleAction) -> bool:
    """验证Action不为None"""
    return action is not None


def validate_has_target(action: BattleAction) -> bool:
    """验证Action有target属性"""
    return hasattr(action, 'target') and getattr(action, 'target') is not None


# 组合装饰器：常用的装饰器组合
def standard_action_handler(
    validate_target_alive: bool = True,
    validate_caster_alive: bool = False,
    monitor_perf: bool = True,
    safe_exec: bool = True,
    log_exec: bool = False
):
    """
    标准Action处理器装饰器组合
    
    Args:
        validate_target_alive: 验证目标存活
        validate_caster_alive: 验证施法者存活
        monitor_perf: 监控性能
        safe_exec: 安全执行
        log_exec: 记录执行日志
    """
    def decorator(func: Callable) -> Callable:
        # 从内到外应用装饰器
        decorated_func = func
        
        if log_exec:
            decorated_func = log_action_execution()(decorated_func)
        
        if monitor_perf:
            decorated_func = monitor_performance()(decorated_func)
        
        if safe_exec:
            decorated_func = safe_execute()(decorated_func)
        
        if validate_caster_alive:
            decorated_func = validate_caster(alive_required=True)(decorated_func)
        
        if validate_target_alive:
            decorated_func = validate_target(alive_required=True)(decorated_func)
        
        return decorated_func
    
    return decorator
