from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, TypeVar, Generic, Type

# 第三方库导入
try:
    from sqlalchemy import update, delete
    from sqlalchemy.orm import Session
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    update = delete = Session = None
    SQLALCHEMY_AVAILABLE = False

import os
import sys
import json
import logging
import os

# 本地导入
# from src.database import Base, SessionLocal  # 暂时注释掉，避免导入错误

"""数据访问层 (DAL)。

本模块提供了一个统一的数据访问接口，用于解耦业务逻辑与数据存储。
它定义了一个抽象基类 `DataAccess`，以及两个具体的实现：
- `SQLAlchemyDataAccess`: 使用 SQLAlchemy ORM 与关系型数据库进行交互。
- `JsonDataAccess`: 使用本地 JSON 文件作为数据存储。

通过使用这些类，可以方便地在不同数据源之间切换，而无需修改核心业务代码。
"""


try:
    # 尝试导入Base和SessionLocal（如果可用）
    pass  # 暂时不导入，避免错误
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    Session = update = delete = None


T = TypeVar('T')


class DataAccess(Generic[T], ABC):
    """
    数据访问的抽象基类 (ABC)。

    定义了对任何数据源进行通用 CRUD (创建, 读取, 更新, 删除) 操作的接口。
    使用了泛型 `T` 来表示正在操作的数据模型类型。
    """
    
    @abstractmethod
    def create(self, data: Any) -> T:
        """创建一个新的数据记录。"""
        pass
        
    @abstractmethod
    def read(self, id: Any) -> Optional[T]:
        """根据唯一标识符读取单个数据记录。"""
        pass
        
    @abstractmethod
    def update(self, id: Any, data: Dict[str, Any]) -> bool:
        """根据唯一标识符更新一个已存在的数据记录。"""
        pass
        
    @abstractmethod
    def delete(self, id: Any) -> bool:
        """根据唯一标识符删除一个数据记录。"""
        pass
        
    @abstractmethod
    def list(self, filters: Optional[Dict[str, Any]] = None) -> List[T]:
        """根据一组可选的过滤器列出所有符合条件的数据记录。"""
        pass


class SQLAlchemyDataAccess(DataAccess[T]):
    """
    使用 SQLAlchemy 实现的 `DataAccess` 接口。

    此类通过 SQLAlchemy ORM 与关系型数据库进行交互，为任何
    SQLAlchemy 模型提供通用的 CRUD 功能。
    
    注意: 每个操作（create, read, etc.）都会创建一个新的数据库会话，
    这保证了操作的原子性但可能影响需要多个操作在同一事务中完成的场景。
    """
    
    def __init__(self, model_class: Type[T]):
        """
        初始化一个 SQLAlchemy 数据访问实例。

        Args:
            model_class: 要操作的 SQLAlchemy 模型类 (例如, `User`, `Item`)。
        """
        self.model_class = model_class
        
    def create(self, data: Dict[str, Any]) -> T:
        """
        在数据库中创建一个新的记录。

        Args:
            data: 一个包含新记录数据的字典。字典的键应与模型类的属性名相匹配。

        Returns:
            新创建的 SQLAlchemy 模型实例。
        """
        with SessionLocal() as session:
            obj = self.model_class(**data)
            session.add(obj)
            session.commit()
            session.refresh(obj)
            return obj
            
    def read(self, id: Any) -> Optional[T]:
        """
        根据主键从数据库中读取一个记录。

        Args:
            id: 要查找的记录的主键值。

        Returns:
            如果找到，则返回 SQLAlchemy 模型实例，否则返回 None。
        """
        with SessionLocal() as session:
            return session.query(self.model_class).get(id)
            
    def update(self, id: Any, data: Dict[str, Any]) -> bool:
        """
        更新数据库中的一个记录。

        Args:
            id: 要更新的记录的主键值。
            data: 一个包含要更新的字段和新值的字典。

        Returns:
            如果成功更新了至少一行，则返回 True，否则返回 False。
        """
        with SessionLocal() as session:
            result = session.execute(
                update(self.model_class)
                .where(self.model_class.id == id)
                .values(**data)
            )
            session.commit()
            return result.rowcount > 0
            
    def delete(self, id: Any) -> bool:
        """
        从数据库中删除一个记录。

        Args:
            id: 要删除的记录的主键值。

        Returns:
            如果成功删除了记录，则返回 True，否则返回 False。
        """
        with SessionLocal() as session:
            result = session.execute(
                delete(self.model_class)
                .where(self.model_class.id == id)
            )
            session.commit()
            return result.rowcount > 0
            
    def list(self, filters: Optional[Dict[str, Any]] = None) -> List[T]:
        """
        从数据库中列出符合条件的所有记录。

        Args:
            filters: 一个可选的字典，用于过滤结果。键是模型属性名，值是期望的值。

        Returns:
            一个包含所有符合条件的 SQLAlchemy 模型实例的列表。
        """
        with SessionLocal() as session:
            query = session.query(self.model_class)
            if filters:
                query = query.filter_by(**filters)
            return query.all()


class JsonDataAccess(DataAccess[Dict[str, Any]]):
    """
    基于本地 JSON 文件实现的 `DataAccess` 接口。

    此类将数据存储在一个简单的 JSON 文件中，适合用于小型数据集、
    配置管理或无数据库环境下的快速原型开发。
    """
    
    def __init__(self, file_path: str, id_field: str = "id"):
        """
        初始化一个 JSON 数据访问实例。

        Args:
            file_path: JSON 数据文件的路径。
            id_field: 在每个JSON对象中用作唯一标识符的字段名。
        """
        self.file_path = file_path
        self.id_field = id_field
        self._ensure_file_exists()
        
    def _ensure_file_exists(self) -> None:
        """确保JSON文件存在。如果不存在，则创建一个包含空列表的文件。"""
        if not os.path.exists(self.file_path):
            dir_path = os.path.dirname(self.file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump([], f)
                
    def _load_data(self) -> List[Dict[str, Any]]:
        """从JSON文件中加载所有数据，并处理潜在的解析错误。"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if not content:
                    return []
                return json.loads(content)
        except (json.JSONDecodeError, FileNotFoundError):
            logging.error(f"无法加载或解析JSON文件: {self.file_path}")
            return []
            
    def _save_data(self, data: List[Dict[str, Any]]) -> None:
        """将数据以格式化的方式写回JSON文件。"""
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
    def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        在JSON文件中创建一个新的数据记录。

        如果数据中未提供ID，会自动生成一个自增的整数ID。

        Args:
            data: 要创建的新记录，以字典形式表示。

        Returns:
            创建成功后的完整记录（可能已添加ID）。
        """
        all_data = self._load_data()
        if self.id_field not in data:
            max_id = 0
            for item in all_data:
                item_id = item.get(self.id_field)
                if isinstance(item_id, int):
                    max_id = max(max_id, item_id)
            data[self.id_field] = max_id + 1
            
        all_data.append(data)
        self._save_data(all_data)
        return data
        
    def read(self, id: Any) -> Optional[Dict[str, Any]]:
        """
        根据ID从JSON文件中读取一个记录。

        Args:
            id: 要查找的记录的ID。

        Returns:
            如果找到，则返回代表记录的字典，否则返回 None。
        """
        all_data = self._load_data()
        for item in all_data:
            if item.get(self.id_field) == id:
                return item
        return None
        
    def update(self, id: Any, data: Dict[str, Any]) -> bool:
        """
        更新JSON文件中的一个记录。

        Args:
            id: 要更新的记录的ID。
            data: 包含更新后数据的字典。

        Returns:
            如果找到并成功更新记录，则返回 True，否则返回 False。
        """
        all_data = self._load_data()
        for i, item in enumerate(all_data):
            if item.get(self.id_field) == id:
                # 保留原始ID，更新其他字段
                data[self.id_field] = id
                all_data[i].update(data)
                self._save_data(all_data)
                return True
        return False
        
    def delete(self, id: Any) -> bool:
        """
        从JSON文件中删除一个记录。

        Args:
            id: 要删除的记录的ID。

        Returns:
            如果成功找到并删除记录，则返回 True，否则返回 False。
        """
        all_data = self._load_data()
        initial_count = len(all_data)
        filtered_data = [item for item in all_data if item.get(self.id_field) != id]
        if len(filtered_data) < initial_count:
            self._save_data(filtered_data)
            return True
        return False
        
    def list(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        从JSON文件中列出所有符合条件的记录。

        Args:
            filters: 一个可选的字典，用于过滤结果。

        Returns:
            一个包含所有符合条件的记录的列表。
        """
        all_data = self._load_data()
        if not filters:
            return all_data
            
        filtered_list = []
        for item in all_data:
            if all(item.get(key) == value for key, value in filters.items()):
                filtered_list.append(item)
        return filtered_list 


__all__ = [
    'DataAccess',
    'SQLAlchemyDataAccess', 
    'JsonDataAccess'
]