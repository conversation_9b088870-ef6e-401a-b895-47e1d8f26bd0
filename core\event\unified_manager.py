"""
统一事件管理器

这是事件系统优化的核心组件，整合了原有的多种事件监听方式：
- 订阅式事件监听
- 条件式事件监听  
- 配置式事件监听

提供统一、高性能、类型安全的事件管理接口。
"""

from __future__ import annotations
from typing import Dict, List, Callable, DefaultDict, TYPE_CHECKING, Optional, Any, Protocol, Union, Set
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum, auto
import inspect
import time
import threading
from weakref import WeakSet

if TYPE_CHECKING:
    from ..interfaces import IBattleState
    from .events import GameEvent
    from ..action import BattleAction

from .events import GameEvent
from ..effect.triggers import EventType, TriggerCondition


class EventPriority(Enum):
    """事件监听器优先级"""
    HIGHEST = 0
    HIGH = 10
    NORMAL = 50
    LOW = 90
    LOWEST = 100


@dataclass
class EventListener:
    """统一事件监听器"""
    handler: Callable[[GameEvent, 'IBattleState'], List['BattleAction']]
    priority: EventPriority = EventPriority.NORMAL
    condition: Optional[TriggerCondition] = None
    once: bool = False  # 是否只触发一次
    enabled: bool = True
    name: str = ""
    
    def __post_init__(self):
        if not self.name:
            self.name = getattr(self.handler, '__name__', 'anonymous')


class IEventHandler(Protocol):
    """统一事件处理接口"""
    
    def handle_event(self, event: GameEvent, battle_state: 'IBattleState') -> List['BattleAction']:
        """处理事件的标准接口"""
        ...


class UnifiedEventManager:
    """
    统一事件管理器
    
    整合了原有的多种事件监听方式，提供统一的高性能事件管理接口。
    
    特性:
    - 高性能的订阅式事件分发
    - 支持条件触发和优先级控制
    - 统一的事件类型映射
    - 异常隔离和错误处理
    - 兼容性适配器支持
    """
    
    def __init__(self, max_cache_size: int = 100):
        # 订阅字典：事件类型 -> 监听器列表
        self._subscriptions: DefaultDict[EventType, List[EventListener]] = defaultdict(list)

        # 事件类型映射表（整合配置式）
        self._event_type_map = self._build_event_type_map()

        # 性能优化：缓存已排序的监听器列表（带大小限制）
        self._sorted_cache: Dict[EventType, List[EventListener]] = {}
        self._cache_dirty: Set[EventType] = set()
        self._max_cache_size = max_cache_size

        # 统计信息（使用更详细的统计）
        self._stats: Dict[str, Union[int, float]] = {
            'events_dispatched': 0,
            'listeners_triggered': 0,
            'errors_occurred': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_dispatch_time': 0.0,
            'avg_dispatch_time': 0.0,
            'cache_evictions': 0
        }

        # 一次性监听器缓存（用于清理）
        self._once_listeners: List[EventListener] = []

        # 线程安全
        self._lock = threading.RLock()

        # 错误处理配置
        self._enable_error_logging = True
    
    def _build_event_type_map(self) -> Dict[str, EventType]:
        """构建事件类型映射表"""
        return {
            # 战斗生命周期事件
            "BattleStartEvent": EventType.BATTLE_START,
            "BattleEndEvent": EventType.BATTLE_END,
            
            # 回合事件
            "RoundStartEvent": EventType.ROUND_START,
            "RoundEndEvent": EventType.ROUND_END,
            
            # 精灵生命周期事件
            "SpiritFaintEvent": EventType.SPIRIT_FAINT,
            "SpiritReviveEvent": EventType.SPIRIT_REVIVE,
            "SpiritSummonedEvent": EventType.SPIRIT_SUMMONED,
            
            # 行动事件
            "ActionStartEvent": EventType.ACTION_START,
            "BeforeActionEvent": EventType.BEFORE_ACTION,
            "ActionDecisionEvent": EventType.ACTION_DECISION,
            "ModifyActionEvent": EventType.MODIFY_ACTION,
            "ActionCompleteEvent": EventType.ACTION_COMPLETE,
            "AfterActionEndEvent": EventType.AFTER_ACTION_END,
            
            # 攻击事件
            "DeclareAttackEvent": EventType.DECLARE_ATTACK,
            "BeforeAttackEvent": EventType.BEFORE_ATTACK,
            "ModifyAttackEvent": EventType.MODIFY_ATTACK,
            "ApplyAttackEffectsEvent": EventType.APPLY_ATTACK_EFFECTS,
            "AfterAttackEvent": EventType.AFTER_ATTACK,
            
            # 伤害事件
            "BeforeDamageAppliedEvent": EventType.BEFORE_DAMAGE_APPLIED,
            "AfterDamageEvent": EventType.AFTER_DAMAGE,
            
            # 效果事件
            "EffectAppliedEvent": EventType.EFFECT_APPLIED,
            "ImmunityEvent": EventType.ON_IMMUNITY,
            
            # 特殊事件
            "CommuningTriggeredEvent": EventType.COMMUNING_TRIGGERED,
        }
    
    def subscribe(self, 
                  event_type: Union[EventType, str], 
                  handler: Union[Callable, IEventHandler],
                  priority: EventPriority = EventPriority.NORMAL,
                  condition: Optional[TriggerCondition] = None,
                  once: bool = False,
                  name: str = "") -> EventListener:
        """
        统一订阅接口
        
        Args:
            event_type: 事件类型
            handler: 事件处理器（函数或实现IEventHandler的对象）
            priority: 优先级
            condition: 触发条件
            once: 是否只触发一次
            name: 监听器名称
            
        Returns:
            EventListener: 创建的监听器对象
        """
        # 标准化事件类型
        resolved_event_type: EventType
        if isinstance(event_type, str):
            mapped_type = self._event_type_map.get(event_type)
            if not mapped_type:
                raise ValueError(f"未知的事件类型: {event_type}")
            resolved_event_type = mapped_type
        else:
            resolved_event_type = event_type

        # 标准化处理器
        handler_func: Callable[[GameEvent, 'IBattleState'], List['BattleAction']]
        if hasattr(handler, 'handle_event'):
            # IEventHandler对象
            handler_func = handler.handle_event  # type: ignore
        else:
            # 普通函数
            handler_func = handler  # type: ignore

        # 创建监听器
        listener = EventListener(
            handler=handler_func,
            priority=priority,
            condition=condition,
            once=once,
            name=name
        )

        with self._lock:
            # 添加到订阅列表
            self._subscriptions[resolved_event_type].append(listener)

            # 标记缓存为脏
            self._cache_dirty.add(resolved_event_type)
            if resolved_event_type in self._sorted_cache:
                del self._sorted_cache[resolved_event_type]

            # 记录一次性监听器
            if once:
                self._once_listeners.append(listener)

        return listener
    
    def unsubscribe(self, event_type: Union[EventType, str], listener: EventListener) -> bool:
        """
        取消订阅

        Args:
            event_type: 事件类型
            listener: 要取消的监听器

        Returns:
            bool: 是否成功取消
        """
        # 标准化事件类型
        resolved_event_type: Optional[EventType] = None
        if isinstance(event_type, str):
            resolved_event_type = self._event_type_map.get(event_type)
            if not resolved_event_type:
                return False
        else:
            resolved_event_type = event_type

        with self._lock:
            # 从订阅列表中移除
            if resolved_event_type and resolved_event_type in self._subscriptions:
                try:
                    self._subscriptions[resolved_event_type].remove(listener)

                    # 标记缓存为脏
                    self._cache_dirty.add(resolved_event_type)
                    if resolved_event_type in self._sorted_cache:
                        del self._sorted_cache[resolved_event_type]

                    # 从一次性监听器列表中移除
                    if listener in self._once_listeners:
                        self._once_listeners.remove(listener)

                    return True
                except ValueError:
                    pass

        return False

    def dispatch(self, event: GameEvent, battle_state: 'IBattleState') -> List['BattleAction']:
        """
        优化的统一事件分发接口

        Args:
            event: 要分发的事件
            battle_state: 当前战斗状态

        Returns:
            List[BattleAction]: 监听器生成的动作列表
        """
        start_time = time.perf_counter()

        # 获取事件类型
        event_type = self._get_event_type(event)
        if not event_type:
            return []

        # 获取已排序的监听器（使用缓存）
        listeners = self._get_sorted_listeners(event_type)
        if not listeners:
            return []

        # 统计
        self._stats['events_dispatched'] += 1

        # 分发给所有监听器
        all_actions = []
        listeners_to_remove = []

        with self._lock:
            for listener in listeners:
                if not listener.enabled:
                    continue

                try:
                    # 检查触发条件
                    if listener.condition and not self._should_trigger(listener.condition, event, battle_state):
                        continue

                    # 调用处理器
                    actions = listener.handler(event, battle_state)
                    if actions:
                        all_actions.extend(actions)

                    # 统计
                    self._stats['listeners_triggered'] += 1

                    # 标记一次性监听器待移除
                    if listener.once:
                        listeners_to_remove.append(listener)

                except Exception as e:
                    # 异常隔离：单个监听器失败不影响其他监听器
                    self._stats['errors_occurred'] += 1
                    if self._enable_error_logging:
                        try:
                            # 尝试使用日志系统
                            import logging
                            logger = logging.getLogger(__name__)
                            logger.warning(f"事件监听器 {listener.name} 处理事件 {event.name} 时出错: {e}")
                        except:
                            # 如果日志系统不可用，回退到print
                            print(f"⚠️ 事件监听器 {listener.name} 处理事件 {event.name} 时出错: {e}")

        # 清理一次性监听器
        for listener in listeners_to_remove:
            self.unsubscribe(event_type, listener)
            if listener in self._once_listeners:
                self._once_listeners.remove(listener)

        # 更新性能统计
        dispatch_time = time.perf_counter() - start_time
        self._stats['total_dispatch_time'] += dispatch_time
        if self._stats['events_dispatched'] > 0:
            self._stats['avg_dispatch_time'] = self._stats['total_dispatch_time'] / self._stats['events_dispatched']

        return all_actions

    def _get_sorted_listeners(self, event_type: EventType) -> List[EventListener]:
        """获取已排序的监听器列表（使用缓存）"""
        # 检查缓存
        if event_type in self._sorted_cache and event_type not in self._cache_dirty:
            self._stats['cache_hits'] += 1
            return self._sorted_cache[event_type]

        # 缓存未命中，重新排序
        self._stats['cache_misses'] += 1
        listeners = self._subscriptions.get(event_type, [])
        sorted_listeners = sorted(listeners, key=lambda l: l.priority.value)

        # 检查缓存大小限制
        if len(self._sorted_cache) >= self._max_cache_size:
            # 移除最旧的缓存项（简单的LRU策略）
            oldest_key = next(iter(self._sorted_cache))
            del self._sorted_cache[oldest_key]
            self._stats['cache_evictions'] += 1

        # 更新缓存
        self._sorted_cache[event_type] = sorted_listeners
        self._cache_dirty.discard(event_type)

        return sorted_listeners

    def _get_event_type(self, event: GameEvent) -> Optional[EventType]:
        """获取事件类型（精简版本，内置映射逻辑）"""
        # 内置的事件类型映射（避免额外的模块依赖）
        event_class = type(event)

        # 直接映射常用事件类型（性能最优）
        if event_class.__name__ == 'RoundStartEvent':
            return EventType.ROUND_START
        elif event_class.__name__ == 'RoundEndEvent':
            return EventType.ROUND_END
        elif event_class.__name__ == 'BattleStartEvent':
            return EventType.BATTLE_START
        elif event_class.__name__ == 'BattleEndEvent':
            return EventType.BATTLE_END
        elif event_class.__name__ == 'SpiritFaintEvent':
            return EventType.SPIRIT_FAINT
        elif event_class.__name__ == 'SpiritReviveEvent':
            return EventType.SPIRIT_REVIVE
        elif event_class.__name__ == 'AfterDamageEvent':
            return EventType.AFTER_DAMAGE
        elif event_class.__name__ == 'BeforeAttackEvent':
            return EventType.BEFORE_ATTACK

        # 回退到配置映射
        return self._event_type_map.get(event_class.__name__)

    def _should_trigger(self, condition: TriggerCondition, event: GameEvent, battle_state: 'IBattleState') -> bool:
        """检查是否应该触发监听器"""
        try:
            # 如果条件是可调用的，直接调用
            if callable(condition):
                return condition(event, battle_state)  # type: ignore

            # 如果条件有check方法，调用check方法
            if hasattr(condition, 'check'):
                return condition.check(event, battle_state)  # type: ignore

            # 如果条件有evaluate方法，调用evaluate方法
            if hasattr(condition, 'evaluate'):
                return condition.evaluate(event, battle_state)  # type: ignore

            # 默认情况下总是触发
            return True

        except Exception as e:
            # 条件检查失败时，记录错误但不阻止事件分发
            if self._enable_error_logging:
                print(f"⚠️ 条件检查失败: {e}")
            return True

    def clear_subscriptions(self, event_type: Optional[Union[EventType, str]] = None):
        """清理订阅"""
        with self._lock:
            if event_type is None:
                # 清理所有订阅
                self._subscriptions.clear()
                self._once_listeners.clear()
                self._sorted_cache.clear()
                self._cache_dirty.clear()
            else:
                # 清理特定事件类型的订阅
                if isinstance(event_type, str):
                    event_type = self._event_type_map.get(event_type)

                if event_type and event_type in self._subscriptions:
                    # 从一次性监听器列表中移除
                    listeners_to_remove = self._subscriptions[event_type]
                    for listener in listeners_to_remove:
                        if listener in self._once_listeners:
                            self._once_listeners.remove(listener)

                    # 清理订阅和缓存
                    del self._subscriptions[event_type]
                    if event_type in self._sorted_cache:
                        del self._sorted_cache[event_type]
                    self._cache_dirty.discard(event_type)

    def get_stats(self) -> Dict[str, Union[int, float]]:
        """获取统计信息"""
        return self._stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self._stats = {
            'events_dispatched': 0,
            'listeners_triggered': 0,
            'errors_occurred': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_dispatch_time': 0.0,
            'avg_dispatch_time': 0.0,
            'cache_evictions': 0
        }

    def get_subscription_count(self, event_type: Optional[Union[EventType, str]] = None) -> int:
        """获取订阅数量"""
        if event_type is None:
            return sum(len(listeners) for listeners in self._subscriptions.values())

        if isinstance(event_type, str):
            event_type = self._event_type_map.get(event_type)

        if event_type:
            return len(self._subscriptions.get(event_type, []))

        return 0

    def set_error_logging(self, enabled: bool):
        """设置错误日志记录开关"""
        self._enable_error_logging = enabled

    def get_listeners(self, event_type: Union[EventType, str]) -> List[EventListener]:
        """获取指定事件类型的所有监听器"""
        resolved_type: Optional[EventType] = None
        if isinstance(event_type, str):
            resolved_type = self._event_type_map.get(event_type)
        else:
            resolved_type = event_type

        if resolved_type:
            return self._subscriptions.get(resolved_type, []).copy()
        return []

    def clear_cache(self):
        """清理所有缓存"""
        with self._lock:
            self._sorted_cache.clear()
            self._cache_dirty.clear()

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._sorted_cache),
            'max_cache_size': self._max_cache_size,
            'dirty_entries': len(self._cache_dirty),
            'cache_hit_rate': (
                self._stats['cache_hits'] /
                (self._stats['cache_hits'] + self._stats['cache_misses'])
                if (self._stats['cache_hits'] + self._stats['cache_misses']) > 0 else 0
            )
        }


# 全局统一事件管理器实例
unified_event_manager = UnifiedEventManager()
