# Include base requirements
-r requirements.txt

# Development tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0
isort>=5.12.0
pre-commit>=3.0.0

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-xdist>=3.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
myst-parser>=1.0.0

# Debugging and profiling
ipdb>=0.13.0
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Code quality
bandit>=1.7.0
safety>=2.3.0
vulture>=2.7.0

# Type checking
types-PyYAML>=6.0.0
types-requests>=2.28.0

# Performance testing
locust>=2.14.0

# Optional development tools
jupyter>=1.0.0
notebook>=6.5.0
ipykernel>=6.20.0
