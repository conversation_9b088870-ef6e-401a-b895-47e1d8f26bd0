#!/usr/bin/env python3
"""
调试伤害计算问题

分析为什么几十万攻击力只造成几百伤害
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_damage_calculation():
    """调试伤害计算"""
    print("🔧 调试伤害计算...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        print(f"📊 精灵信息:")
        print(f"  攻击方: {spirit1.name}")
        print(f"    实际攻击力: {spirit1.attributes.get_actual_attack(spirit1)}")
        print(f"    面板攻击力: {spirit1.attributes.attack}")
        print(f"    基础攻击力: {spirit1.attributes.base_attack}")
        
        print(f"  防御方: {spirit2.name}")
        print(f"    物理防御: {spirit2.attributes.pdef}")
        print(f"    魔法防御: {spirit2.attributes.mdef}")
        print(f"    基础物防: {spirit2.attributes.base_pdef}")
        print(f"    基础魔防: {spirit2.attributes.base_mdef}")
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=5,
            turn_order_bonus_energy=50
        )
        
        print(f"\n🎯 执行一回合战斗...")
        
        # 执行一回合并观察伤害
        result = engine.execute_round()
        
        print(f"回合结果: {result}")
        
        # 检查精灵状态变化
        print(f"\n📈 战斗后状态:")
        all_spirits = engine.battle_state.get_all_spirits()
        for spirit in all_spirits:
            print(f"  {spirit.name}: {spirit.current_hp}/{spirit.max_hp} HP")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_damage_calculation():
    """直接测试伤害计算函数"""
    print("\n🔧 直接测试伤害计算函数...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        # 创建模拟的伤害动作
        class MockDamageAction:
            def __init__(self):
                self.power_multiplier = 1.0
                self.damage_type = "PHYSICAL"
                self.is_ultimate = False
                self.is_indirect = False
        
        action = MockDamageAction()
        
        # 创建战斗状态
        from core.formation import Formation
        from core.battle.models import BattleState
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        battle_state = BattleState(formation1, formation2)
        
        # 直接调用伤害计算函数
        from core.battle.utilities.formula_damage_calculator import calculate_formula_damage
        
        print(f"📊 伤害计算输入:")
        print(f"  攻击方: {spirit1.name}")
        print(f"    实际攻击力: {spirit1.attributes.get_actual_attack(spirit1)}")
        print(f"  防御方: {spirit2.name}")
        print(f"    物理防御: {spirit2.attributes.pdef}")
        print(f"  技能倍率: {action.power_multiplier}")
        print(f"  伤害类型: {action.damage_type}")
        
        final_damage, breakdown = calculate_formula_damage(
            caster=spirit1,
            target=spirit2,
            action=action,
            battle_state=battle_state
        )
        
        print(f"\n📈 伤害计算结果:")
        print(f"  最终伤害: {final_damage}")
        print(f"  计算详情字段数: {len(breakdown)}")
        
        # 显示详细的计算步骤
        if "calculation_steps" in breakdown:
            print(f"\n🔍 计算步骤:")
            for step in breakdown["calculation_steps"]:
                print(f"    {step}")
        
        # 显示计算阶段
        if "calculation_phases" in breakdown:
            print(f"\n📋 计算阶段:")
            for phase_name, phase_data in breakdown["calculation_phases"].items():
                print(f"    {phase_name}: {phase_data}")
        
        # 分析问题
        print(f"\n🔍 问题分析:")
        actual_attack = spirit1.attributes.get_actual_attack(spirit1)
        actual_defense = spirit2.attributes.pdef
        
        print(f"  实攻: {actual_attack}")
        print(f"  实防: {actual_defense}")
        print(f"  实攻-实防: {actual_attack - actual_defense}")
        print(f"  防御占攻击比例: {actual_defense / actual_attack * 100:.2f}%")
        
        if actual_defense / actual_attack > 0.9:
            print(f"  ⚠️ 防御过高，使用替代公式!")
            alt_damage = 0.2 * actual_attack * (1 - actual_defense / (actual_attack + actual_defense))
            print(f"  替代公式结果: {alt_damage}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_attribute_values():
    """分析属性数值"""
    print("\n🔧 分析属性数值...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建精灵
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        for spirit_name in available_spirits:
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"\n📊 {spirit.name} 属性分析:")
            
            # 攻击力相关
            base_attack = spirit.attributes.base_attack
            panel_attack = spirit.attributes.attack
            actual_attack = spirit.attributes.get_actual_attack(spirit)
            
            print(f"  攻击力:")
            print(f"    基础攻击: {base_attack}")
            print(f"    面板攻击: {panel_attack}")
            print(f"    实际攻击: {actual_attack}")
            
            # 防御力相关
            base_pdef = spirit.attributes.base_pdef
            panel_pdef = spirit.attributes.pdef
            base_mdef = spirit.attributes.base_mdef
            panel_mdef = spirit.attributes.mdef
            
            print(f"  防御力:")
            print(f"    基础物防: {base_pdef}")
            print(f"    面板物防: {panel_pdef}")
            print(f"    基础魔防: {base_mdef}")
            print(f"    面板魔防: {panel_mdef}")
            
            # 生命值
            base_hp = spirit.attributes.base_hp
            panel_hp = spirit.attributes.hp
            current_hp = spirit.current_hp
            
            print(f"  生命值:")
            print(f"    基础生命: {base_hp}")
            print(f"    面板生命: {panel_hp}")
            print(f"    当前生命: {current_hp}")
            
            # 分析数值合理性
            print(f"  数值分析:")
            if actual_attack > 100000:
                print(f"    ⚠️ 攻击力过高: {actual_attack}")
            if panel_pdef < 1000 and actual_attack > 100000:
                print(f"    ⚠️ 防御力相对攻击力过低: {panel_pdef} vs {actual_attack}")
                print(f"    ⚠️ 防御占攻击比例: {panel_pdef / actual_attack * 100:.4f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 属性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 伤害计算问题调试")
    print("="*60)
    
    tests = [
        ("属性数值分析", analyze_attribute_values),
        ("直接伤害计算测试", test_direct_damage_calculation),
        ("战斗伤害调试", debug_damage_calculation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
