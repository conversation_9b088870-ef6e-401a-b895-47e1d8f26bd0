#!/usr/bin/env python3
"""
调试超杀技能问题

检查超杀技能的阈值和释放机制
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_ultimate_skills():
    """调试超杀技能"""
    print("🔧 调试超杀技能...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 获取精灵服务
        from core.spirit.spirit_service import get_spirit_service
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        print(f"可用精灵: {available_spirits}")
        
        if not available_spirits:
            print("❌ 没有可用精灵")
            return False
        
        # 检查每个精灵的超杀技能
        for spirit_name in available_spirits:
            print(f"\n📊 精灵: {spirit_name}")
            spirit = spirit_service.create_spirit(spirit_name, team=0, position=(1, 1))
            
            print(f"  当前气势: {spirit.energy}")
            print(f"  气势上限: {spirit.max_energy}")
            
            # 检查超杀管理器
            if hasattr(spirit, 'ultimate_manager'):
                ultimate_manager = spirit.ultimate_manager
                print(f"  超杀管理器: {ultimate_manager}")
                print(f"  超杀管理器类型: {type(ultimate_manager)}")
                
                if ultimate_manager:
                    print(f"  超杀管理器属性:")
                    for attr_name in dir(ultimate_manager):
                        if not attr_name.startswith('_'):
                            try:
                                attr_value = getattr(ultimate_manager, attr_name)
                                if not callable(attr_value):
                                    print(f"    {attr_name}: {attr_value}")
                            except:
                                print(f"    {attr_name}: <无法访问>")
                    
                    # 检查阈值
                    threshold = getattr(ultimate_manager, 'threshold', None)
                    print(f"  超杀阈值: {threshold}")
                    
                    # 检查超杀技能
                    if hasattr(ultimate_manager, 'skills'):
                        skills = ultimate_manager.skills
                        print(f"  超杀技能数量: {len(skills) if hasattr(skills, '__len__') else '未知'}")
                        
                        if hasattr(skills, '__iter__'):
                            for i, skill in enumerate(skills):
                                print(f"    超杀技能 {i}: {skill}")
                                if hasattr(skill, 'metadata'):
                                    metadata = skill.metadata
                                    print(f"      名称: {getattr(metadata, 'name', '未知')}")
                                    print(f"      描述: {getattr(metadata, 'description', '未知')}")
                                    print(f"      气势消耗: {getattr(metadata, 'energy_cost', '未知')}")
                    
                    # 检查是否可以释放超杀
                    can_cast = False
                    if hasattr(ultimate_manager, 'can_cast_ultimate'):
                        try:
                            can_cast = ultimate_manager.can_cast_ultimate()
                            print(f"  当前可以释放超杀: {can_cast}")
                        except Exception as e:
                            print(f"  检查超杀释放失败: {e}")
                    
                    # 尝试设置足够的气势
                    print(f"\n  🔋 设置气势到阈值...")
                    if threshold:
                        spirit.energy = threshold
                        print(f"  设置后气势: {spirit.energy}")
                        
                        # 再次检查是否可以释放
                        if hasattr(ultimate_manager, 'can_cast_ultimate'):
                            try:
                                can_cast = ultimate_manager.can_cast_ultimate()
                                print(f"  设置后可以释放超杀: {can_cast}")
                            except Exception as e:
                                print(f"  设置后检查超杀释放失败: {e}")
                else:
                    print("  ❌ 超杀管理器为 None")
            else:
                print("  ❌ 精灵没有 ultimate_manager 属性")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_battle_with_ultimate():
    """测试战斗中的超杀释放"""
    print("\n🔧 测试战斗中的超杀释放...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        if len(available_spirits) < 2:
            print("❌ 可用精灵不足")
            return False
        
        # 创建阵型和精灵
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 创建战斗引擎
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=10,
            turn_order_bonus_energy=100  # 增加气势奖励
        )
        
        print(f"✅ 战斗创建成功")
        print(f"精灵1: {spirit1.name}, 超杀阈值: {getattr(spirit1.ultimate_manager, 'threshold', '未知')}")
        print(f"精灵2: {spirit2.name}, 超杀阈值: {getattr(spirit2.ultimate_manager, 'threshold', '未知')}")
        
        # 执行多回合，观察超杀释放
        for round_num in range(1, 6):
            print(f"\n🎯 执行第 {round_num} 回合...")
            
            # 显示回合前状态
            all_spirits = engine.battle_state.get_all_spirits()
            for spirit in all_spirits:
                threshold = getattr(spirit.ultimate_manager, 'threshold', 300) if hasattr(spirit, 'ultimate_manager') else 300
                print(f"  {spirit.name}: 气势 {spirit.energy}/{threshold}")
            
            # 执行回合
            result = engine.execute_round()
            
            # 显示回合后状态
            print(f"  回合结果: {result}")
            for spirit in all_spirits:
                threshold = getattr(spirit.ultimate_manager, 'threshold', 300) if hasattr(spirit, 'ultimate_manager') else 300
                print(f"  {spirit.name}: 气势 {spirit.energy}/{threshold}")
            
            # 检查是否有超杀释放
            if result.get("type") == "battle_end":
                print(f"  战斗结束: {result}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 战斗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 超杀技能调试")
    print("="*60)
    
    tests = [
        ("超杀技能检查", debug_ultimate_skills),
        ("战斗中超杀测试", test_battle_with_ultimate),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
