/* 精灵战斗模拟器 - 自定义样式 */

/* ==================== 全局样式 ==================== */
:root {
  /* 主题色彩 */
  --primary-blue: #3b82f6;
  --primary-blue-dark: #1d4ed8;
  --secondary-purple: #8b5cf6;
  --accent-gold: #f59e0b;
  --success-green: #10b981;
  --danger-red: #ef4444;
  --warning-orange: #f97316;
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-purple) 100%);
  --gradient-gold: linear-gradient(135deg, #fbbf24 0%, var(--accent-gold) 100%);
  --gradient-battle: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
}

/* 基础动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* ==================== 通用组件样式 ==================== */

/* 美化按钮 */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  transform: translateY(-1px);
}

/* 美化卡片 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: 12px 12px 0 0;
}

.card-body {
  padding: 20px;
}

/* 美化输入框 */
.input-field {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid var(--gray-300);
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-field:invalid {
  border-color: var(--danger-red);
}

/* 美化选择框 */
.select-field {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid var(--gray-300);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-field:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ==================== 页面布局样式 ==================== */

/* 主容器 */
.main-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 标题样式 */
.main-title {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeIn 1s ease-out;
}

/* Battle UI Styling */
.battle-arena {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
}

/* Tab Styling */
.el-tabs__header {
  margin-bottom: 0 !important;
  padding: 0 16px !important;
  background-color: rgba(15, 23, 42, 0.7) !important;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2) !important;
}

.el-tabs__content {
  height: calc(100% - 40px) !important;
  padding: 8px !important;
  overflow: hidden !important;
  flex: 1 !important;
  background-color: rgba(15, 23, 42, 0.5) !important;
}

.el-tabs__nav {
  border: none !important;
}

.el-tabs__item {
  color: #94a3b8 !important;
  height: 40px !important;
  line-height: 40px !important;
}

.el-tabs__item.is-active {
  color: #a78bfa !important;
}

.el-tabs__active-bar {
  background-color: #a78bfa !important;
}

/* Slider Styling */
.el-slider__runway {
  background-color: rgba(148, 163, 184, 0.3);
}

.el-slider__bar {
  background: linear-gradient(to right, #8b5cf6, #ec4899);
}

/* Button Styling */
.el-button--primary {
  background-color: #8b5cf6 !important;
  border-color: #7c3aed !important;
}

.el-button--success {
  background-color: #10b981 !important;
  border-color: #059669 !important;
}

.el-button--warning {
  background-color: #f59e0b !important;
  border-color: #d97706 !important;
}

.el-button--danger {
  background-color: #ef4444 !important;
  border-color: #dc2626 !important;
}