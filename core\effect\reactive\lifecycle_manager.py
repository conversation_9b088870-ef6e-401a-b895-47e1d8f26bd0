"""
效果生命周期管理器

管理效果的创建、更新、销毁等生命周期事件
"""
from __future__ import annotations
import time
import weakref
from typing import Dict, List, Any, Callable, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict

from ...logging import get_logger

logger = get_logger("core.effect.reactive.lifecycle_manager")


class EffectLifecycleEvent(Enum):
    """效果生命周期事件类型"""
    CREATED = "created"           # 效果创建
    APPLIED = "applied"           # 效果应用
    UPDATED = "updated"           # 效果更新
    TRIGGERED = "triggered"       # 效果触发
    EXPIRED = "expired"           # 效果过期
    REMOVED = "removed"           # 效果移除
    SUSPENDED = "suspended"       # 效果暂停
    RESUMED = "resumed"           # 效果恢复


@dataclass
class EffectLifecycleRecord:
    """效果生命周期记录"""
    effect_id: str
    effect_name: str
    event_type: EffectLifecycleEvent
    timestamp: float
    owner_id: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = time.time()


class EffectDependency:
    """效果依赖关系"""
    
    def __init__(self, effect_id: str, depends_on: List[str], dependency_type: str = "requires"):
        self.effect_id = effect_id
        self.depends_on = depends_on  # 依赖的效果ID列表
        self.dependency_type = dependency_type  # 依赖类型：requires, conflicts, enhances
        self.is_satisfied = False
    
    def check_satisfaction(self, active_effects: Set[str]) -> bool:
        """检查依赖是否满足"""
        if self.dependency_type == "requires":
            # 需要所有依赖效果都存在
            self.is_satisfied = all(dep_id in active_effects for dep_id in self.depends_on)
        elif self.dependency_type == "conflicts":
            # 需要所有冲突效果都不存在
            self.is_satisfied = not any(dep_id in active_effects for dep_id in self.depends_on)
        elif self.dependency_type == "enhances":
            # 至少有一个增强效果存在
            self.is_satisfied = any(dep_id in active_effects for dep_id in self.depends_on)
        
        return self.is_satisfied


class EffectLifecycleManager:
    """效果生命周期管理器
    
    管理效果的完整生命周期，包括依赖关系、自动更新等
    """
    
    def __init__(self, owner: Any):
        self.owner = weakref.ref(owner) if owner else None
        self.lifecycle_listeners: Dict[EffectLifecycleEvent, List[Callable]] = defaultdict(list)
        self.effect_listeners: Dict[str, Dict[EffectLifecycleEvent, List[Callable]]] = defaultdict(lambda: defaultdict(list))
        self.lifecycle_history: List[EffectLifecycleRecord] = []
        self.max_history_size = 200
        
        # 依赖关系管理
        self.dependencies: Dict[str, EffectDependency] = {}
        self.active_effects: Set[str] = set()
        
        # 自动更新配置
        self.auto_update_enabled = True
        self.update_interval = 0.1  # 100ms
        self.last_update_time = 0
        
        # 性能统计
        self.stats = {
            "total_events": 0,
            "total_updates": 0,
            "dependency_checks": 0,
            "last_reset": time.time()
        }
    
    def add_lifecycle_listener(self, event_type: EffectLifecycleEvent, callback: Callable[[EffectLifecycleRecord], None]):
        """添加生命周期事件监听器
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self.lifecycle_listeners[event_type].append(callback)
        logger.debug(f"添加生命周期监听器: {event_type.value}")
    
    def add_effect_listener(self, effect_id: str, event_type: EffectLifecycleEvent, 
                           callback: Callable[[EffectLifecycleRecord], None]):
        """添加特定效果的事件监听器
        
        Args:
            effect_id: 效果ID
            event_type: 事件类型
            callback: 回调函数
        """
        self.effect_listeners[effect_id][event_type].append(callback)
        logger.debug(f"添加效果监听器: {effect_id} - {event_type.value}")
    
    def add_dependency(self, dependency: EffectDependency):
        """添加效果依赖关系
        
        Args:
            dependency: 依赖关系
        """
        self.dependencies[dependency.effect_id] = dependency
        logger.debug(f"添加效果依赖: {dependency.effect_id} -> {dependency.depends_on}")
    
    def notify_event(self, effect_id: str, effect_name: str, event_type: EffectLifecycleEvent, **metadata):
        """通知生命周期事件
        
        Args:
            effect_id: 效果ID
            effect_name: 效果名称
            event_type: 事件类型
            **metadata: 额外元数据
        """
        owner = self.owner() if self.owner else None
        owner_id = getattr(owner, 'id', 'unknown') if owner else 'unknown'
        
        # 创建事件记录
        record = EffectLifecycleRecord(
            effect_id=effect_id,
            effect_name=effect_name,
            event_type=event_type,
            timestamp=time.time(),
            owner_id=owner_id,
            metadata=metadata
        )
        
        # 记录事件历史
        self._record_event(record)
        
        # 更新活跃效果集合
        self._update_active_effects(effect_id, event_type)
        
        # 触发监听器
        self._trigger_listeners(record)
        
        # 检查依赖关系
        if event_type in [EffectLifecycleEvent.APPLIED, EffectLifecycleEvent.REMOVED]:
            self._check_dependencies()
        
        # 自动更新检查
        if self.auto_update_enabled:
            self._check_auto_update()
    
    def _record_event(self, record: EffectLifecycleRecord):
        """记录事件历史"""
        self.lifecycle_history.append(record)
        self.stats["total_events"] += 1
        
        # 限制历史记录大小
        if len(self.lifecycle_history) > self.max_history_size:
            self.lifecycle_history.pop(0)
    
    def _update_active_effects(self, effect_id: str, event_type: EffectLifecycleEvent):
        """更新活跃效果集合"""
        if event_type in [EffectLifecycleEvent.APPLIED, EffectLifecycleEvent.CREATED]:
            self.active_effects.add(effect_id)
        elif event_type in [EffectLifecycleEvent.REMOVED, EffectLifecycleEvent.EXPIRED]:
            self.active_effects.discard(effect_id)
    
    def _trigger_listeners(self, record: EffectLifecycleRecord):
        """触发监听器"""
        try:
            # 触发全局生命周期监听器
            for callback in self.lifecycle_listeners[record.event_type]:
                try:
                    callback(record)
                except Exception as e:
                    logger.error(f"生命周期监听器回调失败 {record.event_type.value}: {e}")
            
            # 触发特定效果监听器
            for callback in self.effect_listeners[record.effect_id][record.event_type]:
                try:
                    callback(record)
                except Exception as e:
                    logger.error(f"效果监听器回调失败 {record.effect_id} - {record.event_type.value}: {e}")
        
        except Exception as e:
            logger.error(f"触发监听器失败: {e}")
    
    def _check_dependencies(self):
        """检查所有效果的依赖关系"""
        self.stats["dependency_checks"] += 1
        
        for effect_id, dependency in self.dependencies.items():
            old_satisfaction = dependency.is_satisfied
            new_satisfaction = dependency.check_satisfaction(self.active_effects)
            
            # 依赖状态发生变化
            if old_satisfaction != new_satisfaction:
                if new_satisfaction:
                    logger.debug(f"效果依赖满足: {effect_id}")
                    self._handle_dependency_satisfied(effect_id, dependency)
                else:
                    logger.debug(f"效果依赖不满足: {effect_id}")
                    self._handle_dependency_unsatisfied(effect_id, dependency)
    
    def _handle_dependency_satisfied(self, effect_id: str, dependency: EffectDependency):
        """处理依赖满足的情况"""
        # 可以在这里触发效果激活、增强等逻辑
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'effect_manager'):
            effect = owner.effect_manager.get_effect_by_id(effect_id)
            if effect and hasattr(effect, 'on_dependency_satisfied'):
                try:
                    effect.on_dependency_satisfied(dependency)
                except Exception as e:
                    logger.error(f"处理依赖满足失败 {effect_id}: {e}")
    
    def _handle_dependency_unsatisfied(self, effect_id: str, dependency: EffectDependency):
        """处理依赖不满足的情况"""
        # 可以在这里触发效果停用、削弱等逻辑
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'effect_manager'):
            effect = owner.effect_manager.get_effect_by_id(effect_id)
            if effect and hasattr(effect, 'on_dependency_unsatisfied'):
                try:
                    effect.on_dependency_unsatisfied(dependency)
                except Exception as e:
                    logger.error(f"处理依赖不满足失败 {effect_id}: {e}")
    
    def _check_auto_update(self):
        """检查是否需要自动更新"""
        current_time = time.time()
        if current_time - self.last_update_time >= self.update_interval:
            self._perform_auto_update()
            self.last_update_time = current_time
    
    def _perform_auto_update(self):
        """执行自动更新"""
        self.stats["total_updates"] += 1
        
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'effect_manager'):
            try:
                # 触发效果管理器的更新
                owner.effect_manager.update_effects()
            except Exception as e:
                logger.error(f"自动更新失败: {e}")
    
    def get_effect_history(self, effect_id: str = None, event_type: EffectLifecycleEvent = None, 
                          limit: int = 20) -> List[EffectLifecycleRecord]:
        """获取效果历史记录"""
        records = self.lifecycle_history
        
        if effect_id:
            records = [r for r in records if r.effect_id == effect_id]
        
        if event_type:
            records = [r for r in records if r.event_type == event_type]
        
        return records[-limit:] if records else []
    
    def get_active_effects(self) -> Set[str]:
        """获取当前活跃的效果ID集合"""
        return self.active_effects.copy()
    
    def clear_history(self):
        """清空历史记录"""
        self.lifecycle_history.clear()
        logger.debug("清空效果生命周期历史")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "active_effects_count": len(self.active_effects),
            "dependencies_count": len(self.dependencies),
            "lifecycle_listeners_count": sum(len(listeners) for listeners in self.lifecycle_listeners.values()),
            "effect_listeners_count": sum(
                sum(len(listeners) for listeners in effect_listeners.values())
                for effect_listeners in self.effect_listeners.values()
            ),
            "history_size": len(self.lifecycle_history)
        }
