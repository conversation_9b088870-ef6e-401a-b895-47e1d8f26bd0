"""
神曜圣谕·女帝 - 技能定义模块

包含女帝的所有技能定义：
- 顺天应人 - 被动技能
- 试星之手 - 普攻技能  
- 命定主位 - 超杀技能
- 宿命之环 - 神曜技能
- 星轨逆转 - 通灵技能
- 命压一技 - 通灵-普攻技能
- 天理昭昭 - 通灵-超杀技能
"""
from __future__ import annotations
from typing import List, TYPE_CHECKING

if TYPE_CHECKING:
    from core.spirit.refactored_spirit import RefactoredSpirit

from core.skill.skills import (
    Skill, SkillMetadata, DamageComponent, SkillEffectComponent,
    SingleEnemySelector, SelfSelector
)
from .skill_components import (
    ShiXingZhiShouComponent, MingDingZhuWeiComponent, 
    TianLiZhaoZhaoComponent, MingYaYiJiComponent
)
from .passive_effects import create_shuntian_yingren_effect, create_xinggui_nizhuan_effect
from .suming_zhihuan_shenyao import create_suming_zhihuan_effect


def create_nudi_skills(spirit: RefactoredSpirit) -> List[Skill]:
    """创建神曜圣谕·女帝的技能"""
    
    skills = [
        # 顺天应人 - 被动技能
        Skill(
            metadata=SkillMetadata(
                name="顺天应人",
                description="被动：受击时获得20%的初始减伤，每次通灵后，减伤倍率提高10%，若自身拥有嘲讽，则额外获得30%减伤；己阵精灵免疫时，若自身存活且拥有150气势则立即出手一次（每个大回合限1次）",
                cast_type="PASSIVE"
            ),
            target_selector=SelfSelector(),
            components=[SkillEffectComponent(effect_factory=lambda: create_shuntian_yingren_effect(spirit))]
        ),
        
        # 试星之手 - 普攻技能
        Skill(
            metadata=SkillMetadata(
                name="试星之手",
                description="攻击对手，造成攻击120%的物理伤害，攻击后，对目标造成自身最大生命值*15%的伤害",
                cast_type="ACTIVE",
                energy_cost=0
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=1.2, damage_type="physical"),
                ShiXingZhiShouComponent()
            ]
        ),
        
        # 命定主位 - 超杀技能
        Skill(
            metadata=SkillMetadata(
                name="命定主位",
                description="攻击目标，造成攻击*300%的物理伤害，攻击后，对目标造成自身最大生命值*30%的伤害并令自身获得嘲讽（持续2次攻击）与免疫（持续1次攻击）",
                cast_type="ULTIMATE",
                energy_cost=300
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=3.0, damage_type="physical"),
                MingDingZhuWeiComponent()
            ]
        ),
        
        # 宿命之环 - 神曜技能
        Skill(
            metadata=SkillMetadata(
                name="宿命之环",
                description="神曜技：进入战斗时，获得嘲讽（持续2次攻击）及免疫（持续1次攻击）。3级神格：该次免疫时令自身获得50点气势。6级神格：通灵时额外触发1次。10级神格：每次触发后，嘲讽及免疫的持续时间增加1次攻击",
                cast_type="PASSIVE"
            ),
            target_selector=SelfSelector(),
            components=[SkillEffectComponent(effect_factory=lambda: create_suming_zhihuan_effect(spirit, spirit.metadata.shenge_level))]
        ),
        
        # 星轨逆转 - 通灵技能
        Skill(
            metadata=SkillMetadata(
                name="星轨逆转",
                description="通灵技：集齐100点进度后，触发通灵，最多通灵两次。复活并获得100%生命上限的护盾，清除所有负面效果，永久变身为命运女神",
                cast_type="TONGLING",
                energy_cost=0
            ),
            target_selector=SelfSelector(),
            components=[SkillEffectComponent(effect_factory=lambda: create_xinggui_nizhuan_effect(spirit))]
        ),
        
        # 命压一技 - 通灵-普攻技能
        Skill(
            metadata=SkillMetadata(
                name="命压一技",
                description="通灵-普攻：攻击对手，造成攻击150%的物理伤害，攻击后，对目标造成自身最大生命值*20%的伤害",
                cast_type="TONGLING_ACTIVE",
                energy_cost=0
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=1.5, damage_type="physical"),
                MingYaYiJiComponent()
            ]
        ),
        
        # 天理昭昭 - 通灵-超杀技能
        Skill(
            metadata=SkillMetadata(
                name="天理昭昭",
                description="通灵-超杀：攻击目标，造成攻击*400%的物理伤害，攻击后，对目标造成自身最大生命值*40%的伤害",
                cast_type="TONGLING_ULTIMATE",
                energy_cost=300
            ),
            target_selector=SingleEnemySelector(),
            components=[
                DamageComponent(power_multiplier=4.0, damage_type="physical"),
                TianLiZhaoZhaoComponent()
            ]
        )
    ]
    
    return skills


# 技能数据配置
NUDI_SKILLS_DATA = [
    {
        "name": "顺天应人",
        "type": "PASSIVE",
        "description": "被动：受击时获得20%的初始减伤，每次通灵后，减伤倍率提高10%，若自身拥有嘲讽，则额外获得30%减伤；己阵精灵免疫时，若自身存活且拥有150气势则立即出手一次（每个大回合限1次）"
    },
    {
        "name": "试星之手",
        "type": "ACTIVE",
        "description": "攻击对手，造成攻击120%的物理伤害，攻击后，对目标造成自身最大生命值*15%的伤害"
    },
    {
        "name": "命定主位",
        "type": "ULTIMATE",
        "description": "攻击目标，造成攻击*300%的物理伤害，攻击后，对目标造成自身最大生命值*30%的伤害并令自身获得嘲讽（持续2次攻击）与免疫（持续1次攻击）"
    },
    {
        "name": "宿命之环",
        "type": "SHENYAO",
        "description": "神曜技：进入战斗时，获得嘲讽（持续2次攻击）及免疫（持续1次攻击）。3级神格：该次免疫时令自身获得50点气势。6级神格：通灵时额外触发1次。10级神格：每次触发后，嘲讽及免疫的持续时间增加1次攻击"
    },
    {
        "name": "星轨逆转",
        "type": "TONGLING",
        "description": "通灵技：集齐100点进度后，触发通灵，最多通灵两次。复活并获得100%生命上限的护盾，清除所有负面效果，永久变身为命运女神"
    },
    {
        "name": "命压一技",
        "type": "TONGLING_ACTIVE",
        "description": "通灵-普攻：攻击对手，造成攻击150%的物理伤害，攻击后，对目标造成自身最大生命值*20%的伤害"
    },
    {
        "name": "天理昭昭",
        "type": "TONGLING_ULTIMATE",
        "description": "通灵-超杀：攻击目标，造成攻击*400%的物理伤害，攻击后，对目标造成自身最大生命值*40%的伤害"
    }
]


# 导出函数
__all__ = [
    'create_nudi_skills',
    'NUDI_SKILLS_DATA'
]
