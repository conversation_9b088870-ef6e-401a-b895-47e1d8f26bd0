"""
神曜圣谕·女帝 - 精灵模块

这个模块包含了神曜圣谕·女帝精灵的完整实现，包括：
- 基础效果（嘲讽、减伤等）
- 被动技能效果（顺天应人、星轨逆转等）
- 神曜技能（宿命之环）
- 技能组件（各种技能的特殊效果）
- 技能定义（所有技能的完整定义）
- 精灵主体（精灵的创建和配置）

模块结构：
- effects.py: 基础效果类
- passive_effects.py: 被动技能效果
- suming_zhihuan_shenyao.py: 宿命之环神曜技
- skill_components.py: 技能组件
- skills.py: 技能定义
- spirit.py: 主精灵文件
"""

# 导入主要的创建函数
from .spirit import (
    create_nudi_spirit,
    create_nudi_passive_effects,
    create_shen_yao_sheng_yu_nu_di_passive_effects,  # 向后兼容
    create_passive_effects,  # 向后兼容
    SPIRIT_DATA,
    NUDI_INFO
)

# 导入宿命之环神曜技
from .suming_zhihuan_shenyao import (
    SuMingZhiHuanShenYaoEffect,
    create_suming_zhihuan_effect
)

# 导入基础效果
from .effects import (
    TauntEffect,
    DamageReductionEffect,
    create_taunt_effect,
    create_damage_reduction_effect
)

# 导入被动效果
from .passive_effects import (
    ShunTianYingRenPassiveEffect,
    XingGuiNiZhuanTongLingEffect,
    create_shuntian_yingren_effect,
    create_xinggui_nizhuan_effect
)

# 导入技能组件
from .skill_components import (
    ShiXingZhiShouComponent,
    MingDingZhuWeiComponent,
    TianLiZhaoZhaoComponent,
    MingYaYiJiComponent
)

# 导入技能定义
from .skills import (
    create_nudi_skills,
    NUDI_SKILLS_DATA
)


# 主要导出接口
__all__ = [
    # 主要创建函数
    'create_nudi_spirit',
    'create_nudi_passive_effects',
    
    # 向后兼容函数
    'create_shen_yao_sheng_yu_nu_di_passive_effects',
    'create_passive_effects',
    
    # 宿命之环神曜技
    'SuMingZhiHuanShenYaoEffect',
    'create_suming_zhihuan_effect',
    
    # 基础效果
    'TauntEffect',
    'DamageReductionEffect',
    'create_taunt_effect',
    'create_damage_reduction_effect',
    
    # 被动效果
    'ShunTianYingRenPassiveEffect',
    'XingGuiNiZhuanTongLingEffect',
    'create_shuntian_yingren_effect',
    'create_xinggui_nizhuan_effect',
    
    # 技能组件
    'ShiXingZhiShouComponent',
    'MingDingZhuWeiComponent',
    'TianLiZhaoZhaoComponent',
    'MingYaYiJiComponent',
    
    # 技能定义
    'create_nudi_skills',
    'NUDI_SKILLS_DATA',
    
    # 数据配置
    'SPIRIT_DATA',
    'NUDI_INFO'
]


# 模块信息
__version__ = "1.0.0"
__author__ = "Augment Agent"
__description__ = "神曜圣谕·女帝精灵模块 - 完整的模块化实现"


def get_module_info():
    """获取模块信息"""
    return {
        "name": "神曜圣谕·女帝",
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "spirit_id": "shen_yao_sheng_yu_nu_di",
        "spirit_name": "神曜圣谕·女帝",
        "files": [
            "effects.py",
            "passive_effects.py", 
            "suming_zhihuan_shenyao.py",
            "skill_components.py",
            "skills.py",
            "spirit.py",
            "__init__.py"
        ],
        "main_features": [
            "模块化架构",
            "完整的技能系统",
            "神格等级支持",
            "事件驱动效果",
            "向后兼容接口"
        ]
    }


def validate_module():
    """验证模块完整性"""
    try:
        # 测试主要功能
        spirit = create_nudi_spirit()
        effects = create_nudi_passive_effects(spirit)
        skills = create_nudi_skills(spirit)
        
        return {
            "valid": True,
            "spirit_created": spirit is not None,
            "effects_count": len(effects),
            "skills_count": len(skills),
            "spirit_name": spirit.name if spirit else None
        }
    except Exception as e:
        return {
            "valid": False,
            "error": str(e)
        }


# 快速测试函数
def quick_test():
    """快速测试模块功能"""
    print("🔍 测试神曜圣谕·女帝模块...")
    
    # 获取模块信息
    info = get_module_info()
    print(f"📋 模块: {info['name']} v{info['version']}")
    
    # 验证模块
    validation = validate_module()
    if validation["valid"]:
        print("✅ 模块验证通过")
        print(f"   精灵: {validation['spirit_name']}")
        print(f"   被动效果: {validation['effects_count']}个")
        print(f"   技能: {validation['skills_count']}个")
    else:
        print(f"❌ 模块验证失败: {validation['error']}")
    
    return validation["valid"]


if __name__ == "__main__":
    # 如果直接运行此模块，执行快速测试
    quick_test()
