#!/usr/bin/env python3
"""
测试超杀阈值修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ultrakill_threshold():
    """测试超杀阈值"""
    print("🔧 测试超杀阈值修复...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗引擎
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        available_spirits = spirit_service.list_available_spirits()
        
        formation1 = Formation()
        formation2 = Formation()
        
        spirit1 = spirit_service.create_spirit(available_spirits[0], team=0, position=(1, 1))
        spirit2 = spirit_service.create_spirit(available_spirits[1], team=1, position=(3, 1))
        
        formation1.add_spirit(spirit1, 1, 1)
        formation2.add_spirit(spirit2, 3, 1)
        
        # 设置超杀气势
        if hasattr(spirit1, 'components'):
            from core.components import EnergyComponent
            energy_component = spirit1.components.get_component(EnergyComponent)
            if energy_component:
                energy_component._current_energy = 300
                print(f"🔥 为 {spirit1.name} 设置超杀气势: 300")
        
        engine = create_battle_engine(
            formation1,
            formation2,
            round_limit=2,
            turn_order_bonus_energy=50  # 正常的顺位加气
        )
        
        print(f"✅ 战斗引擎创建成功")
        print(f"  精灵1: {spirit1.name} (气势: {spirit1.energy})")
        print(f"  精灵2: {spirit2.name} (气势: {spirit2.energy})")
        
        # 测试第一只精灵的回合
        print(f"\n🎯 测试第一只精灵回合...")
        result = engine.execute_next_spirit_turn()
        
        if result.get("type") == "spirit_turn":
            spirit_name = result.get("spirit_name", "Unknown")
            actions_count = result.get("actions_generated", 0)
            
            print(f"  精灵: {spirit_name}")
            print(f"  生成动作: {actions_count}")
            
            # 检查是否触发超杀
            if actions_count >= 3 and spirit_name == "天恩圣祭·空灵圣龙":
                print(f"  ✅ 超杀阈值修复成功！{spirit_name} 触发了超杀技能")
                return True
            elif spirit_name == "天恩圣祭·空灵圣龙":
                print(f"  ❌ 超杀阈值问题：{spirit_name} 应该触发超杀但只生成了 {actions_count} 个动作")
                print(f"  当前气势: {spirit1.energy}")
                return False
            else:
                print(f"  ℹ️ 其他精灵 {spirit_name} 正常行动")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 超杀阈值修复测试")
    print("="*50)
    
    result = test_ultrakill_threshold()
    
    print("\n" + "="*50)
    if result:
        print("✅ 超杀阈值修复验证成功")
    else:
        print("❌ 超杀阈值仍有问题")

if __name__ == "__main__":
    main()
