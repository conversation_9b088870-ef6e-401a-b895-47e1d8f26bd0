#!/usr/bin/env python3
"""
测试攻击事件分发
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_attack_events():
    """测试攻击事件分发"""
    print("🔧 测试攻击事件分发...")
    
    try:
        # 初始化系统
        from core.system_manager import initialize_core_systems
        initialize_core_systems()
        
        # 创建战斗
        from core.spirit.spirit_service import get_spirit_service
        from core.formation import Formation
        from core.battle.engines.factory import create_battle_engine
        
        spirit_service = get_spirit_service()
        
        fuyao_spirit = spirit_service.create_spirit("神曜虚无·伏妖", team=0, position=(1, 1))
        other_spirit = spirit_service.create_spirit("神曜圣谕·女帝", team=1, position=(3, 1))
        
        print(f"✅ 创建精灵: {fuyao_spirit.name} vs {other_spirit.name}")
        
        formation1 = Formation()
        formation2 = Formation()
        formation1.add_spirit(fuyao_spirit, 1, 1)
        formation2.add_spirit(other_spirit, 3, 1)
        
        engine = create_battle_engine(formation1, formation2, round_limit=2, turn_order_bonus_energy=50)
        battle_state = engine.battle_state
        
        # 检查事件管理器
        print(f"\n📋 检查事件管理器:")
        if hasattr(battle_state, 'unified_event_manager'):
            event_manager = battle_state.unified_event_manager
            print(f"  事件管理器: {type(event_manager).__name__}")
            
            # 检查订阅情况
            subscriptions = getattr(event_manager, '_subscriptions', {})
            print(f"  事件订阅数量: {sum(len(listeners) for listeners in subscriptions.values())}")
            
            for event_type, listeners in subscriptions.items():
                if listeners:
                    print(f"    {event_type}: {len(listeners)} 个监听器")
        
        # 检查被动效果
        print(f"\n📋 检查被动效果:")
        print(f"  伏妖效果数量: {len(fuyao_spirit.effect_manager.effects)}")
        for effect_id, effect in fuyao_spirit.effect_manager.effects.items():
            effect_name = getattr(effect, 'name', 'Unknown')
            print(f"    - {effect_name}")
            
            # 检查触发条件
            if hasattr(effect, 'get_trigger_conditions'):
                conditions = effect.get_trigger_conditions()
                print(f"      触发条件: {len(conditions)} 个")
                for condition in conditions:
                    print(f"        - {condition.event_type}")
        
        # 模拟攻击事件
        print(f"\n📋 模拟攻击事件:")
        
        # 创建攻击前事件
        from core.event.events import BeforeAttackEvent
        
        attack_event = BeforeAttackEvent(
            attacker=fuyao_spirit,
            target=other_spirit,
            skill_name="咒缚锁妖"
        )
        
        print(f"  创建攻击事件: {type(attack_event).__name__}")
        print(f"    攻击者: {attack_event.attacker.name}")
        print(f"    目标: {attack_event.target.name}")
        
        # 分发事件
        if hasattr(battle_state, 'unified_event_manager'):
            print(f"  分发攻击事件...")
            
            # 记录分发前的效果数量
            target_effects_before = len(other_spirit.effect_manager.effects)
            print(f"    分发前目标效果数量: {target_effects_before}")
            
            try:
                # 🔧 修复：使用正确的方法名
                actions = event_manager.dispatch(attack_event, battle_state)
                print(f"    事件分发成功，生成 {len(actions)} 个动作")
                
                # 检查动作类型
                for i, action in enumerate(actions):
                    action_type = type(action).__name__
                    print(f"      动作{i+1}: {action_type}")

                # 🔧 修复：执行生成的动作
                print(f"    执行生成的动作...")
                from core.battle.execution import UnifiedActionExecutor
                executor = UnifiedActionExecutor(battle_state, None, None)

                try:
                    executor.execute_actions(actions)
                    print(f"    动作执行完成")
                except Exception as exec_error:
                    print(f"    动作执行失败: {exec_error}")

                # 检查执行后的效果数量
                target_effects_after = len(other_spirit.effect_manager.effects)
                print(f"    执行后目标效果数量: {target_effects_after}")
                
                if target_effects_after > target_effects_before:
                    print(f"    ✅ 攻击事件成功触发被动效果！")
                    return True
                else:
                    print(f"    ❌ 攻击事件没有触发被动效果")
                    return False
                    
            except Exception as e:
                print(f"    ❌ 事件分发失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"  ❌ 没有事件管理器")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("="*50)
    print("🔧 攻击事件分发测试")
    print("="*50)
    
    result = test_attack_events()
    
    print("\n" + "="*50)
    if result:
        print("✅ 攻击事件分发测试成功")
        print("被动效果可以被攻击事件触发")
    else:
        print("❌ 攻击事件分发测试失败")

if __name__ == "__main__":
    main()
