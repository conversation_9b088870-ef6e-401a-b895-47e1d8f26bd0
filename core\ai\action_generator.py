"""
智能行动生成器

整合所有检查和计算逻辑，生成精灵的智能行动：
- 行动能力检查
- 技能选择
- 目标选择
- 动态条件评估
- 条件性效果计算
- 行动生成
"""

from __future__ import annotations
from typing import List, Optional, TYPE_CHECKING, Dict, Any, Tu<PERSON>
from dataclasses import dataclass

if TYPE_CHECKING:
    from core.interfaces import IBattleEntity, IBattleState
    from core.skill.skills import Skill

from .capability_checker import ActionCapabilityChecker
from .condition_evaluator import DynamicConditionEvaluator
from .effect_calculator import ConditionalEffectCalculator
from .targeting_integration import EnhancedTargetSelector
from .components_integration import get_components_skill_selector
from core.logging import get_logger

logger = get_logger("ai.generator")

@dataclass
class SkillSelectionResult:
    """技能选择结果"""
    skill: Optional['Skill']
    reason: str
    priority_score: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class TargetSelectionResult:
    """目标选择结果"""
    targets: List['IBattleEntity']
    reason: str
    priority_scores: Dict[str, float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.priority_scores is None:
            self.priority_scores = {}
        if self.metadata is None:
            self.metadata = {}

class SimpleSkillSelector:
    """简化的技能选择器 - 基于固定逻辑：能量够就超杀，否则普攻"""

    def select_skill(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> SkillSelectionResult:
        """选择技能 - 使用固定逻辑"""

        try:
            # 获取精灵的当前能量
            current_energy = getattr(spirit, 'energy', 0)

            # 获取超杀阈值
            ultimate_threshold = self._get_ultimate_threshold(spirit)

            # 获取技能组件
            skill_component = self._get_skill_component(spirit)
            if not skill_component:
                return SkillSelectionResult(
                    skill=None,
                    reason="无法获取技能组件"
                )

            # 判断是否可以使用超杀
            if current_energy >= ultimate_threshold:
                ultimate_skill = self._get_ultimate_skill(skill_component)
                if ultimate_skill:
                    skill_name = getattr(ultimate_skill.metadata, 'name', 'Unknown') if hasattr(ultimate_skill, 'metadata') else 'Unknown'
                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 选择超杀技能: {skill_name} (能量: {current_energy}/{ultimate_threshold})")

                    return SkillSelectionResult(
                        skill=ultimate_skill,
                        reason=f"能量达到超杀阈值 ({current_energy}/{ultimate_threshold})",
                        priority_score=100.0,
                        metadata={'skill_type': 'ultimate', 'energy': current_energy, 'threshold': ultimate_threshold}
                    )

            # 否则使用普攻
            basic_attack = self._get_basic_attack(skill_component)
            if basic_attack:
                skill_name = getattr(basic_attack.metadata, 'name', 'Unknown') if hasattr(basic_attack, 'metadata') else 'Unknown'
                logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 选择普攻: {skill_name} (能量不足超杀: {current_energy}/{ultimate_threshold})")

                return SkillSelectionResult(
                    skill=basic_attack,
                    reason=f"能量不足超杀，使用普攻 ({current_energy}/{ultimate_threshold})",
                    priority_score=50.0,
                    metadata={'skill_type': 'basic_attack', 'energy': current_energy, 'threshold': ultimate_threshold}
                )

            return SkillSelectionResult(
                skill=None,
                reason="既没有超杀技能也没有普攻技能"
            )

        except Exception as e:
            logger.error(f"技能选择失败: {e}")
            return SkillSelectionResult(
                skill=None,
                reason=f"技能选择出错: {e}"
            )

    def _get_ultimate_threshold(self, spirit: 'IBattleEntity') -> int:
        """获取超杀阈值"""
        try:
            if hasattr(spirit, 'get_ultimate_threshold'):
                return spirit.get_ultimate_threshold()
            return 300  # 默认阈值
        except:
            return 300

    def _check_ultimate_energy_threshold(self, spirit: 'IBattleEntity', skill: 'Skill') -> bool:
        """检查超杀技能的气势阈值"""
        try:
            # 方法1: 从超杀管理器获取阈值
            if hasattr(spirit, 'ultimate_manager') and spirit.ultimate_manager:
                skill_name = getattr(skill.metadata, 'name', '') if hasattr(skill, 'metadata') else ''
                if hasattr(spirit.ultimate_manager, 'get_ultimate_skill'):
                    skill_config = spirit.ultimate_manager.get_ultimate_skill(skill_name)
                    if skill_config:
                        current_energy = getattr(spirit, 'current_energy', 0)
                        return current_energy >= skill_config.energy_threshold

                # 获取最低阈值
                if hasattr(spirit.ultimate_manager, 'get_lowest_threshold'):
                    threshold = spirit.ultimate_manager.get_lowest_threshold()
                    current_energy = getattr(spirit, 'current_energy', 0)
                    return current_energy >= threshold

            # 方法2: 使用默认阈值
            current_energy = getattr(spirit, 'current_energy', 0)
            return current_energy >= 300  # 默认超杀阈值
        except:
            return False

    def _get_skill_component(self, spirit: 'IBattleEntity'):
        """获取技能组件"""
        try:
            if hasattr(spirit, 'components'):
                from core.components import SkillComponent
                return spirit.components.get_component(SkillComponent)
            return None
        except:
            return None

    def _get_ultimate_skill(self, skill_component):
        """获取超杀技能"""
        try:
            if hasattr(skill_component, 'get_skills_by_type'):
                ultimate_skills = skill_component.get_skills_by_type('ULTIMATE')
                if ultimate_skills:
                    return ultimate_skills[0]  # 返回第一个超杀技能

            # 回退：查找cast_type为ULTIMATE的技能
            if hasattr(skill_component, 'skills'):
                for skill in skill_component.skills:
                    if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'cast_type'):
                        if skill.metadata.cast_type == 'ULTIMATE':
                            return skill
            return None
        except:
            return None

    def _get_basic_attack(self, skill_component):
        """获取普攻技能"""
        try:
            if hasattr(skill_component, 'get_skills_by_type'):
                basic_skills = skill_component.get_skills_by_type('ACTIVE')
                if basic_skills:
                    # 找能量消耗为0的技能（普攻）
                    for skill in basic_skills:
                        if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'energy_cost'):
                            if skill.metadata.energy_cost == 0:
                                return skill
                    # 如果没找到，返回第一个
                    return basic_skills[0]

            # 回退：查找能量消耗为0的技能
            if hasattr(skill_component, 'skills'):
                for skill in skill_component.skills:
                    if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'energy_cost'):
                        if skill.metadata.energy_cost == 0:
                            return skill
            return None
        except:
            return None
    
    def _get_available_skills(self, spirit: 'IBattleEntity', battle_state: 'IBattleState') -> List['Skill']:
        """获取可用技能列表 - 与components系统集成"""
        available_skills = []

        try:
            # 使用components系统获取技能
            if hasattr(spirit, 'components'):
                from core.components import SkillComponent
                skill_component = spirit.components.get_component(SkillComponent)

                if skill_component:
                    # 使用SkillComponent的方法获取可用技能
                    if hasattr(skill_component, 'get_available_skills'):
                        # 如果有专门的获取可用技能方法
                        available_skills = skill_component.get_available_skills(battle_state)
                    elif hasattr(skill_component, 'skills'):
                        # 否则遍历所有技能并检查可用性
                        for skill in skill_component.skills:
                            if self._can_use_skill(spirit, skill, battle_state, skill_component):
                                available_skills.append(skill)

                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 通过components获得 {len(available_skills)} 个可用技能")

            # 回退方案：直接从精灵获取技能
            elif hasattr(spirit, 'skills'):
                for skill in spirit.skills:
                    if self._can_use_skill(spirit, skill, battle_state):
                        available_skills.append(skill)

                logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 直接获得 {len(available_skills)} 个可用技能")

        except Exception as e:
            logger.error(f"获取可用技能时出错: {e}")

        return available_skills
    
    def _can_use_skill(self, spirit: 'IBattleEntity', skill: 'Skill', battle_state: 'IBattleState', skill_component=None) -> bool:
        """检查是否可以使用技能 - 与SkillComponent集成"""
        try:
            # 优先使用SkillComponent的检查方法
            if skill_component and hasattr(skill_component, 'can_use_skill'):
                return skill_component.can_use_skill(skill, battle_state)

            # 回退到基础检查
            # 1. 检查能量消耗
            if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'energy_cost'):
                energy_cost = skill.metadata.energy_cost or 0

                # 🔧 超杀技能适配：超杀技能不检查energy_cost，而是检查energy_threshold
                cast_type = getattr(skill.metadata, 'cast_type', '')
                if cast_type in ['ULTIMATE', 'TONGLING_ULTIMATE']:
                    # 超杀技能使用阈值检查
                    return self._check_ultimate_energy_threshold(spirit, skill)

                # 普通技能检查energy_cost
                if energy_cost > 0:
                    current_energy = getattr(spirit, 'current_energy', 0)
                    if current_energy < energy_cost:
                        return False

            # 2. 检查技能冷却（通过SkillComponent）
            if skill_component and hasattr(skill_component, 'is_skill_on_cooldown'):
                skill_name = getattr(skill.metadata, 'name', '') if hasattr(skill, 'metadata') else ''
                if skill_name and skill_component.is_skill_on_cooldown(skill_name):
                    return False
            elif hasattr(skill, 'is_on_cooldown') and callable(skill.is_on_cooldown):
                if skill.is_on_cooldown():
                    return False

            # 3. 检查技能特殊条件
            if hasattr(skill, 'can_use') and callable(skill.can_use):
                if not skill.can_use(spirit, battle_state):
                    return False

            # 4. 检查技能是否被禁用
            if skill_component and hasattr(skill_component, 'is_skill_disabled'):
                skill_name = getattr(skill.metadata, 'name', '') if hasattr(skill, 'metadata') else ''
                if skill_name and skill_component.is_skill_disabled(skill_name):
                    return False

            return True

        except Exception as e:
            logger.warning(f"检查技能可用性时出错: {e}")
            return False
    
    def _evaluate_skill_priority(self, spirit: 'IBattleEntity', skill: 'Skill', battle_state: 'IBattleState') -> float:
        """评估技能优先级"""
        score = 0.0
        
        try:
            # 基础分数
            score += 10.0
            
            # 技能类型加成
            if hasattr(skill, 'metadata'):
                cast_type = getattr(skill.metadata, 'cast_type', 'ACTIVE')
                if cast_type == 'ULTIMATE':
                    score += 50.0  # 超杀技能优先级最高
                elif cast_type == 'HERO':
                    score += 40.0  # 英雄技能次之
                elif cast_type == 'ACTIVE':
                    score += 20.0  # 主动技能
                
                # 能量消耗考虑（能量消耗越高，威力通常越大）
                energy_cost = getattr(skill.metadata, 'energy_cost', 0)
                if energy_cost > 0:
                    score += energy_cost * 0.1
            
            # 伤害倍率加成
            if hasattr(skill, 'components'):
                for component in skill.components:
                    if hasattr(component, 'power_multiplier'):
                        score += component.power_multiplier * 5.0
            
            # 生命值状态影响
            if hasattr(spirit, 'current_hp') and hasattr(spirit, 'max_hp'):
                hp_percentage = spirit.current_hp / spirit.max_hp
                if hp_percentage <= 0.3:
                    # 低血量时优先使用高伤害技能
                    score += 15.0
            
        except Exception as e:
            logger.warning(f"评估技能优先级时出错: {e}")
        
        return score

class TargetSelector:
    """目标选择器 - 与现有targeting系统集成"""

    def select_targets(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> TargetSelectionResult:
        """选择攻击目标 - 使用技能的target_selector"""

        try:
            # 使用技能自带的目标选择器
            if hasattr(skill, 'target_selector') and skill.target_selector:
                from ..skill.skills import SkillContext

                # 创建技能上下文
                context = SkillContext(
                    skill_level=getattr(skill, 'current_level', 1),
                    additional_data={'ai_selection': True}
                )

                # 使用技能的目标选择器获取目标
                targets = skill.target_selector.select_targets(spirit, battle_state, context)

                if targets:
                    target_names = [getattr(t, 'name', 'Unknown') for t in targets]
                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 使用 {type(skill.target_selector).__name__} 选择目标: {', '.join(target_names)}")

                    return TargetSelectionResult(
                        targets=targets,
                        reason=f"使用技能的 {type(skill.target_selector).__name__} 选择了 {len(targets)} 个目标",
                        metadata={
                            'selector_type': type(skill.target_selector).__name__,
                            'target_names': target_names
                        }
                    )
                else:
                    return TargetSelectionResult(
                        targets=[],
                        reason=f"技能的 {type(skill.target_selector).__name__} 没有找到有效目标"
                    )

            # 回退到智能目标选择
            return self._intelligent_target_selection(spirit, skill, battle_state)

        except Exception as e:
            logger.error(f"目标选择时出错: {e}")
            return self._fallback_target_selection(spirit, battle_state)

    def _intelligent_target_selection(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> TargetSelectionResult:
        """智能目标选择 - 当技能没有目标选择器时使用"""

        # 根据技能类型推断目标类型
        skill_type = getattr(skill.metadata, 'cast_type', 'ACTIVE') if hasattr(skill, 'metadata') else 'ACTIVE'

        if skill_type in ['PASSIVE']:
            # 被动技能通常不需要目标
            return TargetSelectionResult(
                targets=[],
                reason="被动技能不需要目标"
            )

        # 获取敌方目标
        enemy_team = 1 - getattr(spirit, 'team', 0)
        enemies = battle_state.get_living_spirits(enemy_team)

        if not enemies:
            return TargetSelectionResult(
                targets=[],
                reason="没有存活的敌方目标"
            )

        # 智能选择：优先攻击血量最低的敌人
        target = min(enemies, key=lambda e: getattr(e, 'current_hp', float('inf')))

        return TargetSelectionResult(
            targets=[target],
            reason=f"智能选择血量最低的敌人: {getattr(target, 'name', 'Unknown')}",
            metadata={'selection_method': 'lowest_hp'}
        )

    def _fallback_target_selection(
        self,
        spirit: 'IBattleEntity',
        battle_state: 'IBattleState'
    ) -> TargetSelectionResult:
        """回退目标选择 - 发生错误时使用"""

        try:
            enemy_team = 1 - getattr(spirit, 'team', 0)
            enemies = battle_state.get_living_spirits(enemy_team)

            if enemies:
                target = enemies[0]  # 简单选择第一个敌人
                return TargetSelectionResult(
                    targets=[target],
                    reason=f"回退选择: {getattr(target, 'name', 'Unknown')}",
                    metadata={'selection_method': 'fallback'}
                )
        except:
            pass

        return TargetSelectionResult(
            targets=[],
            reason="回退目标选择失败"
        )
    
    def _evaluate_target_priority(
        self, 
        attacker: 'IBattleEntity', 
        target: 'IBattleEntity', 
        skill: 'Skill', 
        battle_state: 'IBattleState'
    ) -> float:
        """评估目标优先级"""
        score = 0.0
        
        try:
            # 基础分数
            score += 10.0
            
            # 生命值因素（优先攻击低血量目标）
            if hasattr(target, 'current_hp') and hasattr(target, 'max_hp'):
                hp_percentage = target.current_hp / target.max_hp
                score += (1.0 - hp_percentage) * 20.0  # 血量越低分数越高
                
                # 斩杀线加成
                if hp_percentage <= 0.3:
                    score += 15.0
                if hp_percentage <= 0.1:
                    score += 25.0
            
            # 威胁度评估（优先攻击高攻击力目标）
            target_attack = getattr(target, 'attack', 0)
            if target_attack > 0:
                score += target_attack * 0.01
            
            # 无法行动状态加成（配合御神英雄技等）
            from core.status import battle_status_checker
            if battle_status_checker.status_checker.is_unable_to_act(target):
                score += 30.0  # 无法行动的目标优先级很高
            
            # 职业优先级
            if hasattr(target, 'metadata') and hasattr(target.metadata, 'professions'):
                professions = [prof.name for prof in target.metadata.professions]
                if 'HERO' in professions:
                    score += 25.0  # 优先攻击英雄
                elif 'SUPPORT' in professions:
                    score += 20.0  # 其次攻击辅助
                elif 'TANK' in professions:
                    score -= 10.0  # 坦克优先级较低
            
        except Exception as e:
            logger.warning(f"评估目标优先级时出错: {e}")
        
        return score

class IntelligentActionGenerator:
    """智能行动生成器主类 - 简化版本，专注于条件性效果计算"""

    def __init__(self):
        self.capability_checker = ActionCapabilityChecker()
        self.condition_evaluator = DynamicConditionEvaluator()
        self.effect_calculator = ConditionalEffectCalculator()
        self.skill_selector = SimpleSkillSelector()  # 使用简化的技能选择器
    
    def generate_actions(
        self, 
        spirit: 'IBattleEntity', 
        battle_state: 'IBattleState'
    ) -> List[Any]:  # 返回BattleAction列表
        """生成精灵的智能行动"""
        
        logger.info(f"为 {getattr(spirit, 'name', 'Unknown')} 生成行动")
        
        actions = []
        
        try:
            # 第1步：检查行动能力
            capability = self.capability_checker.can_act(spirit, battle_state)
            if not capability.can_act:
                return self._create_unable_to_act_actions(spirit, capability)
            
            # 第2步：选择技能
            skill_result = self.skill_selector.select_skill(spirit, battle_state)
            if not skill_result.skill:
                return self._create_no_skill_actions(spirit, skill_result.reason)
            
            # 第3步：选择目标（使用技能自带的目标选择器）
            targets = self._select_targets_from_skill(spirit, skill_result.skill, battle_state)
            if not targets:
                return self._create_no_target_actions(spirit, "技能的目标选择器没有找到有效目标")
            
            # 第4步：为每个目标生成增强行动
            for target in targets:
                enhanced_action = self._create_enhanced_action(
                    spirit, target, skill_result.skill, battle_state
                )
                if enhanced_action:
                    # 检查是否是超杀技能返回的多个动作
                    if isinstance(enhanced_action, list):
                        actions.extend(enhanced_action)
                    else:
                        actions.append(enhanced_action)
            
            logger.info(f"为 {getattr(spirit, 'name', 'Unknown')} 生成了 {len(actions)} 个行动")
            
        except Exception as e:
            logger.error(f"生成行动时出错: {e}")
            actions = self._create_error_actions(spirit, str(e))

        return actions

    def _select_targets_from_skill(
        self,
        spirit: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> List['IBattleEntity']:
        """使用技能自带的目标选择器选择目标"""

        try:
            # 检查技能是否有目标选择器
            if hasattr(skill, 'target_selector') and skill.target_selector:
                # 确保battle_state有必要的方法
                if hasattr(battle_state, 'get_living_spirits') and not hasattr(battle_state, 'get_targetable_living_spirits'):
                    battle_state.get_targetable_living_spirits = battle_state.get_living_spirits

                # 创建技能上下文
                context = None
                try:
                    from core.skill.skills import SkillContext
                    context = SkillContext(
                        skill_level=getattr(skill, 'current_level', 1),
                        additional_data={'ai_selection': True}
                    )
                except:
                    pass  # 如果无法创建上下文，使用None

                # 使用技能的目标选择器
                try:
                    targets = skill.target_selector.select_targets(spirit, battle_state, context)
                except Exception as e:
                    logger.warning(f"技能目标选择器执行失败: {e}")
                    targets = []

                if targets:
                    target_names = [getattr(t, 'name', 'Unknown') for t in targets]
                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 使用技能目标选择器选择: {', '.join(target_names)}")
                    return targets
                else:
                    logger.debug(f"{getattr(spirit, 'name', 'Unknown')} 技能目标选择器没有找到目标")
                    return []

            # 如果技能没有目标选择器，返回空列表
            logger.warning(f"技能 {getattr(skill.metadata, 'name', 'Unknown') if hasattr(skill, 'metadata') else 'Unknown'} 没有目标选择器")
            return []

        except Exception as e:
            logger.error(f"使用技能目标选择器失败: {e}")
            return []
    
    def _create_enhanced_action(
        self,
        attacker: 'IBattleEntity',
        target: 'IBattleEntity',
        skill: 'Skill',
        battle_state: 'IBattleState'
    ) -> Optional[Any]:  # 返回BattleAction
        """创建增强的攻击动作"""
        
        try:
            # 检查是否是超杀技能
            is_ultimate = False
            if hasattr(skill, 'metadata') and hasattr(skill.metadata, 'cast_type'):
                is_ultimate = skill.metadata.cast_type == 'ULTIMATE'

            # 如果是超杀技能，直接使用技能的cast方法
            if is_ultimate:
                logger.info(f"使用超杀技能 {getattr(skill.metadata, 'name', 'Unknown')} 的cast方法")

                try:
                    # 直接调用技能的cast方法，这会触发我们修复的超杀逻辑
                    skill_actions = skill.cast(battle_state)

                    if skill_actions:
                        logger.info(f"超杀技能生成了 {len(skill_actions)} 个动作")
                        # 返回所有动作，让调用者处理
                        return skill_actions
                    else:
                        logger.warning(f"超杀技能没有生成任何动作")

                except Exception as e:
                    logger.error(f"超杀技能cast失败: {e}")
                    # 回退到普通处理

            # 普通技能或超杀技能失败时的处理
            # 评估动态条件
            conditions = self.condition_evaluator.evaluate_attack_conditions(
                attacker, target, skill, battle_state
            )

            # 计算条件性效果
            conditional_effects = self.effect_calculator.calculate_conditional_effects(
                attacker, target, skill, conditions, battle_state
            )

            # 创建增强攻击动作
            from core.action import EnhancedAttackAction
            enhanced_action = EnhancedAttackAction(
                caster=attacker,
                target=target,
                skill=skill,
                conditional_effects=conditional_effects.effects
            )

            return enhanced_action
            
        except Exception as e:
            logger.error(f"创建增强行动时出错: {e}")
            # 回退到基础攻击动作
            try:
                from core.action import LogAction
                return LogAction(
                    caster=attacker,
                    message=f"{getattr(attacker, 'name', 'Unknown')} 攻击 {getattr(target, 'name', 'Unknown')} (回退行动)"
                )
            except:
                return None
    
    def _create_unable_to_act_actions(self, spirit: 'IBattleEntity', capability) -> List[Any]:
        """创建无法行动的动作"""
        from core.action import LogAction, UnableToActEvent
        
        return [
            LogAction(
                caster=spirit,
                message=f"{getattr(spirit, 'name', 'Unknown')} {capability.reason}，跳过行动"
            ),
            UnableToActEvent(
                caster=spirit,
                reason=capability.reason,
                blocked_by=capability.blocked_by
            )
        ]
    
    def _create_no_skill_actions(self, spirit: 'IBattleEntity', reason: str) -> List[Any]:
        """创建没有技能的动作"""
        from core.action import LogAction
        
        return [
            LogAction(
                caster=spirit,
                message=f"{getattr(spirit, 'name', 'Unknown')} {reason}，跳过行动"
            )
        ]
    
    def _create_no_target_actions(self, spirit: 'IBattleEntity', reason: str) -> List[Any]:
        """创建没有目标的动作"""
        from core.action import LogAction
        
        return [
            LogAction(
                caster=spirit,
                message=f"{getattr(spirit, 'name', 'Unknown')} {reason}，跳过行动"
            )
        ]
    
    def _create_error_actions(self, spirit: 'IBattleEntity', error_msg: str) -> List[Any]:
        """创建错误处理动作"""
        from core.action import LogAction
        
        return [
            LogAction(
                caster=spirit,
                message=f"{getattr(spirit, 'name', 'Unknown')} 行动生成失败: {error_msg}"
            )
        ]
